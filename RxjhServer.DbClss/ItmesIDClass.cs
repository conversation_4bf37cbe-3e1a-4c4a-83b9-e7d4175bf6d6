using System;
using System.Collections.Generic;

namespace RxjhServer.DbClss;

public class ItmesIDClass
{
	private object AsyncLocksw;

	private long long_0;

	private long long_1;

	public ItmesIDClass()
	{
		AsyncLocksw = new object();
		try
		{
			long_0 = 0L;
			long_1 = 0L;
			long_0 = long.Parse(DBA.GetDBValue_3("EXEC   XWWL_GetItemSerial2   1000").ToString());
			long_1 = long_0 + 1000;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(100, "Toàn cục ID Phạm sai lầm" + ex.Message);
			World.conn.Dispose();
			List<Players> list = new List<Players>();
			foreach (Players value in World.allConnectedChars.Values)
			{
				list.Add(value);
			}
			foreach (Players item in list)
			{
				try
				{
					item.Client?.Dispose();
				}
				catch (Exception ex2)
				{
					Form1.WriteLine(1, "Bảo tồn nhân vật s<PERSON> l<PERSON> lầ<PERSON>" + ex2.Message);
				}
			}
			list.Clear();
		}
	}

	public long AcquireBuffer()
	{
		Lock @lock = new Lock(AsyncLocksw, "AcquireBuffer");
		try
		{
			if (long_0 < long_1)
			{
				return long_0++;
			}
			long_0 = long.Parse(DBA.GetDBValue_3("EXEC   XWWL_GetItemSerial2   1000").ToString());
			long_1 = long_0 + 1000;
			return long_0++;
		}
		finally
		{
			int num3 = 2;
			while (true)
			{
				switch (num3)
				{
				case 0:
					break;
				default:
					if (@lock != null)
					{
						num3 = 1;
						continue;
					}
					break;
				case 1:
					((IDisposable)@lock).Dispose();
					num3 = 0;
					continue;
				}
				break;
			}
		}
	}
}
