using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;
using RxjhServer.HelperTools;

namespace RxjhServer.DbClss;

public class RxjhClass
{
	private static ItmesIDClass itmesIDClass_0 = new ItmesIDClass();

	public static string smethod_0(string string_0)
	{
		return BitConverter.ToString(new MD5CryptoServiceProvider().ComputeHash(Encoding.ASCII.GetBytes(string_0))).Replace("-", string.Empty).ToLower();
	}

	public static long GetDBItmeId()
	{
		return itmesIDClass_0.AcquireBuffer();
	}

	public static void ChangeDoorService(int int_0, int int_1, int int_2)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Guild  SET  MonPhucWord={1},MonPhucMauSac={2}  WHERE  ID='{0}'", int_0, int_1, int_2));
	}

	public static void msglog(string string_0, string string_1, string string_2, string string_3, int int_0)
	{
		DBA.ExeSqlCommand($"INSERT  INTO  MsgLog  (userid,username,ToUserName,msg,msgType)  VALUES      ('{string_0}','{string_1}','{string_2}','{string_3}',{int_0})");
	}

	public static string Get_RandomGiftCode()
	{
		string charSet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
		string text = "";
		for (int i = 0; i < 10; i++)
		{
			text += charSet[new Random(World.GetRandomSeed()).Next(0, charSet.Length)];
		}
		DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT Giftcode FROM GIFTCODE WHERE Giftcode='{text}'");
		if (dBToDataTable.Rows.Count > 0)
		{
			return "";
		}
		return text;
	}

	public static string GenerateGiftcode()
	{
		string rdom = "";
		Random rand = new Random();
		for (int i = 1; i <= 10; i++)
		{
			int rd = rand.Next(1, 3);
			string random = "";
			if (rd == 1)
			{
				random = Convert.ToString(char.ToUpper((char)rand.Next(65, 90)));
			}
			if (rd == 2)
			{
				random = Convert.ToString(rand.Next(1, 10));
			}
			rdom += random;
		}
		DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT Giftcode FROM GIFTCODE WHERE Giftcode='{rdom}'");
		if (dBToDataTable.Rows.Count > 0)
		{
			return "";
		}
		return rdom;
	}

	public static string RanDomGiftCode(string Username, int Cash, int Bonus, string NoiDung)
	{
		try
		{
			string Giftcode = GenerateGiftcode();
			if (Giftcode == "")
			{
				RanDomGiftCode(Username, Cash, Bonus, NoiDung);
			}
			DBA.ExeSqlCommand($"INSERT INTO GIFTCODE (Username,Giftcode,Cash,Bonus,Noi_dung) VALUES ('{Username}','{Giftcode}',{Cash},{Bonus},'{NoiDung}')");
			return Giftcode;
		}
		catch
		{
			return "";
		}
	}

	public static int[] SuDung_GiftCode(string UserName, string PlayerUsed, string Giftcode)
	{
		int[] GiaTri = new int[4];
		try
		{
			DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT * FROM GIFTCODE WHERE Username='{UserName}' AND Giftcode='{Giftcode}'");
			if (dBToDataTable != null)
			{
				int DaSuDung = Convert.ToInt32(dBToDataTable.Rows[0]["Da_su_dung"]);
				if (DaSuDung == 1)
				{
					return null;
				}
				GiaTri.SetValue(Convert.ToInt32(dBToDataTable.Rows[0]["Cash"]), 0);
				GiaTri.SetValue(Convert.ToInt32(dBToDataTable.Rows[0]["Bonus"]), 1);
				GiaTri.SetValue(Convert.ToInt32(dBToDataTable.Rows[0]["Goi_Item"]), 2);
				DBA.ExeSqlCommand(string.Format("UPDATE GIFTCODE SET Da_su_dung='1',Nguoi_su_dung='{1}' WHERE Giftcode='{0}'", Giftcode, PlayerUsed));
				return GiaTri;
			}
			return null;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Lỗi Giftcode" + ex.Message);
			return null;
		}
	}

	public static void DropRecord(string string_0, string string_1, long long_0, int int_0, string string_2, int int_1, int int_2, int int_3, int int_4, int int_5, int int_6, int int_7, int int_8, string string_3)
	{
		if (World.DropRecord == 1)
		{
			string sql = $"INSERT  INTO  DropRecord  (FLD_ID,FLD_NAME,FLD_QJID,FLD_PID,FLD_INAME,FLD_MAGIC0,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4,FLD_MAP,FLD_X,FLD_Y,FLD_TYPE)      VALUES      (@UserId,@UserName,@qjid,@pid,@iname,@magic0,@magic1,@magic2,@magic3,@magic4,@map,@x,@y,@type)";
			SqlParameter[] prams = new SqlParameter[14]
			{
				SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@qjid", SqlDbType.Int, 4, long_0),
				SqlDBA.MakeInParam("@pid", SqlDbType.Int, 4, int_0),
				SqlDBA.MakeInParam("@iname", SqlDbType.VarChar, 30, string_2),
				SqlDBA.MakeInParam("@magic0", SqlDbType.Int, 4, int_1),
				SqlDBA.MakeInParam("@magic1", SqlDbType.Int, 4, int_2),
				SqlDBA.MakeInParam("@magic2", SqlDbType.Int, 4, int_3),
				SqlDBA.MakeInParam("@magic3", SqlDbType.Int, 4, int_4),
				SqlDBA.MakeInParam("@magic4", SqlDbType.Int, 4, int_5),
				SqlDBA.MakeInParam("@map", SqlDbType.Int, 4, int_6),
				SqlDBA.MakeInParam("@x", SqlDbType.Int, 4, int_7),
				SqlDBA.MakeInParam("@y", SqlDbType.Int, 4, int_8),
				SqlDBA.MakeInParam("@type", SqlDbType.VarChar, 30, string_3)
			};
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = sql,
				Type = 1
			});
		}
	}

	public static void StoreRecord(string string_0, string string_1, int int_0, string string_2, string string_3, int int_1, long long_0, int int_2, int int_3, int int_4, int int_5, int int_6)
	{
		if (World.StoreRecord == 1)
		{
			string sql = $"INSERT  INTO  StoreRecord  (FLD_ID,FLD_NAME,FLD_PID,FLD_INAME,FLD_TYPE,FLD_NUM,FLD_PRICE,FLD_MAGIC0,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4)      VALUES      (@UserId,@UserName,@pid,@iname,@type,@number,@price,@magic0,@magic1,@magic2,@magic3,@magic4)";
			SqlParameter[] prams = new SqlParameter[12]
			{
				SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@pid", SqlDbType.Int, 4, int_0),
				SqlDBA.MakeInParam("@iname", SqlDbType.VarChar, 30, string_2),
				SqlDBA.MakeInParam("@type", SqlDbType.VarChar, 30, string_3),
				SqlDBA.MakeInParam("@number", SqlDbType.Int, 4, int_1),
				SqlDBA.MakeInParam("@price", SqlDbType.VarChar, 50, long_0.ToString()),
				SqlDBA.MakeInParam("@magic0", SqlDbType.Int, 4, int_2),
				SqlDBA.MakeInParam("@magic1", SqlDbType.Int, 4, int_3),
				SqlDBA.MakeInParam("@magic2", SqlDbType.Int, 4, int_4),
				SqlDBA.MakeInParam("@magic3", SqlDbType.Int, 4, int_5),
				SqlDBA.MakeInParam("@magic4", SqlDbType.Int, 4, int_6)
			};
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = sql,
				Type = 1
			});
		}
	}

	public static void DrugRecord(string string_0, string string_1, int int_0, string string_2, int int_1)
	{
		if (World.DrugRecord == 1)
		{
			string sql = $"INSERT  INTO  DrugRecord(FLD_ID,FLD_NAME,FLD_PID,FLD_INAME,FLD_NUM)  VALUES  (@UserId,@UserName,@pid,@iname,@number)";
			SqlParameter[] prams = new SqlParameter[5]
			{
				SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@pid", SqlDbType.Int, 4, int_0),
				SqlDBA.MakeInParam("@iname", SqlDbType.VarChar, 30, string_2),
				SqlDBA.MakeInParam("@number", SqlDbType.Int, 4, int_1)
			};
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = sql,
				Type = 1
			});
		}
	}

	public static void SyntheticRecord(string string_0, string string_1, string string_2, int int_0, string string_3, string string_4, X_Vat_Pham_Loai VatPhamCLass_0, int cuonghoasl = 0, int magic0_before = 0)
	{
		if (World.SyntheticRecord == 1)
		{
			string sql = $"INSERT  INTO  SyntheticRecord(FLD_ID,FLD_NAME,FLD_QJID,FLD_PID,FLD_INAME,FLD_MAGIC0,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4,FLD_TYPE,FLD_CZID,FLD_SUCCESS,FLD_QHJD,magic0_before)      VALUES      (@UserId,@UserName,@qjid,@pid,@iname,@magic0,@magic1,@magic2,@magic3,@magic4,@type,@czid,@success,@qhjd,@magic0_before)";
			SqlParameter[] prams = new SqlParameter[15]
			{
				SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@qjid", SqlDbType.Int, 4, (int)VatPhamCLass_0.GetItemGlobal_ID),
				SqlDBA.MakeInParam("@pid", SqlDbType.Int, 4, (int)VatPhamCLass_0.GetVatPham_ID),
				SqlDBA.MakeInParam("@iname", SqlDbType.VarChar, 30, VatPhamCLass_0.DatDuocVatPhamTen_XungHao()),
				SqlDBA.MakeInParam("@magic0", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC0),
				SqlDBA.MakeInParam("@magic1", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC1),
				SqlDBA.MakeInParam("@magic2", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC2),
				SqlDBA.MakeInParam("@magic3", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC3),
				SqlDBA.MakeInParam("@magic4", SqlDbType.Int, 4, VatPhamCLass_0.FLD_MAGIC4),
				SqlDBA.MakeInParam("@type", SqlDbType.VarChar, 30, string_3),
				SqlDBA.MakeInParam("@czid", SqlDbType.Int, 4, int_0),
				SqlDBA.MakeInParam("@success", SqlDbType.VarChar, 30, string_4),
				SqlDBA.MakeInParam("@qhjd", SqlDbType.Int, 4, (cuonghoasl == 0) ? VatPhamCLass_0.FLD_CuongHoaSoLuong : cuonghoasl),
				SqlDBA.MakeInParam("@magic0_before", SqlDbType.Int, 4, magic0_before)
			};
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = sql,
				Type = 1
			});
		}
	}

	public static void DelItemRecord(string string_0, string string_1, double double_0, string string_2, int int_0, int int_1, int int_2, int int_3, int int_4, int int_5, string string_6)
	{
		DBA.ExeSqlCommand($"INSERT  INTO  DelItemRecord (UserId,UserName,VatPham_ID,VatPhamTen,MAGIC0,MAGIC1,MAGIC2,MAGIC3,MAGIC4,VatPhamSoLuong,TrangThai)  VALUES  ('{string_0}','{string_1}','{double_0}','{string_2}',{int_0},{int_1},{int_2},{int_3},{int_4},{int_5},'{string_6}')");
	}

	public static void BachBaoCacRecord(string string_0, string string_1, double double_0, string string_2, int int_0, int int_1)
	{
		DBA.ExeSqlCommand($"INSERT  INTO  BachBaoCacRecord (UserId,UserName,VatPham_ID,VatPhamTen,VatPhamSoLuong,NguyenBaoSoLuong)  VALUES  ('{string_0}','{string_1}','{double_0}','{string_2}',{int_0},{int_1})");
	}

	public static void LoginRecord(string string_0, string string_1, string string_2, string string_3)
	{
		if (World.LoginRecord == 1)
		{
			string sql = $"INSERT  INTO  LoginRecord  (UserId,UserName,UserIp,LoaiHinh)  VALUES  (@UserId,@UserName,@UserIp,@LoaiHinh)";
			SqlParameter[] prams = new SqlParameter[4]
			{
				SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@UserIp", SqlDbType.VarChar, 30, string_2),
				SqlDBA.MakeInParam("@LoaiHinh", SqlDbType.VarChar, 30, string_3)
			};
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = sql,
				Type = 1
			});
		}
	}

	public static void ItemRecord(string string_0, string string_1, string string_2, string string_3, double double_0, int int_0, string string_4, int int_1, string string_5, int int_2, string string_6)
	{
		if (World.ItemRecord == 1)
		{
			string sql = $"INSERT  INTO  ItemRecord(UserId,UserName,ToUserId,ToUserName,Global_ID,VatPham_ID,VatPhamTen,VatPhamSoLuong,VatPhamThuocTinh,SoTien,LoaiHinh)      VALUES      (@UserId,@UserName,@ToUserId,@ToUserName,@Global_ID,@VatPham_ID,@VatPhamTen,@VatPhamSoLuong,@VatPham_ThuocTinh,@SoTien,@LoaiHinh)";
			SqlParameter[] prams = new SqlParameter[11]
			{
				SqlDBA.MakeInParam("@UserId", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@UserName", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@ToUserId", SqlDbType.VarChar, 30, string_2),
				SqlDBA.MakeInParam("@ToUserName", SqlDbType.VarChar, 30, string_3),
				SqlDBA.MakeInParam("@Global_ID", SqlDbType.VarChar, 30, double_0),
				SqlDBA.MakeInParam("@VatPham_ID", SqlDbType.VarChar, 30, int_0),
				SqlDBA.MakeInParam("@VatPhamTen", SqlDbType.VarChar, 30, string_4),
				SqlDBA.MakeInParam("@VatPhamSoLuong", SqlDbType.Int, 4, int_1),
				SqlDBA.MakeInParam("@VatPham_ThuocTinh", SqlDbType.VarChar, 100, string_5),
				SqlDBA.MakeInParam("@SoTien", SqlDbType.Int, 4, int_2),
				SqlDBA.MakeInParam("@LoaiHinh", SqlDbType.VarChar, 10, string_6)
			};
			World.SqlPool.Enqueue(new DbPoolClass
			{
				Conn = DBA.getstrConnection(null),
				Prams = prams,
				Sql = sql,
				Type = 1
			});
		}
	}

	public static void BangChien_TienDatCuoc(string string_0, string string_1, int int_0, int int_1)
	{
		DBA.ExeSqlCommand($"INSERT  INTO  BangChien_TienDatCuoc  (UserId,UserName,BangPhaiID,NguyenBaoSoLuong)  VALUES  ('{string_0}','{string_1}',{int_0},{int_1})");
	}

	public static void BangChien_TienDatCuoc_XoaBo(string string_0, string string_1, int int_0, int int_1)
	{
		DBA.ExeSqlCommand($"DELETE  FROM  BangChien_TienDatCuoc  WHERE  UserId='{string_0}'  and  UserName='{string_1}'  and  UserName='{int_0}'");
		switch (int_1)
		{
		case -1:
			DBA.ExeSqlCommand($"UPDATE  TBL_XWWL_Guild  SET  Thua=Thua+1  WHERE  ID='{int_0}'");
			BachBaoCacRecord(string_0, string_1, 0.0, "BangChien失Thua输掉", 1, 50);
			break;
		case 0:
			DBA.ExeSqlCommand($"UPDATE  TBL_XWWL_Guild  SET  Hoa=Hoa+1  WHERE  ID='{int_0}'");
			break;
		case 1:
			DBA.ExeSqlCommand($"UPDATE  TBL_XWWL_Guild  SET  Thang=Thang+1  WHERE  ID='{int_0}'");
			BachBaoCacRecord(string_0, string_1, 0.0, "BangChienThangLoiDatDuoc", 1, 50);
			break;
		}
	}

	public static void ApplyForDoorBadge(int int_0, byte[] byte_0)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Guild  SET  MonHuy={1}  WHERE  ID='{0}'", int_0, Converter.ToString1(byte_0)));
	}

	public static byte[] GetTheDoorBadge(int int_0)
	{
		try
		{
			DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_XWWL_Guild  WHERE  ID  =  {int_0}");
			if (dBToDataTable == null)
			{
				return null;
			}
			if (dBToDataTable.Rows.Count == 0)
			{
				dBToDataTable.Dispose();
				return null;
			}
			if (Buffer.IsEquals(dBToDataTable.Rows[0]["MonHuy"].GetType().ToString(), "System.DBNull"))
			{
				dBToDataTable.Dispose();
				return null;
			}
			byte[] array = (byte[])dBToDataTable.Rows[0]["MonHuy"];
			if (array != null)
			{
				dBToDataTable.Dispose();
				return array;
			}
			dBToDataTable.Dispose();
			return null;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "DatDuocMonHuyerror      " + ex.Message);
			return null;
		}
	}

	public static void BangPhaiAssignAPosition(int int_0, string string_0)
	{
		DBA.ExeSqlCommand("UPDATE  TBL_XWWL_GuildMember  SET  Leve=@zw  WHERE  FLD_NAME=@Username", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@zw", SqlDbType.Int, 0, int_0),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_0)
		});
	}

	public static void ChuyenBangChu_ChucVi(string string_0, string string_1, string string_2)
	{
		string string_3 = "UPDATE  TBL_XWWL_GuildMember  SET  Leve=@zw  WHERE  FLD_NAME=@Username";
		SqlParameter[] sqlParameter_ = new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@zw", SqlDbType.Int, 0, 5),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_1)
		};
		string string_4 = "UPDATE  TBL_XWWL_Guild  SET  G_Master=@Uname  WHERE  G_Name=@Gname";
		SqlParameter[] sqlParameter_2 = new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@Uname", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@Gname", SqlDbType.VarChar, 30, string_2)
		};
		DBA.ExeSqlCommand(string_3, sqlParameter_);
		DBA.ExeSqlCommand(string_4, sqlParameter_2);
	}

	public static int TaoMoi_BangPhai_XacNhan(string string_0)
	{
		return (int)DBA.GetDBValue_3("EXEC  XWWL_SELECT_Guild_DATA  @bpnamea", new SqlParameter[1] { SqlDBA.MakeInParam("@bpnamea", SqlDbType.VarChar, 30, string_0) });
	}

	public static int TaoMoi_BangPhai(string string_0, string string_1, int int_0)
	{
		return (int)DBA.GetDBValue_3("EXEC  XWWL_INT_Guild_DATA_New  @name,  @bpname,@leve", new SqlParameter[3]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, string_1),
			SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, int_0)
		});
	}

	public static int GiaNhapBangPhai(string string_0, string string_1, int int_0)
	{
		return (int)DBA.GetDBValue_3(string.Format("EXEC  XWWL_JR_Guild_DATA_New  @name,  @bpname,@leve", string_0, string_1, int_0), new SqlParameter[3]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, string_1),
			SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, int_0)
		});
	}

	public static int RoiKhoiBangPhai(string string_0)
	{
		return (int)DBA.GetDBValue_3("EXEC  XWWL_Out_Guild_DATA  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
	}

	public static int TrucXuatBangPhai(string string_0, string string_1)
	{
		return (int)DBA.GetDBValue_3("EXEC  XWWL_OutBz_Guild_DATA  @name,  @bpname", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, string_1)
		});
	}

	public static int GetUserName(string string_0)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT  FLD_NAME  FROM  TBL_XWWL_Char  WHERE  FLD_NAME=@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			return 1;
		}
		dBToDataTable.Dispose();
		return -1;
	}

	 public static DataTable DatDuocBangPhai_SoLuongMember(string string_0)
    {
        var dBToDataTable = DBA.GetDBToDataTable(
            @"SELECT gm.*, ch.FLD_JOB
          FROM TBL_XWWL_GuildMember gm
          JOIN TBL_XWWL_Char ch ON gm.FLD_NAME = ch.FLD_NAME
          WHERE gm.G_Name = @name",
            new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });

        if (dBToDataTable == null || dBToDataTable.Rows.Count == 0)
            return null;

        return dBToDataTable;
    }

	public static DataTable Get_Guild_Member_data_V19(string name)
	{
		string rxjhgameDB = Config.IniReadValue("rxjhgame", "DataName").Trim().ToString();
		string rxjhaccountDB = Config.IniReadValue("rxjhaccount", "DataName").Trim().ToString();
		string sqlCommand = string.Format("SELECT Acc.FLD_ONLINE, Guild.FLD_NAME, Guild.Leve, Guild.FLD_LEVEL  FROM ((" + rxjhgameDB + ".dbo.TBL_XWWL_GuildMember as Guild inner join " + rxjhgameDB + ".dbo.TBL_XWWL_Char as GameChar on Guild.FLD_NAME COLLATE DATABASE_DEFAULT = GameChar.FLD_NAME COLLATE DATABASE_DEFAULT) inner join " + rxjhaccountDB + ".dbo.TBL_ACCOUNT as Acc on Acc.FLD_ID COLLATE DATABASE_DEFAULT = GameChar.FLD_ID COLLATE DATABASE_DEFAULT )  WHERE G_Name=@name");
		SqlParameter[] prams = new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name) };
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, prams);
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			return null;
		}
		return dBToDataTable;
	}

	public static DataTable 得到帮派数据new(string name)
	{
		string sqlCommand = $"SELECT * FROM TBL_XWWL_Guild WHERE G_Name = @name";
		SqlParameter[] prams = new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, name) };
		DataTable dBToDataTable = DBA.GetDBToDataTable(sqlCommand, prams);
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			return null;
		}
		return dBToDataTable;
	}

	    public static DataTable DatDuocBangPhaiSoLieu(string string_0)
    {
        var dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_Guild  WHERE  G_Name  =  @name",
            new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
        if (dBToDataTable == null) return null;
        if (dBToDataTable.Rows.Count != 0) return dBToDataTable;
        return null;
    }

	public static DataTable 得到门战帮派数据(string string_0)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_GuildPVP  WHERE  帮派  =  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		while (true)
		{
			switch ((dBToDataTable == null) ? 3 : 2)
			{
			default:
				dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_GuildPVP  WHERE  帮派  =  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
				break;
			case 2:
				if (dBToDataTable.Rows.Count == 0)
				{
					goto case 1;
				}
				return dBToDataTable;
			case 1:
				return null;
			case 3:
				return null;
			case 0:
				break;
			}
		}
	}

	public static DataTable GetUserNameBp(string string_0)
	{
		try
		{
			DataTable dBToDataTable = DBA.GetDBToDataTable($"EXEC  XWWL_LOAD_Guild  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
			if (dBToDataTable == null)
			{
				return null;
			}
			if (dBToDataTable.Rows.Count == 0)
			{
				return null;
			}
			return dBToDataTable;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(4, "GetUserNameBp Error : " + ex);
			return null;
		}
	}

	public static DataTable GetUserWarehouse(string string_0, string string_1)
	{
		DataTable dataTable = null;
		DataTable dBToDataTable = DBA.GetDBToDataTable("select  *  from    [TBL_XWWL_Warehouse]    where  FLD_ID=@Userid  and  FLD_NAME  =@Username", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_1)
		});
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		DBA.ExeSqlCommand("EXEC  XWWL_CREATE_USER_BANK  @Userid,@Username,@aa,@zb", new SqlParameter[4]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_1),
			SqlDBA.MakeInParam("@aa", SqlDbType.Int, 0, 0),
			SqlDBA.MakeInParam("@zb", SqlDbType.VarBinary, World.Item_Db_Byte_Length * 60, new byte[World.Item_Db_Byte_Length * 60])
		});
		dataTable = DBA.GetDBToDataTable("select  *  from  [TBL_XWWL_Warehouse]  where  FLD_ID=@Userid  and  FLD_NAME  =@Username", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, string_1)
		});
		if (dataTable == null)
		{
			return null;
		}
		if (dataTable.Rows.Count == 0)
		{
			return null;
		}
		return dataTable;
	}

	public static DataTable GetUserPublicWarehouse(string string_0)
	{
		DataTable dataTable = null;
		DataTable dBToDataTable = DBA.GetDBToDataTable("select  *  from  [TBL_XWWL_PublicWarehouse]  where  FLD_ID=@Userid", new SqlParameter[1] { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		Converter.ToString1(new byte[World.Item_Db_Byte_Length * 60]);
		Converter.ToString1(new byte[60]);
		DBA.ExeSqlCommand("EXEC  XWWL_CREATE_ID_BANK      @Userid,@aaa,@ck,@ck1", new SqlParameter[4]
		{
			SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@aaa", SqlDbType.Int, 0, 0),
			SqlDBA.MakeInParam("@ck", SqlDbType.VarBinary, World.Item_Db_Byte_Length * 60, new byte[World.Item_Db_Byte_Length * 60]),
			SqlDBA.MakeInParam("@ck1", SqlDbType.VarBinary, 50, new byte[50])
		});
		dataTable = DBA.GetDBToDataTable("select  *  from  [TBL_XWWL_PublicWarehouse]  where  FLD_ID='" + string_0 + "'", new SqlParameter[1] { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, string_0) });
		if (dataTable == null)
		{
			return null;
		}
		if (dataTable.Rows.Count == 0)
		{
			return null;
		}
		return dataTable;
	}

	public static int SetUserName(string string_0, string string_1, int int_0, byte[] byte_0)
	{
		byte[] bytesItem = new byte[World.Item_Db_Byte_Length];
		byte[] buffer2 = new byte[16 * World.Item_Db_Byte_Length];
		byte[] bytesItems = new byte[36 * World.Item_Db_Byte_Length];
		Buffer.GetBytes(GetDBItmeId());
		byte[] bytesItemId = new byte[4];
		Buffer.GetBytes(1);
		switch (int_0)
		{
		case 1:
			bytesItemId = Buffer.GetBytes(100200002);
			break;
		case 2:
			bytesItemId = Buffer.GetBytes(200200002);
			break;
		case 3:
			bytesItemId = Buffer.GetBytes(300200002);
			break;
		case 4:
			bytesItemId = Buffer.GetBytes(400200002);
			break;
		case 5:
			bytesItemId = Buffer.GetBytes(500200002);
			break;
		case 6:
			bytesItemId = Buffer.GetBytes(700200002);
			break;
		case 7:
			bytesItemId = Buffer.GetBytes(800200001);
			break;
		case 8:
			bytesItemId = Buffer.GetBytes(100204001);
			break;
		case 9:
			bytesItemId = Buffer.GetBytes(200204001);
			break;
		case 10:
			bytesItemId = Buffer.GetBytes(900200001);
			break;
		case 11:
			bytesItemId = Buffer.GetBytes(400204001);
			break;
		case 12:
			bytesItemId = Buffer.GetBytes(300204001);
			break;
		case 13:
			bytesItemId = Buffer.GetBytes(500204001);
			break;
		}
		Buffer.BlockCopy(BitConverter.GetBytes(GetDBItmeId()), 0, bytesItem, 0, 4);
		Buffer.BlockCopy(bytesItemId, 0, bytesItem, 8, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(1), 0, bytesItem, 12, 4);
		Buffer.BlockCopy(bytesItem, 0, bytesItems, 0, World.Item_Db_Byte_Length);
		if (World.GoiQua_TrucTuyen != 0)
		{
			byte[] bytesItem2 = new byte[World.Item_Db_Byte_Length];
			Buffer.BlockCopy(BitConverter.GetBytes(GetDBItmeId()), 0, bytesItem2, 0, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(World.GoiQua_TrucTuyen), 0, bytesItem2, 8, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(1), 0, bytesItem2, 12, 4);
			Buffer.BlockCopy(BitConverter.GetBytes(1), 0, bytesItem2, 72, 0);
			Buffer.BlockCopy(bytesItem2, 0, bytesItems, World.Item_Db_Byte_Length, World.Item_Db_Byte_Length);
		}
		int num = 0;
		DataTable dBToDataTable = DBA.GetDBToDataTable($"Select FLD_INDEX FROM TBL_XWWL_Char Where FLD_ID=@FLD_ID", new SqlParameter[1] { SqlDBA.MakeInParam("@FLD_ID", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable.Rows.Count >= 4)
		{
			dBToDataTable.Dispose();
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			num = 0;
		}
		else
		{
			List<int> list = new List<int>();
			for (int i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				int item = (int)dBToDataTable.Rows[i]["FLD_INDEX"];
				list.Add(item);
			}
			for (int j = 0; j < 4; j++)
			{
				if (!list.Contains(j))
				{
					num = j;
					break;
				}
			}
		}
		dBToDataTable.Dispose();
		int num2 = 0;
		int num3 = 0;
		switch (int_0)
		{
		case 4:
			num2 = 124;
			num3 = 116;
			break;
		case 6:
			num2 = 130;
			num3 = 114;
			break;
		case 7:
			num2 = 124;
			num3 = 136;
			break;
		case 1:
		case 8:
			num2 = 145;
			num3 = 116;
			break;
		case 10:
			num2 = 145;
			num3 = 116;
			break;
		case 11:
			num2 = 124;
			num3 = 116;
			break;
		case 2:
		case 3:
		case 5:
		case 9:
		case 12:
			num2 = 133;
			num3 = 118;
			break;
		case 13:
			num2 = 118;
			num3 = 136;
			break;
		}
		if (DBA.ExeSqlCommand($"EXEC XWWL_INT_USER_DATA @FLD_ID,@name,@rwid,@zy,@hp,@mp,@coue,@xrwhex,@xrwhex2", new SqlParameter[9]
		{
			SqlDBA.MakeInParam("@FLD_ID", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_1),
			SqlDBA.MakeInParam("@rwid", SqlDbType.Int, 0, num),
			SqlDBA.MakeInParam("@zy", SqlDbType.Int, 0, int_0),
			SqlDBA.MakeInParam("@hp", SqlDbType.Int, 0, num2),
			SqlDBA.MakeInParam("@mp", SqlDbType.Int, 0, num3),
			SqlDBA.MakeInParam("@coue", SqlDbType.VarBinary, 10, byte_0),
			SqlDBA.MakeInParam("@xrwhex", SqlDbType.VarBinary, buffer2.Length, buffer2),
			SqlDBA.MakeInParam("@xrwhex2", SqlDbType.VarBinary, bytesItems.Length, bytesItems)
		}) == -1)
		{
			return -1;
		}
		return 1;
	}

	public static int GetCwUserName(string string_0, string string_1, int int_0, long long_0)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT  Name  FROM  TBL_XWWL_Cw  WHERE  Name=@name", string_0), new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			if (DBA.ExeSqlCommand(string.Format("EXEC  XWWL_INT_Cw_DATA  @zrname,@name,@id,@type,@zb1,@zb2", string_1, string_0, long_0, int_0, Converter.ToString(new byte[292]), Converter.ToString(new byte[1168])), new SqlParameter[6]
			{
				SqlDBA.MakeInParam("@zrname", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@id", SqlDbType.Int, 0, long_0),
				SqlDBA.MakeInParam("@type", SqlDbType.Int, 0, int_0),
				SqlDBA.MakeInParam("@zb1", SqlDbType.VarBinary, 5 * World.Item_Db_Byte_Length, new byte[5 * World.Item_Db_Byte_Length]),
				SqlDBA.MakeInParam("@zb2", SqlDbType.VarBinary, 16 * World.Item_Db_Byte_Length, new byte[16 * World.Item_Db_Byte_Length])
			}) != -1)
			{
				return 1;
			}
			return -1;
		}
		dBToDataTable.Dispose();
		return -1;
	}

	public static void DoiMoiBangPhai_VinhDu(string string_0, string string_1, int int_0, int int_1, int int_2, int int_3, int int_4, string string_2)
	{
		try
		{
			SqlParameter[] prams = new SqlParameter[8]
			{
				SqlDBA.MakeInParam("@rwname", SqlDbType.VarChar, 50, string_0),
				SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 50, string_1),
				SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, int_0),
				SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, int_1),
				SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, int_2),
				SqlDBA.MakeInParam("@jobleve", SqlDbType.Int, 0, int_3),
				SqlDBA.MakeInParam("@rongyu", SqlDbType.Int, 0, int_4),
				SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, string_2)
			};
			DbPoolClass dbPoolClass = new DbPoolClass();
			dbPoolClass.Conn = DBA.getstrConnection(null);
			dbPoolClass.Prams = prams;
			dbPoolClass.Sql = "UPDATE_menpai_DATA_New";
			DbPoolClass obj = dbPoolClass;
			World.SqlPool.Enqueue(obj);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Bảo tồn môn phái xê\u0301p ha\u0323ng số liệu phạm sai lầm" + ex.Message);
		}
	}

	public static int Set_NguoiVinhDu_SoLieu(int int_0, string string_0, int int_1, int int_2, int int_3, string string_1, string string_2, int int_4)
	{
		DataTable dataTable = DatDuocBangPhaiSoLieu(string_1);
		if (dataTable != null)
		{
			if (dataTable.Rows.Count > 0)
			{
				string_2 = dataTable.Rows[0]["G_Master"].ToString();
			}
			dataTable.Dispose();
		}
		DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT  FLD_TenNhanVat  FROM  TBL_VinhDuHeThong  WHERE  FLD_TenNhanVat=@name  and  FLD_TYPE=@lx", string_0, int_0), new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@lx", SqlDbType.Int, 0, int_0)
		});
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			if (DBA.ExeSqlCommand(string.Format("EXEC  XWWL_INT_RY_DATA  @lx,@name,@job,@level,@zx,@bpname,@mzname,@jf", int_0, string_0, int_1, int_2, int_3, string_1, string_2, int_4), new SqlParameter[8]
			{
				SqlDBA.MakeInParam("@lx", SqlDbType.Int, 0, int_0),
				SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, int_1),
				SqlDBA.MakeInParam("@level", SqlDbType.Int, 0, int_2),
				SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, int_3),
				SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@mzname", SqlDbType.VarChar, 30, string_2),
				SqlDBA.MakeInParam("@jf", SqlDbType.Int, 0, int_4)
			}) != -1)
			{
				return 1;
			}
			return -1;
		}
		dBToDataTable.Dispose();
		if (DBA.ExeSqlCommand($"UPDATE  TBL_VinhDuHeThong  SET  FLD_DiemSo  =FLD_DiemSo+{int_4}  WHERE  FLD_TenNhanVat='{string_0}'  and    FLD_TYPE={int_0}", "GameServer") != -1)
		{
			return 1;
		}
		return -1;
	}

	public static int SetBangPhaiVinhDuSoLieu(string string_0, string string_1, int int_0, int int_1, int int_2, int int_3)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT  *  FROM  TBL_VinhDuHeThong  WHERE  FLD_TYPE  =  3  and  FLD_BangPhai=@mpname", string_0), new SqlParameter[1] { SqlDBA.MakeInParam("@mpname", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			string empty = string.Empty;
			if (DBA.ExeSqlCommand(string.Format("EXEC  XWWL_INT_RY_DATA  @lx,@name,@job,@level,@zx,@bpname,@mzname,@jf", 3, empty, int_1, int_0, int_2, string_0, string_1, int_3), new SqlParameter[8]
			{
				SqlDBA.MakeInParam("@lx", SqlDbType.Int, 0, 3),
				SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, empty),
				SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, int_1),
				SqlDBA.MakeInParam("@level", SqlDbType.Int, 0, int_0),
				SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, int_2),
				SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@mzname", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@jf", SqlDbType.Int, 0, int_3)
			}) != -1)
			{
				return 1;
			}
			return -1;
		}
		dBToDataTable.Dispose();
		if (DBA.ExeSqlCommand($"UPDATE  TBL_VinhDuHeThong  SET  FLD_DiemSo  =FLD_DiemSo+1  WHERE  FLD_BangPhai='{string_0}'  and  FLD_TYPE=      3", "GameServer") != -1)
		{
			return 1;
		}
		return -1;
	}

	public static DataTable DatDuocDanhSach_TruyenThu(string string_0)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_TruyenThuHeThong  WHERE  NguoiNhanThu_NhatVatTen  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		dBToDataTable.Dispose();
		return null;
	}

	public static void SetTruyenThuDaXem(int int_0, int int_1)
	{
		DBA.ExeSqlCommand($"UPDATE  TBL_TruyenThuHeThong  SET  DanhDauDaXem=@rd  WHERE  ID=@id", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@rd", SqlDbType.Int, 0, int_1),
			SqlDBA.MakeInParam("@id", SqlDbType.Int, 30, int_0)
		});
	}

	public static string GetCoupleName(string couplename)
	{
		string name = "";
		DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT  FLD_QlNAME  FROM  TBL_XWWL_Char  WHERE  FLD_NAME  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, couplename) });
		if (dBToDataTable == null)
		{
			name = "";
		}
		else if (dBToDataTable.Rows.Count != 0)
		{
			name = dBToDataTable.Rows[0]["FLD_QlNAME"].ToString();
		}
		dBToDataTable.Dispose();
		return name;
	}

	public static void ThayDoiHonNhan_TrangThai(string string_0, int int_0)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Char  SET  FLD_MARITAL_STATUS={1}  WHERE  FLD_NAME='{0}'", string_0, int_0));
	}

	public static void LyHonTrangThai(string string_0)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Char  SET  FLD_QlNAME='{1}',FLD_QlDu={2},FLD_LOVE_WORD='{3}',FLD_MARITAL_STATUS={4},FLD_MARRIED={5}  WHERE  FLD_NAME='{0}'", string_0, string.Empty, 0, string.Empty, 0, 0));
	}

	public static void CreateBiography(string string_0, string string_1, int int_0, string string_2, int int_1)
	{
		DBA.GetDBValue_3("EXEC  INT_CS_DATA_New  @fname,  @sname,  @msg,  @npcid,@type", new SqlParameter[5]
		{
			SqlDBA.MakeInParam("@fname", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@sname", SqlDbType.VarChar, 30, string_1),
			SqlDBA.MakeInParam("@msg", SqlDbType.NVarChar, 2000, string_2),
			SqlDBA.MakeInParam("@npcid", SqlDbType.Int, 0, int_0),
			SqlDBA.MakeInParam("@type", SqlDbType.Int, 0, int_1)
		});
	}

	public static DataTable DatDuocTenNhanVatWord(string string_0)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_Char  WHERE  FLD_NAME  =  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		while (true)
		{
			switch ((dBToDataTable == null) ? 3 : 0)
			{
			default:
				dBToDataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_Char  WHERE  FLD_NAME  =  @name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
				break;
			case 0:
				if (dBToDataTable.Rows.Count != 0)
				{
					goto case 1;
				}
				return null;
			case 1:
				return dBToDataTable;
			case 3:
				return null;
			case 2:
				break;
			}
		}
	}

	public static void TaoMoi_BangPhaiVinhDu(string string_0, string string_1, int int_0, int int_1, int int_2, int int_3, int int_4, string string_2)
	{
		DBA.ExeSqlCommand("EXEC  INT_menpai_DATA_New  @rwname,  @bpname,@zx,  @leve,@job,@jobleve,@rongyu,@fq", new SqlParameter[8]
		{
			SqlDBA.MakeInParam("@rwname", SqlDbType.VarChar, 50, string_0),
			SqlDBA.MakeInParam("@bpname", SqlDbType.VarChar, 50, string_1),
			SqlDBA.MakeInParam("@zx", SqlDbType.Int, 0, int_0),
			SqlDBA.MakeInParam("@leve", SqlDbType.Int, 0, int_1),
			SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, int_2),
			SqlDBA.MakeInParam("@jobleve", SqlDbType.Int, 0, int_3),
			SqlDBA.MakeInParam("@rongyu", SqlDbType.Int, 0, int_4),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 50, string_2)
		});
	}

	public static DataTable DatDuocBangPhaiVinhDuSoLieu(string string_0, string string_1)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  VinhDuBangPhaiXepHang  WHERE  FLD_BP  =  @name  and  FLD_FQ=@fq", new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0),
			SqlDBA.MakeInParam("@fq", SqlDbType.VarChar, 30, string_1)
		});
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		return null;
	}

	public static DataTable DatDuocMasterData(string string_0)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_SuDoSoLieu  WHERE  FLD_TNAME  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		return null;
	}

	public static DataTable DatDuocApprenticeData(string string_0)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_SuDoSoLieu  WHERE  FLD_SNAME  =@name", new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return null;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			return dBToDataTable;
		}
		return null;
	}

	public static void AddGuildPoint(int point, string FLD_NAME)
	{
		string sqlCommand = $"UPDATE TBL_XWWL_GuildMember SET FLD_GuildPoint=FLD_GuildPoint+@point WHERE FLD_NAME=@Username";
		SqlParameter[] prams = new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@point", SqlDbType.Int, 0, point),
			SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, FLD_NAME)
		};
		DBA.ExeSqlCommand(sqlCommand, prams);
	}

	public static void AddGuildLeve(int diem, string G_Name)
	{
	}

	public static void AddSuDo(int diem, string FLD_TNAME)
	{
		string sqlCommand = $"UPDATE TBL_SuDoSoLieu SET FLD_STYHD = FLD_STYHD+@diem where FLD_TNAME=@name";
		SqlParameter[] prams = new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@diem", SqlDbType.Int, 0, diem),
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, FLD_TNAME)
		};
		DBA.ExeSqlCommand(sqlCommand, prams);
	}

	public static void ThangCapSuDo(int diem, string FLD_TNAME)
	{
		string sqlCommand = $"UPDATE TBL_SuDoSoLieu SET FLD_STLEVEL = FLD_STLEVEL+@diem where FLD_TNAME=@name";
		SqlParameter[] prams = new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@diem", SqlDbType.Int, 0, diem),
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, FLD_TNAME)
		};
		DBA.ExeSqlCommand(sqlCommand, prams);
	}

	public static void ResetSuDo(int diem, string FLD_TNAME)
	{
		string sqlCommand = $"UPDATE TBL_SuDoSoLieu SET FLD_STYHD = FLD_STYHD * @diem where FLD_TNAME=@name";
		SqlParameter[] prams = new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@diem", SqlDbType.Int, 0, diem),
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, FLD_TNAME)
		};
		DBA.ExeSqlCommand(sqlCommand, prams);
	}

	public static void AddSkill_1(int Skill, string FLD_TNAME)
	{
		string sqlCommand = $"UPDATE TBL_SuDoSoLieu SET FLD_STWG1 = @skill where FLD_TNAME=@name";
		SqlParameter[] prams = new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@skill", SqlDbType.Int, 0, Skill),
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, FLD_TNAME)
		};
		DBA.ExeSqlCommand(sqlCommand, prams);
	}

	public static void AddSkill_2(int Skill, string FLD_TNAME)
	{
		string sqlCommand = $"UPDATE TBL_SuDoSoLieu SET FLD_STWG2 = @skill where FLD_TNAME=@name";
		SqlParameter[] prams = new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@skill", SqlDbType.Int, 0, Skill),
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, FLD_TNAME)
		};
		DBA.ExeSqlCommand(sqlCommand, prams);
	}

	public static void AddSkill_3(int Skill, string FLD_TNAME)
	{
		string sqlCommand = $"UPDATE TBL_SuDoSoLieu SET FLD_STWG3 = @skill where FLD_TNAME=@name";
		SqlParameter[] prams = new SqlParameter[2]
		{
			SqlDBA.MakeInParam("@skill", SqlDbType.Int, 0, Skill),
			SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, FLD_TNAME)
		};
		DBA.ExeSqlCommand(sqlCommand, prams);
	}

	public static int TaoMoiQuanHeSuDo(string string_0, string string_1, int int_0, int int_1, int int_2, int int_3, int int_4, int int_5, int int_6)
	{
		DataTable dBToDataTable = DBA.GetDBToDataTable(string.Format("SELECT  FLD_TNAME  FROM  TBL_SuDoSoLieu  WHERE  FLD_TNAME=@name", string_0), new SqlParameter[1] { SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 30, string_0) });
		if (dBToDataTable == null)
		{
			return -1;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			dBToDataTable.Dispose();
			if (DBA.ExeSqlCommand(string.Format("EXEC  INT_St_DATA  @sname,@tname,@tlevel,@index,@stlevel,@styhd,@stwg1,@stwg2,@stwg3", string_1, string_0, int_0, int_1, int_2, int_3, int_4, int_5, int_6), new SqlParameter[9]
			{
				SqlDBA.MakeInParam("@sname", SqlDbType.VarChar, 30, string_1),
				SqlDBA.MakeInParam("@tname", SqlDbType.VarChar, 30, string_0),
				SqlDBA.MakeInParam("@tlevel", SqlDbType.Int, 0, int_0),
				SqlDBA.MakeInParam("@index", SqlDbType.Int, 0, int_1),
				SqlDBA.MakeInParam("@stlevel", SqlDbType.Int, 0, int_2),
				SqlDBA.MakeInParam("@styhd", SqlDbType.Int, 0, int_3),
				SqlDBA.MakeInParam("@stwg1", SqlDbType.Int, 0, int_4),
				SqlDBA.MakeInParam("@stwg2", SqlDbType.Int, 0, int_5),
				SqlDBA.MakeInParam("@stwg3", SqlDbType.Int, 0, int_6)
			}) != -1)
			{
				return 1;
			}
			return -1;
		}
		dBToDataTable.Dispose();
		return -1;
	}

	public static int GiaiTruQuanHeThayTro(string string_0, string string_1)
	{
		if (DBA.ExeSqlCommand($"delete  [TBL_SuDoSoLieu]  WHERE  FLD_TNAME  ='{string_0}'  and  FLD_SNAME='{string_1}'", "GameServer") != -1)
		{
			return 1;
		}
		return -1;
	}

	public static int LayDuocCongHienKinhNghiem(string string_0, string string_1)
	{
		int num = 0;
		try
		{
			DataTable dBToDataTable = DBA.GetDBToDataTable($"SELECT  FLD_SuPhu  from  [TBL_XWWL_Char]  where  FLD_NAME  ='{string_1}'");
			if (dBToDataTable == null)
			{
				return num;
			}
			if (dBToDataTable.Rows.Count > 0)
			{
				byte[] array = new byte[28];
				byte[] array2 = (byte[])dBToDataTable.Rows[0]["FLD_SuPhu"];
				System.Buffer.BlockCopy(array2, 0, array, 0, array2.Length);
				byte[] array3 = new byte[16];
				System.Buffer.BlockCopy(array, 0, array3, 0, 16);
				if (Encoding.Default.GetString(array3).Replace("\0", string.Empty).Trim() == string_0)
				{
					int num2 = Buffer.ToInt32(array, 18);
					if (num2 <= 0)
					{
						dBToDataTable.Dispose();
						return 0;
					}
					num += num2;
					array[18] = 0;
					array[19] = 0;
					array[20] = 0;
					array[21] = 0;
					DBA.ExeSqlCommand($"UPDATE  [TBL_XWWL_Char]  set  FLD_SuPhu=@sf  where  FLD_NAME  =@name", new SqlParameter[2]
					{
						SqlDBA.MakeInParam("@sf", SqlDbType.VarBinary, 28, array),
						SqlDBA.MakeInParam("@name", SqlDbType.VarChar, string_1.Length, string_1)
					});
				}
			}
			dBToDataTable.Dispose();
			return num;
		}
		catch
		{
			return 0;
		}
	}

	public static void DeleteUserLogin(string UserId)
	{
		DBA.ExeSqlCommand($"DELETE TBL_UserLimited WHERE UserID = '{UserId}'", "rxjhaccount");
	}

	public static void Delete_All_UserLogin(int PortSv)
	{
		DBA.ExeSqlCommand($"DELETE TBL_UserLimited WHERE PortChannel={PortSv}", "rxjhaccount");
	}
	   public static int UpdateBachBaoNewRecord(int id, string status)
        {
            Form1.WriteLine(1, $"Update Cash {id} - {status}");
            var query = $"UPDATE CASH_SHOP_LOG SET STATUS = '{status}' WHERE ID = {id}";
            return DBA.ExeSqlCommand(query, "BBG");
        }
	public static int InsertCashShopLog(string status, string message, string buyer, int amount, double price,
            int marketId, string productId)
        {
            var query = @"INSERT INTO CASH_SHOP_LOG (STATUS, MESSAGE, USERNAME, AMOUNT, PRICE, ITEM_ID, PRODUCT_ID) 
                  VALUES (@Status, @Message, @Username, @Amount, @Price, @ItemId, @ProductId)";
    
            var parameters = new[]
            {
                new SqlParameter("@Status", status),
                new SqlParameter("@Message", message),
                new SqlParameter("@Username", buyer),
                new SqlParameter("@Amount", amount),
                new SqlParameter("@Price", price),
                new SqlParameter("@ItemId", marketId),
                new SqlParameter("@ProductId", productId)
            };
    
            var insertedId = DBA.ExeSqlCommand(query, "BBG", parameters);
            return insertedId;
        }

    public static void Update_Gold_Extra(string string_0, int int_0)
	{
		DBA.ExeSqlCommand(string.Format("UPDATE  TBL_XWWL_Char  SET  FLD_MONEYEXTRALEVEL={1}  WHERE  FLD_NAME='{0}'", string_0, int_0));
	}
}
