using System;
using System.Data;
using System.Data.SqlClient;
using System.Linq;

namespace RxjhServer.DbClss;

public class SqlDBA
{
	public static int RunProc(SqlConnection sqlConnection_0, string string_0, SqlParameter[] sqlParameter_0)
	{
		try
		{
			sqlConnection_0.Open();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(100, "SqlDBA Số liệu tầng _ Sai lầm 1" + ex.Message);
			return -1;
		}
		SqlCommand sqlCommand = CreateCommand(sqlConnection_0, string_0, sqlParameter_0);
		try
		{
			sqlCommand.ExecuteNonQuery();
		}
		catch (Exception ex2)
		{
			Form1.WriteLine(100, "SqlDBA Số liệu tầng _ Sai lầm 2" + ex2.Message);
			sqlCommand.Parameters.Clear();
			return -1;
		}
		finally
		{
			sqlConnection_0.Close();
			sqlConnection_0.Dispose();
		}
		return (int)sqlCommand.Parameters["ReturnValue"].Value;
	}

	public static int RunProcSql(SqlConnection sqlConnection_0, string string_0, SqlParameter[] sqlParameter_0)
	{
		int result = -1;
		try
		{
			sqlConnection_0.Open();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(100, "SqlDBA Số liệu tầng _ Sai lầm 3" + ex.Message);
			return result;
		}
		SqlCommand sqlCommand = CreateCommandSql(sqlConnection_0, string_0, sqlParameter_0);
		try
		{
			return sqlCommand.ExecuteNonQuery();
		}
		catch (Exception ex2)
		{
			Form1.WriteLine(100, "SqlDBA Số liệu tầng _ Sai lầm 4" + ex2.Message + " "+ string_0 + " " );
			sqlCommand.Parameters.Clear();
			return result;
		}
		finally
		{
			sqlConnection_0.Close();
			sqlConnection_0.Dispose();
		}
	}

	public static void RunProc(SqlConnection sqlConnection_0, string string_0, out SqlDataReader sqlDataReader_0)
	{
		SqlCommand sqlCommand = CreateCommand(sqlConnection_0, string_0, null);
		sqlDataReader_0 = sqlCommand.ExecuteReader(CommandBehavior.CloseConnection);
	}

	public static void RunProc(SqlConnection sqlConnection_0, string string_0, SqlParameter[] sqlParameter_0, out SqlDataReader sqlDataReader_0)
	{
		SqlCommand sqlCommand = CreateCommand(sqlConnection_0, string_0, sqlParameter_0);
		sqlDataReader_0 = sqlCommand.ExecuteReader(CommandBehavior.CloseConnection);
	}

	public static void RunProc(SqlConnection sqlConnection_0, string string_0, SqlParameter[] sqlParameter_0, out DataSet dataSet_0)
	{
		SqlCommand sqlCommand = CreateCommand(sqlConnection_0, string_0, sqlParameter_0);
		SqlDataAdapter sqlDataAdapter = new SqlDataAdapter(sqlCommand);
		try
		{
			DataSet dataSet = new DataSet();
			sqlDataAdapter.Fill(dataSet);
			sqlCommand.Parameters.Clear();
			sqlConnection_0.Close();
			sqlConnection_0.Dispose();
			dataSet_0 = dataSet;
			sqlDataAdapter.Dispose();
		}
		finally
		{
			int num = 1;
			while (true)
			{
				switch (num)
				{
				case 0:
					break;
				default:
					if (sqlDataAdapter != null)
					{
						num = 2;
						continue;
					}
					break;
				case 2:
					((IDisposable)sqlDataAdapter).Dispose();
					num = 0;
					continue;
				}
				break;
			}
		}
	}

	public static DataTable RunProcc(SqlConnection sqlConnection_0, string string_0, SqlParameter[] sqlParameter_0)
	{
		int num = 0;
		DataTable dataTable = new DataTable();
		SqlCommand sqlCommand = CreateCommand(sqlConnection_0, string_0, sqlParameter_0);
		SqlDataAdapter sqlDataAdapter = new SqlDataAdapter(sqlCommand);
		try
		{
			try
			{
				sqlDataAdapter.Fill(dataTable);
			}
			catch
			{
			}
			sqlCommand.Parameters.Clear();
			sqlDataAdapter.Dispose();
			sqlConnection_0.Close();
			sqlConnection_0.Dispose();
			return dataTable;
		}
		finally
		{
			num = 1;
			while (true)
			{
				switch (num)
				{
				case 0:
					break;
				default:
					if (sqlDataAdapter != null)
					{
						num = 2;
						continue;
					}
					break;
				case 2:
					((IDisposable)sqlDataAdapter).Dispose();
					num = 0;
					continue;
				}
				break;
			}
		}
	}

	public static SqlCommand CreateCommand(SqlConnection sqlConnection_0, string string_0, SqlParameter[] sqlParameter_0)
	{
		SqlParameter[] array = null;
		SqlCommand sqlCommand = new SqlCommand(string_0, sqlConnection_0);
		sqlCommand.CommandType = CommandType.StoredProcedure;
		sqlCommand.CommandTimeout = 180;
		if (sqlParameter_0 != null)
		{
			array = sqlParameter_0;
			SqlParameter[] array2 = array;
			SqlParameter[] array3 = array2;
			SqlParameter[] array4 = array3;
			foreach (SqlParameter value in array4)
			{
				sqlCommand.Parameters.Add(value);
			}
		}
		sqlCommand.Parameters.Add(new SqlParameter("ReturnValue", SqlDbType.Int, 4, ParameterDirection.ReturnValue, isNullable: false, 0, 0, string.Empty, DataRowVersion.Default, null));
		return sqlCommand;
	}

	public static SqlCommand CreateCommandSql(SqlConnection sqlConnection_0, string string_0, SqlParameter[] sqlParameter_0)
	{
		SqlParameter[] array = null;
		SqlCommand sqlCommand = new SqlCommand(string_0, sqlConnection_0);
		sqlCommand.CommandType = CommandType.Text;
		sqlCommand.CommandTimeout = 180;
		if (sqlParameter_0 != null)
		{
			array = sqlParameter_0;
			SqlParameter[] array2 = array;
			SqlParameter[] array3 = array2;
			SqlParameter[] array4 = array3;
			foreach (SqlParameter value in array4)
			{
				sqlCommand.Parameters.Add(value);
			}
		}
		sqlCommand.Parameters.Add(new SqlParameter("ReturnValue", SqlDbType.Int, 4, ParameterDirection.ReturnValue, isNullable: false, 0, 0, string.Empty, DataRowVersion.Default, null));
		return sqlCommand;
	}

	public static SqlParameter MakeInParam(string string_0, SqlDbType sqlDbType_0, int int_0, object object_0)
	{
		return MakeParam(string_0, sqlDbType_0, int_0, ParameterDirection.Input, object_0);
	}

	public static SqlParameter MakeOutParam(string string_0, SqlDbType sqlDbType_0, int int_0)
	{
		return MakeParam(string_0, sqlDbType_0, int_0, ParameterDirection.Output, null);
	}

	public static SqlParameter MakeParam(string string_0, SqlDbType sqlDbType_0, int int_0, ParameterDirection parameterDirection_0, object object_0)
	{
		SqlParameter sqlParameter = null;
		while (true)
		{
			SqlParameter sqlParameter2;
			switch ((int_0 > 0) ? 2 : 0)
			{
			case 0:
				sqlParameter2 = new SqlParameter(string_0, sqlDbType_0);
				goto IL_0087;
			case 2:
			case 3:
				sqlParameter2 = new SqlParameter(string_0, sqlDbType_0, int_0);
				goto IL_0087;
			case 8:
				if (parameterDirection_0 == ParameterDirection.Output)
				{
					goto case 1;
				}
				goto case 4;
			case 1:
			case 5:
				if (object_0 == null)
				{
					break;
				}
				goto case 4;
			case 4:
				sqlParameter.Value = object_0;
				break;
			default:
				continue;
			case 6:
				break;
				IL_0087:
				sqlParameter = sqlParameter2;
				sqlParameter.Direction = parameterDirection_0;
				goto case 8;
			}
			break;
		}
		return sqlParameter;
	}
}
