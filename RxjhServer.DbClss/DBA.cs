using System;
using System.Collections;
using System.Data;
using System.Data.SqlClient;

namespace RxjhServer.DbClss;

public class DBA
{
	public static void serlog(string string_0)
	{
		try
		{
			int num = 0;
			string[] array = null;
			string sqlJl = World.SqlJl;
			if (sqlJl.Length == 0)
			{
				return;
			}
			char[] separator = new char[1] { '|' };
			string[] array2 = sqlJl.Split(separator);
			array = array2;
			foreach (string text in array)
			{
				if (string_0.ToLower().IndexOf(text.ToLower()) != -1)
				{
					Form1.WriteLine(99, string_0);
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "serlog Error  " + ex);
		}
	}

	public static void serlog(string string_0, SqlParameter[] sqlParameter_0)
	{
		try
		{
			int num4 = 0;
			SqlParameter[] array2 = null;
			int num5 = 0;
			SqlParameter sqlParameter = null;
			string sqlJl = World.SqlJl;
			while (sqlJl.Length != 0)
			{
				string[] array = sqlJl.Split('|');
				int num3 = 0;
				bool flag = true;
				while (true)
				{
					switch ((num3 >= array.Length) ? 13 : 3)
					{
					case 8:
					case 12:
						return;
					case 3:
						if (string_0.ToLower().IndexOf(array[num3].ToLower()) != -1)
						{
							goto case 0;
						}
						goto case 6;
					case 0:
						Form1.WriteLine(99, string_0);
						goto case 6;
					case 6:
						num3++;
						continue;
					default:
						sqlJl = World.SqlJl;
						break;
					case 13:
						num4 = 0;
						goto case 7;
					case 7:
					case 15:
					case 19:
						if (num4 < array.Length)
						{
							array2 = sqlParameter_0;
							num5 = 0;
							goto case 1;
						}
						return;
					case 1:
					case 5:
					case 14:
						if (num5 < array2.Length)
						{
							sqlParameter = array2[num5];
							goto case 9;
						}
						goto case 11;
					case 9:
						if (sqlParameter.SqlValue.ToString().ToLower().IndexOf(array[num4].ToLower()) != -1)
						{
							goto case 4;
						}
						goto case 10;
					case 4:
						Form1.WriteLine(99, string_0 + " Thông số SQL  " + sqlParameter.SqlValue.ToString());
						goto case 10;
					case 10:
						num5++;
						goto case 1;
					case 11:
						num4++;
						goto case 7;
					case 2:
					case 16:
					case 18:
						continue;
					case 17:
						break;
					}
					break;
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "serlog Error  " + ex);
		}
	}

	public static void Setlog(string string_0, SqlParameter[] sqlParameter_0, Exception exception_0)
	{
		try
		{
			SqlParameter[] array = null;
			Form1.WriteLine(100, "-----------DBA Số liệu tầng _ Sai lầm -----------");
			Form1.WriteLine(100, string_0);
			if (sqlParameter_0 != null)
			{
				array = sqlParameter_0;
				SqlParameter[] array2 = array;
				SqlParameter[] array3 = array2;
				foreach (SqlParameter sqlParameter in array3)
				{
					Form1.WriteLine(100, sqlParameter.SqlValue.ToString());
				}
			}
			Form1.WriteLine(100, exception_0?.ToString() ?? "");
			logo.errorLog(string_0 + " " + exception_0);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "setlog Error  " + ex);
		}
	}

	public static string getstrConnection(string string_0)
	{
		try
		{
			DbClass value = null;
			string result = null;
			bool flag = true;
			while (flag)
			{
				if (string_0 == null)
				{
					goto IL_009e;
				}
				goto IL_0099;
				IL_009e:
				string_0 = "GameServer";
				goto IL_0099;
				IL_0099:
				while (true)
				{
					bool flag2 = true;
					while (true)
					{
						object obj;
						switch ((!World.Db.TryGetValue(string_0, out value)) ? 1 : 6)
						{
						case 1:
						case 3:
							obj = null;
							goto IL_0085;
						case 6:
							obj = value.SqlConnect;
							goto IL_0085;
						case 4:
							return result;
						case 0:
							break;
						case 7:
							goto end_IL_0092;
						case 2:
							goto end_IL_0099;
						default:
							goto IL_00ae;
							IL_0085:
							result = (string)obj;
							goto case 4;
						}
						continue;
						end_IL_0092:
						break;
					}
					continue;
					end_IL_0099:
					break;
				}
				goto IL_009e;
				IL_00ae:;
			}
		}
		catch
		{
			return null;
		}
		return null;
	}

	public static int ExeSqlCommand(string string_0, SqlParameter[] sqlParameter_0)
	{
		int num = 0;
		serlog(string_0, sqlParameter_0);
		SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		try
		{
			SqlCommand sqlCommand = SqlDBA.CreateCommandSql(sqlConnection, string_0, sqlParameter_0);
			try
			{
				int num2 = 0;
				int num3 = -1;
				num = 0;
				try
				{
					sqlConnection.Open();
				}
				catch
				{
					return -1;
				}
				num = 1;
				try
				{
					num3 = sqlCommand.ExecuteNonQuery();
				}
				catch (Exception exception_)
				{
					Setlog(string_0, sqlParameter_0, exception_);
				}
				sqlCommand.Dispose();
				sqlConnection.Close();
				sqlConnection.Dispose();
				num2 = num3;
				num = 2;
				return num2;
			}
			finally
			{
				num = 0;
				while (true)
				{
					switch (num)
					{
					case 2:
						break;
					case 1:
						((IDisposable)sqlCommand).Dispose();
						num = 2;
						continue;
					default:
						if (sqlCommand != null)
						{
							num = 1;
							continue;
						}
						break;
					}
					break;
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "ExeSqlCommand Error  " + ex);
			return -1;
		}
		finally
		{
			num = 0;
			while (true)
			{
				switch (num)
				{
				case 2:
					break;
				case 1:
					((IDisposable)sqlConnection).Dispose();
					num = 2;
					continue;
				default:
					if (sqlConnection != null)
					{
						num = 1;
						continue;
					}
					break;
				}
				break;
			}
		}
	}
public static int ExeSqlCommand(string query, string connectionName, SqlParameter[] parameters = null)
    {
        serlog(query);
        using SqlConnection sqlConnection = new(getstrConnection(connectionName));
        using SqlCommand sqlCommand = new(query + "; SELECT SCOPE_IDENTITY();", sqlConnection);
        var result = -1;

        try
        {
            sqlConnection.Open();

            // Add parameters if they exist
            if (parameters != null) sqlCommand.Parameters.AddRange(parameters);

            // Execute the command and get the inserted ID
            var id = sqlCommand.ExecuteScalar();
            result = id != null ? Convert.ToInt32(id) : -1;
        }
        catch (Exception exception_)
        {
            Setlog(query, parameters, exception_);
        }
        finally
        {
            sqlCommand.Dispose();
            sqlConnection.Close();
            sqlConnection.Dispose();
        }

        return result;
    }
	public static int ExeSqlCommand(string string_0, SqlParameter[] sqlParameter_0, string string_1)
	{
		serlog(string_0, sqlParameter_0);
		SqlConnection sqlConnection = new SqlConnection(getstrConnection(string_1));
		try
		{
			SqlCommand sqlCommand = SqlDBA.CreateCommandSql(sqlConnection, string_0, sqlParameter_0);
			try
			{
				int num2 = -1;
				try
				{
					sqlConnection.Open();
				}
				catch
				{
					return -1;
				}
				try
				{
					num2 = sqlCommand.ExecuteNonQuery();
				}
				catch (Exception exception_)
				{
					Setlog(string_0, sqlParameter_0, exception_);
				}
				sqlCommand.Dispose();
				sqlConnection.Close();
				sqlConnection.Dispose();
				return num2;
			}
			finally
			{
				int num = 2;
				while (true)
				{
					switch (num)
					{
					case 0:
						break;
					default:
						if (sqlCommand != null)
						{
							num = 1;
							continue;
						}
						break;
					case 1:
						((IDisposable)sqlCommand).Dispose();
						num = 0;
						continue;
					}
					break;
				}
			}
		}
		finally
		{
			int num = 2;
			while (true)
			{
				switch (num)
				{
				case 0:
					break;
				default:
					if (sqlConnection != null)
					{
						num = 1;
						continue;
					}
					break;
				case 1:
					((IDisposable)sqlConnection).Dispose();
					num = 0;
					continue;
				}
				break;
			}
		}
	}

	public static int ExeSqlCommand(string string_0)
	{
		int num = 0;
		serlog(string_0);
		SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		try
		{
			SqlCommand sqlCommand = new SqlCommand(string_0, sqlConnection);
			try
			{
				int num2 = 0;
				int num3 = -1;
				num = 2;
				try
				{
					sqlConnection.Open();
				}
				catch
				{
					return -1;
				}
				num = 1;
				try
				{
					num3 = sqlCommand.ExecuteNonQuery();
				}
				catch (Exception exception_)
				{
					Setlog(string_0, null, exception_);
				}
				sqlCommand.Dispose();
				sqlConnection.Close();
				sqlConnection.Dispose();
				num2 = num3;
				num = 0;
				return num2;
			}
			finally
			{
				num = 2;
				while (true)
				{
					switch (num)
					{
					case 0:
						break;
					default:
						if (sqlCommand != null)
						{
							num = 1;
							continue;
						}
						break;
					case 1:
						((IDisposable)sqlCommand).Dispose();
						num = 0;
						continue;
					}
					break;
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "serlog Error  " + ex);
			return -1;
		}
		finally
		{
			num = 2;
			while (true)
			{
				switch (num)
				{
				case 0:
					break;
				default:
					if (sqlConnection != null)
					{
						num = 1;
						continue;
					}
					break;
				case 1:
					((IDisposable)sqlConnection).Dispose();
					num = 0;
					continue;
				}
				break;
			}
		}
	}

	public static int ExeSqlCommand(string string_0, string string_1)
	{
		int num = 0;
		serlog(string_0);
		SqlConnection sqlConnection = new SqlConnection(getstrConnection(string_1));
		try
		{
			SqlCommand sqlCommand = new SqlCommand(string_0, sqlConnection);
			try
			{
				int num2 = 0;
				int num3 = -1;
				num = 1;
				try
				{
					sqlConnection.Open();
				}
				catch
				{
					return -1;
				}
				num = 0;
				try
				{
					num3 = sqlCommand.ExecuteNonQuery();
				}
				catch (Exception exception_)
				{
					Setlog(string_0, null, exception_);
				}
				sqlCommand.Dispose();
				sqlConnection.Close();
				sqlConnection.Dispose();
				num2 = num3;
				num = 2;
				return num2;
			}
			finally
			{
				num = 1;
				while (true)
				{
					switch (num)
					{
					case 2:
						break;
					case 0:
						((IDisposable)sqlCommand).Dispose();
						num = 2;
						continue;
					default:
						if (sqlCommand != null)
						{
							num = 0;
							continue;
						}
						break;
					}
					break;
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "serlog Error  " + ex);
			return -1;
		}
		finally
		{
			num = 1;
			while (true)
			{
				switch (num)
				{
				case 2:
					break;
				case 0:
					((IDisposable)sqlConnection).Dispose();
					num = 2;
					continue;
				default:
					if (sqlConnection != null)
					{
						num = 0;
						continue;
					}
					break;
				}
				break;
			}
		}
	}

	public static int ExeSqlCommand(string string_0, ref Exception exception_0, string string_1)
	{
		int num = 0;
		serlog(string_0);
		SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		try
		{
			SqlCommand sqlCommand = new SqlCommand(string_0, sqlConnection);
			try
			{
				try
				{
					sqlConnection.Open();
				}
				catch (Exception ex)
				{
					exception_0 = ex;
					return -1;
				}
				int result = sqlCommand.ExecuteNonQuery();
				sqlCommand.Dispose();
				sqlConnection.Close();
				sqlConnection.Dispose();
				return result;
			}
			finally
			{
				num = 1;
				while (true)
				{
					switch (num)
					{
					case 2:
						break;
					case 0:
						((IDisposable)sqlCommand).Dispose();
						num = 2;
						continue;
					default:
						if (sqlCommand != null)
						{
							num = 0;
							continue;
						}
						break;
					}
					break;
				}
			}
		}
		finally
		{
			num = 1;
			while (true)
			{
				switch (num)
				{
				case 2:
					break;
				case 0:
					((IDisposable)sqlConnection).Dispose();
					num = 2;
					continue;
				default:
					if (sqlConnection != null)
					{
						num = 0;
						continue;
					}
					break;
				}
				break;
			}
		}
	}

	public static DataTable GetDBToDataTable(string string_0, SqlParameter[] sqlParameter_0)
	{
		int num2 = 0;
		serlog(string_0, sqlParameter_0);
		SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		try
		{
			SqlDataAdapter sqlDataAdapter = new SqlDataAdapter();
			try
			{
				SqlCommand sqlCommand11 = (sqlDataAdapter.SelectCommand = SqlDBA.CreateCommandSql(sqlConnection, string_0, sqlParameter_0));
				SqlCommand sqlCommand9 = sqlCommand11;
				try
				{
					try
					{
						sqlConnection.Open();
					}
					catch (Exception ex)
					{
						Form1.WriteLine(100, "DBA Số liệu tầng _ Sai lầm 111 " + ex.Message + " " + string_0);
						return null;
					}
					DataTable dataTable = new DataTable();
					try
					{
						sqlDataAdapter.Fill(dataTable);
					}
					catch (Exception exception_)
					{
						Setlog(string_0, sqlParameter_0, exception_);
					}
					sqlDataAdapter.Dispose();
					sqlConnection.Close();
					sqlConnection.Dispose();
					return dataTable;
				}
				finally
				{
					num2 = 2;
					while (true)
					{
						switch (num2)
						{
						case 0:
							break;
						default:
							if (sqlCommand9 != null)
							{
								num2 = 1;
								continue;
							}
							break;
						case 1:
							((IDisposable)sqlCommand9).Dispose();
							num2 = 0;
							continue;
						}
						break;
					}
				}
			}
			finally
			{
				num2 = 2;
				while (true)
				{
					switch (num2)
					{
					case 0:
						break;
					default:
						if (sqlDataAdapter != null)
						{
							num2 = 1;
							continue;
						}
						break;
					case 1:
						((IDisposable)sqlDataAdapter).Dispose();
						num2 = 0;
						continue;
					}
					break;
				}
			}
		}
		finally
		{
			num2 = 2;
			while (true)
			{
				switch (num2)
				{
				case 0:
					break;
				default:
					if (sqlConnection != null)
					{
						num2 = 1;
						continue;
					}
					break;
				case 1:
					((IDisposable)sqlConnection).Dispose();
					num2 = 0;
					continue;
				}
				break;
			}
		}
	}

	public static DataTable GetDBToDataTable(string string_0, SqlParameter[] sqlParameter_0, string string_1)
	{
		int num2 = 0;
		serlog(string_0, sqlParameter_0);
		SqlConnection sqlConnection = new SqlConnection(getstrConnection(string_1));
		try
		{
			SqlDataAdapter sqlDataAdapter = new SqlDataAdapter();
			try
			{
				SqlCommand sqlCommand11 = (sqlDataAdapter.SelectCommand = SqlDBA.CreateCommandSql(sqlConnection, string_0, sqlParameter_0));
				SqlCommand sqlCommand9 = sqlCommand11;
				try
				{
					try
					{
						sqlConnection.Open();
					}
					catch (Exception ex)
					{
						Form1.WriteLine(100, "DBA Số liệu tầng _ Sai lầm 222 " + ex.Message + " " + string_0);
						return null;
					}
					DataTable dataTable = new DataTable();
					try
					{
						sqlDataAdapter.Fill(dataTable);
					}
					catch (Exception exception_)
					{
						Setlog(string_0, sqlParameter_0, exception_);
					}
					sqlDataAdapter.Dispose();
					sqlConnection.Close();
					sqlConnection.Dispose();
					return dataTable;
				}
				finally
				{
					num2 = 2;
					while (true)
					{
						switch (num2)
						{
						case 0:
							break;
						default:
							if (sqlCommand9 != null)
							{
								num2 = 1;
								continue;
							}
							break;
						case 1:
							((IDisposable)sqlCommand9).Dispose();
							num2 = 0;
							continue;
						}
						break;
					}
				}
			}
			finally
			{
				num2 = 2;
				while (true)
				{
					switch (num2)
					{
					case 0:
						break;
					default:
						if (sqlDataAdapter != null)
						{
							num2 = 1;
							continue;
						}
						break;
					case 1:
						((IDisposable)sqlDataAdapter).Dispose();
						num2 = 0;
						continue;
					}
					break;
				}
			}
		}
		finally
		{
			num2 = 2;
			while (true)
			{
				switch (num2)
				{
				case 0:
					break;
				default:
					if (sqlConnection != null)
					{
						num2 = 1;
						continue;
					}
					break;
				case 1:
					((IDisposable)sqlConnection).Dispose();
					num2 = 0;
					continue;
				}
				break;
			}
		}
	}

	public static DataTable GetDBToDataTable(string string_0)
	{
		int num2 = 0;
		serlog(string_0);
		SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		try
		{
			SqlDataAdapter sqlDataAdapter = new SqlDataAdapter();
			try
			{
				SqlCommand sqlCommand11 = (sqlDataAdapter.SelectCommand = new SqlCommand(string_0, sqlConnection));
				SqlCommand sqlCommand9 = sqlCommand11;
				try
				{
					try
					{
						sqlConnection.Open();
					}
					catch (Exception ex)
					{
						Form1.WriteLine(100, "DBA Số liệu tầng _ Sai lầm 333 " + ex.Message + " " + string_0);
						return null;
					}
					DataTable dataTable = new DataTable();
					try
					{
						sqlDataAdapter.Fill(dataTable);
					}
					catch (Exception exception_)
					{
						Setlog(string_0, null, exception_);
					}
					sqlDataAdapter.Dispose();
					sqlConnection.Close();
					sqlConnection.Dispose();
					return dataTable;
				}
				finally
				{
					num2 = 2;
					while (true)
					{
						switch (num2)
						{
						case 0:
							break;
						default:
							if (sqlCommand9 != null)
							{
								num2 = 1;
								continue;
							}
							break;
						case 1:
							((IDisposable)sqlCommand9).Dispose();
							num2 = 0;
							continue;
						}
						break;
					}
				}
			}
			finally
			{
				num2 = 2;
				while (true)
				{
					switch (num2)
					{
					case 0:
						break;
					default:
						if (sqlDataAdapter != null)
						{
							num2 = 1;
							continue;
						}
						break;
					case 1:
						((IDisposable)sqlDataAdapter).Dispose();
						num2 = 0;
						continue;
					}
					break;
				}
			}
		}
		finally
		{
			num2 = 2;
			while (true)
			{
				switch (num2)
				{
				case 0:
					break;
				default:
					if (sqlConnection != null)
					{
						num2 = 1;
						continue;
					}
					break;
				case 1:
					((IDisposable)sqlConnection).Dispose();
					num2 = 0;
					continue;
				}
				break;
			}
		}
	}

	public static DataTable GetDBToDataTable(string string_0, string string_1)
	{
		int num = 0;
		serlog(string_0);
		SqlConnection sqlConnection = new SqlConnection(getstrConnection(string_1));
		try
		{
			SqlDataAdapter sqlDataAdapter = new SqlDataAdapter();
			try
			{
				SqlCommand sqlCommand11 = (sqlDataAdapter.SelectCommand = new SqlCommand(string_0, sqlConnection));
				SqlCommand sqlCommand9 = sqlCommand11;
				try
				{
					try
					{
						sqlConnection.Open();
					}
					catch
					{
						return null;
					}
					DataTable dataTable = new DataTable();
					try
					{
						sqlDataAdapter.Fill(dataTable);
					}
					catch (Exception exception_)
					{
						Setlog(string_0, null, exception_);
					}
					sqlDataAdapter.Dispose();
					sqlConnection.Close();
					sqlConnection.Dispose();
					return dataTable;
				}
				finally
				{
					num = 1;
					while (true)
					{
						switch (num)
						{
						case 0:
							break;
						default:
							if (sqlCommand9 != null)
							{
								num = 2;
								continue;
							}
							break;
						case 2:
							((IDisposable)sqlCommand9).Dispose();
							num = 0;
							continue;
						}
						break;
					}
				}
			}
			finally
			{
				num = 1;
				while (true)
				{
					switch (num)
					{
					case 0:
						break;
					default:
						if (sqlDataAdapter != null)
						{
							num = 2;
							continue;
						}
						break;
					case 2:
						((IDisposable)sqlDataAdapter).Dispose();
						num = 0;
						continue;
					}
					break;
				}
			}
		}
		finally
		{
			num = 1;
			while (true)
			{
				switch (num)
				{
				case 0:
					break;
				default:
					if (sqlConnection != null)
					{
						num = 2;
						continue;
					}
					break;
				case 2:
					((IDisposable)sqlConnection).Dispose();
					num = 0;
					continue;
				}
				break;
			}
		}
	}

	public static DataRowCollection GetDBValue(string string_0, string string_1)
	{
		return GetDBToDataTable(string_0).Rows;
	}

	public static ArrayList GetDBValue_1(string string_0, string string_1)
	{
		int num = 0;
		serlog(string_0);
		SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		try
		{
			SqlCommand sqlCommand = new SqlCommand(string_0, sqlConnection);
			try
			{
				try
				{
					sqlConnection.Open();
				}
				catch
				{
					return null;
				}
				ArrayList arrayList = null;
				ArrayList arrayList2 = null;
				int num2 = 0;
				SqlDataReader sqlDataReader = sqlCommand.ExecuteReader();
				num = 7;
				if (!sqlDataReader.HasRows)
				{
					num = 0;
					sqlDataReader.Close();
					sqlDataReader.Dispose();
					sqlConnection.Close();
					sqlConnection.Dispose();
					arrayList = null;
					num = 6;
					return arrayList;
				}
				arrayList2 = new ArrayList();
				num = 9;
				if (sqlDataReader.Read())
				{
					num = 5;
					num2 = 0;
					num = 8;
					while (true)
					{
						num = 3;
						if (num2 >= sqlDataReader.FieldCount)
						{
							break;
						}
						arrayList2.Add(sqlDataReader[num2]);
						num2++;
						num = 4;
					}
					num = 1;
				}
				sqlDataReader.Close();
				sqlDataReader.Dispose();
				sqlConnection.Close();
				sqlConnection.Dispose();
				sqlCommand.Dispose();
				arrayList = arrayList2;
				num = 2;
				return arrayList;
			}
			finally
			{
				num = 1;
				while (true)
				{
					switch (num)
					{
					case 0:
						break;
					default:
						if (sqlCommand != null)
						{
							num = 2;
							continue;
						}
						break;
					case 2:
						((IDisposable)sqlCommand).Dispose();
						num = 0;
						continue;
					}
					break;
				}
			}
		}
		finally
		{
			num = 1;
			while (true)
			{
				switch (num)
				{
				case 0:
					break;
				default:
					if (sqlConnection != null)
					{
						num = 2;
						continue;
					}
					break;
				case 2:
					((IDisposable)sqlConnection).Dispose();
					num = 0;
					continue;
				}
				break;
			}
		}
	}

	public static ArrayList GetDBValue_2(string string_0, string string_1)
	{
		int num = 0;
		serlog(string_0);
		SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		try
		{
			SqlCommand sqlCommand = new SqlCommand(string_0, sqlConnection);
			try
			{
				try
				{
					sqlConnection.Open();
				}
				catch
				{
					return null;
				}
				ArrayList arrayList = null;
				ArrayList arrayList2 = null;
				SqlDataReader sqlDataReader = sqlCommand.ExecuteReader();
				num = 1;
				if (!sqlDataReader.HasRows)
				{
					num = 2;
					sqlDataReader.Close();
					sqlDataReader.Dispose();
					sqlConnection.Close();
					sqlConnection.Dispose();
					arrayList = null;
					num = 4;
					return arrayList;
				}
				arrayList2 = new ArrayList();
				num = 6;
				while (true)
				{
					num = 0;
					if (!sqlDataReader.Read())
					{
						break;
					}
					arrayList2.Add(sqlDataReader[0]);
					num = 3;
				}
				num = 7;
				sqlDataReader.Close();
				sqlDataReader.Dispose();
				sqlConnection.Close();
				sqlConnection.Dispose();
				sqlCommand.Dispose();
				arrayList = arrayList2;
				num = 5;
				return arrayList;
			}
			finally
			{
				num = 0;
				while (true)
				{
					switch (num)
					{
					case 1:
						break;
					default:
						if (sqlCommand != null)
						{
							num = 2;
							continue;
						}
						break;
					case 2:
						((IDisposable)sqlCommand).Dispose();
						num = 1;
						continue;
					}
					break;
				}
			}
		}
		finally
		{
			num = 0;
			while (true)
			{
				switch (num)
				{
				case 1:
					break;
				default:
					if (sqlConnection != null)
					{
						num = 2;
						continue;
					}
					break;
				case 2:
					((IDisposable)sqlConnection).Dispose();
					num = 1;
					continue;
				}
				break;
			}
		}
	}

	public static object GetDBValue_3(string string_0)
	{
		int num2 = 0;
		serlog(string_0);
		object result = null;
		SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		try
		{
			SqlCommand sqlCommand = new SqlCommand(string_0, sqlConnection);
			try
			{
				try
				{
					sqlConnection.Open();
				}
				catch
				{
					return null;
				}
				try
				{
					result = sqlCommand.ExecuteScalar();
				}
				catch (Exception ex)
				{
					Form1.WriteLine(100, "DBA Số liệu tầng _ Sai lầm 444 " + ex.Message + " " + string_0);
				}
				sqlCommand.Dispose();
				sqlConnection.Close();
				sqlConnection.Dispose();
				return result;
			}
			finally
			{
				num2 = 0;
				while (true)
				{
					switch (num2)
					{
					case 2:
						break;
					case 1:
						((IDisposable)sqlCommand).Dispose();
						num2 = 2;
						continue;
					default:
						if (sqlCommand != null)
						{
							num2 = 1;
							continue;
						}
						break;
					}
					break;
				}
			}
		}
		finally
		{
			num2 = 0;
			while (true)
			{
				switch (num2)
				{
				case 2:
					break;
				case 1:
					((IDisposable)sqlConnection).Dispose();
					num2 = 2;
					continue;
				default:
					if (sqlConnection != null)
					{
						num2 = 1;
						continue;
					}
					break;
				}
				break;
			}
		}
	}

	public static object GetDBValue_3(string string_0, SqlParameter[] sqlParameter_0)
	{
		int num2 = 0;
		serlog(string_0, sqlParameter_0);
		object result = null;
		SqlConnection sqlConnection = new SqlConnection(getstrConnection(null));
		try
		{
			SqlCommand sqlCommand = SqlDBA.CreateCommandSql(sqlConnection, string_0, sqlParameter_0);
			try
			{
				try
				{
					sqlConnection.Open();
				}
				catch
				{
					return null;
				}
				try
				{
					result = sqlCommand.ExecuteScalar();
				}
				catch (Exception ex)
				{
					Form1.WriteLine(100, "DBA Số liệu tầng _ Sai lầm 555 " + ex.Message + " " + string_0);
				}
				sqlCommand.Dispose();
				sqlConnection.Close();
				sqlConnection.Dispose();
				return result;
			}
			finally
			{
				num2 = 0;
				while (true)
				{
					switch (num2)
					{
					case 2:
						break;
					case 1:
						((IDisposable)sqlCommand).Dispose();
						num2 = 2;
						continue;
					default:
						if (sqlCommand != null)
						{
							num2 = 1;
							continue;
						}
						break;
					}
					break;
				}
			}
		}
		finally
		{
			num2 = 0;
			while (true)
			{
				switch (num2)
				{
				case 2:
					break;
				case 1:
					((IDisposable)sqlConnection).Dispose();
					num2 = 2;
					continue;
				default:
					if (sqlConnection != null)
					{
						num2 = 1;
						continue;
					}
					break;
				}
				break;
			}
		}
	}

	public static object GetDBValue_3(string string_0, string string_1)
	{
		int num2 = 0;
		serlog(string_0);
		object result = null;
		SqlConnection sqlConnection = new SqlConnection(getstrConnection(string_1));
		try
		{
			SqlCommand sqlCommand = new SqlCommand(string_0, sqlConnection);
			try
			{
				try
				{
					sqlConnection.Open();
				}
				catch
				{
					return null;
				}
				try
				{
					result = sqlCommand.ExecuteScalar();
				}
				catch (Exception ex)
				{
					Form1.WriteLine(100, "DBA Số liệu tầng _ Sai lầm 666 " + ex.Message + " " + string_0);
				}
				sqlCommand.Dispose();
				sqlConnection.Close();
				sqlConnection.Dispose();
				return result;
			}
			finally
			{
				num2 = 0;
				while (true)
				{
					switch (num2)
					{
					case 2:
						break;
					case 1:
						((IDisposable)sqlCommand).Dispose();
						num2 = 2;
						continue;
					default:
						if (sqlCommand != null)
						{
							num2 = 1;
							continue;
						}
						break;
					}
					break;
				}
			}
		}
		finally
		{
			num2 = 0;
			while (true)
			{
				switch (num2)
				{
				case 2:
					break;
				case 1:
					((IDisposable)sqlConnection).Dispose();
					num2 = 2;
					continue;
				default:
					if (sqlConnection != null)
					{
						num2 = 1;
						continue;
					}
					break;
				}
				break;
			}
		}
	}

	public static string GetstrConnection(string string_0)
	{
		try
		{
			if (string_0 == null)
			{
				string_0 = "GameServer";
			}
			if (World.Db.TryGetValue(string_0, out var value))
			{
				return value.SqlConnect;
			}
			return World.Db.TryGetValue(string_0, out value) ? value.SqlConnect : null;
		}
		catch
		{
			Form1.WriteLine(1, "GetstrConnection Erro");
			return null;
		}
	}

	public static object GetDBValue_3(string string_0, SqlParameter[] sqlParameter_0, string string_1)
	{
		serlog(string_0, sqlParameter_0);
		object result = null;
		using SqlConnection sqlConnection = new SqlConnection(GetstrConnection(string_1));
		using SqlCommand sqlCommand = SqlDBA.CreateCommandSql(sqlConnection, string_0, sqlParameter_0);
		try
		{
			sqlConnection.Open();
		}
		catch
		{
			return null;
		}
		try
		{
			result = sqlCommand.ExecuteScalar();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(100, "DBASoLieu层_错误15" + ex.Message + " " + string_0);
		}
		sqlCommand.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}
}
