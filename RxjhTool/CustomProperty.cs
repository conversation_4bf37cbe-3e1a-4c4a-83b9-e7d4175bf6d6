using System;
using System.Reflection;

namespace RxjhTool;

public class CustomProperty
{
	private string string_0 = string.Empty;

	private object _defaultValue;

	private object _value;

	private object _对象;

	private PropertyInfo[] _propertyInfos;

	public string Name
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
			if (PropertyNames == null)
			{
				PropertyNames = new string[1] { string_0 };
			}
		}
	}

	public string[] PropertyNames { get; set; }

	public Type ValueType { get; set; }

	public object DefaultValue
	{
		get
		{
			return _defaultValue;
		}
		set
		{
			_defaultValue = value;
			if (_defaultValue != null)
			{
				if (_value == null)
				{
					_value = _defaultValue;
				}
				if (ValueType == null)
				{
					ValueType = _defaultValue.GetType();
				}
			}
		}
	}

	public object Value
	{
		get
		{
			return _value;
		}
		set
		{
			_value = value;
			method_1();
		}
	}

	public bool IsReadOnly { get; set; }

	public string Description { get; set; }

	public string Category { get; set; }

	public bool IsBrowsable { get; set; }

	public object ObjectSource
	{
		get
		{
			return _对象;
		}
		set
		{
			_对象 = value;
			method_0();
		}
	}

	public Type EditorType { get; set; }

	public bool ConverterType { get; set; }

	protected PropertyInfo[] PropertyInfos
	{
		get
		{
			int num = 0;
			Type type = null;
			if (_propertyInfos == null)
			{
				type = ObjectSource.GetType();
				_propertyInfos = new PropertyInfo[PropertyNames.Length];
				for (num = 0; num < PropertyNames.Length; num++)
				{
					_propertyInfos[num] = type.GetProperty(PropertyNames[num]);
				}
			}
			return _propertyInfos;
		}
	}

	public CustomProperty()
	{
	}

	public CustomProperty(string string_4, string string_5, bool bool_3, string string_6, string string_7, object object_0)
		: this(string_4, new string[1] { string_5 }, null, null, null, (bool_3 ? 1 : 0) != 0, bool_4: true, string_6, string_7, object_0, null, bool_5: false)
	{
	}

	public CustomProperty(string string_4, string string_5, bool bool_3, string string_6, string string_7, object object_0, bool bool_4)
		: this(string_4, new string[1] { string_5 }, null, null, null, (bool_3 ? 1 : 0) != 0, bool_4: true, string_6, string_7, object_0, null, (bool_4 ? 1 : 0) != 0)
	{
	}

	public CustomProperty(string string_4, string string_5, bool bool_3, string string_6, string string_7, object object_0, Type type_2)
		: this(string_4, new string[1] { string_5 }, null, null, null, (bool_3 ? 1 : 0) != 0, bool_4: true, string_6, string_7, object_0, type_2, bool_5: false)
	{
	}

	public CustomProperty(string string_4, string string_5, bool bool_3, string string_6, string string_7, object object_0, Type type_2, bool bool_4)
		: this(string_4, new string[1] { string_5 }, null, null, null, (bool_3 ? 1 : 0) != 0, bool_4: true, string_6, string_7, object_0, type_2, (bool_4 ? 1 : 0) != 0)
	{
	}

	public CustomProperty(string string_4, string[] string_5, Type type_2, object object_0, object object_1, bool bool_3, bool bool_4, string string_6, string string_7, object object_2, Type type_3, bool bool_5)
	{
		Name = string_4;
		PropertyNames = string_5;
		ValueType = type_2;
		_defaultValue = object_0;
		_value = object_1;
		IsReadOnly = bool_3;
		IsBrowsable = bool_4;
		Category = string_6;
		Description = string_7;
		ObjectSource = object_2;
		EditorType = type_3;
		ConverterType = bool_5;
	}

	protected void method_0()
	{
		object obj = null;
		if (PropertyInfos.Length != 0)
		{
			obj = PropertyInfos[0].GetValue(_对象, null);
			if (_defaultValue == null)
			{
				DefaultValue = obj;
			}
			_value = obj;
		}
	}

	protected void method_1()
	{
		int num = 0;
		PropertyInfo[] array = null;
		if (_对象 != null)
		{
			array = PropertyInfos;
			for (num = 0; num < array.Length; num++)
			{
				array[num].SetValue(_对象, _value, null);
			}
		}
	}

	public void ResetValue()
	{
		Value = DefaultValue;
	}
}
