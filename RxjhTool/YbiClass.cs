namespace RxjhTool;

public class YbiClass
{
	private int int_0;

	private int int_1;

	private string string_0;

	private string string_1;

	private byte[] _FLD_byte;

	private int int_2;

	private int int_3;

	private int int_4;

	private int int_5;

	private int int_6;

	private int int_7;

	private int int_8;

	private int int_9;

	private int int_10;

	private int int_11;

	private int int_12;

	private int int_13;

	private int int_14;

	private int int_15;

	private int int_16;

	private int int_17;

	private int int_18;

	private int int_19;

	private int int_20;

	private int int_21;

	private int int_22;

	private int int_23;

	private int int_24;

	public int _FLD_MM1;

	public int _FLD_MM2;

	public int _FLD_TZ;

	public int _FLD_QGSLL;

	public int _FLD_CJL;

	public int ID
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int FLD_PID
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public string FLD_Name
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public string FLD_说明
	{
		get
		{
			return string_1;
		}
		set
		{
			string_1 = value;
		}
	}

	public byte[] FLD_byte
	{
		get
		{
			return _FLD_byte;
		}
		set
		{
			_FLD_byte = value;
		}
	}

	public int FLD_QUESTITEM
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public int FLD_NJ
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public int FLD_RESIDE1
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = value;
		}
	}

	public int FLD_RESIDE2
	{
		get
		{
			return int_5;
		}
		set
		{
			int_5 = value;
		}
	}

	public int FLD_SEX
	{
		get
		{
			return int_6;
		}
		set
		{
			int_6 = value;
		}
	}

	public int FLD_AT1
	{
		get
		{
			return int_7;
		}
		set
		{
			int_7 = value;
		}
	}

	public int FLD_AT2
	{
		get
		{
			return int_8;
		}
		set
		{
			int_8 = value;
		}
	}

	public int FLD_LEVEL
	{
		get
		{
			return int_9;
		}
		set
		{
			int_9 = value;
		}
	}

	public int FLD_JOB_LEVEL
	{
		get
		{
			return int_10;
		}
		set
		{
			int_10 = value;
		}
	}

	public int FLD_ZX
	{
		get
		{
			return int_11;
		}
		set
		{
			int_11 = value;
		}
	}

	public int FLD_EL
	{
		get
		{
			return int_12;
		}
		set
		{
			int_12 = value;
		}
	}

	public int FLD_MM1
	{
		get
		{
			return _FLD_MM1;
		}
		set
		{
			_FLD_MM1 = value;
		}
	}

	public int FLD_MM2
	{
		get
		{
			return _FLD_MM2;
		}
		set
		{
			_FLD_MM2 = value;
		}
	}

	public int FLD_WX
	{
		get
		{
			return int_13;
		}
		set
		{
			int_13 = value;
		}
	}

	public int FLD_WXJD
	{
		get
		{
			return int_14;
		}
		set
		{
			int_14 = value;
		}
	}

	public int FLD_MONEY
	{
		get
		{
			return int_15;
		}
		set
		{
			int_15 = value;
		}
	}

	public int FLD_WEIGHT
	{
		get
		{
			return int_16;
		}
		set
		{
			int_16 = value;
		}
	}

	public int FLD_TYPE
	{
		get
		{
			return int_17;
		}
		set
		{
			int_17 = value;
		}
	}

	public int FLD_MAGIC1
	{
		get
		{
			return int_18;
		}
		set
		{
			int_18 = value;
		}
	}

	public int FLD_TZ
	{
		get
		{
			return _FLD_TZ;
		}
		set
		{
			_FLD_TZ = value;
		}
	}

	public int FLD_QGSLL
	{
		get
		{
			return _FLD_QGSLL;
		}
		set
		{
			_FLD_QGSLL = value;
		}
	}

	public int FLD_MAGIC2
	{
		get
		{
			return int_19;
		}
		set
		{
			int_19 = value;
		}
	}

	public int FLD_MAGIC3
	{
		get
		{
			return int_20;
		}
		set
		{
			int_20 = value;
		}
	}

	public int FLD_MAGIC4
	{
		get
		{
			return int_21;
		}
		set
		{
			int_21 = value;
		}
	}

	public int FLD_MAGIC5
	{
		get
		{
			return int_22;
		}
		set
		{
			int_22 = value;
		}
	}

	public int FLD_SIDE
	{
		get
		{
			return int_23;
		}
		set
		{
			int_23 = value;
		}
	}

	public int FLD_DF
	{
		get
		{
			return int_24;
		}
		set
		{
			int_24 = value;
		}
	}

	public int FLD_CJL
	{
		get
		{
			return _FLD_CJL;
		}
		set
		{
			_FLD_CJL = value;
		}
	}
}
