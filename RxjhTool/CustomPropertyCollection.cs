using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing.Design;

namespace RxjhTool;

public class CustomPropertyCollection : List<CustomProperty>, ICustomTypeDescriptor
{
	public AttributeCollection GetAttributes()
	{
		return TypeDescriptor.GetAttributes(this, noCustomTypeDesc: true);
	}

	public string GetClassName()
	{
		return TypeDescriptor.GetClassName(this, noCustomTypeDesc: true);
	}

	public string GetComponentName()
	{
		return TypeDescriptor.GetComponentName(this, noCustomTypeDesc: true);
	}

	public TypeConverter GetConverter()
	{
		return TypeDescriptor.GetConverter(this, noCustomTypeDesc: true);
	}

	public EventDescriptor GetDefaultEvent()
	{
		return TypeDescriptor.GetDefaultEvent(this, noCustomTypeDesc: true);
	}

	public PropertyDescriptor GetDefaultProperty()
	{
		return TypeDescriptor.GetDefaultProperty(this, noCustomTypeDesc: true);
	}

	public object GetEditor(Type editorBaseType)
	{
		return TypeDescriptor.GetEditor(this, editorBaseType, noCustomTypeDesc: true);
	}

	public EventDescriptorCollection GetEvents(Attribute[] attributes)
	{
		return TypeDescriptor.GetEvents(this, attributes, noCustomTypeDesc: true);
	}

	public EventDescriptorCollection GetEvents()
	{
		return TypeDescriptor.GetEvents(this, noCustomTypeDesc: true);
	}

	public PropertyDescriptorCollection GetProperties(Attribute[] attributes)
	{
		PropertyDescriptorCollection propertyDescriptorCollection = new PropertyDescriptorCollection(null);
		using Enumerator enumerator = GetEnumerator();
		CustomProperty customProperty = null;
		List<Attribute> list = null;
		while (enumerator.MoveNext())
		{
			customProperty = enumerator.Current;
			list = new List<Attribute>();
			list.Add(new CategoryAttribute(customProperty.Category));
			if (!customProperty.IsBrowsable)
			{
				list.Add(new BrowsableAttribute(customProperty.IsBrowsable));
			}
			if (customProperty.IsReadOnly)
			{
				list.Add(new ReadOnlyAttribute(customProperty.IsReadOnly));
			}
			if (customProperty.EditorType != null)
			{
				list.Add(new EditorAttribute(customProperty.EditorType, typeof(UITypeEditor)));
			}
			if (customProperty.ConverterType)
			{
				list.Add(new TypeConverterAttribute(typeof(SpellingOptionsConverter)));
			}
			propertyDescriptorCollection.Add(new CustomPropertyDescriptor(customProperty, list.ToArray()));
		}
		return propertyDescriptorCollection;
	}

	public PropertyDescriptorCollection GetProperties()
	{
		return TypeDescriptor.GetProperties(this, noCustomTypeDesc: true);
	}

	public object GetPropertyOwner(PropertyDescriptor propertyDescriptor_0)
	{
		return this;
	}
}
