using System;
using System.ComponentModel;

namespace RxjhTool;

[DefaultProperty("Name")]
public class Customer
{
	private string string_0;

	private int int_0;

	private DateTime dateTime_0;

	private string string_1;

	private string string_2;

	private string string_3;

	private bool bool_0;

	[Category("ID Settings")]
	[Description("Name of the customer")]
	public string Name
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	[Category("ID Settings")]
	[Description("Social Security Number of the customer")]
	public string SSN
	{
		get
		{
			return string_1;
		}
		set
		{
			string_1 = value;
		}
	}

	[Description("Address of the customer")]
	[Category("ID Settings")]
	public string Address
	{
		get
		{
			return string_2;
		}
		set
		{
			string_2 = value;
		}
	}

	[Category("ID Settings")]
	[Description("Date of Birth of the Customer (optional)")]
	public DateTime DateOfBirth
	{
		get
		{
			return dateTime_0;
		}
		set
		{
			dateTime_0 = value;
		}
	}

	[Description("Age of the customer")]
	[Category("ID Settings")]
	public int Age
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	[Category("Marketting Settings")]
	[Description("If the customer has bought more than 10 times, this is set to true")]
	public bool FrequentBuyer
	{
		get
		{
			return bool_0;
		}
		set
		{
			bool_0 = value;
		}
	}

	[Category("Marketting Settings")]
	[Description("Most current e-mail of the customer")]
	public string Email
	{
		get
		{
			return string_3;
		}
		set
		{
			string_3 = value;
		}
	}
}
