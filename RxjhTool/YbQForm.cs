using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Data;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;
using RxjhServer;
using RxjhServer.DbClss;

namespace RxjhTool;

public class YbQForm : Form
{
	private byte[] byte_1A24628 = new byte[256];

	private Dictionary<int, 任务类> 任务 = new Dictionary<int, 任务类>();

	private string 文件地区 = string.Empty;

	private string 文件时间 = string.Empty;

	private string 增加内容 = "\r\n\r\n购买最新5.0服务端支持LUA游戏脚本\r\n自定义任务系统,功能齐全\r\n请联系QQ：1146341013";

	private IContainer components;

	private MenuStrip menuStrip1;

	private ToolStripMenuItem 文件ToolStripMenuItem;

	private ToolStripMenuItem 打开YbqcfgToolStripMenuItem;

	private ToolStripMenuItem 保存YbqcfgToolStripMenuItem;

	private StatusStrip statusStrip1;

	private SplitContainer splitContainer1;

	private ListView listView;

	private ColumnHeader columnHeader1;

	private ColumnHeader columnHeader2;

	private PropertyGrid propertyGrid1;

	private ToolStripStatusLabel toolStripStatusLabel1;

	private ContextMenuStrip contextMenuStrip1;

	private ToolStripMenuItem 生成任务脚本ToolStripMenuItem;

	private ToolStripMenuItem 入库ToolStripMenuItem;

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.menuStrip1 = new System.Windows.Forms.MenuStrip();
		this.文件ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.打开YbqcfgToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.保存YbqcfgToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.入库ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.statusStrip1 = new System.Windows.Forms.StatusStrip();
		this.toolStripStatusLabel1 = new System.Windows.Forms.ToolStripStatusLabel();
		this.splitContainer1 = new System.Windows.Forms.SplitContainer();
		this.listView = new System.Windows.Forms.ListView();
		this.columnHeader1 = new System.Windows.Forms.ColumnHeader();
		this.columnHeader2 = new System.Windows.Forms.ColumnHeader();
		this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
		this.生成任务脚本ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.propertyGrid1 = new System.Windows.Forms.PropertyGrid();
		this.menuStrip1.SuspendLayout();
		this.statusStrip1.SuspendLayout();
		this.splitContainer1.Panel1.SuspendLayout();
		this.splitContainer1.Panel2.SuspendLayout();
		this.splitContainer1.SuspendLayout();
		this.contextMenuStrip1.SuspendLayout();
		base.SuspendLayout();
		this.menuStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
		this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[1] { this.文件ToolStripMenuItem });
		this.menuStrip1.Location = new System.Drawing.Point(0, 0);
		this.menuStrip1.Name = "menuStrip1";
		this.menuStrip1.Size = new System.Drawing.Size(940, 30);
		this.menuStrip1.TabIndex = 0;
		this.menuStrip1.Text = "menuStrip1";
		this.文件ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[3] { this.打开YbqcfgToolStripMenuItem, this.保存YbqcfgToolStripMenuItem, this.入库ToolStripMenuItem });
		this.文件ToolStripMenuItem.Name = "文件ToolStripMenuItem";
		this.文件ToolStripMenuItem.Size = new System.Drawing.Size(53, 26);
		this.文件ToolStripMenuItem.Text = "文件";
		this.打开YbqcfgToolStripMenuItem.Name = "打开YbqcfgToolStripMenuItem";
		this.打开YbqcfgToolStripMenuItem.Size = new System.Drawing.Size(122, 26);
		this.打开YbqcfgToolStripMenuItem.Text = "打开";
		this.打开YbqcfgToolStripMenuItem.Click += new System.EventHandler(打开YbqcfgToolStripMenuItem_Click);
		this.保存YbqcfgToolStripMenuItem.Name = "保存YbqcfgToolStripMenuItem";
		this.保存YbqcfgToolStripMenuItem.Size = new System.Drawing.Size(122, 26);
		this.保存YbqcfgToolStripMenuItem.Text = "保存";
		this.保存YbqcfgToolStripMenuItem.Click += new System.EventHandler(保存YbqcfgToolStripMenuItem_Click);
		this.入库ToolStripMenuItem.Name = "入库ToolStripMenuItem";
		this.入库ToolStripMenuItem.Size = new System.Drawing.Size(122, 26);
		this.入库ToolStripMenuItem.Text = "入库";
		this.入库ToolStripMenuItem.Click += new System.EventHandler(入库ToolStripMenuItem_Click);
		this.statusStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
		this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[1] { this.toolStripStatusLabel1 });
		this.statusStrip1.Location = new System.Drawing.Point(0, 592);
		this.statusStrip1.Name = "statusStrip1";
		this.statusStrip1.Padding = new System.Windows.Forms.Padding(1, 0, 19, 0);
		this.statusStrip1.Size = new System.Drawing.Size(940, 26);
		this.statusStrip1.TabIndex = 1;
		this.statusStrip1.Text = "statusStrip1";
		this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
		this.toolStripStatusLabel1.Size = new System.Drawing.Size(167, 20);
		this.toolStripStatusLabel1.Text = "toolStripStatusLabel1";
		this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.splitContainer1.Location = new System.Drawing.Point(0, 30);
		this.splitContainer1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
		this.splitContainer1.Name = "splitContainer1";
		this.splitContainer1.Panel1.Controls.Add(this.listView);
		this.splitContainer1.Panel2.Controls.Add(this.propertyGrid1);
		this.splitContainer1.Size = new System.Drawing.Size(940, 562);
		this.splitContainer1.SplitterDistance = 374;
		this.splitContainer1.SplitterWidth = 5;
		this.splitContainer1.TabIndex = 2;
		this.listView.Columns.AddRange(new System.Windows.Forms.ColumnHeader[2] { this.columnHeader1, this.columnHeader2 });
		this.listView.ContextMenuStrip = this.contextMenuStrip1;
		this.listView.Dock = System.Windows.Forms.DockStyle.Fill;
		this.listView.FullRowSelect = true;
		this.listView.GridLines = true;
		this.listView.HideSelection = false;
		this.listView.Location = new System.Drawing.Point(0, 0);
		this.listView.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
		this.listView.Name = "listView";
		this.listView.Size = new System.Drawing.Size(374, 562);
		this.listView.TabIndex = 5;
		this.listView.UseCompatibleStateImageBehavior = false;
		this.listView.View = System.Windows.Forms.View.Details;
		this.listView.SelectedIndexChanged += new System.EventHandler(listView_SelectedIndexChanged);
		this.listView.DoubleClick += new System.EventHandler(listView_DoubleClick);
		this.columnHeader1.Text = "任务ID";
		this.columnHeader1.Width = 48;
		this.columnHeader2.Text = "任务名";
		this.columnHeader2.Width = 140;
		this.contextMenuStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
		this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[1] { this.生成任务脚本ToolStripMenuItem });
		this.contextMenuStrip1.Name = "contextMenuStrip1";
		this.contextMenuStrip1.Size = new System.Drawing.Size(139, 28);
		this.生成任务脚本ToolStripMenuItem.Name = "生成任务脚本ToolStripMenuItem";
		this.生成任务脚本ToolStripMenuItem.Size = new System.Drawing.Size(138, 24);
		this.生成任务脚本ToolStripMenuItem.Text = "编辑任务";
		this.生成任务脚本ToolStripMenuItem.Click += new System.EventHandler(编辑任务ToolStripMenuItem_Click);
		this.propertyGrid1.Dock = System.Windows.Forms.DockStyle.Fill;
		this.propertyGrid1.Location = new System.Drawing.Point(0, 0);
		this.propertyGrid1.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
		this.propertyGrid1.Name = "propertyGrid1";
		this.propertyGrid1.Size = new System.Drawing.Size(561, 562);
		this.propertyGrid1.TabIndex = 0;
		base.AutoScaleDimensions = new System.Drawing.SizeF(8f, 15f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(940, 618);
		base.Controls.Add(this.splitContainer1);
		base.Controls.Add(this.statusStrip1);
		base.Controls.Add(this.menuStrip1);
		base.MainMenuStrip = this.menuStrip1;
		base.Margin = new System.Windows.Forms.Padding(4, 4, 4, 4);
		base.Name = "YbQForm";
		this.Text = "YBQ编辑器";
		base.Load += new System.EventHandler(Form1_Load);
		this.menuStrip1.ResumeLayout(false);
		this.menuStrip1.PerformLayout();
		this.statusStrip1.ResumeLayout(false);
		this.statusStrip1.PerformLayout();
		this.splitContainer1.Panel1.ResumeLayout(false);
		this.splitContainer1.Panel2.ResumeLayout(false);
		this.splitContainer1.ResumeLayout(false);
		this.contextMenuStrip1.ResumeLayout(false);
		base.ResumeLayout(false);
		base.PerformLayout();
	}

	public YbQForm()
	{
		InitializeComponent();
		int num = 0;
		do
		{
			byte_1A24628[num] = (byte)(((uint)(num >> 4) & 1u) | ((uint)(num >> 2) & 0x18u) | ((uint)(num >> 1) & 0x40u) | (uint)(2 * ((num & 3) | (4 * ((num & 4) | (2 * (num & 0xF8)))))));
			num++;
		}
		while (num < 256);
	}

	private void Form1_Load(object sender, EventArgs e)
	{
		toolStripStatusLabel1.Text = "任务总数:" + 任务.Count;
	}

	private int getlen(byte[] byte_0, ref int 处理长度)
	{
		try
		{
			bool flag = true;
			while (true)
			{
				IL_0008:
				int num2 = 处理长度++;
				int num3 = num2;
				int num4 = num3;
				bool flag2 = true;
				while (true)
				{
					switch ((num4 >= byte_0.Length) ? 4 : 0)
					{
					case 9:
						goto end_IL_00e1;
					case 0:
						if (byte_0[num4] != 32)
						{
							goto case 2;
						}
						goto case 6;
					case 2:
					case 3:
						if (byte_0[num4] != 10)
						{
							num4++;
							continue;
						}
						goto case 6;
					case 4:
						goto end_IL_00e1;
					case 6:
					{
						byte[] array = new byte[num4 - num3];
						System.Buffer.BlockCopy(byte_0, num3, array, 0, array.Length);
						string @string = Encoding.Default.GetString(array);
						处理长度 += array.Length;
						num2 = int.Parse(@string);
						goto case 7;
					}
					case 7:
						return num2;
					case 1:
					case 5:
					case 8:
						continue;
					}
					goto IL_0008;
					continue;
					end_IL_00e1:
					break;
				}
				break;
			}
		}
		catch
		{
		}
		return 0;
	}

	private string getString(byte[] byte_0, ref int 处理长度)
	{
		int num = 处理长度++;
		for (int i = num; i < byte_0.Length; i++)
		{
			if (byte_0[i] == 32 || byte_0[i] == 10)
			{
				byte[] array = new byte[i - num];
				System.Buffer.BlockCopy(byte_0, num, array, 0, array.Length);
				string @string = Encoding.Default.GetString(array);
				处理长度 += array.Length;
				byte[] array2 = new byte[int.Parse(@string)];
				System.Buffer.BlockCopy(byte_0, 处理长度, array2, 0, array2.Length);
				string string2 = Encoding.Default.GetString(array2);
				处理长度 += array2.Length + 1;
				return string2;
			}
		}
		return string.Empty;
	}

	private string getStringtou(byte[] byte_0, ref int 处理长度)
	{
		int num = 处理长度;
		for (int i = num; i < byte_0.Length; i++)
		{
			if (byte_0[i] == 32 || byte_0[i] == 10)
			{
				byte[] array = new byte[i - num];
				System.Buffer.BlockCopy(byte_0, 处理长度, array, 0, array.Length);
				string @string = Encoding.Default.GetString(array);
				处理长度 += array.Length + 1;
				return @string;
			}
		}
		return string.Empty;
	}

	private byte[] Write(byte[] 数据)
	{
		int num = 0;
		bool flag = true;
		while (true)
		{
			int num2 = 0;
			while (true)
			{
				IL_00e3:
				bool flag2 = true;
				while (true)
				{
					IL_0018:
					if ((long)num2 < (long)数据.Length)
					{
						num = 0;
						bool flag3 = true;
						while (true)
						{
							switch ((num >= byte_1A24628.Length) ? 3 : 9)
							{
							case 5:
								goto IL_0018;
							case 9:
								if (数据[num2] != byte_1A24628[num])
								{
									num++;
									continue;
								}
								goto case 7;
							case 7:
								数据[num2] = (byte)num;
								goto case 0;
							case 0:
							case 3:
								num2++;
								goto IL_00e3;
							case 1:
							case 8:
							case 10:
								continue;
							case 6:
								goto IL_00d4;
							case 2:
							case 4:
								goto IL_00e3;
							}
							break;
						}
						break;
					}
					goto IL_00d4;
					IL_00d4:
					return 数据;
				}
				break;
			}
		}
	}

	private byte WriteByte(byte 数据)
	{
		bool flag = true;
		while (true)
		{
			int num = 0;
			while (true)
			{
				IL_0077:
				bool flag2 = true;
				while (true)
				{
					switch ((num >= byte_1A24628.Length) ? 5 : 2)
					{
					case 2:
						if (数据 != byte_1A24628[num])
						{
							num++;
							goto IL_0077;
						}
						goto case 0;
					case 0:
						数据 = (byte)num;
						goto case 5;
					case 5:
					case 6:
						return 数据;
					case 3:
						continue;
					case 1:
					case 4:
						goto IL_0077;
					}
					break;
				}
				break;
			}
		}
	}

	private void WriteInt(FileStream tdbtt, int 数据)
	{
		byte[] array = Write(Encoding.Default.GetBytes(数据.ToString()));
		tdbtt.Write(array, 0, array.Length);
		tdbtt.WriteByte(WriteByte(32));
	}

	private void WriteString(FileStream tdbtt, string 数据)
	{
		byte[] array = Write(Encoding.Default.GetBytes(数据));
		WriteInt(tdbtt, array.Length);
		tdbtt.Write(array, 0, array.Length);
		tdbtt.WriteByte(WriteByte(32));
	}

	private void 打开YbqcfgToolStripMenuItem_Click(object sender, EventArgs e)
	{
		try
		{
			int num19 = 0;
			FileStream fileStream = null;
			int num20 = 0;
			byte[] array = null;
			int num21 = 0;
			int num10 = 0;
			任务阶段类 任务阶段类2 = null;
			int num11 = 0;
			int num12 = 0;
			int num13 = 0;
			int num14 = 0;
			int num15 = 0;
			X_Nhiem_Vu_Loai value = null;
			int num16 = 0;
			int num17 = 0;
			int num18 = 0;
			Dictionary<int, 任务类>.ValueCollection.Enumerator enumerator = default(Dictionary<int, 任务类>.ValueCollection.Enumerator);
			OpenFileDialog openFileDialog = new OpenFileDialog();
			openFileDialog.Filter = "Ybq.cfg file|Ybq.cfg";
			openFileDialog.FilterIndex = 1;
			openFileDialog.RestoreDirectory = true;
			num19 = 3;
			if (openFileDialog.ShowDialog() == DialogResult.OK)
			{
				num19 = 1;
				任务.Clear();
				listView.Items.Clear();
				fileStream = new FileStream(openFileDialog.FileName, FileMode.Open, FileAccess.Read, FileShare.Read);
				num19 = 2;
				try
				{
					BinaryReader binaryReader = new BinaryReader(fileStream);
					try
					{
						MemoryStream memoryStream = new MemoryStream();
						byte[] array2 = new byte[binaryReader.BaseStream.Length];
						num19 = 16;
						while (true)
						{
							num19 = 13;
							if (binaryReader.Read(array2, 0, array2.Length) <= 0)
							{
								break;
							}
							int 处理长度 = 0;
							文件地区 = getStringtou(array2, ref 处理长度);
							文件时间 = getStringtou(array2, ref 处理长度);
							getlen(array2, ref 处理长度);
							num21 = 处理长度;
							num19 = 11;
							while (true)
							{
								num19 = 4;
								if ((long)num21 >= (long)array2.Length)
								{
									break;
								}
								num19 = 3;
								if ((long)(num21 + 1) >= (long)array2.Length)
								{
									num19 = 15;
									byte value2 = byte_1A24628[array2[num21]];
									memoryStream.WriteByte(value2);
									num19 = 5;
								}
								else
								{
									num19 = 12;
									if (array2[num21] == 13)
									{
										num19 = 21;
										num19 = 0;
										if (array2[num21 + 1] == 10)
										{
											num19 = 17;
											Console.WriteLine(num21);
											num19 = 10;
											goto IL_01db;
										}
									}
									byte value3 = byte_1A24628[array2[num21]];
									memoryStream.WriteByte(value3);
									num19 = 8;
								}
								goto IL_01db;
								IL_01db:
								num21++;
								num19 = 1;
							}
							num19 = 18;
						}
						num19 = 14;
						array = memoryStream.ToArray();
						num20 = 0;
						num19 = 20;
						while (true)
						{
							num19 = 19;
							if (num20 >= array.Length)
							{
								break;
							}
							num19 = 9;
							try
							{
								bool flag = true;
								while (true)
								{
									任务类 任务类2 = new 任务类();
									任务类2.任务ID = getlen(array, ref num20);
									任务类2.任务名 = getString(array, ref num20);
									任务类2.任务等级 = getlen(array, ref num20);
									num19 = 15;
									bool flag2 = true;
									while (true)
									{
										IL_0275:
										if (World.NhiemVulist.TryGetValue(任务类2.任务ID, out value))
										{
											num19 = 24;
											goto IL_0297;
										}
										goto IL_0acf;
										IL_0297:
										任务类2.任务名 = value.任务名;
										任务类2.任务等级 = value.任务等级;
										num19 = 22;
										goto IL_0acf;
										IL_0acf:
										while (true)
										{
											IL_0acf_2:
											任务类2.任务未知1 = getlen(array, ref num20);
											任务类2.任务未知2 = getlen(array, ref num20);
											任务类2.任务未知3 = getlen(array, ref num20);
											任务类2.任务未知4 = getlen(array, ref num20);
											任务类2.任务未知5 = getlen(array, ref num20);
											任务类2.任务未知6 = getlen(array, ref num20);
											任务类2.任务未知7 = getlen(array, ref num20);
											任务类2.任务接受提示1 = getString(array, ref num20);
											任务类2.任务拒绝提示1 = getString(array, ref num20);
											任务类2.任务接受提示2 = getString(array, ref num20);
											任务类2.任务拒绝提示2 = getString(array, ref num20);
											num14 = getlen(array, ref num20);
											num13 = 0;
											num19 = 32;
											while (true)
											{
												bool flag3 = true;
												while (true)
												{
													IL_0393:
													num19 = 28;
													while (num13 >= num14)
													{
														num19 = 19;
														bool flag4 = true;
														while (true)
														{
															IL_03a6:
															任务类2.任务阶段数量 = getlen(array, ref num20);
															num19 = 18;
															bool flag5 = true;
															while (true)
															{
																IL_03c1:
																if (任务类2.任务阶段数量 > 0)
																{
																	num19 = 16;
																	bool flag6 = true;
																	while (true)
																	{
																		IL_03df:
																		任务类2.NpcID = getlen(array, ref num20);
																		任务类2.Npc未知1 = getlen(array, ref num20);
																		任务类2.Npc坐标.地图Id = getlen(array, ref num20);
																		任务类2.Npc坐标.坐标X = getlen(array, ref num20);
																		任务类2.Npc坐标.坐标Y = getlen(array, ref num20);
																		num17 = getlen(array, ref num20);
																		num18 = 0;
																		num19 = 25;
																		while (true)
																		{
																			bool flag7 = true;
																			while (true)
																			{
																				IL_0460:
																				num19 = 12;
																				while (num18 >= num17)
																				{
																					num19 = 14;
																					bool flag8 = true;
																					while (true)
																					{
																						IL_0473:
																						任务类2.任务欢迎接受提示1 = getString(array, ref num20);
																						任务类2.任务欢迎接受提示1 = 任务类2.任务欢迎接受提示1.Replace(增加内容, string.Empty);
																						任务类2.任务欢迎接受提示2 = getString(array, ref num20);
																						任务类2.任务欢迎接受提示3 = getString(array, ref num20);
																						任务类2.任务欢迎接受提示4 = getString(array, ref num20);
																						任务类2.任务欢迎接受提示5 = getString(array, ref num20);
																						任务类2.任务欢迎拒绝提示1 = getString(array, ref num20);
																						任务类2.任务欢迎拒绝提示1 = 任务类2.任务欢迎拒绝提示1.Replace(增加内容, string.Empty);
																						任务类2.任务欢迎拒绝提示2 = getString(array, ref num20);
																						任务类2.任务欢迎拒绝提示3 = getString(array, ref num20);
																						任务类2.任务欢迎拒绝提示4 = getString(array, ref num20);
																						任务类2.任务欢迎拒绝提示5 = getString(array, ref num20);
																						num10 = 0;
																						num19 = 17;
																						bool flag9 = true;
																						while (true)
																						{
																							IL_0967:
																							bool flag10 = true;
																							while (true)
																							{
																								IL_0567:
																								num19 = 30;
																								bool flag11 = true;
																								while (true)
																								{
																									IL_0571:
																									if (num10 >= 任务类2.任务阶段数量 - 1)
																									{
																										num19 = 13;
																										bool flag12 = true;
																										while (true)
																										{
																											IL_0595:
																											num15 = 任务类2.任务阶段.Count - 1;
																											num19 = 4;
																											bool flag13 = true;
																											while (true)
																											{
																												IL_05ae:
																												num19 = 3;
																												while (true)
																												{
																													switch ((num15 < 0) ? 23 : 27)
																													{
																													case 15:
																														goto IL_0275;
																													case 24:
																														goto IL_0297;
																													case 6:
																													case 32:
																														goto IL_0393;
																													case 19:
																														goto IL_03a6;
																													case 18:
																														goto IL_03c1;
																													case 16:
																														goto IL_03df;
																													case 20:
																													case 25:
																														goto IL_0460;
																													case 14:
																														goto IL_0473;
																													case 10:
																													case 17:
																														goto IL_0567;
																													case 30:
																														goto IL_0571;
																													case 13:
																														goto IL_0595;
																													case 4:
																													case 9:
																														goto IL_05ae;
																													case 2:
																														goto end_IL_0231;
																													case 27:
																														if (num15 != 0)
																														{
																															任务类2.任务阶段[num15].需要物品 = 任务类2.任务阶段[num15 - 1].需要物品;
																															num15--;
																															num19 = 9;
																															goto IL_05ae;
																														}
																														num19 = 21;
																														goto case 21;
																													case 21:
																														任务类2.任务阶段[num15].需要物品 = 任务类2.需要物品;
																														num19 = 0;
																														goto case 0;
																													case 0:
																													case 23:
																														num16 = getlen(array, ref num20);
																														num19 = 5;
																														goto case 5;
																													case 5:
																														if (num16 > 0)
																														{
																															num19 = 8;
																															goto case 8;
																														}
																														goto case 11;
																													case 8:
																														Console.WriteLine(num16);
																														num19 = 11;
																														goto case 11;
																													case 11:
																														num20++;
																														num19 = 31;
																														goto IL_0a32;
																													case 3:
																														continue;
																													case 1:
																													case 26:
																														goto IL_0802;
																													case 7:
																														goto IL_080b;
																													case 29:
																														goto IL_08cb;
																													case 12:
																														goto IL_098b;
																													case 31:
																														goto IL_0a32;
																													case 28:
																														goto IL_0a66;
																													case 22:
																														goto IL_0acf_2;
																													}
																													break;
																												}
																												break;
																											}
																											break;
																										}
																										break;
																									}
																									任务阶段类2 = new 任务阶段类();
																									任务阶段类2.任务阶段内容 = getString(array, ref num20);
																									任务阶段类2.NpcID = getlen(array, ref num20);
																									任务阶段类2.Npc未知1 = getlen(array, ref num20);
																									任务阶段类2.Npc地图ID = getlen(array, ref num20);
																									任务阶段类2.Npc坐标X = getlen(array, ref num20);
																									任务阶段类2.Npc坐标Y = getlen(array, ref num20);
																									num11 = getlen(array, ref num20);
																									num12 = 0;
																									num19 = 1;
																									goto IL_0802;
																									IL_0802:
																									num19 = 29;
																									goto IL_08cb;
																									IL_080b:
																									任务阶段类2.任务条件符合提示1 = getString(array, ref num20);
																									任务阶段类2.任务条件符合提示2 = getString(array, ref num20);
																									任务阶段类2.任务条件符合提示3 = getString(array, ref num20);
																									任务阶段类2.任务条件符合提示4 = getString(array, ref num20);
																									任务阶段类2.任务条件符合提示5 = getString(array, ref num20);
																									任务阶段类2.任务条件不符合提示1 = getString(array, ref num20);
																									任务阶段类2.任务条件不符合提示2 = getString(array, ref num20);
																									任务阶段类2.任务条件不符合提示3 = getString(array, ref num20);
																									任务阶段类2.任务条件不符合提示4 = getString(array, ref num20);
																									任务阶段类2.任务条件不符合提示5 = getString(array, ref num20);
																									任务类2.任务阶段.Add(任务阶段类2);
																									num10++;
																									num19 = 10;
																									goto IL_0967;
																									IL_08cb:
																									if (num12 < num11)
																									{
																										List<任务需要物品类> 需要物品 = 任务阶段类2.需要物品;
																										任务需要物品类 任务需要物品类2 = new 任务需要物品类();
																										任务需要物品类2.物品ID = getlen(array, ref num20);
																										任务需要物品类2.物品数量 = getlen(array, ref num20);
																										任务需要物品类2.地图ID = getlen(array, ref num20);
																										任务需要物品类2.坐标X = getlen(array, ref num20);
																										任务需要物品类2.坐标Y = getlen(array, ref num20);
																										需要物品.Add(任务需要物品类2);
																										num12++;
																										num19 = 26;
																										goto IL_0802;
																									}
																									num19 = 7;
																									goto IL_080b;
																								}
																								break;
																							}
																							break;
																						}
																						break;
																					}
																					goto end_IL_0a20;
																					IL_098b:;
																				}
																				break;
																			}
																			List<任务需要物品类> 需要物品2 = 任务类2.需要物品;
																			任务需要物品类 任务需要物品类3 = new 任务需要物品类();
																			任务需要物品类3.物品ID = getlen(array, ref num20);
																			任务需要物品类3.物品数量 = getlen(array, ref num20);
																			任务需要物品类3.地图ID = getlen(array, ref num20);
																			任务需要物品类3.坐标X = getlen(array, ref num20);
																			任务需要物品类3.坐标Y = getlen(array, ref num20);
																			需要物品2.Add(任务需要物品类3);
																			num18++;
																			num19 = 20;
																			continue;
																			end_IL_0a20:
																			break;
																		}
																		break;
																	}
																	break;
																}
																goto IL_0a32;
																IL_0a32:
																任务.Add(任务类2.任务ID, 任务类2);
																num19 = 2;
																goto end_IL_0231;
															}
															break;
														}
														goto end_IL_0ac7;
														IL_0a66:;
													}
													break;
												}
												List<任务奖励物品类> 奖励物品 = 任务类2.奖励物品;
												任务奖励物品类 任务奖励物品类2 = new 任务奖励物品类();
												任务奖励物品类2.物品ID = getlen(array, ref num20);
												任务奖励物品类2.物品数量 = getlen(array, ref num20);
												奖励物品.Add(任务奖励物品类2);
												num13++;
												num19 = 6;
												continue;
												end_IL_0ac7:
												break;
											}
											break;
										}
										break;
									}
									continue;
									end_IL_0231:
									break;
								}
							}
							catch
							{
							}
						}
						num19 = 7;
						enumerator = 任务.Values.GetEnumerator();
						num19 = 2;
						try
						{
							num19 = 1;
							while (true)
							{
								num19 = 0;
								if (!enumerator.MoveNext())
								{
									break;
								}
								任务类 current = enumerator.Current;
								listView.Items.Insert(listView.Items.Count, new ListViewItem(new string[2]
								{
									current.任务ID.ToString(),
									current.任务名
								}));
								num19 = 2;
							}
							num19 = 4;
							num19 = 3;
						}
						finally
						{
							((IDisposable)enumerator).Dispose();
						}
						toolStripStatusLabel1.Text = "任务总数:" + 任务.Count;
						num19 = 6;
					}
					finally
					{
						num19 = 0;
						while (true)
						{
							switch (num19)
							{
							case 1:
								break;
							default:
								if (binaryReader != null)
								{
									num19 = 2;
									continue;
								}
								break;
							case 2:
								((IDisposable)binaryReader).Dispose();
								num19 = 1;
								continue;
							}
							break;
						}
					}
				}
				finally
				{
					num19 = 0;
					while (true)
					{
						switch (num19)
						{
						case 1:
							break;
						default:
							if (fileStream != null)
							{
								num19 = 2;
								continue;
							}
							break;
						case 2:
							((IDisposable)fileStream).Dispose();
							num19 = 1;
							continue;
						}
						break;
					}
				}
			}
			num19 = 0;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "Mở ra Ybq.cfg Phạm sai lầm -" + ex.Message);
		}
	}

	private void 保存YbqcfgToolStripMenuItem_Click(object sender, EventArgs e)
	{
		try
		{
			int num2 = 0;
			FileStream fileStream = null;
			List<任务需要物品类>.Enumerator enumerator = default(List<任务需要物品类>.Enumerator);
			任务阶段类 任务阶段类2 = null;
			List<任务阶段类>.Enumerator enumerator2 = default(List<任务阶段类>.Enumerator);
			任务类 任务类2 = null;
			List<任务奖励物品类>.Enumerator enumerator3 = default(List<任务奖励物品类>.Enumerator);
			List<任务需要物品类>.Enumerator enumerator4 = default(List<任务需要物品类>.Enumerator);
			SaveFileDialog saveFileDialog = new SaveFileDialog();
			saveFileDialog.Filter = "cfg files (*.cfg)|*.cfg|All files (*.*)|*.*";
			saveFileDialog.FilterIndex = 1;
			saveFileDialog.RestoreDirectory = true;
			num2 = 3;
			if (saveFileDialog.ShowDialog() == DialogResult.OK)
			{
				num2 = 2;
				fileStream = new FileStream(saveFileDialog.FileName, FileMode.Create, FileAccess.Write, FileShare.Read);
				num2 = 1;
				try
				{
					byte[] bytes = Encoding.Default.GetBytes(文件地区);
					byte[] bytes2 = Encoding.Default.GetBytes(文件时间);
					Encoding @default = Encoding.Default;
					string s = 任务.Count.ToString();
					byte[] bytes3 = @default.GetBytes(s);
					fileStream.Write(bytes, 0, bytes.Length);
					fileStream.WriteByte(32);
					fileStream.Write(bytes2, 0, bytes2.Length);
					fileStream.WriteByte(10);
					fileStream.Write(bytes3, 0, bytes3.Length);
					fileStream.WriteByte(32);
					using Dictionary<int, 任务类>.ValueCollection.Enumerator enumerator5 = 任务.Values.GetEnumerator();
					num2 = 11;
					while (true)
					{
						num2 = 14;
						if (!enumerator5.MoveNext())
						{
							break;
						}
						任务类2 = enumerator5.Current;
						WriteInt(fileStream, 任务类2.任务ID);
						WriteString(fileStream, 任务类2.任务名);
						WriteInt(fileStream, 任务类2.任务等级);
						WriteInt(fileStream, 任务类2.任务未知1);
						WriteInt(fileStream, 任务类2.任务未知2);
						WriteInt(fileStream, 任务类2.任务未知3);
						WriteInt(fileStream, 任务类2.任务未知4);
						WriteInt(fileStream, 任务类2.任务未知5);
						WriteInt(fileStream, 任务类2.任务未知6);
						WriteInt(fileStream, 任务类2.任务未知7);
						WriteString(fileStream, 任务类2.任务接受提示1);
						WriteString(fileStream, 任务类2.任务拒绝提示1);
						WriteString(fileStream, 任务类2.任务接受提示2);
						WriteString(fileStream, 任务类2.任务拒绝提示2);
						WriteInt(fileStream, 任务类2.奖励物品.Count);
						num2 = 9;
						if (任务类2.奖励物品.Count > 0)
						{
							num2 = 13;
							enumerator3 = 任务类2.奖励物品.GetEnumerator();
							num2 = 1;
							try
							{
								num2 = 4;
								while (true)
								{
									num2 = 3;
									if (!enumerator3.MoveNext())
									{
										break;
									}
									任务奖励物品类 current = enumerator3.Current;
									WriteInt(fileStream, current.物品ID);
									WriteInt(fileStream, current.物品数量);
									num2 = 0;
								}
								num2 = 2;
								num2 = 1;
							}
							finally
							{
								((IDisposable)enumerator3).Dispose();
							}
						}
						num2 = 5;
						if (任务类2.任务阶段数量 > 0)
						{
							num2 = 10;
							WriteInt(fileStream, 任务类2.任务阶段.Count + 1);
							WriteInt(fileStream, 任务类2.NpcID);
							WriteInt(fileStream, 任务类2.Npc未知1);
							WriteInt(fileStream, 任务类2.Npc坐标.地图Id);
							WriteInt(fileStream, 任务类2.Npc坐标.坐标X);
							WriteInt(fileStream, 任务类2.Npc坐标.坐标Y);
							WriteInt(fileStream, 任务类2.需要物品.Count);
							num2 = 4;
							if (任务类2.需要物品.Count > 0)
							{
								num2 = 7;
								enumerator4 = 任务类2.需要物品.GetEnumerator();
								num2 = 0;
								try
								{
									num2 = 2;
									while (true)
									{
										num2 = 0;
										if (!enumerator4.MoveNext())
										{
											break;
										}
										任务需要物品类 current2 = enumerator4.Current;
										WriteInt(fileStream, current2.物品ID);
										WriteInt(fileStream, current2.物品数量);
										WriteInt(fileStream, current2.地图ID);
										WriteInt(fileStream, current2.坐标X);
										WriteInt(fileStream, current2.坐标Y);
										num2 = 1;
									}
									num2 = 3;
									num2 = 4;
								}
								finally
								{
									((IDisposable)enumerator4).Dispose();
								}
							}
							num2 = 6;
							try
							{
								num2 = 3;
								if (任务类2.任务欢迎接受提示1.Length > 10)
								{
									num2 = 10;
									num2 = 6;
									while (true)
									{
										num2 = 2;
										if (任务类2.任务欢迎接受提示1.LastIndexOf("\r\n") < 任务类2.任务欢迎接受提示1.Length - 2)
										{
											break;
										}
										任务类2.任务欢迎接受提示1 = 任务类2.任务欢迎接受提示1.Remove(任务类2.任务欢迎接受提示1.Length - 2, 2);
										num2 = 7;
									}
									num2 = 8;
								}
								num2 = 0;
								if (任务类2.任务欢迎拒绝提示1.Length > 10)
								{
									num2 = 4;
									num2 = 9;
									while (true)
									{
										num2 = 5;
										if (任务类2.任务欢迎拒绝提示1.LastIndexOf("\r\n") < 任务类2.任务欢迎拒绝提示1.Length - 2)
										{
											break;
										}
										任务类2.任务欢迎拒绝提示1 = 任务类2.任务欢迎拒绝提示1.Remove(任务类2.任务欢迎拒绝提示1.Length - 2, 2);
										num2 = 1;
									}
									num2 = 11;
								}
								num2 = 12;
							}
							catch (Exception ex)
							{
								Form1.WriteLine(2, "Bảo tồn Ybq.cfg Phạm sai lầm -" + ex.Message);
							}
							WriteString(fileStream, 任务类2.任务欢迎接受提示1 + 增加内容);
							WriteString(fileStream, 任务类2.任务欢迎接受提示2);
							WriteString(fileStream, 任务类2.任务欢迎接受提示3);
							WriteString(fileStream, 任务类2.任务欢迎接受提示4);
							WriteString(fileStream, 任务类2.任务欢迎接受提示5);
							WriteString(fileStream, 任务类2.任务欢迎拒绝提示1 + 增加内容);
							WriteString(fileStream, 任务类2.任务欢迎拒绝提示2);
							WriteString(fileStream, 任务类2.任务欢迎拒绝提示3);
							WriteString(fileStream, 任务类2.任务欢迎拒绝提示4);
							WriteString(fileStream, 任务类2.任务欢迎拒绝提示5);
							enumerator2 = 任务类2.任务阶段.GetEnumerator();
							num2 = 15;
							try
							{
								num2 = 2;
								while (true)
								{
									num2 = 0;
									if (!enumerator2.MoveNext())
									{
										break;
									}
									任务阶段类2 = enumerator2.Current;
									WriteString(fileStream, 任务阶段类2.任务阶段内容);
									WriteInt(fileStream, 任务阶段类2.NpcID);
									WriteInt(fileStream, 任务阶段类2.Npc未知1);
									WriteInt(fileStream, 任务阶段类2.Npc地图ID);
									WriteInt(fileStream, 任务阶段类2.Npc坐标X);
									WriteInt(fileStream, 任务阶段类2.Npc坐标Y);
									WriteInt(fileStream, 任务阶段类2.需要物品.Count);
									num2 = 4;
									if (任务阶段类2.需要物品.Count > 0)
									{
										num2 = 3;
										enumerator = 任务阶段类2.需要物品.GetEnumerator();
										num2 = 5;
										try
										{
											num2 = 2;
											while (true)
											{
												num2 = 0;
												if (!enumerator.MoveNext())
												{
													break;
												}
												任务需要物品类 current3 = enumerator.Current;
												WriteInt(fileStream, current3.物品ID);
												WriteInt(fileStream, current3.物品数量);
												WriteInt(fileStream, current3.地图ID);
												WriteInt(fileStream, current3.坐标X);
												WriteInt(fileStream, current3.坐标Y);
												num2 = 1;
											}
											num2 = 3;
											num2 = 4;
										}
										finally
										{
											((IDisposable)enumerator).Dispose();
										}
									}
									WriteString(fileStream, 任务阶段类2.任务条件符合提示1);
									WriteString(fileStream, 任务阶段类2.任务条件符合提示2);
									WriteString(fileStream, 任务阶段类2.任务条件符合提示3);
									WriteString(fileStream, 任务阶段类2.任务条件符合提示4);
									WriteString(fileStream, 任务阶段类2.任务条件符合提示5);
									WriteString(fileStream, 任务阶段类2.任务条件不符合提示1);
									WriteString(fileStream, 任务阶段类2.任务条件不符合提示2);
									WriteString(fileStream, 任务阶段类2.任务条件不符合提示3);
									WriteString(fileStream, 任务阶段类2.任务条件不符合提示4);
									WriteString(fileStream, 任务阶段类2.任务条件不符合提示5);
									num2 = 1;
								}
								num2 = 6;
								num2 = 7;
							}
							finally
							{
								((IDisposable)enumerator2).Dispose();
							}
							WriteInt(fileStream, 0);
							fileStream.WriteByte(WriteByte(10));
							num2 = 12;
						}
						else
						{
							Encoding default2 = Encoding.Default;
							string s2 = 任务类2.任务阶段.Count.ToString();
							byte[] array = Write(default2.GetBytes(s2));
							fileStream.Write(array, 0, array.Length);
							fileStream.WriteByte(WriteByte(10));
							num2 = 3;
						}
					}
					num2 = 8;
					num2 = 2;
				}
				finally
				{
					num2 = 0;
					while (true)
					{
						switch (num2)
						{
						case 2:
							break;
						case 1:
							((IDisposable)fileStream).Dispose();
							num2 = 2;
							continue;
						default:
							if (fileStream != null)
							{
								num2 = 1;
								continue;
							}
							break;
						}
						break;
					}
				}
			}
			num2 = 0;
		}
		catch (Exception ex2)
		{
			Form1.WriteLine(2, "Bảo tồn Ybq.cfg Phạm sai lầm -" + ex2.Message);
		}
	}

	private void listView_SelectedIndexChanged(object sender, EventArgs e)
	{
		任务类 value = null;
		if (listView.SelectedItems.Count > 0 && 任务.TryGetValue(int.Parse(listView.SelectedItems[0].SubItems[0].Text), out value))
		{
			CustomPropertyCollection customPropertyCollection = new CustomPropertyCollection();
			customPropertyCollection.Add(new CustomProperty("任务ID", "任务ID", bool_3: true, "基本", "任务ID", value));
			customPropertyCollection.Add(new CustomProperty("任务名", "任务名", bool_3: false, "基本", "任务名", value));
			customPropertyCollection.Add(new CustomProperty("任务等级", "任务等级", bool_3: false, "基本", "任务等级", value));
			customPropertyCollection.Add(new CustomProperty("任务拒绝提示1", "任务拒绝提示1", bool_3: false, "基本", "拒绝接受任务后的提示", value, typeof(MultilineStringEditor)));
			customPropertyCollection.Add(new CustomProperty("任务拒绝提示2", "任务拒绝提示2", bool_3: false, "基本", "拒绝接受任务后的提示", value, typeof(MultilineStringEditor)));
			customPropertyCollection.Add(new CustomProperty("任务接受提示1", "任务接受提示1", bool_3: false, "基本", "接受任务后的提示", value, typeof(MultilineStringEditor)));
			customPropertyCollection.Add(new CustomProperty("任务接受提示2", "任务接受提示2", bool_3: false, "基本", "接受任务后的提示", value, typeof(MultilineStringEditor)));
			customPropertyCollection.Add(new CustomProperty("NpcID", "NpcID", bool_3: false, "Npc", "NpcID", value));
			customPropertyCollection.Add(new CustomProperty("Npc坐标", "Npc坐标", bool_3: false, "Npc", "Npc坐标", value, bool_4: true));
			customPropertyCollection.Add(new CustomProperty("奖励物品列表", "奖励物品", bool_3: false, "奖励物品", "奖励物品", value, typeof(My奖励物品CollectionEditor)));
			customPropertyCollection.Add(new CustomProperty("需要物品列表", "需要物品", bool_3: false, "需要物品", "需要物品", value, typeof(My需要物品CollectionEditor)));
			customPropertyCollection.Add(new CustomProperty("不符合内容", "任务欢迎拒绝提示1", bool_3: false, "任务内容", "打开任务后条件不符合的提示内容", value, typeof(MultilineStringEditor)));
			customPropertyCollection.Add(new CustomProperty("符合内容", "任务欢迎接受提示1", bool_3: false, "任务内容", "打开任务后条件符合的提示内容", value, typeof(MultilineStringEditor)));
			customPropertyCollection.Add(new CustomProperty("阶段数量", "任务阶段数量", bool_3: false, "任务阶段", "任务阶段总数量=阶段列表的数量+1", value));
			customPropertyCollection.Add(new CustomProperty("阶段列表", "任务阶段", bool_3: false, "任务阶段", "任务阶段列表", value, typeof(My任务阶段CollectionEditor)));
			propertyGrid1.SelectedObject = customPropertyCollection;
		}
	}

	private string getnpcname(int NpcWordId)
	{
		string empty = string.Empty;
		DataTable dBToDataTable = DBA.GetDBToDataTable($"select FLD_PID, FLD_NAME from [TBL_XWWL_NPC] where FLD_PID={NpcWordId}", "PublicDb");
		if (dBToDataTable.Rows.Count > 0 && int.Parse(dBToDataTable.Rows[0]["FLD_PID"].ToString()) == NpcWordId)
		{
			return (string)dBToDataTable.Rows[0]["FLD_NAME"];
		}
		dBToDataTable.Dispose();
		return empty;
	}

	private void 编辑任务ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		int num2 = 0;
		if (listView.SelectedItems.Count != 0)
		{
			num2 = int.Parse(listView.FocusedItem.SubItems[0].Text);
			if (!World.NhiemVulist.TryGetValue(num2, out var _))
			{
				MessageBox.Show("数据库中不存在此任务数据,请先入库!");
			}
		}
	}

	private void listView_DoubleClick(object sender, EventArgs e)
	{
		int num2 = 0;
		if (listView.SelectedItems.Count != 0)
		{
			num2 = int.Parse(listView.FocusedItem.SubItems[0].Text);
			if (!World.NhiemVulist.TryGetValue(num2, out var _))
			{
				MessageBox.Show("数据库中不存在此任务数据,请先入库!");
			}
		}
	}

	private void 入库ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		using Dictionary<int, 任务类>.ValueCollection.Enumerator enumerator9 = 任务.Values.GetEnumerator();
		byte[] array = null;
		byte[] array2 = null;
		任务类 任务类2 = null;
		int num2 = 0;
		int num3 = 0;
		byte[] array3 = null;
		int num4 = 0;
		int num5 = 0;
		int num6 = 0;
		int num7 = 0;
		int num8 = 0;
		string text = null;
		string text2 = null;
		string text3 = null;
		int num9 = 0;
		string[] array4 = null;
		while (enumerator9.MoveNext())
		{
			任务类2 = enumerator9.Current;
			if (任务类2.任务ID == 9751)
			{
				continue;
			}
			array3 = new byte[6];
			array2 = new byte[任务类2.任务阶段.Count * 530];
			System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.任务ID), 0, array3, 0, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.NpcID), 0, array3, 2, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.任务阶段.Count), 0, array3, 4, 2);
			text2 = getnpcname(任务类2.NpcID);
			if (text2 == string.Empty)
			{
				text2 = "热血江湖";
			}
			array4 = new string[13];
			for (num9 = 0; num9 < 13; num9++)
			{
				array4[num9] = "NULL";
			}
			text = "NULL";
			text3 = "NULL";
			if (任务类2.奖励物品.Count > 0)
			{
				text = string.Empty;
				foreach (任务奖励物品类 item in 任务类2.奖励物品)
				{
					text = text + item.物品ID + ";" + item.物品数量 + ";0|";
				}
			}
			if (text != "NULL")
			{
				text.Remove(text.LastIndexOf("|"), 1);
			}
			if (任务类2.需要物品.Count > 0)
			{
				text3 = string.Empty;
				foreach (任务需要物品类 item2 in 任务类2.需要物品)
				{
					text3 = text3 + item2.物品ID + "," + item2.物品数量 + ",0;";
				}
			}
			if (text3 != "NULL")
			{
				text3.Remove(text3.LastIndexOf(";"), 1);
			}
			if (任务类2.任务ID != 311)
			{
			}
			if (任务类2.任务阶段.Count > 1)
			{
				for (num2 = 1; num2 < 任务类2.任务阶段.Count + 1; num2++)
				{
					System.Buffer.BlockCopy(BitConverter.GetBytes(num2), 0, array2, (num2 - 1) * 530, 2);
					System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.任务阶段[num2 - 1].NpcID), 0, array2, (num2 - 1) * 530 + 2, 2);
					System.Buffer.BlockCopy(BitConverter.GetBytes(20), 0, array2, (num2 - 1) * 530 + 4, 2);
					if (num2 == 1)
					{
						if (任务类2.需要物品.Count > 0)
						{
							num5 = 0;
							foreach (任务需要物品类 item3 in 任务类2.需要物品)
							{
								System.Buffer.BlockCopy(BitConverter.GetBytes(item3.物品ID), 0, array2, 10 + num5 * 12, 4);
								System.Buffer.BlockCopy(BitConverter.GetBytes(item3.物品数量), 0, array2, 14 + num5 * 12, 2);
								System.Buffer.BlockCopy(BitConverter.GetBytes(item3.物品数量), 0, array2, 16 + num5 * 12, 2);
								num5++;
							}
						}
						else if (任务类2.任务阶段[num2 - 1].需要物品.Count > 0)
						{
							num4 = 0;
							foreach (任务需要物品类 item4 in 任务类2.任务阶段[num2 - 1].需要物品)
							{
								System.Buffer.BlockCopy(BitConverter.GetBytes(item4.物品ID), 0, array2, 10 + num4 * 12, 4);
								System.Buffer.BlockCopy(BitConverter.GetBytes(item4.物品数量), 0, array2, 14 + num4 * 12, 2);
								System.Buffer.BlockCopy(BitConverter.GetBytes(item4.物品数量), 0, array2, 16 + num4 * 12, 2);
								num4++;
							}
						}
					}
					else if (任务类2.任务阶段[num2 - 1].需要物品.Count > 0)
					{
						num6 = 0;
						foreach (任务需要物品类 item5 in 任务类2.任务阶段[num2 - 1].需要物品)
						{
							System.Buffer.BlockCopy(BitConverter.GetBytes(item5.物品ID), 0, array2, (num2 - 1) * 530 + 6 + 4 + num6 * 12, 4);
							System.Buffer.BlockCopy(BitConverter.GetBytes(item5.物品数量), 0, array2, (num2 - 1) * 530 + 6 + 4 + 4 + num6 * 12, 2);
							System.Buffer.BlockCopy(BitConverter.GetBytes(item5.物品数量), 0, array2, (num2 - 1) * 530 + 6 + 4 + 4 + 2 + num6 * 12, 2);
							num6++;
						}
					}
					if (num2 == 任务类2.任务阶段.Count && 任务类2.奖励物品.Count > 0)
					{
						num3 = 0;
						foreach (任务奖励物品类 item6 in 任务类2.奖励物品)
						{
							System.Buffer.BlockCopy(BitConverter.GetBytes(item6.物品ID), 0, array2, 126 + num3 * 8 + (num2 - 1) * 530, 4);
							System.Buffer.BlockCopy(BitConverter.GetBytes(item6.物品数量), 0, array2, 126 + (num3 * 8 + 4) + (num2 - 1) * 530, 4);
							num3++;
						}
					}
					byte[] bytes = Encoding.Default.GetBytes(getnpcname(任务类2.任务阶段[num2 - 1].NpcID));
					System.Buffer.BlockCopy(bytes, 0, array2, (num2 - 1) * 530 + 206, bytes.Length);
					System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.任务阶段[num2 - 1].Npc地图ID), 0, array2, (num2 - 1) * 530 + 220, 2);
					System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.任务阶段[num2 - 1].Npc坐标X), 0, array2, (num2 - 1) * 530 + 222, 4);
					System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.任务阶段[num2 - 1].Npc坐标Y), 0, array2, (num2 - 1) * 530 + 226, 4);
					byte[] bytes2 = Encoding.Default.GetBytes(任务类2.任务阶段[num2 - 1].任务阶段内容);
					System.Buffer.BlockCopy(bytes2, 0, array2, (num2 - 1) * 530 + 230, bytes2.Length);
				}
			}
			else if (任务类2.任务阶段.Count == 0)
			{
				array2 = new byte[530];
				array3 = new byte[6];
				System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.任务ID), 0, array3, 0, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.NpcID), 0, array3, 2, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array3, 4, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array2, 0, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.NpcID), 0, array2, 2, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array2, 4, 2);
				byte[] bytes3 = Encoding.Default.GetBytes(text2);
				System.Buffer.BlockCopy(bytes3, 0, array2, 206, bytes3.Length);
				System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.Npc坐标.地图Id), 0, array2, 220, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.Npc坐标.坐标X), 0, array2, 222, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.Npc坐标.坐标Y), 0, array2, 226, 4);
				byte[] bytes4 = Encoding.Default.GetBytes(任务类2.任务名);
				System.Buffer.BlockCopy(bytes4, 0, array2, 230, bytes4.Length);
			}
			else
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array2, 0, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.任务阶段[0].NpcID), 0, array2, 2, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(20), 0, array2, 4, 2);
				if (任务类2.需要物品.Count > 0)
				{
					num7 = 0;
					foreach (任务需要物品类 item7 in 任务类2.需要物品)
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(item7.物品ID), 0, array2, 10 + num7 * 12, 4);
						System.Buffer.BlockCopy(BitConverter.GetBytes(item7.物品数量), 0, array2, 14 + num7 * 12, 2);
						System.Buffer.BlockCopy(BitConverter.GetBytes(item7.物品数量), 0, array2, 16 + num7 * 12, 2);
						num7++;
					}
				}
				if (任务类2.奖励物品.Count > 0)
				{
					num8 = 0;
					foreach (任务奖励物品类 item8 in 任务类2.奖励物品)
					{
						System.Buffer.BlockCopy(BitConverter.GetBytes(item8.物品ID), 0, array2, 126 + num8 * 8, 4);
						System.Buffer.BlockCopy(BitConverter.GetBytes(item8.物品数量), 0, array2, 126 + (num8 * 8 + 4), 4);
						num8++;
					}
				}
				getnpcname(任务类2.任务阶段[0].NpcID);
				byte[] bytes5 = Encoding.Default.GetBytes(getnpcname(任务类2.任务阶段[0].NpcID));
				System.Buffer.BlockCopy(bytes5, 0, array2, 206, bytes5.Length);
				System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.任务阶段[0].Npc地图ID), 0, array2, 220, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.任务阶段[0].Npc坐标X), 0, array2, 222, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(任务类2.任务阶段[0].Npc坐标Y), 0, array2, 226, 4);
				array = Encoding.Default.GetBytes(任务类2.任务阶段[0].任务阶段内容);
				if (array.Length > 200)
				{
					System.Buffer.BlockCopy(array, 0, array2, 230, 200);
				}
				else
				{
					System.Buffer.BlockCopy(array, 0, array2, 230, array.Length);
				}
			}
			byte[] dst = new byte[array3.Length + array2.Length];
			System.Buffer.BlockCopy(array3, 0, dst, 0, array3.Length);
			System.Buffer.BlockCopy(array2, 0, dst, 6, array2.Length);
		}
	}
}
