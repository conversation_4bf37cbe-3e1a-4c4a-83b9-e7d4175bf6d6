using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhTool;

public class YbiForm : Form
{
	public static Dictionary<int, YbiClass> ybidate = new Dictionary<int, YbiClass>();

	private static byte[] byte_0 = new byte[64]
	{
		18, 29, 7, 25, 15, 31, 22, 27, 9, 26,
		3, 13, 19, 14, 20, 11, 5, 2, 23, 16,
		10, 24, 28, 17, 6, 30, 0, 21, 12, 8,
		4, 1, 26, 31, 17, 10, 30, 16, 24, 2,
		29, 8, 20, 15, 28, 11, 13, 4, 19, 23,
		0, 12, 14, 27, 6, 18, 21, 3, 9, 7,
		22, 1, 25, 5
	};

	private byte[] byte_1A24628 = new byte[256];

	private byte[] byte_8B42F4 = new byte[150]
	{
		45, 151, 132, 242, 40, 209, 41, 84, 241, 18,
		2, 40, 107, 32, 97, 66, 245, 236, 31, 82,
		34, 68, 59, 35, 87, 63, 98, 111, 242, 163,
		36, 210, 167, 22, 86, 193, 242, 2, 72, 98,
		39, 66, 33, 172, 35, 173, 67, 242, 50, 24,
		4, 80, 69, 113, 191, 110, 120, 97, 114, 88,
		34, 146, 22, 2, 120, 98, 215, 40, 82, 223,
		232, 179, 66, 20, 177, 166, 110, 49, 175, 38,
		33, 179, 215, 84, 233, 47, 114, 44, 63, 81,
		244, 17, 2, 242, 183, 64, 37, 195, 37, 130,
		67, 50, 36, 241, 238, 255, 66, 18, 4, 158,
		47, 249, 100, 33, 246, 49, 114, 8, 116, 130,
		35, 25, 222, 207, 35, 39, 56, 54, 237, 242,
		73, 98, 113, 40, 232, 34, 59, 183, 53, 66,
		119, 66, 47, 164, 66, 39, 148, 2, 98, 167
	};

	private byte[] 物品 = new byte[8520000];

	private byte[] KEY = new byte[64];

	private byte[] 武功 = new byte[6897664];

	private byte[] 气功 = new byte[1421312];

	private byte[] 称号 = new byte[9216];

	private byte[] 屏蔽1 = new byte[131072];

	private byte[] 屏蔽2 = new byte[262144];

	private byte[] NPC = new byte[16097280];

	private byte[] 未知1 = new byte[96];

	private byte[] 地图名 = new byte[102400];

	private byte[] 未知2 = new byte[98304];

	private IContainer components;

	private MenuStrip menuStrip1;

	private ToolStripMenuItem 文件ToolStripMenuItem;

	private ToolStripMenuItem 打开YBIToolStripMenuItem;

	private ToolStripMenuItem 保存YBIToolStripMenuItem;

	private ListBox listBox1;

	private GroupBox groupBox1;

	private GroupBox groupBox2;

	private Label label1;

	private Label label11;

	private Label label10;

	private Label label9;

	private Label label8;

	private Label label7;

	private Label label6;

	private Label label5;

	private Label label4;

	private Label label3;

	private Label label2;

	private Label label19;

	private Label label18;

	private Label label17;

	private Label label16;

	private Label label15;

	private Label label14;

	private Label label13;

	private Label label12;

	private Label label25;

	private TextBox textBox13;

	private TextBox textBox12;

	private TextBox textBox11;

	private TextBox textBox10;

	private TextBox textBox9;

	private TextBox textBox8;

	private TextBox textBox7;

	private TextBox textBox6;

	private TextBox textBox5;

	private TextBox textBox4;

	private TextBox textBox3;

	private TextBox textBox2;

	private TextBox textBox1;

	private TextBox textBox14;

	private TextBox textBox21;

	private TextBox textBox22;

	private TextBox textBox23;

	private TextBox textBox24;

	private TextBox textBox25;

	private TextBox textBox26;

	private Label label27;

	private Label label28;

	private Button button1;

	private ToolStripMenuItem 功能ToolStripMenuItem;

	private ToolStripMenuItem 物品入库ToolStripMenuItem;

	private TextBox textBox15;

	private RadioButton radioButton1;

	private RadioButton radioButton2;

	private Button button2;

	private GroupBox groupBox3;

	private Label label20;

	private ComboBox comboBox1;

	private TextBox textBox16;

	private Label label22;

	private Label label21;

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.menuStrip1 = new System.Windows.Forms.MenuStrip();
		this.文件ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.打开YBIToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.保存YBIToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.功能ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.物品入库ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
		this.listBox1 = new System.Windows.Forms.ListBox();
		this.groupBox1 = new System.Windows.Forms.GroupBox();
		this.groupBox2 = new System.Windows.Forms.GroupBox();
		this.label20 = new System.Windows.Forms.Label();
		this.button1 = new System.Windows.Forms.Button();
		this.textBox14 = new System.Windows.Forms.TextBox();
		this.textBox21 = new System.Windows.Forms.TextBox();
		this.textBox22 = new System.Windows.Forms.TextBox();
		this.textBox23 = new System.Windows.Forms.TextBox();
		this.textBox24 = new System.Windows.Forms.TextBox();
		this.textBox25 = new System.Windows.Forms.TextBox();
		this.textBox26 = new System.Windows.Forms.TextBox();
		this.textBox13 = new System.Windows.Forms.TextBox();
		this.textBox12 = new System.Windows.Forms.TextBox();
		this.textBox11 = new System.Windows.Forms.TextBox();
		this.textBox10 = new System.Windows.Forms.TextBox();
		this.textBox9 = new System.Windows.Forms.TextBox();
		this.textBox8 = new System.Windows.Forms.TextBox();
		this.textBox7 = new System.Windows.Forms.TextBox();
		this.textBox6 = new System.Windows.Forms.TextBox();
		this.textBox5 = new System.Windows.Forms.TextBox();
		this.textBox4 = new System.Windows.Forms.TextBox();
		this.textBox3 = new System.Windows.Forms.TextBox();
		this.textBox2 = new System.Windows.Forms.TextBox();
		this.textBox1 = new System.Windows.Forms.TextBox();
		this.label25 = new System.Windows.Forms.Label();
		this.label19 = new System.Windows.Forms.Label();
		this.label18 = new System.Windows.Forms.Label();
		this.label17 = new System.Windows.Forms.Label();
		this.label16 = new System.Windows.Forms.Label();
		this.label15 = new System.Windows.Forms.Label();
		this.label14 = new System.Windows.Forms.Label();
		this.label13 = new System.Windows.Forms.Label();
		this.label12 = new System.Windows.Forms.Label();
		this.label11 = new System.Windows.Forms.Label();
		this.label10 = new System.Windows.Forms.Label();
		this.label9 = new System.Windows.Forms.Label();
		this.label8 = new System.Windows.Forms.Label();
		this.label7 = new System.Windows.Forms.Label();
		this.label6 = new System.Windows.Forms.Label();
		this.label5 = new System.Windows.Forms.Label();
		this.label4 = new System.Windows.Forms.Label();
		this.label3 = new System.Windows.Forms.Label();
		this.label2 = new System.Windows.Forms.Label();
		this.label1 = new System.Windows.Forms.Label();
		this.label27 = new System.Windows.Forms.Label();
		this.label28 = new System.Windows.Forms.Label();
		this.textBox15 = new System.Windows.Forms.TextBox();
		this.radioButton1 = new System.Windows.Forms.RadioButton();
		this.radioButton2 = new System.Windows.Forms.RadioButton();
		this.button2 = new System.Windows.Forms.Button();
		this.groupBox3 = new System.Windows.Forms.GroupBox();
		this.comboBox1 = new System.Windows.Forms.ComboBox();
		this.label21 = new System.Windows.Forms.Label();
		this.label22 = new System.Windows.Forms.Label();
		this.textBox16 = new System.Windows.Forms.TextBox();
		this.menuStrip1.SuspendLayout();
		this.groupBox1.SuspendLayout();
		this.groupBox2.SuspendLayout();
		this.groupBox3.SuspendLayout();
		base.SuspendLayout();
		this.menuStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
		this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[2] { this.文件ToolStripMenuItem, this.功能ToolStripMenuItem });
		this.menuStrip1.Location = new System.Drawing.Point(0, 0);
		this.menuStrip1.Name = "menuStrip1";
		this.menuStrip1.Size = new System.Drawing.Size(1065, 28);
		this.menuStrip1.TabIndex = 0;
		this.menuStrip1.Text = "menuStrip1";
		this.文件ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[2] { this.打开YBIToolStripMenuItem, this.保存YBIToolStripMenuItem });
		this.文件ToolStripMenuItem.Name = "文件ToolStripMenuItem";
		this.文件ToolStripMenuItem.Size = new System.Drawing.Size(53, 24);
		this.文件ToolStripMenuItem.Text = "文件";
		this.打开YBIToolStripMenuItem.Name = "打开YBIToolStripMenuItem";
		this.打开YBIToolStripMenuItem.Size = new System.Drawing.Size(171, 26);
		this.打开YBIToolStripMenuItem.Text = "打开YBi.cfg";
		this.打开YBIToolStripMenuItem.Click += new System.EventHandler(打开YBIToolStripMenuItem_Click);
		this.保存YBIToolStripMenuItem.Name = "保存YBIToolStripMenuItem";
		this.保存YBIToolStripMenuItem.Size = new System.Drawing.Size(171, 26);
		this.保存YBIToolStripMenuItem.Text = "保存YBi.cfg";
		this.保存YBIToolStripMenuItem.Click += new System.EventHandler(保存YBIToolStripMenuItem_Click);
		this.功能ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[1] { this.物品入库ToolStripMenuItem });
		this.功能ToolStripMenuItem.Name = "功能ToolStripMenuItem";
		this.功能ToolStripMenuItem.Size = new System.Drawing.Size(53, 24);
		this.功能ToolStripMenuItem.Text = "功能";
		this.物品入库ToolStripMenuItem.Name = "物品入库ToolStripMenuItem";
		this.物品入库ToolStripMenuItem.Size = new System.Drawing.Size(224, 26);
		this.物品入库ToolStripMenuItem.Text = "物品入库";
		this.物品入库ToolStripMenuItem.Click += new System.EventHandler(物品入库ToolStripMenuItem_Click);
		this.listBox1.FormattingEnabled = true;
		this.listBox1.ItemHeight = 15;
		this.listBox1.Location = new System.Drawing.Point(23, 32);
		this.listBox1.Margin = new System.Windows.Forms.Padding(4);
		this.listBox1.Name = "listBox1";
		this.listBox1.Size = new System.Drawing.Size(255, 439);
		this.listBox1.TabIndex = 1;
		this.listBox1.SelectedValueChanged += new System.EventHandler(listBox1_SelectedValueChanged);
		this.groupBox1.Controls.Add(this.listBox1);
		this.groupBox1.Location = new System.Drawing.Point(16, 121);
		this.groupBox1.Margin = new System.Windows.Forms.Padding(4);
		this.groupBox1.Name = "groupBox1";
		this.groupBox1.Padding = new System.Windows.Forms.Padding(4);
		this.groupBox1.Size = new System.Drawing.Size(300, 499);
		this.groupBox1.TabIndex = 2;
		this.groupBox1.TabStop = false;
		this.groupBox1.Text = "物品列表";
		this.groupBox2.Controls.Add(this.textBox16);
		this.groupBox2.Controls.Add(this.label22);
		this.groupBox2.Controls.Add(this.label20);
		this.groupBox2.Controls.Add(this.button1);
		this.groupBox2.Controls.Add(this.textBox14);
		this.groupBox2.Controls.Add(this.textBox21);
		this.groupBox2.Controls.Add(this.textBox22);
		this.groupBox2.Controls.Add(this.textBox23);
		this.groupBox2.Controls.Add(this.textBox24);
		this.groupBox2.Controls.Add(this.textBox25);
		this.groupBox2.Controls.Add(this.textBox26);
		this.groupBox2.Controls.Add(this.textBox13);
		this.groupBox2.Controls.Add(this.textBox12);
		this.groupBox2.Controls.Add(this.textBox11);
		this.groupBox2.Controls.Add(this.textBox10);
		this.groupBox2.Controls.Add(this.textBox9);
		this.groupBox2.Controls.Add(this.textBox8);
		this.groupBox2.Controls.Add(this.textBox7);
		this.groupBox2.Controls.Add(this.textBox6);
		this.groupBox2.Controls.Add(this.textBox5);
		this.groupBox2.Controls.Add(this.textBox4);
		this.groupBox2.Controls.Add(this.textBox3);
		this.groupBox2.Controls.Add(this.textBox2);
		this.groupBox2.Controls.Add(this.textBox1);
		this.groupBox2.Controls.Add(this.label25);
		this.groupBox2.Controls.Add(this.label19);
		this.groupBox2.Controls.Add(this.label18);
		this.groupBox2.Controls.Add(this.label17);
		this.groupBox2.Controls.Add(this.label16);
		this.groupBox2.Controls.Add(this.label15);
		this.groupBox2.Controls.Add(this.label14);
		this.groupBox2.Controls.Add(this.label13);
		this.groupBox2.Controls.Add(this.label12);
		this.groupBox2.Controls.Add(this.label11);
		this.groupBox2.Controls.Add(this.label10);
		this.groupBox2.Controls.Add(this.label9);
		this.groupBox2.Controls.Add(this.label8);
		this.groupBox2.Controls.Add(this.label7);
		this.groupBox2.Controls.Add(this.label6);
		this.groupBox2.Controls.Add(this.label5);
		this.groupBox2.Controls.Add(this.label4);
		this.groupBox2.Controls.Add(this.label3);
		this.groupBox2.Controls.Add(this.label2);
		this.groupBox2.Controls.Add(this.label1);
		this.groupBox2.Location = new System.Drawing.Point(353, 121);
		this.groupBox2.Margin = new System.Windows.Forms.Padding(4);
		this.groupBox2.Name = "groupBox2";
		this.groupBox2.Padding = new System.Windows.Forms.Padding(4);
		this.groupBox2.Size = new System.Drawing.Size(691, 499);
		this.groupBox2.TabIndex = 3;
		this.groupBox2.TabStop = false;
		this.groupBox2.Text = "物品属性";
		this.label20.AutoSize = true;
		this.label20.Location = new System.Drawing.Point(365, 336);
		this.label20.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label20.Name = "label20";
		this.label20.Size = new System.Drawing.Size(99, 15);
		this.label20.TabIndex = 53;
		this.label20.Text = "注:120字以内";
		this.button1.Location = new System.Drawing.Point(556, 460);
		this.button1.Margin = new System.Windows.Forms.Padding(4);
		this.button1.Name = "button1";
		this.button1.Size = new System.Drawing.Size(100, 29);
		this.button1.TabIndex = 52;
		this.button1.Text = "修改";
		this.button1.UseVisualStyleBackColor = true;
		this.button1.Click += new System.EventHandler(button1_Click);
		this.textBox14.Location = new System.Drawing.Point(475, 360);
		this.textBox14.Margin = new System.Windows.Forms.Padding(4);
		this.textBox14.Multiline = true;
		this.textBox14.Name = "textBox14";
		this.textBox14.Size = new System.Drawing.Size(180, 88);
		this.textBox14.TabIndex = 51;
		this.textBox14.TextChanged += new System.EventHandler(textBox14_TextChanged);
		this.textBox21.Location = new System.Drawing.Point(475, 204);
		this.textBox21.Margin = new System.Windows.Forms.Padding(4);
		this.textBox21.Name = "textBox21";
		this.textBox21.Size = new System.Drawing.Size(180, 25);
		this.textBox21.TabIndex = 44;
		this.textBox22.Location = new System.Drawing.Point(475, 169);
		this.textBox22.Margin = new System.Windows.Forms.Padding(4);
		this.textBox22.Name = "textBox22";
		this.textBox22.Size = new System.Drawing.Size(180, 25);
		this.textBox22.TabIndex = 43;
		this.textBox23.Location = new System.Drawing.Point(475, 134);
		this.textBox23.Margin = new System.Windows.Forms.Padding(4);
		this.textBox23.Name = "textBox23";
		this.textBox23.Size = new System.Drawing.Size(180, 25);
		this.textBox23.TabIndex = 42;
		this.textBox24.Location = new System.Drawing.Point(475, 99);
		this.textBox24.Margin = new System.Windows.Forms.Padding(4);
		this.textBox24.Name = "textBox24";
		this.textBox24.Size = new System.Drawing.Size(180, 25);
		this.textBox24.TabIndex = 41;
		this.textBox25.Location = new System.Drawing.Point(475, 64);
		this.textBox25.Margin = new System.Windows.Forms.Padding(4);
		this.textBox25.Name = "textBox25";
		this.textBox25.Size = new System.Drawing.Size(180, 25);
		this.textBox25.TabIndex = 40;
		this.textBox26.Location = new System.Drawing.Point(475, 29);
		this.textBox26.Margin = new System.Windows.Forms.Padding(4);
		this.textBox26.Name = "textBox26";
		this.textBox26.Size = new System.Drawing.Size(180, 25);
		this.textBox26.TabIndex = 39;
		this.textBox13.Location = new System.Drawing.Point(144, 438);
		this.textBox13.Margin = new System.Windows.Forms.Padding(4);
		this.textBox13.Name = "textBox13";
		this.textBox13.Size = new System.Drawing.Size(180, 25);
		this.textBox13.TabIndex = 38;
		this.textBox12.Location = new System.Drawing.Point(144, 404);
		this.textBox12.Margin = new System.Windows.Forms.Padding(4);
		this.textBox12.Name = "textBox12";
		this.textBox12.Size = new System.Drawing.Size(180, 25);
		this.textBox12.TabIndex = 37;
		this.textBox11.Location = new System.Drawing.Point(144, 370);
		this.textBox11.Margin = new System.Windows.Forms.Padding(4);
		this.textBox11.Name = "textBox11";
		this.textBox11.Size = new System.Drawing.Size(180, 25);
		this.textBox11.TabIndex = 36;
		this.textBox10.Location = new System.Drawing.Point(144, 336);
		this.textBox10.Margin = new System.Windows.Forms.Padding(4);
		this.textBox10.Name = "textBox10";
		this.textBox10.Size = new System.Drawing.Size(180, 25);
		this.textBox10.TabIndex = 35;
		this.textBox9.Location = new System.Drawing.Point(144, 302);
		this.textBox9.Margin = new System.Windows.Forms.Padding(4);
		this.textBox9.Name = "textBox9";
		this.textBox9.Size = new System.Drawing.Size(180, 25);
		this.textBox9.TabIndex = 34;
		this.textBox8.Location = new System.Drawing.Point(144, 269);
		this.textBox8.Margin = new System.Windows.Forms.Padding(4);
		this.textBox8.Name = "textBox8";
		this.textBox8.Size = new System.Drawing.Size(180, 25);
		this.textBox8.TabIndex = 33;
		this.textBox7.Location = new System.Drawing.Point(144, 235);
		this.textBox7.Margin = new System.Windows.Forms.Padding(4);
		this.textBox7.Name = "textBox7";
		this.textBox7.Size = new System.Drawing.Size(180, 25);
		this.textBox7.TabIndex = 32;
		this.textBox6.Location = new System.Drawing.Point(144, 201);
		this.textBox6.Margin = new System.Windows.Forms.Padding(4);
		this.textBox6.Name = "textBox6";
		this.textBox6.Size = new System.Drawing.Size(180, 25);
		this.textBox6.TabIndex = 31;
		this.textBox5.Location = new System.Drawing.Point(144, 168);
		this.textBox5.Margin = new System.Windows.Forms.Padding(4);
		this.textBox5.Name = "textBox5";
		this.textBox5.Size = new System.Drawing.Size(180, 25);
		this.textBox5.TabIndex = 30;
		this.textBox4.Location = new System.Drawing.Point(144, 134);
		this.textBox4.Margin = new System.Windows.Forms.Padding(4);
		this.textBox4.Name = "textBox4";
		this.textBox4.Size = new System.Drawing.Size(180, 25);
		this.textBox4.TabIndex = 29;
		this.textBox3.Location = new System.Drawing.Point(144, 100);
		this.textBox3.Margin = new System.Windows.Forms.Padding(4);
		this.textBox3.Name = "textBox3";
		this.textBox3.Size = new System.Drawing.Size(180, 25);
		this.textBox3.TabIndex = 28;
		this.textBox2.Location = new System.Drawing.Point(144, 66);
		this.textBox2.Margin = new System.Windows.Forms.Padding(4);
		this.textBox2.Name = "textBox2";
		this.textBox2.Size = new System.Drawing.Size(180, 25);
		this.textBox2.TabIndex = 27;
		this.textBox1.Enabled = false;
		this.textBox1.Location = new System.Drawing.Point(144, 32);
		this.textBox1.Margin = new System.Windows.Forms.Padding(4);
		this.textBox1.Name = "textBox1";
		this.textBox1.Size = new System.Drawing.Size(180, 25);
		this.textBox1.TabIndex = 26;
		this.label25.AutoSize = true;
		this.label25.Location = new System.Drawing.Point(376, 370);
		this.label25.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label25.Name = "label25";
		this.label25.Size = new System.Drawing.Size(71, 15);
		this.label25.TabIndex = 24;
		this.label25.Text = "FLD_DES:";
		this.label19.AutoSize = true;
		this.label19.Location = new System.Drawing.Point(365, 201);
		this.label19.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label19.Name = "label19";
		this.label19.Size = new System.Drawing.Size(79, 15);
		this.label19.TabIndex = 18;
		this.label19.Text = "FLD_TYPE:";
		this.label18.AutoSize = true;
		this.label18.Location = new System.Drawing.Point(16, 70);
		this.label18.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label18.Name = "label18";
		this.label18.Size = new System.Drawing.Size(119, 15);
		this.label18.TabIndex = 17;
		this.label18.Text = "FLD_QUESTITEM:";
		this.label17.AutoSize = true;
		this.label17.Location = new System.Drawing.Point(365, 66);
		this.label17.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label17.Name = "label17";
		this.label17.Size = new System.Drawing.Size(79, 15);
		this.label17.TabIndex = 16;
		this.label17.Text = "FLD_WXJD:";
		this.label16.AutoSize = true;
		this.label16.Location = new System.Drawing.Point(365, 32);
		this.label16.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label16.Name = "label16";
		this.label16.Size = new System.Drawing.Size(63, 15);
		this.label16.TabIndex = 15;
		this.label16.Text = "FLD_WX:";
		this.label15.AutoSize = true;
		this.label15.Location = new System.Drawing.Point(365, 100);
		this.label15.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label15.Name = "label15";
		this.label15.Size = new System.Drawing.Size(63, 15);
		this.label15.TabIndex = 14;
		this.label15.Text = "FLD_EL:";
		this.label14.AutoSize = true;
		this.label14.Location = new System.Drawing.Point(365, 134);
		this.label14.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label14.Name = "label14";
		this.label14.Size = new System.Drawing.Size(87, 15);
		this.label14.TabIndex = 13;
		this.label14.Text = "FLD_MONEY:";
		this.label13.AutoSize = true;
		this.label13.Location = new System.Drawing.Point(16, 138);
		this.label13.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label13.Name = "label13";
		this.label13.Size = new System.Drawing.Size(63, 15);
		this.label13.TabIndex = 12;
		this.label13.Text = "FLD_NJ:";
		this.label12.AutoSize = true;
		this.label12.Location = new System.Drawing.Point(16, 272);
		this.label12.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label12.Name = "label12";
		this.label12.Size = new System.Drawing.Size(63, 15);
		this.label12.TabIndex = 11;
		this.label12.Text = "FLD_DF:";
		this.label11.AutoSize = true;
		this.label11.Location = new System.Drawing.Point(16, 340);
		this.label11.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label11.Name = "label11";
		this.label11.Size = new System.Drawing.Size(71, 15);
		this.label11.TabIndex = 10;
		this.label11.Text = "FLD_AT2:";
		this.label10.AutoSize = true;
		this.label10.Location = new System.Drawing.Point(16, 306);
		this.label10.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label10.Name = "label10";
		this.label10.Size = new System.Drawing.Size(71, 15);
		this.label10.TabIndex = 9;
		this.label10.Text = "FLD_AT1:";
		this.label9.AutoSize = true;
		this.label9.Location = new System.Drawing.Point(365, 168);
		this.label9.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label9.Name = "label9";
		this.label9.Size = new System.Drawing.Size(95, 15);
		this.label9.TabIndex = 8;
		this.label9.Text = "FLD_WEIGHT:";
		this.label8.AutoSize = true;
		this.label8.Location = new System.Drawing.Point(16, 205);
		this.label8.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label8.Name = "label8";
		this.label8.Size = new System.Drawing.Size(103, 15);
		this.label8.TabIndex = 7;
		this.label8.Text = "FLD_RESIDE2:";
		this.label7.AutoSize = true;
		this.label7.Location = new System.Drawing.Point(16, 239);
		this.label7.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label7.Name = "label7";
		this.label7.Size = new System.Drawing.Size(71, 15);
		this.label7.TabIndex = 6;
		this.label7.Text = "FLD_SEX:";
		this.label6.AutoSize = true;
		this.label6.Location = new System.Drawing.Point(16, 408);
		this.label6.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label6.Name = "label6";
		this.label6.Size = new System.Drawing.Size(119, 15);
		this.label6.TabIndex = 5;
		this.label6.Text = "FLD_JOB_LEVEL:";
		this.label5.AutoSize = true;
		this.label5.Location = new System.Drawing.Point(16, 374);
		this.label5.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label5.Name = "label5";
		this.label5.Size = new System.Drawing.Size(87, 15);
		this.label5.TabIndex = 4;
		this.label5.Text = "FLD_LEVEL:";
		this.label4.AutoSize = true;
		this.label4.Location = new System.Drawing.Point(16, 171);
		this.label4.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label4.Name = "label4";
		this.label4.Size = new System.Drawing.Size(103, 15);
		this.label4.TabIndex = 3;
		this.label4.Text = "FLD_RESIDE1:";
		this.label3.AutoSize = true;
		this.label3.Location = new System.Drawing.Point(16, 441);
		this.label3.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label3.Name = "label3";
		this.label3.Size = new System.Drawing.Size(63, 15);
		this.label3.TabIndex = 2;
		this.label3.Text = "FLD_ZX:";
		this.label2.AutoSize = true;
		this.label2.Location = new System.Drawing.Point(16, 104);
		this.label2.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label2.Name = "label2";
		this.label2.Size = new System.Drawing.Size(79, 15);
		this.label2.TabIndex = 1;
		this.label2.Text = "FLD_NAME:";
		this.label1.AutoSize = true;
		this.label1.Location = new System.Drawing.Point(16, 36);
		this.label1.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(71, 15);
		this.label1.TabIndex = 0;
		this.label1.Text = "FLD_PID:";
		this.label27.AutoSize = true;
		this.label27.Location = new System.Drawing.Point(36, 628);
		this.label27.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label27.Name = "label27";
		this.label27.Size = new System.Drawing.Size(75, 15);
		this.label27.TabIndex = 4;
		this.label27.Text = "物品总数:";
		this.label28.AutoSize = true;
		this.label28.Location = new System.Drawing.Point(123, 628);
		this.label28.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label28.Name = "label28";
		this.label28.Size = new System.Drawing.Size(63, 15);
		this.label28.TabIndex = 5;
		this.label28.Text = "label28";
		this.textBox15.Location = new System.Drawing.Point(356, 21);
		this.textBox15.Margin = new System.Windows.Forms.Padding(4);
		this.textBox15.Name = "textBox15";
		this.textBox15.Size = new System.Drawing.Size(175, 25);
		this.textBox15.TabIndex = 6;
		this.radioButton1.AutoSize = true;
		this.radioButton1.Location = new System.Drawing.Point(560, 22);
		this.radioButton1.Margin = new System.Windows.Forms.Padding(4);
		this.radioButton1.Name = "radioButton1";
		this.radioButton1.Size = new System.Drawing.Size(52, 19);
		this.radioButton1.TabIndex = 7;
		this.radioButton1.TabStop = true;
		this.radioButton1.Text = "PID";
		this.radioButton1.UseVisualStyleBackColor = true;
		this.radioButton2.AutoSize = true;
		this.radioButton2.Location = new System.Drawing.Point(623, 22);
		this.radioButton2.Margin = new System.Windows.Forms.Padding(4);
		this.radioButton2.Name = "radioButton2";
		this.radioButton2.Size = new System.Drawing.Size(73, 19);
		this.radioButton2.TabIndex = 8;
		this.radioButton2.TabStop = true;
		this.radioButton2.Text = "物品名";
		this.radioButton2.UseVisualStyleBackColor = true;
		this.button2.Location = new System.Drawing.Point(716, 19);
		this.button2.Margin = new System.Windows.Forms.Padding(4);
		this.button2.Name = "button2";
		this.button2.Size = new System.Drawing.Size(89, 29);
		this.button2.TabIndex = 9;
		this.button2.Text = "确定";
		this.button2.UseVisualStyleBackColor = true;
		this.button2.Click += new System.EventHandler(button2_Click);
		this.groupBox3.Controls.Add(this.comboBox1);
		this.groupBox3.Controls.Add(this.label21);
		this.groupBox3.Controls.Add(this.button2);
		this.groupBox3.Controls.Add(this.textBox15);
		this.groupBox3.Controls.Add(this.radioButton2);
		this.groupBox3.Controls.Add(this.radioButton1);
		this.groupBox3.Location = new System.Drawing.Point(16, 42);
		this.groupBox3.Margin = new System.Windows.Forms.Padding(4);
		this.groupBox3.Name = "groupBox3";
		this.groupBox3.Padding = new System.Windows.Forms.Padding(4);
		this.groupBox3.Size = new System.Drawing.Size(1028, 60);
		this.groupBox3.TabIndex = 10;
		this.groupBox3.TabStop = false;
		this.groupBox3.Text = "查找";
		this.comboBox1.FormattingEnabled = true;
		this.comboBox1.Items.AddRange(new object[17]
		{
			"全部", "衣服", "护手", "武器", "靴子", "内甲", "项链", "耳环", "戒指", "披风",
			"灵兽", "石头", "盒子", "符文", "任务物品", "百宝", "其他"
		});
		this.comboBox1.Location = new System.Drawing.Point(117, 19);
		this.comboBox1.Margin = new System.Windows.Forms.Padding(4);
		this.comboBox1.Name = "comboBox1";
		this.comboBox1.Size = new System.Drawing.Size(160, 23);
		this.comboBox1.TabIndex = 11;
		this.comboBox1.SelectedValueChanged += new System.EventHandler(comboBox1_SelectedValueChanged);
		this.label21.AutoSize = true;
		this.label21.Location = new System.Drawing.Point(20, 25);
		this.label21.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label21.Name = "label21";
		this.label21.Size = new System.Drawing.Size(75, 15);
		this.label21.TabIndex = 10;
		this.label21.Text = "物品类型:";
		this.label22.AutoSize = true;
		this.label22.Location = new System.Drawing.Point(365, 245);
		this.label22.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
		this.label22.Name = "label22";
		this.label22.Size = new System.Drawing.Size(60, 15);
		this.label22.TabIndex = 54;
		this.label22.Text = "持久力:";
		this.textBox16.Location = new System.Drawing.Point(475, 242);
		this.textBox16.Margin = new System.Windows.Forms.Padding(4);
		this.textBox16.Name = "textBox16";
		this.textBox16.Size = new System.Drawing.Size(180, 25);
		this.textBox16.TabIndex = 55;
		base.AutoScaleDimensions = new System.Drawing.SizeF(8f, 15f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(1065, 654);
		base.Controls.Add(this.groupBox3);
		base.Controls.Add(this.label28);
		base.Controls.Add(this.label27);
		base.Controls.Add(this.groupBox2);
		base.Controls.Add(this.groupBox1);
		base.Controls.Add(this.menuStrip1);
		base.MainMenuStrip = this.menuStrip1;
		base.Margin = new System.Windows.Forms.Padding(4);
		base.Name = "YbiForm";
		this.Text = "YBI编辑器";
		base.FormClosing += new System.Windows.Forms.FormClosingEventHandler(YbiForm_FormClosing);
		base.Load += new System.EventHandler(YbiForm_Load);
		this.menuStrip1.ResumeLayout(false);
		this.menuStrip1.PerformLayout();
		this.groupBox1.ResumeLayout(false);
		this.groupBox2.ResumeLayout(false);
		this.groupBox2.PerformLayout();
		this.groupBox3.ResumeLayout(false);
		this.groupBox3.PerformLayout();
		base.ResumeLayout(false);
		base.PerformLayout();
	}

	public YbiForm()
	{
		InitializeComponent();
		int num = 0;
		do
		{
			byte_1A24628[num] = (byte)(((uint)(num >> 4) & 1u) | ((uint)(num >> 2) & 0x18u) | ((uint)(num >> 1) & 0x40u) | (uint)(2 * ((num & 3) | (4 * ((num & 4) | (2 * (num & 0xF8)))))));
			num++;
		}
		while (num < 256);
	}

	private void 打开YBIToolStripMenuItem_Click(object sender, EventArgs e)
	{
		int num2 = 0;
		BinaryReader binaryReader = null;
		Stream stream = null;
		FileStream fileStream = null;
		OpenFileDialog openFileDialog = new OpenFileDialog();
		openFileDialog.Filter = "cfg   files   (*.cfg)|*.cfg|All   files   (*.*)|*.*";
		openFileDialog.FilterIndex = 1;
		openFileDialog.RestoreDirectory = true;
		num2 = 3;
		if (openFileDialog.ShowDialog() != DialogResult.OK)
		{
			return;
		}
		num2 = 0;
		num2 = 2;
		if ((stream = openFileDialog.OpenFile()) != null)
		{
			num2 = 5;
			binaryReader = new BinaryReader(stream);
			fileStream = new FileStream("YBi.cfg.bak", FileMode.Create, FileAccess.Write, FileShare.Read);
			num2 = 1;
			try
			{
				int value = binaryReader.ReadInt32();
				int value2 = binaryReader.ReadInt32();
				fileStream.Write(BitConverter.GetBytes(value), 0, 4);
				fileStream.Write(BitConverter.GetBytes(value2), 0, 4);
				byte[] array = new byte[4];
				num2 = 1;
				while (true)
				{
					num2 = 4;
					if (binaryReader.Read(array, 0, 4) <= 0)
					{
						break;
					}
					fileStream.Write(BitConverter.GetBytes(YbiDecrypt(BitConverter.ToUInt32(array, 0), 0)), 0, 4);
					num2 = 3;
				}
				num2 = 2;
				num2 = 0;
			}
			finally
			{
				num2 = 0;
				while (true)
				{
					switch (num2)
					{
					case 1:
						break;
					default:
						if (fileStream != null)
						{
							num2 = 2;
							continue;
						}
						break;
					case 2:
						((IDisposable)fileStream).Dispose();
						num2 = 1;
						continue;
					}
					break;
				}
			}
			stream.Close();
			num2 = 6;
		}
		读物品全();
		num2 = 4;
	}

	private void 读物品全()
	{
		FileStream fileStream = new FileStream("YBi.cfg.bak", FileMode.Open, FileAccess.Read, FileShare.Read);
		try
		{
			BinaryReader binaryReader = new BinaryReader(fileStream);
			binaryReader.ReadInt32();
			binaryReader.ReadInt32();
			读取数据(binaryReader, 852, 10000, ref 物品);
			读取数据(binaryReader, 64, 1, ref KEY);
			读取数据(binaryReader, 6736, 1024, ref 武功);
			读取数据(binaryReader, 1388, 1024, ref 气功);
			读取数据(binaryReader, 72, 128, ref 称号);
			读取数据(binaryReader, 64, 2048, ref 屏蔽1);
			读取数据(binaryReader, 128, 2048, ref 屏蔽2);
			读取数据(binaryReader, 7860, 2048, ref NPC);
			读取数据(binaryReader, 16, 6, ref 未知1);
			读取数据(binaryReader, 100, 1024, ref 地图名);
			读取数据(binaryReader, 96, 1024, ref 未知2);
		}
		finally
		{
			int num = 0;
			while (true)
			{
				switch (num)
				{
				case 1:
					break;
				default:
					if (fileStream != null)
					{
						num = 2;
						continue;
					}
					break;
				case 2:
					((IDisposable)fileStream).Dispose();
					num = 1;
					continue;
				}
				break;
			}
		}
		显示物品名();
	}

	private void 显示物品名()
	{
		YbiClass ybiClass = null;
		int num2 = 0;
		byte[] array = null;
		string text = null;
		int num3 = 0;
		string text2 = null;
		string text3 = null;
		int num4 = 0;
		int num5 = 0;
		int num6 = 0;
		ybidate.Clear();
		for (int i = 0; i < 10000; i++)
		{
			num2 = 0;
			num5 = 0;
			ybiClass = new YbiClass();
			array = new byte[852];
			Buffer.BlockCopy(物品, i * array.Length, array, 0, array.Length);
			if (BitConverter.ToInt32(array, 0) == 0)
			{
				break;
			}
			text3 = Encoding.Default.GetString(array, 8, 64).Replace("\0", string.Empty).Replace('\'', ' ');
			if (text3.Length == 0)
			{
				text3 = "未知物品(待命名)";
			}
			text2 = Encoding.Default.GetString(array, 156, 256).Replace("\0", string.Empty).Replace('\'', ' ');
			if (text2.Length == 0 || text2 == " ")
			{
				text2 = text3;
			}
			num6 = array[80];
			listBox1.Items.Add(text3);
			num4 = BitConverter.ToInt32(array, 0);
			num3 = BitConverter.ToInt16(array, 96);
			ybiClass.FLD_PID = num4;
			ybiClass.FLD_Name = text3;
			ybiClass.FLD_说明 = text2;
			ybiClass.FLD_ZX = array[92];
			ybiClass.FLD_RESIDE1 = array[74];
			ybiClass.FLD_LEVEL = BitConverter.ToInt16(array, 76);
			ybiClass.FLD_JOB_LEVEL = array[78];
			ybiClass.FLD_SEX = array[79];
			ybiClass.FLD_WEIGHT = BitConverter.ToInt16(array, 82);
			ybiClass.FLD_AT1 = BitConverter.ToInt16(array, 84);
			ybiClass.FLD_AT2 = BitConverter.ToInt16(array, 86);
			ybiClass.FLD_DF = BitConverter.ToInt16(array, 88);
			ybiClass.FLD_NJ = num3;
			ybiClass.FLD_MONEY = BitConverter.ToInt32(array, 100);
			ybiClass.FLD_EL = BitConverter.ToInt16(array, 113);
			ybiClass.FLD_MM1 = BitConverter.ToInt32(array, 108);
			ybiClass.FLD_MM2 = BitConverter.ToInt32(array, 100);
			switch (ybiClass.FLD_RESIDE1)
			{
			case 11:
				ybiClass.FLD_RESIDE1 = 6;
				break;
			case 12:
				ybiClass.FLD_RESIDE1 = 7;
				break;
			case 13:
				ybiClass.FLD_RESIDE1 = 8;
				break;
			case 14:
				ybiClass.FLD_RESIDE1 = 9;
				break;
			case 15:
				ybiClass.FLD_RESIDE1 = 10;
				break;
			case 16:
				ybiClass.FLD_RESIDE1 = 10;
				break;
			case 17:
				ybiClass.FLD_RESIDE1 = 11;
				break;
			case 18:
				ybiClass.FLD_RESIDE1 = 12;
				break;
			case 19:
				ybiClass.FLD_RESIDE1 = 13;
				break;
			}
			switch (num6)
			{
			case 1:
				ybiClass.FLD_RESIDE2 = 1;
				break;
			case 2:
				ybiClass.FLD_RESIDE2 = 2;
				break;
			case 3:
				ybiClass.FLD_RESIDE2 = 4;
				break;
			case 4:
				ybiClass.FLD_RESIDE2 = 5;
				break;
			case 5:
				ybiClass.FLD_RESIDE2 = 6;
				break;
			case 6:
				ybiClass.FLD_RESIDE2 = 7;
				break;
			case 7:
				ybiClass.FLD_RESIDE2 = 8;
				break;
			case 8:
				ybiClass.FLD_RESIDE2 = 10;
				break;
			case 9:
				ybiClass.FLD_RESIDE2 = 12;
				break;
			case 10:
				ybiClass.FLD_RESIDE2 = 16;
				break;
			case 11:
				ybiClass.FLD_RESIDE2 = 18;
				break;
			case 12:
				ybiClass.FLD_RESIDE2 = 16;
				break;
			case 19:
				ybiClass.FLD_RESIDE2 = 19;
				break;
			case 21:
				ybiClass.FLD_RESIDE2 = 14;
				break;
			case 22:
				ybiClass.FLD_RESIDE2 = 15;
				break;
			}
			ybiClass.FLD_WX = BitConverter.ToInt32(array, 412);
			ybiClass.FLD_WXJD = BitConverter.ToInt32(array, 416);
			if (num4 > 900000001 && num4 < 1000000000)
			{
				num2 = 1;
			}
			ybiClass.FLD_QUESTITEM = num2;
			if (num4.ToString().Contains("1008000") || num4.ToString().Contains("16900") || num4.ToString().Contains("26900") || num4.ToString().Contains("1007000"))
			{
				num5 = 1;
			}
			ybiClass.FLD_TYPE = num5;
			ybiClass.FLD_QGSLL = BitConverter.ToInt32(array, 412);
			if (num3 != 0)
			{
				text = "2000000";
				switch (num3.ToString().Length)
				{
				case 1:
					text = "200000000";
					break;
				case 2:
					text = "20000000";
					break;
				case 4:
					text = "200000";
					break;
				}
				int.Parse(text + num3);
			}
			if (ybiClass.FLD_Name.Contains("升天一式 ") || ybiClass.FLD_Name.Contains("升天二式 ") || ybiClass.FLD_Name.Contains("升天三式 ") || ybiClass.FLD_Name.Contains("升天四式 "))
			{
				ybiClass.FLD_RESIDE2 = 19;
			}
			if (ybiClass.FLD_Name.Contains("气功"))
			{
				ybiClass.FLD_RESIDE2 = 19;
				ybiClass.FLD_MAGIC1 = 0;
				ybiClass.FLD_WXJD = 0;
			}
			if (ybiClass.FLD_Name.Contains("灵宠 "))
			{
				ybiClass.FLD_RESIDE2 = 16;
			}
			if (ybiClass.FLD_Name.Contains("灵宠月兔") || ybiClass.FLD_Name.Contains(" 灵宠龙猫"))
			{
				ybiClass.FLD_RESIDE2 = 16;
			}
			if (ybiClass.FLD_Name.Contains("灵宠小白熊") || ybiClass.FLD_Name.Contains("灵宠龙猫") || ybiClass.FLD_Name.Contains("灵宠小白熊"))
			{
				ybiClass.FLD_RESIDE2 = 16;
			}
			if (ybiClass.FLD_Name.Contains("箱"))
			{
				ybiClass.FLD_RESIDE2 = 17;
			}
			if (ybiClass.FLD_Name.Contains("真-"))
			{
				ybiClass.FLD_TZ = 1;
			}
			if (ybiClass.FLD_Name.Contains("天元") || ybiClass.FLD_Name.Contains("轩舞") || ybiClass.FLD_Name.Contains("浩天"))
			{
				ybiClass.FLD_TZ = 1;
			}
			if (ybiClass.FLD_Name.Contains("邪元") || ybiClass.FLD_Name.Contains("赤夜") || ybiClass.FLD_Name.Contains("魔渊"))
			{
				ybiClass.FLD_TZ = 1;
			}
			if (ybiClass.FLD_Name.Contains("真武圣"))
			{
				ybiClass.FLD_TZ = 1;
			}
			if (ybiClass.FLD_Name.Contains("月光圣"))
			{
				ybiClass.FLD_TZ = 1;
			}
			if (ybiClass.FLD_Name.Contains("真武血"))
			{
				ybiClass.FLD_TZ = 1;
			}
			if (ybiClass.FLD_Name.Contains("月光血"))
			{
				ybiClass.FLD_TZ = 1;
			}
			if (ybiClass.FLD_Name.Contains("未完成"))
			{
				ybiClass.FLD_TZ = 1;
			}
			if (ybiClass.FLD_Name.Contains("神龙"))
			{
				ybiClass.FLD_TZ = 1;
				ybiClass.FLD_NJ = 1000;
			}
			if (ybiClass.FLD_Name.Contains("飞流"))
			{
				ybiClass.FLD_TZ = 1;
				ybiClass.FLD_NJ = 1000;
			}
			if (ybiClass.FLD_Name.Contains("强化石") || ybiClass.FLD_Name.Contains("高级强化石"))
			{
				ybiClass.FLD_SIDE = 1;
			}
			if (ybiClass.FLD_Name.Contains("纹龙") || ybiClass.FLD_Name.Contains("绣龙") || ybiClass.FLD_Name.Contains("金龙"))
			{
				ybiClass.FLD_NJ = 1000;
			}
			if (ybiClass.FLD_Name == "玄武" || ybiClass.FLD_Name == "北天玄武刀" || ybiClass.FLD_Name.Contains("玄武龙啸"))
			{
				ybiClass.FLD_NJ = 1000;
			}
			if (ybiClass.FLD_Name == "青龙" || ybiClass.FLD_Name == "青天青龙剑" || ybiClass.FLD_Name.Contains("青龙缠天"))
			{
				ybiClass.FLD_NJ = 1000;
			}
			if (ybiClass.FLD_Name == "朱雀" || ybiClass.FLD_Name == "南天朱雀弓" || ybiClass.FLD_Name.Contains("朱雀覆火"))
			{
				ybiClass.FLD_NJ = 1000;
			}
			if (ybiClass.FLD_Name == "麒麟" || ybiClass.FLD_Name == "中天麒麟枪" || ybiClass.FLD_Name.Contains("麒麟踏宇"))
			{
				ybiClass.FLD_NJ = 1000;
			}
			if (ybiClass.FLD_Name == "白虎" || ybiClass.FLD_Name == "西天白虎扇" || ybiClass.FLD_Name.Contains("虎扇吞日"))
			{
				ybiClass.FLD_NJ = 1000;
			}
			if (ybiClass.FLD_Name == "鬼牙" || ybiClass.FLD_Name == "玄天鬼牙刃" || ybiClass.FLD_Name.Contains("天兆鬼牙"))
			{
				ybiClass.FLD_NJ = 1000;
			}
			if (ybiClass.FLD_Name == "鳳凰" || ybiClass.FLD_Name == "齐天凤凰琴" || ybiClass.FLD_Name.Contains("凤凰栾鸣"))
			{
				ybiClass.FLD_NJ = 1000;
			}
			ybiClass.FLD_MAGIC1 = 0;
			ybiClass.FLD_SIDE = ((num2 == 1) ? 1 : 0);
			ybiClass.FLD_MAGIC2 = 0;
			ybiClass.FLD_MAGIC3 = 0;
			ybiClass.FLD_MAGIC4 = 0;
			ybiClass.FLD_MAGIC5 = 0;
			ybiClass.FLD_byte = array;
			if (!ybidate.ContainsKey(num4))
			{
				ybidate.Add(num4, ybiClass);
			}
		}
		label28.Text = listBox1.Items.Count.ToString();
	}

	private void 保存YBIToolStripMenuItem_Click(object sender, EventArgs e)
	{
		try
		{
			int num2 = 0;
			num2 = 2;
			FileStream fileStream = null;
			byte[] array = null;
			if (listBox1.Items.Count == 0)
			{
				num2 = 5;
				MessageBox.Show("请先打开Ybi.cfg文件!", "Msg");
				num2 = 1;
			}
			else
			{
				fileStream = new FileStream("YBi.cfg.bak", FileMode.Open, FileAccess.Read, FileShare.Read);
				num2 = 3;
				try
				{
					FileStream fileStream2 = new FileStream("YBi.cfg1.bak", FileMode.Create, FileAccess.Write, FileShare.Read);
					try
					{
						BinaryReader binaryReader = new BinaryReader(fileStream);
						try
						{
							fileStream2.Write(BitConverter.GetBytes(binaryReader.ReadInt32()), 0, 4);
							fileStream2.Write(BitConverter.GetBytes(binaryReader.ReadInt32()), 0, 4);
							int num3 = 8;
							fileStream2.Seek(8L, SeekOrigin.Begin);
							Dictionary<int, YbiClass>.ValueCollection.Enumerator enumerator = ybidate.Values.GetEnumerator();
							num2 = 2;
							try
							{
								num2 = 4;
								while (true)
								{
									num2 = 0;
									if (!enumerator.MoveNext())
									{
										break;
									}
									YbiClass current = enumerator.Current;
									byte[] fLD_byte = current.FLD_byte;
									byte[] bytes = Encoding.Default.GetBytes(current.FLD_Name + "\0");
									byte[] bytes2 = Encoding.Default.GetBytes(current.FLD_说明 + "\0");
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_PID), 0, fLD_byte, 0, 4);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_JOB_LEVEL), 0, fLD_byte, 78, 1);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_ZX), 0, fLD_byte, 73, 1);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_RESIDE1), 0, fLD_byte, 74, 1);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_LEVEL), 0, fLD_byte, 76, 2);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_SEX), 0, fLD_byte, 79, 1);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_RESIDE2), 0, fLD_byte, 80, 2);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_WEIGHT), 0, fLD_byte, 82, 2);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_AT1), 0, fLD_byte, 84, 2);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_AT2), 0, fLD_byte, 86, 2);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_DF), 0, fLD_byte, 88, 2);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_NJ), 0, fLD_byte, 96, 2);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_MONEY), 0, fLD_byte, 100, 4);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_EL), 0, fLD_byte, 113, 2);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_WX), 0, fLD_byte, 372, 4);
									Buffer.BlockCopy(BitConverter.GetBytes(current.FLD_WXJD), 0, fLD_byte, 376, 4);
									Buffer.BlockCopy(bytes, 0, fLD_byte, 8, bytes.Length);
									Buffer.BlockCopy(bytes2, 0, fLD_byte, 156, bytes2.Length);
									fileStream2.Write(fLD_byte, 0, fLD_byte.Length);
									num3 += 852;
									num2 = 3;
								}
								num2 = 2;
								num2 = 1;
							}
							finally
							{
								((IDisposable)enumerator).Dispose();
							}
							fileStream.Seek(num3, SeekOrigin.Begin);
							array = new byte[4];
							num2 = 3;
							while (true)
							{
								num2 = 5;
								if (binaryReader.Read(array, 0, 4) <= 0)
								{
									break;
								}
								fileStream2.Write(array, 0, array.Length);
								num2 = 0;
							}
							num2 = 1;
							num2 = 4;
						}
						finally
						{
							num2 = 2;
							while (true)
							{
								switch (num2)
								{
								case 1:
									break;
								case 0:
									((IDisposable)binaryReader).Dispose();
									num2 = 1;
									continue;
								default:
									if (binaryReader != null)
									{
										num2 = 0;
										continue;
									}
									break;
								}
								break;
							}
						}
					}
					finally
					{
						num2 = 2;
						while (true)
						{
							switch (num2)
							{
							case 1:
								break;
							case 0:
								((IDisposable)fileStream2).Dispose();
								num2 = 1;
								continue;
							default:
								if (fileStream2 != null)
								{
									num2 = 0;
									continue;
								}
								break;
							}
							break;
						}
					}
				}
				finally
				{
					num2 = 2;
					while (true)
					{
						switch (num2)
						{
						case 1:
							break;
						case 0:
							((IDisposable)fileStream).Dispose();
							num2 = 1;
							continue;
						default:
							if (fileStream != null)
							{
								num2 = 0;
								continue;
							}
							break;
						}
						break;
					}
				}
				生成新的文件ybi();
				num2 = 0;
			}
			num2 = 4;
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.ToString(), "错误");
		}
	}

	private void 生成新的文件ybi()
	{
		try
		{
			int num2 = 0;
			FileStream fileStream = new FileStream("YBi.cfg1.bak", FileMode.Open, FileAccess.Read, FileShare.Read);
			try
			{
				FileStream fileStream2 = new FileStream("YBiNew.cfg", FileMode.Create, FileAccess.Write, FileShare.Read);
				try
				{
					BinaryReader binaryReader = new BinaryReader(fileStream);
					fileStream2.Write(BitConverter.GetBytes(binaryReader.ReadInt32()), 0, 4);
					fileStream2.Write(BitConverter.GetBytes(binaryReader.ReadInt32()), 0, 4);
					byte[] array = new byte[4];
					num2 = 0;
					while (true)
					{
						num2 = 4;
						if (binaryReader.Read(array, 0, 4) <= 0)
						{
							break;
						}
						fileStream2.Write(BitConverter.GetBytes(YbiDecrypt(BitConverter.ToUInt32(array, 0), 1)), 0, 4);
						num2 = 2;
					}
					num2 = 3;
					num2 = 1;
				}
				finally
				{
					num2 = 0;
					while (true)
					{
						switch (num2)
						{
						case 1:
							break;
						default:
							if (fileStream2 != null)
							{
								num2 = 2;
								continue;
							}
							break;
						case 2:
							((IDisposable)fileStream2).Dispose();
							num2 = 1;
							continue;
						}
						break;
					}
				}
			}
			finally
			{
				num2 = 0;
				while (true)
				{
					switch (num2)
					{
					case 1:
						break;
					default:
						if (fileStream != null)
						{
							num2 = 2;
							continue;
						}
						break;
					case 2:
						((IDisposable)fileStream).Dispose();
						num2 = 1;
						continue;
					}
					break;
				}
			}
			MessageBox.Show("完成,新的文件为'YBiNew.cfg'", "Msg");
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.ToString(), "错误");
		}
	}

	private YbiClass getitem(string name)
	{
		using (Dictionary<int, YbiClass>.ValueCollection.Enumerator enumerator = ybidate.Values.GetEnumerator())
		{
			YbiClass ybiClass = null;
			while (enumerator.MoveNext())
			{
				ybiClass = enumerator.Current;
				if (ybiClass.FLD_Name == name)
				{
					return ybiClass;
				}
			}
		}
		return null;
	}

	private YbiClass getitem(int int_0)
	{
		using (Dictionary<int, YbiClass>.ValueCollection.Enumerator enumerator = ybidate.Values.GetEnumerator())
		{
			YbiClass ybiClass = null;
			while (enumerator.MoveNext())
			{
				ybiClass = enumerator.Current;
				if (ybiClass.FLD_PID == int_0)
				{
					return ybiClass;
				}
			}
		}
		return null;
	}

	public static int YbiDecrypt(uint pValue, int inlen)
	{
		int num2 = 0;
		int num3 = 0;
		int num4 = 0;
		while (true)
		{
			bool flag = true;
			while (true)
			{
				bool flag2 = true;
				while (true)
				{
					IL_001a:
					if (pValue != 0)
					{
						int num5 = (int)pValue & -2;
						num2 = (int)(pValue - num5);
						pValue >>= 1;
						while (num2 > 0)
						{
							bool flag3 = true;
							while (true)
							{
								byte b;
								int num6;
								switch ((inlen != 0) ? 5 : 0)
								{
								case 10:
									break;
								default:
									num3 = 0;
									num4 = 0;
									goto end_IL_001a;
								case 5:
									b = byte_0[num4 + 32];
									goto IL_00be;
								case 0:
								case 9:
									b = byte_0[num4];
									goto IL_00be;
								case 1:
								case 2:
									continue;
								case 6:
									goto IL_00eb;
								case 4:
									goto IL_00fc;
								case 7:
								case 8:
									goto end_IL_001a;
								case 3:
									goto end_IL_0102;
									IL_00be:
									num6 = b;
									num3 += num2 << num6;
									goto end_IL_0102;
								}
								break;
							}
							goto IL_001a;
							IL_00eb:;
						}
						goto end_IL_0102;
					}
					goto IL_00fc;
					IL_00fc:
					return num3;
					continue;
					end_IL_001a:
					break;
				}
				continue;
				end_IL_0102:
				break;
			}
			num4++;
		}
	}

	public void 读取数据(BinaryReader tdbReader, int Size, int Count, ref byte[] 数据)
	{
		for (int i = 0; i < Count; i++)
		{
			byte[] array = new byte[Size];
			tdbReader.Read(array, 0, array.Length);
			Buffer.BlockCopy(array, 0, 数据, i * array.Length, array.Length);
		}
	}

	private void YbiForm_Load(object sender, EventArgs e)
	{
		label28.Text = "0";
		radioButton1.Checked = true;
		comboBox1.SelectedText = "全部";
	}

	private void listBox1_SelectedValueChanged(object sender, EventArgs e)
	{
		YbiClass ybiClass = getitem(listBox1.SelectedItem.ToString());
		if (ybiClass != null)
		{
			textBox1.Text = ybiClass.FLD_PID.ToString();
			textBox2.Text = ybiClass.FLD_QUESTITEM.ToString();
			textBox3.Text = ybiClass.FLD_Name;
			textBox4.Text = ybiClass.FLD_NJ.ToString();
			textBox5.Text = ybiClass.FLD_RESIDE1.ToString();
			textBox6.Text = ybiClass.FLD_RESIDE2.ToString();
			textBox7.Text = ybiClass.FLD_SEX.ToString();
			textBox8.Text = ybiClass.FLD_DF.ToString();
			textBox9.Text = ybiClass.FLD_AT1.ToString();
			textBox10.Text = ybiClass.FLD_AT2.ToString();
			textBox11.Text = ybiClass.FLD_LEVEL.ToString();
			textBox12.Text = ybiClass.FLD_JOB_LEVEL.ToString();
			textBox13.Text = ybiClass.FLD_ZX.ToString();
			textBox14.Text = ybiClass.FLD_说明;
			textBox21.Text = ybiClass.FLD_TYPE.ToString();
			textBox22.Text = ybiClass.FLD_WEIGHT.ToString();
			textBox23.Text = ybiClass.FLD_MONEY.ToString();
			textBox24.Text = ybiClass.FLD_EL.ToString();
			textBox25.Text = ybiClass.FLD_WXJD.ToString();
			textBox26.Text = ybiClass.FLD_WX.ToString();
		}
	}

	private void button1_Click(object sender, EventArgs e)
	{
		YbiClass ybiClass = null;
		if (listBox1.Items.Count == 0)
		{
			MessageBox.Show("请先打开Ybi.cfg文件!", "Msg");
			return;
		}
		if (textBox1.Text.Length == 0)
		{
			MessageBox.Show("请先选择要修改的物品!", "Msg");
			return;
		}
		if (textBox14.Text.Length > 120)
		{
			MessageBox.Show("装备说明不能超过120个文字!");
			return;
		}
		if (textBox3.Text.Length > 15)
		{
			MessageBox.Show("装备名称不能超过15个文字!");
			return;
		}
		ybiClass = getitem(int.Parse(textBox1.Text));
		if (ybiClass != null)
		{
			ybiClass.FLD_QUESTITEM = int.Parse(textBox2.Text);
			ybiClass.FLD_Name = textBox3.Text;
			ybiClass.FLD_NJ = int.Parse(textBox4.Text);
			ybiClass.FLD_RESIDE1 = int.Parse(textBox5.Text);
			ybiClass.FLD_RESIDE2 = int.Parse(textBox6.Text);
			ybiClass.FLD_SEX = int.Parse(textBox7.Text);
			ybiClass.FLD_DF = int.Parse(textBox8.Text);
			ybiClass.FLD_AT1 = int.Parse(textBox9.Text);
			ybiClass.FLD_AT2 = int.Parse(textBox10.Text);
			ybiClass.FLD_LEVEL = int.Parse(textBox11.Text);
			ybiClass.FLD_JOB_LEVEL = int.Parse(textBox12.Text);
			ybiClass.FLD_ZX = int.Parse(textBox13.Text);
			ybiClass.FLD_说明 = textBox14.Text;
			ybiClass.FLD_TYPE = int.Parse(textBox21.Text);
			ybiClass.FLD_WEIGHT = int.Parse(textBox22.Text);
			ybiClass.FLD_MONEY = int.Parse(textBox23.Text);
			ybiClass.FLD_EL = int.Parse(textBox24.Text);
			ybiClass.FLD_WXJD = int.Parse(textBox25.Text);
			ybiClass.FLD_WX = int.Parse(textBox26.Text);
			MessageBox.Show("修改成功!");
		}
		else
		{
			MessageBox.Show("查找物品出错!");
		}
	}

	private void 物品入库ToolStripMenuItem_Click(object sender, EventArgs e)
	{
		try
		{
			DataTable dataTable = null;
			int num2 = 0;
			int num3 = 0;
			int num4 = 0;
			int num5 = 0;
			int num6 = 0;
			int num7 = 0;
			if (listBox1.Items.Count == 0)
			{
				MessageBox.Show("请先打开Ybi.cfg文件!", "Msg");
				return;
			}
			num7 = 0;
			num6 = 0;
			foreach (YbiClass value in ybidate.Values)
			{
				if (value.FLD_PID > 900000001 && value.FLD_PID < 1000000000)
				{
					if (value.FLD_LEVEL > 1)
					{
						value.FLD_QUESTITEM = 0;
						value.FLD_SIDE = 0;
					}
					else
					{
						value.FLD_QUESTITEM = 1;
						value.FLD_SIDE = 1;
					}
				}
				else
				{
					value.FLD_SIDE = 0;
					value.FLD_QUESTITEM = 0;
				}
				if (value.FLD_PID > 1000000166 && value.FLD_PID < 1000000178)
				{
					value.FLD_SIDE = 1;
				}
				if (value.FLD_Name.Contains("肉"))
				{
					value.FLD_SIDE = 1;
				}
				if (value.FLD_Name.Contains("灵兽宝珠"))
				{
					value.FLD_RESIDE2 = 17;
					value.FLD_TYPE = 0;
				}
				if (value.FLD_Name.Contains("强化"))
				{
					value.FLD_SIDE = 1;
					value.FLD_MAGIC1 = 0;
					value.FLD_WXJD = 0;
				}
				if ((value.FLD_Name.Contains("箭") || value.FLD_Name.Contains("回城") || value.FLD_说明.Contains("材料")) && !value.FLD_Name.Contains("箭破残阳") && !value.FLD_Name.Contains("石"))
				{
					value.FLD_SIDE = 1;
				}
				if (value.FLD_Name.Contains("箭"))
				{
					value.FLD_RESIDE2 = 13;
				}
				if (value.FLD_Name.Contains("鞍"))
				{
					value.FLD_RESIDE2 = 5;
				}
				if (value.FLD_Name.Contains("璀璨耀石"))
				{
					value.FLD_SIDE = 1;
				}
				if (value.FLD_Name.Contains("石") || value.FLD_Name.Contains("魂结晶"))
				{
					value.FLD_SIDE = 1;
				}
				if (value.FLD_Name.Contains("晶石碎片"))
				{
					value.FLD_SIDE = 1;
				}
				if (value.FLD_PID > 1000000713 && value.FLD_PID < 1000000996)
				{
					value.FLD_SIDE = 1;
				}
				if (value.FLD_PID > 1000000122 && value.FLD_PID < 1000000188)
				{
					value.FLD_SIDE = 1;
				}
				if (value.FLD_PID > 1000000312 && value.FLD_PID < 1000000321)
				{
					value.FLD_RESIDE2 = 1792;
				}
				if (value.FLD_Name.Contains("药") || value.FLD_Name.Contains("玫瑰") || value.FLD_Name.Contains("参") || value.FLD_Name.Contains("丹"))
				{
					value.FLD_SIDE = 1;
				}
				if (value.FLD_Name.Contains("盒") || value.FLD_Name.Contains("箱"))
				{
					value.FLD_RESIDE2 = 17;
				}
				if (value.FLD_Name.Contains("武功秘传"))
				{
					value.FLD_RESIDE2 = 1792;
					value.FLD_MAGIC1 = 0;
					value.FLD_WXJD = 0;
					value.FLD_WX = 0;
				}
				if (value.FLD_Name.Contains("武功秘籍"))
				{
					value.FLD_RESIDE2 = 1792;
					value.FLD_MAGIC1 = 0;
					value.FLD_WXJD = 0;
					value.FLD_WX = 0;
				}
				if (value.FLD_Name.Contains("武功秘传"))
				{
					value.FLD_RESIDE2 = 1792;
					value.FLD_MAGIC1 = 0;
					value.FLD_WXJD = 0;
					value.FLD_WX = 0;
				}
				if (value.FLD_Name.Contains("失传的秘籍（"))
				{
					value.FLD_RESIDE2 = 1792;
					value.FLD_MAGIC1 = 0;
					value.FLD_WXJD = 0;
					value.FLD_WX = 0;
				}
				if (value.FLD_Name.Contains("武功秘笈"))
				{
					value.FLD_RESIDE2 = 1792;
					value.FLD_MAGIC1 = 0;
					value.FLD_WXJD = 0;
					value.FLD_WX = 0;
				}
				if (value.FLD_LEVEL >= 130 && value.FLD_RESIDE2 == 4)
				{
					value.FLD_TZ = 1;
				}
				if (value.FLD_LEVEL >= 130 && value.FLD_RESIDE2 == 1)
				{
					value.FLD_TZ = 1;
				}
				if (value.FLD_LEVEL >= 130 && value.FLD_RESIDE2 == 5)
				{
					value.FLD_TZ = 1;
				}
				if (value.FLD_LEVEL >= 130 && value.FLD_RESIDE2 == 2)
				{
					value.FLD_TZ = 1;
				}
				if (value.FLD_LEVEL >= 130 && value.FLD_RESIDE2 == 6)
				{
					value.FLD_TZ = 1;
				}
				value.FLD_TYPE = ((value.FLD_PID.ToString().Contains("1008000") || value.FLD_PID.ToString().Contains("16900") || value.FLD_PID.ToString().Contains("26900") || value.FLD_PID.ToString().Contains("1007000")) ? 6 : 0);
				value.FLD_MAGIC1 = num7;
				num2 = 0;
				num3 = 0;
				num4 = 0;
				num5 = 0;
				dataTable = DBA.GetDBToDataTable("SELECT  *  FROM  TBL_XWWL_ITEM  WHERE  FLD_PID  =  " + value.FLD_PID, "PublicDb");
				if (dataTable != null)
				{
					if (dataTable.Rows.Count == 0)
					{
						DBA.ExeSqlCommand($"INSERT INTO TBL_XWWL_ITEM (FLD_PID,  FLD_NAME,  FLD_RESIDE1,  FLD_RESIDE2,  FLD_SEX,  FLD_DF,  FLD_AT1,  FLD_AT2,  FLD_LEVEL,  FLD_JOB_LEVEL,  FLD_ZX,  FLD_MONEY,  FLD_WEIGHT, FLD_DES, FLD_WX, FLD_WXJD, FLD_EL, FLD_QUESTITEM, FLD_NJ, FLD_TYPE, FLD_MAGIC1, FLD_MAGIC2, FLD_MAGIC3 , FLD_MAGIC4, FLD_MAGIC5, FLD_SIDE, FLD_NEED_FIGHTEXP, FLD_RECYCLE_MONEY, FLD_SALE_MONEY, FLD_UP_LEVEL,FLD_SERIES,FLD_LOCK,FLD_INTEGRATION,FLD_NEED_MONEY)  VALUES  ({value.FLD_PID},  '{value.FLD_Name}',  {value.FLD_RESIDE1},  {value.FLD_RESIDE2},{value.FLD_SEX},  {value.FLD_DF},  {value.FLD_AT1},  {value.FLD_AT2},  {value.FLD_LEVEL},  {value.FLD_JOB_LEVEL},  {value.FLD_ZX},  {value.FLD_MONEY},  {value.FLD_WEIGHT},'{value.FLD_说明}',{value.FLD_WX},{value.FLD_WXJD},{value.FLD_EL},{value.FLD_QUESTITEM},{value.FLD_NJ},{value.FLD_TYPE},{value.FLD_MAGIC1},{0},{0},{0},{0},{value.FLD_SIDE},{value.FLD_QGSLL},{value.FLD_MM1},{value.FLD_MM2},{value.FLD_TZ},{num2},{num3},{num4},{num5})", "PublicDb");
						num6++;
					}
					dataTable.Clear();
					dataTable.Dispose();
				}
			}
			MessageBox.Show("完成,共增加新物品" + num6, "Msg");
		}
		catch (Exception ex)
		{
			MessageBox.Show(ex.ToString(), "错误");
		}
	}

	private void button2_Click(object sender, EventArgs e)
	{
		int num3 = 0;
		if (listBox1.Items.Count == 0)
		{
			MessageBox.Show("请先打开Ybi.cfg文件!", "Msg");
			return;
		}
		if (textBox15.Text.Length == 0)
		{
			MessageBox.Show("请先输入要查询的内容!", "Msg");
			return;
		}
		if (radioButton1.Checked)
		{
			try
			{
				bool flag = true;
				while (true)
				{
					YbiClass ybiClass = getitem(Convert.ToInt32(textBox15.Text));
					while (true)
					{
						IL_03cb:
						if (ybiClass != null)
						{
							bool flag2 = true;
							while (true)
							{
								IL_00ab:
								textBox1.Text = ybiClass.FLD_PID.ToString();
								textBox2.Text = ybiClass.FLD_QUESTITEM.ToString();
								textBox3.Text = ybiClass.FLD_Name;
								textBox4.Text = ybiClass.FLD_NJ.ToString();
								textBox5.Text = ybiClass.FLD_RESIDE1.ToString();
								textBox6.Text = ybiClass.FLD_RESIDE2.ToString();
								textBox7.Text = ybiClass.FLD_SEX.ToString();
								textBox8.Text = ybiClass.FLD_DF.ToString();
								textBox9.Text = ybiClass.FLD_AT1.ToString();
								textBox10.Text = ybiClass.FLD_AT2.ToString();
								textBox11.Text = ybiClass.FLD_LEVEL.ToString();
								textBox12.Text = ybiClass.FLD_JOB_LEVEL.ToString();
								textBox13.Text = ybiClass.FLD_ZX.ToString();
								textBox14.Text = ybiClass.FLD_说明;
								textBox21.Text = ybiClass.FLD_TYPE.ToString();
								textBox22.Text = ybiClass.FLD_WEIGHT.ToString();
								textBox23.Text = ybiClass.FLD_MONEY.ToString();
								textBox24.Text = ybiClass.FLD_EL.ToString();
								textBox25.Text = ybiClass.FLD_WXJD.ToString();
								textBox26.Text = ybiClass.FLD_WX.ToString();
								num3 = 0;
								bool flag3 = true;
								while (true)
								{
									switch ((num3 < listBox1.Items.Count) ? 1 : 7)
									{
									case 5:
										goto IL_00ab;
									case 6:
										return;
									case 9:
										return;
									case 1:
										if (listBox1.Items[num3].Equals(textBox3.Text))
										{
											listBox1.SetSelected(num3, value: true);
											return;
										}
										goto case 3;
									case 3:
										num3++;
										continue;
									case 7:
										return;
									case 4:
									case 10:
										return;
									case 2:
									case 8:
									case 11:
										continue;
									case 0:
										goto IL_03cb;
									}
									break;
								}
								break;
							}
							break;
						}
						MessageBox.Show("无此物品,请检查PID是否正确！", "Msg");
						return;
					}
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.ToString(), "错误");
				return;
			}
		}
		if (radioButton2.Checked)
		{
			try
			{
				YbiClass ybiClass2 = getitem(textBox15.Text);
				if (ybiClass2 != null)
				{
					textBox1.Text = ybiClass2.FLD_PID.ToString();
					textBox2.Text = ybiClass2.FLD_QUESTITEM.ToString();
					textBox3.Text = ybiClass2.FLD_Name;
					textBox4.Text = ybiClass2.FLD_NJ.ToString();
					textBox5.Text = ybiClass2.FLD_RESIDE1.ToString();
					textBox6.Text = ybiClass2.FLD_RESIDE2.ToString();
					textBox7.Text = ybiClass2.FLD_SEX.ToString();
					textBox8.Text = ybiClass2.FLD_DF.ToString();
					textBox9.Text = ybiClass2.FLD_AT1.ToString();
					textBox10.Text = ybiClass2.FLD_AT2.ToString();
					textBox11.Text = ybiClass2.FLD_LEVEL.ToString();
					textBox12.Text = ybiClass2.FLD_JOB_LEVEL.ToString();
					textBox13.Text = ybiClass2.FLD_ZX.ToString();
					textBox14.Text = ybiClass2.FLD_说明;
					textBox21.Text = ybiClass2.FLD_TYPE.ToString();
					textBox22.Text = ybiClass2.FLD_WEIGHT.ToString();
					textBox23.Text = ybiClass2.FLD_MONEY.ToString();
					textBox24.Text = ybiClass2.FLD_EL.ToString();
					textBox25.Text = ybiClass2.FLD_WXJD.ToString();
					textBox26.Text = ybiClass2.FLD_WX.ToString();
				}
				else
				{
					MessageBox.Show("无此物品,请检查物品名是否正确！", "Msg");
				}
				return;
			}
			catch (Exception ex2)
			{
				MessageBox.Show(ex2.ToString(), "错误");
				return;
			}
		}
		MessageBox.Show("请选择查询的类型", "Msg");
	}

	private YbiClass getitem_reside2(int reside2)
	{
		using (Dictionary<int, YbiClass>.ValueCollection.Enumerator enumerator = ybidate.Values.GetEnumerator())
		{
			YbiClass ybiClass = null;
			while (enumerator.MoveNext())
			{
				ybiClass = enumerator.Current;
				if (ybiClass.FLD_RESIDE2 == reside2)
				{
					return ybiClass;
				}
			}
		}
		return null;
	}

	private void comboBox1_SelectedValueChanged(object sender, EventArgs e)
	{
		int num2 = 0;
		uint num3 = 0u;
		string text = comboBox1.SelectedItem.ToString();
		if (text == null)
		{
			goto IL_038c;
		}
		num3 = Class3.smethod_0(text);
		if (num3 <= 1583700141)
		{
			if (num3 <= 806828493)
			{
				if (num3 <= 386101911)
				{
					if (num3 != 351508891)
					{
						if (num3 != 386101911 || !(text == "灵兽"))
						{
							goto IL_038c;
						}
						num2 = 15;
					}
					else
					{
						if (!(text == "任务物品"))
						{
							goto IL_038c;
						}
						num2 = 1000;
					}
				}
				else if (num3 != 762145023)
				{
					if (num3 != 806828493 || !(text == "其他"))
					{
						goto IL_038c;
					}
					num2 = 2000;
				}
				else
				{
					if (!(text == "盒子"))
					{
						goto IL_038c;
					}
					num2 = 17;
				}
			}
			else if (num3 <= 963180387)
			{
				if (num3 != 867671693)
				{
					if (num3 != 963180387 || !(text == "武器"))
					{
						goto IL_038c;
					}
					num2 = 4;
				}
				else
				{
					if (!(text == "衣服"))
					{
						goto IL_038c;
					}
					num2 = 1;
				}
			}
			else if (num3 != 1502406078)
			{
				if (num3 != 1583700141)
				{
					goto IL_038c;
				}
				num2 = 3000;
			}
			else
			{
				if (!(text == "项链"))
				{
					goto IL_038c;
				}
				num2 = 7;
			}
		}
		else if (num3 <= 3322318871u)
		{
			if (num3 <= 2913798006u)
			{
				if (num3 != 1895148392)
				{
					if (num3 != 2913798006u || !(text == "戒指"))
					{
						goto IL_038c;
					}
					num2 = 10;
				}
				else
				{
					if (!(text == "护手"))
					{
						goto IL_038c;
					}
					num2 = 2;
				}
			}
			else if (num3 != 3205149562u)
			{
				if (num3 != 3322318871u || !(text == "耳环"))
				{
					goto IL_038c;
				}
				num2 = 8;
			}
			else
			{
				if (!(text == "符文"))
				{
					goto IL_038c;
				}
				num2 = 18;
			}
		}
		else if (num3 <= 3558870252u)
		{
			if (num3 != 3376198402u)
			{
				if (num3 != 3558870252u || !(text == "披风"))
				{
					goto IL_038c;
				}
				num2 = 12;
			}
			else
			{
				if (!(text == "石头"))
				{
					goto IL_038c;
				}
				num2 = 16;
			}
		}
		else if (num3 != 4059967668u)
		{
			if (num3 != 4183737334u)
			{
				if (num3 != 4237204361u || !(text == "靴子"))
				{
					goto IL_038c;
				}
				num2 = 5;
			}
			else
			{
				if (!(text == "内甲"))
				{
					goto IL_038c;
				}
				num2 = 6;
			}
		}
		else
		{
			if (!(text == "百宝"))
			{
				goto IL_038c;
			}
			num2 = 4000;
		}
		goto IL_0394;
		IL_0394:
		listBox1.Items.Clear();
		switch (num2)
		{
		case 4:
			foreach (YbiClass value in ybidate.Values)
			{
				if (value.FLD_RESIDE2 == num2)
				{
					listBox1.Items.Add(value.FLD_Name);
				}
			}
			break;
		case 2:
			foreach (YbiClass value16 in ybidate.Values)
			{
				if (value16.FLD_RESIDE2 == num2)
				{
					listBox1.Items.Add(value16.FLD_Name);
				}
			}
			break;
		default:
			foreach (YbiClass value2 in ybidate.Values)
			{
				if (value2.FLD_RESIDE2 != 1 && value2.FLD_RESIDE2 != 2 && value2.FLD_RESIDE2 != 4 && value2.FLD_RESIDE2 != 5 && value2.FLD_RESIDE2 != 6 && value2.FLD_RESIDE2 != 7 && value2.FLD_RESIDE2 != 8 && value2.FLD_RESIDE2 != 10 && value2.FLD_RESIDE2 != 12 && value2.FLD_RESIDE2 != 15 && value2.FLD_RESIDE2 != 16 && value2.FLD_RESIDE2 != 17 && value2.FLD_RESIDE2 != 18 && value2.FLD_PID != 1000000001 && value2.FLD_PID != 1000000251 && value2.FLD_PID != 1000000250 && value2.FLD_PID != 1000000071 && value2.FLD_PID != 1000000006 && value2.FLD_PID != 1000000005 && value2.FLD_PID != 1000000026 && value2.FLD_PID != 1000000023 && value2.FLD_PID != 1000000008 && value2.FLD_PID != 1000000003 && value2.FLD_PID != 1000000002 && (value2.FLD_PID <= 900000001 || value2.FLD_PID >= 1000000000) && value2.FLD_TYPE != 6)
				{
					listBox1.Items.Add(value2.FLD_Name);
				}
			}
			break;
		case 17:
			foreach (YbiClass value3 in ybidate.Values)
			{
				if (value3.FLD_PID == 1000000001 || value3.FLD_PID == 1000000251 || value3.FLD_PID == 1000000250 || value3.FLD_PID == 1000000071 || value3.FLD_PID == 1000000006 || value3.FLD_PID == 1000000005 || value3.FLD_PID == 1000000026 || value3.FLD_PID == 1000000023 || value3.FLD_PID == 1000000008 || value3.FLD_PID == 1000000003 || value3.FLD_PID == 1000000002)
				{
					listBox1.Items.Add(value3.FLD_Name);
				}
			}
			break;
		case 10:
			foreach (YbiClass value4 in ybidate.Values)
			{
				if (value4.FLD_RESIDE2 == num2)
				{
					listBox1.Items.Add(value4.FLD_Name);
				}
			}
			break;
		case 1:
			foreach (YbiClass value5 in ybidate.Values)
			{
				if (value5.FLD_RESIDE2 == num2)
				{
					listBox1.Items.Add(value5.FLD_Name);
				}
			}
			break;
		case 16:
			foreach (YbiClass value6 in ybidate.Values)
			{
				if (value6.FLD_RESIDE2 == num2)
				{
					listBox1.Items.Add(value6.FLD_Name);
				}
			}
			break;
		case 18:
			foreach (YbiClass value7 in ybidate.Values)
			{
				if (value7.FLD_RESIDE2 == num2)
				{
					listBox1.Items.Add(value7.FLD_Name);
				}
			}
			break;
		case 15:
			foreach (YbiClass value8 in ybidate.Values)
			{
				if (value8.FLD_RESIDE2 == num2)
				{
					listBox1.Items.Add(value8.FLD_Name);
				}
			}
			break;
		case 6:
			foreach (YbiClass value9 in ybidate.Values)
			{
				if (value9.FLD_RESIDE2 == num2)
				{
					listBox1.Items.Add(value9.FLD_Name);
				}
			}
			break;
		case 5:
			foreach (YbiClass value10 in ybidate.Values)
			{
				if (value10.FLD_RESIDE2 == num2)
				{
					listBox1.Items.Add(value10.FLD_Name);
				}
			}
			break;
		case 12:
			foreach (YbiClass value11 in ybidate.Values)
			{
				if (value11.FLD_RESIDE2 == num2)
				{
					listBox1.Items.Add(value11.FLD_Name);
				}
			}
			break;
		case 8:
			foreach (YbiClass value12 in ybidate.Values)
			{
				if (value12.FLD_RESIDE2 == num2)
				{
					listBox1.Items.Add(value12.FLD_Name);
				}
			}
			break;
		case 1000:
			foreach (YbiClass value13 in ybidate.Values)
			{
				if (value13.FLD_PID > 900000001 && value13.FLD_PID < 1000000000)
				{
					listBox1.Items.Add(value13.FLD_Name);
				}
			}
			break;
		case 4000:
			foreach (YbiClass value14 in ybidate.Values)
			{
				if (value14.FLD_TYPE == 6)
				{
					listBox1.Items.Add(value14.FLD_Name);
				}
			}
			break;
		case 3000:
		{
			using (Dictionary<int, YbiClass>.ValueCollection.Enumerator enumerator10 = ybidate.Values.GetEnumerator())
			{
				while (enumerator10.MoveNext())
				{
					listBox1.Items.Add(enumerator10.Current.FLD_Name);
				}
			}
			break;
		}
		case 7:
			foreach (YbiClass value15 in ybidate.Values)
			{
				if (value15.FLD_RESIDE2 == num2)
				{
					listBox1.Items.Add(value15.FLD_Name);
				}
			}
			break;
		}
		label28.Text = listBox1.Items.Count.ToString();
		return;
		IL_038c:
		num2 = 3000;
		goto IL_0394;
	}

	private void YbiForm_FormClosing(object sender, FormClosingEventArgs e)
	{
		try
		{
			ybidate?.Clear();
			物品 = null;
			KEY = null;
			武功 = null;
			气功 = null;
			称号 = null;
			屏蔽1 = null;
			屏蔽2 = null;
			NPC = null;
			未知1 = null;
			地图名 = null;
			未知2 = null;
		}
		catch (Exception)
		{
		}
	}

	private void textBox14_TextChanged(object sender, EventArgs e)
	{
	}
}
