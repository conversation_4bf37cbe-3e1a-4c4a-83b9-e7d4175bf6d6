using System;
using System.ComponentModel;
using System.Globalization;

namespace RxjhTool;

public class SpellingOptionsConverter : ExpandableObjectConverter
{
	public override bool CanConvertTo(ITypeDescriptorContext context, Type destinationType)
	{
		if (destinationType != typeof(坐标类))
		{
			return base.CanConvertTo(context, destinationType);
		}
		return true;
	}

	public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value, Type destinationType)
	{
		if (destinationType == typeof(string) && value is 坐标类)
		{
			坐标类 坐标类2 = (坐标类)value;
			return "地图ID:" + 坐标类2.地图Id + "，坐标X: " + 坐标类2.坐标X + "，坐标Y: " + 坐标类2.坐标Y;
		}
		return base.ConvertTo(context, culture, value, destinationType);
	}

	public override bool CanConvertFrom(ITypeDescriptorContext context, Type sourceType)
	{
		if (sourceType != typeof(string))
		{
			return base.CanConvertFrom(context, sourceType);
		}
		return true;
	}

	public override object ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, object value)
	{
		if (value is string)
		{
			try
			{
				string text = (string)value;
				int num2 = text.IndexOf(':');
				int num3 = text.IndexOf(',');
				if (num2 != -1 && num3 != -1)
				{
					text.Substring(num2 + 1, num3 - num2 - 1);
					int num4 = text.IndexOf(':', num3 + 1);
					int num5 = text.IndexOf(',', num3 + 1);
					text.Substring(num4 + 1, num5 - num4 - 1);
					int num6 = text.IndexOf(':', num5 + 1);
					text.Substring(num6 + 1);
					return new 坐标类();
				}
			}
			catch
			{
				throw new ArgumentException("无法将“" + (string)value + "”转换为 SpellingOptions 类型");
			}
		}
		return base.ConvertFrom(context, culture, value);
	}
}
