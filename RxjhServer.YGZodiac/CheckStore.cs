using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using RxjhServer.Properties;

namespace RxjhServer.YGZodiac;

public class CheckStore : Form
{
	public static Players vplayer;

	private PictureBox ViTriKhoDo;

	private List<PictureBox> WareHouse;

	private List<PictureBox> PublicWareHouse;

	private IContainer components = null;

	private PictureBox picboxWarehouse;

	private PictureBox pictureBox1;

	private Label label1;

	private System.Windows.Forms.Timer timer1;

	private CheckBox chkReload;

	public CheckStore()
	{
		InitializeComponent();
		HamTaoKhoRieng();
		HamTaoKhoChung();
		label1.Text = "User name : " + vplayer.UserName;
	}

	private void txtUsername_TextChanged(object sender, EventArgs e)
	{
	}

	private void HideInventor()
	{
		foreach (PictureBox ViTri2 in PublicWareHouse)
		{
			ViTri2.Visible = false;
		}
		foreach (PictureBox ViTri in WareHouse)
		{
			ViTri.Visible = false;
		}
	}

	private void HamTaoKhoRieng()
	{
		WareHouse = new List<PictureBox>();
		for (int i = 0; i < 60; i++)
		{
			string name = $"KhoRiengViTri{i}";
			ViTriKhoDo = new PictureBox();
			ViTriKhoDo.Tag = i;
			ViTriKhoDo.BackColor = Color.Black;
			ViTriKhoDo.Size = new Size(32, 32);
			ViTriKhoDo.Visible = false;
			int Vitri = i % 6;
			int SoDong = i / 6;
			int X = 54 + 33 * Vitri;
			int Y = 97 + 35 * SoDong;
			Point point = new Point(X, Y);
			ViTriKhoDo.Location = point;
			base.Controls.Add(ViTriKhoDo);
			WareHouse.Add(ViTriKhoDo);
			ViTriKhoDo.BringToFront();
			ViTriKhoDo.Click += HienThiInfo;
			ViTriKhoDo.Name = name;
		}
		GetKhoRiengItem();
	}

	private void GetKhoRiengItem()
	{
		for (int i = 0; i < 60; i++)
		{
			int ItemID = (int)vplayer.PersonalWarehouse[i].GetVatPham_ID;
			string name = $"KhoRiengViTri{i}";
			if (World.Tool_AddItem_Images_Path.Length > 0)
			{
				foreach (PictureBox ViTri in WareHouse)
				{
					if (ViTri.Name == name)
					{
						if (File.Exists(World.Tool_AddItem_Images_Path + $"{ItemID}.jpg") && ItemID != 0)
						{
							ViTri.Image = Image.FromFile(World.Tool_AddItem_Images_Path + $"{ItemID}.jpg");
							ViTri.Visible = true;
						}
						else
						{
							ViTri.Visible = false;
						}
					}
				}
				continue;
			}
			foreach (PictureBox ViTri2 in WareHouse)
			{
				if (ViTri2.Name == name)
				{
					if (File.Exists($"Items\\{ItemID}.jpg") && ItemID != 0)
					{
						ViTri2.Image = Image.FromFile($"Items\\{ItemID}.jpg");
						ViTri2.Visible = true;
					}
					else
					{
						ViTri2.Visible = false;
					}
				}
			}
		}
	}

	private void HamTaoKhoChung()
	{
		PublicWareHouse = new List<PictureBox>();
		for (int i = 0; i < 60; i++)
		{
			string name = $"KhoChungViTri{i}";
			ViTriKhoDo = new PictureBox();
			ViTriKhoDo.Tag = i + 100;
			ViTriKhoDo.BackColor = Color.Black;
			ViTriKhoDo.Size = new Size(32, 32);
			ViTriKhoDo.Visible = false;
			int Vitri = i % 6;
			int SoDong = i / 6;
			int X = 341 + 33 * Vitri;
			int Y = 97 + 35 * SoDong;
			Point point = new Point(X, Y);
			ViTriKhoDo.Location = point;
			base.Controls.Add(ViTriKhoDo);
			PublicWareHouse.Add(ViTriKhoDo);
			ViTriKhoDo.BringToFront();
			ViTriKhoDo.Click += HienThiInfo;
			ViTriKhoDo.Name = name;
		}
		GetKhoChungItem();
	}

	private void GetKhoChungItem()
	{
		for (int i = 0; i < 60; i++)
		{
			int ItemID = (int)vplayer.PublicWarehouse[i].GetVatPham_ID;
			string name = $"KhoChungViTri{i}";
			if (World.Tool_AddItem_Images_Path.Length > 0)
			{
				foreach (PictureBox ViTri in PublicWareHouse)
				{
					if (ViTri.Name == name)
					{
						if (File.Exists(World.Tool_AddItem_Images_Path + $"{ItemID}.jpg") && ItemID != 0)
						{
							ViTri.Image = Image.FromFile(World.Tool_AddItem_Images_Path + $"{ItemID}.jpg");
							ViTri.Visible = true;
						}
						else
						{
							ViTri.Visible = false;
						}
					}
				}
				continue;
			}
			foreach (PictureBox ViTri2 in PublicWareHouse)
			{
				if (ViTri2.Name == name)
				{
					if (File.Exists($"Items\\{ItemID}.jpg") && ItemID != 0)
					{
						ViTri2.Image = Image.FromFile($"Items\\{ItemID}.jpg");
						ViTri2.Visible = true;
					}
					else
					{
						ViTri2.Visible = false;
					}
				}
			}
		}
	}

	private void HienThiInfo(object sender, EventArgs e)
	{
		int index = Convert.ToInt32(((PictureBox)sender).Tag);
		if (index < 100)
		{
			if (GetItemID(index, 2) != 0)
			{
				ItemInfo Info = new ItemInfo(GetItemID(index, 2), GetItemSeri(index, 2), GetItemAmount(index, 2), GetItemName(index, 2), GetItemPhamChat(index, 2), GetItemCuongHoa(index, 2), GetItemLevel(index, 2), GetItemTheLuc(index, 2), GetItemGender(index, 2), GetItemOptionType(index, 1, 2), GetItemOptionLevel(index, 1, 2), GetItemOptionType(index, 2, 2), GetItemOptionLevel(index, 2, 2), GetItemOptionType(index, 3, 2), GetItemOptionLevel(index, 3, 2), GetItemOptionType(index, 4, 2), GetItemOptionLevel(index, 4, 2), GetThuocTinh(index, 2), GetThuocTinhLevel(index, 2), GetTinhNgo(index, 2), GetTrungCap(index, 2), GetKhoa(index, 2), 2, index, vplayer);
				Info.ShowDialog();
			}
			return;
		}
		index -= 100;
		if (GetItemID(index, 3) != 0)
		{
			ItemInfo Info2 = new ItemInfo(GetItemID(index, 3), GetItemSeri(index, 3), GetItemAmount(index, 3), GetItemName(index, 3), GetItemPhamChat(index, 3), GetItemCuongHoa(index, 3), GetItemLevel(index, 3), GetItemTheLuc(index, 3), GetItemGender(index, 3), GetItemOptionType(index, 1, 3), GetItemOptionLevel(index, 1, 3), GetItemOptionType(index, 2, 3), GetItemOptionLevel(index, 2, 3), GetItemOptionType(index, 3, 3), GetItemOptionLevel(index, 3, 3), GetItemOptionType(index, 4, 3), GetItemOptionLevel(index, 4, 3), GetThuocTinh(index, 3), GetThuocTinhLevel(index, 3), GetTinhNgo(index, 3), GetTrungCap(index, 3), GetKhoa(index, 3), 3, index, vplayer);
			Info2.ShowDialog();
		}
	}

	private string GetItemName(int index, int type)
	{
		if (type == 2)
		{
			return vplayer.PersonalWarehouse[index].DatDuocVatPhamTen_XungHao();
		}
		return vplayer.PublicWarehouse[index].DatDuocVatPhamTen_XungHao();
	}

	private int GetItemAmount(int index, int type)
	{
		if (type == 2)
		{
			return vplayer.PersonalWarehouse[index].GetVatPhamSoLuong;
		}
		return vplayer.PublicWarehouse[index].GetVatPhamSoLuong;
	}

	private long GetItemSeri(int index, int type)
	{
		if (type == 2)
		{
			return vplayer.PersonalWarehouse[index].GetItemGlobal_ID;
		}
		return vplayer.PublicWarehouse[index].GetItemGlobal_ID;
	}

	private int GetItemGender(int index, int type)
	{
		if (type == 2)
		{
			return vplayer.PersonalWarehouse[index].GetGender();
		}
		return vplayer.PublicWarehouse[index].GetGender();
	}

	private int GetItemPhamChat(int index, int type)
	{
		if (type == 2)
		{
			return vplayer.PersonalWarehouse[index].FLD_FJ_TienHoa;
		}
		return vplayer.PublicWarehouse[index].FLD_FJ_TienHoa;
	}

	private int GetItemCuongHoa(int index, int type)
	{
		if (type == 2)
		{
			return vplayer.PersonalWarehouse[index].FLD_CuongHoaSoLuong;
		}
		return vplayer.PublicWarehouse[index].FLD_CuongHoaSoLuong;
	}

	private int GetItemLevel(int index, int type)
	{
		ItmeClass ITEM = ((type != 2) ? ItmeClass.GetItmeID(GetItemID(index, 3)) : ItmeClass.GetItmeID(GetItemID(index, 2)));
		return ITEM.FLD_LEVEL;
	}

	private int GetItemTheLuc(int index, int type)
	{
		ItmeClass ITEM = ((type != 2) ? ItmeClass.GetItmeID(GetItemID(index, 3)) : ItmeClass.GetItmeID(GetItemID(index, 2)));
		return ITEM.FLD_ZX;
	}

	private int GetItemOptionType(int index, int SoDong, int type)
	{
		int OptionType = 0;
		if (type == 2)
		{
			switch (SoDong)
			{
			case 1:
				OptionType = vplayer.PersonalWarehouse[index].ThuocTinh1.ThuocTinhLoaiHinh;
				break;
			case 2:
				OptionType = vplayer.PersonalWarehouse[index].ThuocTinh2.ThuocTinhLoaiHinh;
				break;
			case 3:
				OptionType = vplayer.PersonalWarehouse[index].ThuocTinh3.ThuocTinhLoaiHinh;
				break;
			case 4:
				OptionType = vplayer.PersonalWarehouse[index].ThuocTinh4.ThuocTinhLoaiHinh;
				break;
			}
		}
		else
		{
			switch (SoDong)
			{
			case 1:
				OptionType = vplayer.PublicWarehouse[index].ThuocTinh1.ThuocTinhLoaiHinh;
				break;
			case 2:
				OptionType = vplayer.PublicWarehouse[index].ThuocTinh2.ThuocTinhLoaiHinh;
				break;
			case 3:
				OptionType = vplayer.PublicWarehouse[index].ThuocTinh3.ThuocTinhLoaiHinh;
				break;
			case 4:
				OptionType = vplayer.PublicWarehouse[index].ThuocTinh4.ThuocTinhLoaiHinh;
				break;
			}
		}
		return OptionType;
	}

	private int GetItemOptionLevel(int index, int SoDong, int type)
	{
		int OptionLevel = 0;
		if (type == 2)
		{
			switch (SoDong)
			{
			case 1:
				OptionLevel = vplayer.PersonalWarehouse[index].ThuocTinh1.ThuocTinhSoLuong;
				break;
			case 2:
				OptionLevel = vplayer.PersonalWarehouse[index].ThuocTinh2.ThuocTinhSoLuong;
				break;
			case 3:
				OptionLevel = vplayer.PersonalWarehouse[index].ThuocTinh3.ThuocTinhSoLuong;
				break;
			case 4:
				OptionLevel = vplayer.PersonalWarehouse[index].ThuocTinh4.ThuocTinhSoLuong;
				break;
			}
		}
		else
		{
			switch (SoDong)
			{
			case 1:
				OptionLevel = vplayer.PublicWarehouse[index].ThuocTinh1.ThuocTinhSoLuong;
				break;
			case 2:
				OptionLevel = vplayer.PublicWarehouse[index].ThuocTinh2.ThuocTinhSoLuong;
				break;
			case 3:
				OptionLevel = vplayer.PublicWarehouse[index].ThuocTinh3.ThuocTinhSoLuong;
				break;
			case 4:
				OptionLevel = vplayer.PublicWarehouse[index].ThuocTinh4.ThuocTinhSoLuong;
				break;
			}
		}
		return OptionLevel;
	}

	private int GetThuocTinh(int index, int type)
	{
		if (type == 2)
		{
			return vplayer.PersonalWarehouse[index].FLDThuocTinhLoaiHinh;
		}
		return vplayer.PublicWarehouse[index].FLDThuocTinhLoaiHinh;
	}

	private int GetThuocTinhLevel(int index, int type)
	{
		if (type == 2)
		{
			return vplayer.PersonalWarehouse[index].FLDThuocTinhSoLuong;
		}
		return vplayer.PublicWarehouse[index].FLDThuocTinhSoLuong;
	}

	private int GetTinhNgo(int index, int type)
	{
		if (type == 2)
		{
			return vplayer.PersonalWarehouse[index].FLD_FJ_LowSoul;
		}
		return vplayer.PublicWarehouse[index].FLD_FJ_LowSoul;
	}

	private int GetTrungCap(int index, int type)
	{
		if (type == 2)
		{
			return vplayer.PersonalWarehouse[index].FLD_FJ_TrungCapPhuHon;
		}
		return vplayer.PublicWarehouse[index].FLD_FJ_TrungCapPhuHon;
	}

	private int GetKhoa(int index, int type)
	{
		bool isKhoa = ((type != 2) ? vplayer.PublicWarehouse[index].Vat_Pham_Khoa_Lai : vplayer.PersonalWarehouse[index].Vat_Pham_Khoa_Lai);
		int Khoa = 0;
		if (isKhoa)
		{
			Khoa = 1;
		}
		return Khoa;
	}

	private int GetItemID(int index, int type)
	{
		if (type == 2)
		{
			return (int)vplayer.PersonalWarehouse[index].GetVatPham_ID;
		}
		return (int)vplayer.PublicWarehouse[index].GetVatPham_ID;
	}

	public static void SetvPlayer(string name)
	{
		foreach (Players item in World.allConnectedChars.Values)
		{
			if (item.UserName == name)
			{
				vplayer = item;
				break;
			}
		}
	}

	private void timer1_Tick(object sender, EventArgs e)
	{
		GetKhoChungItem();
		GetKhoRiengItem();
	}

	private void chkReload_CheckedChanged(object sender, EventArgs e)
	{
		if (chkReload.Checked)
		{
			if (World.allConnectedChars.Values.Count > 0)
			{
				timer1.Start();
			}
		}
		else
		{
			timer1.Stop();
		}
	}

	private void CheckStore_Load(object sender, EventArgs e)
	{
		HideInventor();
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.components = new System.ComponentModel.Container();
		this.pictureBox1 = new System.Windows.Forms.PictureBox();
		this.picboxWarehouse = new System.Windows.Forms.PictureBox();
		this.label1 = new System.Windows.Forms.Label();
		this.timer1 = new System.Windows.Forms.Timer(this.components);
		this.chkReload = new System.Windows.Forms.CheckBox();
		((System.ComponentModel.ISupportInitialize)this.pictureBox1).BeginInit();
		((System.ComponentModel.ISupportInitialize)this.picboxWarehouse).BeginInit();
		base.SuspendLayout();
		this.pictureBox1.Image = RxjhServer.Properties.Resources.npc_window_store02;
		this.pictureBox1.Location = new System.Drawing.Point(299, 12);
		this.pictureBox1.Name = "pictureBox1";
		this.pictureBox1.Size = new System.Drawing.Size(281, 483);
		this.pictureBox1.TabIndex = 1;
		this.pictureBox1.TabStop = false;
		this.picboxWarehouse.Image = RxjhServer.Properties.Resources.npc_window_store01;
		this.picboxWarehouse.Location = new System.Drawing.Point(12, 12);
		this.picboxWarehouse.Name = "picboxWarehouse";
		this.picboxWarehouse.Size = new System.Drawing.Size(281, 483);
		this.picboxWarehouse.TabIndex = 0;
		this.picboxWarehouse.TabStop = false;
		this.label1.AutoSize = true;
		this.label1.Location = new System.Drawing.Point(121, 43);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(69, 13);
		this.label1.TabIndex = 2;
		this.label1.Text = "User Name : ";
		this.timer1.Interval = 1000;
		this.timer1.Tick += new System.EventHandler(timer1_Tick);
		this.chkReload.AutoSize = true;
		this.chkReload.Location = new System.Drawing.Point(424, 43);
		this.chkReload.Name = "chkReload";
		this.chkReload.Size = new System.Drawing.Size(60, 17);
		this.chkReload.TabIndex = 3;
		this.chkReload.Text = "Reload";
		this.chkReload.UseVisualStyleBackColor = true;
		this.chkReload.CheckedChanged += new System.EventHandler(chkReload_CheckedChanged);
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(592, 501);
		base.Controls.Add(this.chkReload);
		base.Controls.Add(this.label1);
		base.Controls.Add(this.pictureBox1);
		base.Controls.Add(this.picboxWarehouse);
		base.Name = "CheckStore";
		this.Text = "CheckStore";
		base.Load += new System.EventHandler(CheckStore_Load);
		((System.ComponentModel.ISupportInitialize)this.pictureBox1).EndInit();
		((System.ComponentModel.ISupportInitialize)this.picboxWarehouse).EndInit();
		base.ResumeLayout(false);
		base.PerformLayout();
	}
}
