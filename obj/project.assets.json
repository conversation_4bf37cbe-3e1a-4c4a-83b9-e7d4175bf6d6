{"version": 3, "targets": {".NETFramework,Version=v4.8.1": {"KeraLua/1.4.1": {"type": "package", "compile": {"lib/net46/KeraLua.dll": {"related": ".xml"}}, "runtime": {"lib/net46/KeraLua.dll": {"related": ".xml"}}, "build": {"build/net46/KeraLua.targets": {}}, "runtimeTargets": {"runtimes/android-arm/native/liblua54.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/liblua54.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/liblua54.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/liblua54.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/ios/native/liblua54.framework/Info.plist": {"assetType": "native", "rid": "ios"}, "runtimes/ios/native/liblua54.framework/liblua54": {"assetType": "native", "rid": "ios"}, "runtimes/linux-x64/native/liblua54.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst/native/liblua54.framework/Info.plist": {"assetType": "native", "rid": "maccatalyst"}, "runtimes/maccatalyst/native/liblua54.framework/liblua54": {"assetType": "native", "rid": "maccatalyst"}, "runtimes/osx/native/liblua54.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/tvos/native/liblua54.framework/Info.plist": {"assetType": "native", "rid": "tvos"}, "runtimes/tvos/native/liblua54.framework/liblua54": {"assetType": "native", "rid": "tvos"}, "runtimes/win-arm/native/lua54.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/lua54.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/lua54.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/lua54.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.NETFramework.ReferenceAssemblies/1.0.3": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies.net481": "1.0.3"}}, "Microsoft.NETFramework.ReferenceAssemblies.net481/1.0.3": {"type": "package", "build": {"build/Microsoft.NETFramework.ReferenceAssemblies.net481.targets": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NLua/1.7.3": {"type": "package", "dependencies": {"KeraLua": "1.4.1"}, "compile": {"lib/net46/NLua.dll": {}}, "runtime": {"lib/net46/NLua.dll": {}}}}, ".NETFramework,Version=v4.8.1/win-x86": {"KeraLua/1.4.1": {"type": "package", "compile": {"lib/net46/KeraLua.dll": {"related": ".xml"}}, "runtime": {"lib/net46/KeraLua.dll": {"related": ".xml"}}, "native": {"runtimes/win-x86/native/lua54.dll": {}}, "build": {"build/net46/KeraLua.targets": {}}}, "Microsoft.NETFramework.ReferenceAssemblies/1.0.3": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies.net481": "1.0.3"}}, "Microsoft.NETFramework.ReferenceAssemblies.net481/1.0.3": {"type": "package", "build": {"build/Microsoft.NETFramework.ReferenceAssemblies.net481.targets": {}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NLua/1.7.3": {"type": "package", "dependencies": {"KeraLua": "1.4.1"}, "compile": {"lib/net46/NLua.dll": {}}, "runtime": {"lib/net46/NLua.dll": {}}}}}, "libraries": {"KeraLua/1.4.1": {"sha512": "SHfGjaWED+nHxMWbIqAVN90bs5xLnOOojuQaSSzZqJAKXSzcDi94ikr/7GQxM1/4ny/tKnnGHSKkOQyZ65572Q==", "type": "package", "path": "keralua/1.4.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "build/net46/KeraLua.targets", "keralua.1.4.1.nupkg.sha512", "keralua.nuspec", "lib/MonoAndroid/KeraLua.dll", "lib/MonoAndroid/KeraLua.xml", "lib/net46/KeraLua.dll", "lib/net46/KeraLua.xml", "lib/net8.0-android28/KeraLua.dll", "lib/net8.0-android28/KeraLua.xml", "lib/net8.0-ios11.0/KeraLua.dll", "lib/net8.0-ios11.0/KeraLua.xml", "lib/net8.0-maccatalyst13.0/KeraLua.dll", "lib/net8.0-maccatalyst13.0/KeraLua.xml", "lib/net8.0-macos10.13/KeraLua.dll", "lib/net8.0-macos10.13/KeraLua.xml", "lib/net8.0-tvos11.0/KeraLua.dll", "lib/net8.0-tvos11.0/KeraLua.xml", "lib/net8.0/KeraLua.dll", "lib/net8.0/KeraLua.xml", "lib/netstandard2.0/KeraLua.dll", "lib/netstandard2.0/KeraLua.xml", "lib/uap10.0/KeraLua.dll", "lib/uap10.0/KeraLua.pri", "lib/uap10.0/KeraLua.xml", "lib/xamarinios/KeraLua.dll", "lib/xamarinios/KeraLua.xml", "lib/xamarinmac/KeraLua.dll", "lib/xamarinmac/KeraLua.xml", "lib/xamarintvos/KeraLua.dll", "lib/xamarintvos/KeraLua.xml", "runtimes/android-arm/native/liblua54.so", "runtimes/android-arm64/native/liblua54.so", "runtimes/android-x64/native/liblua54.so", "runtimes/android-x86/native/liblua54.so", "runtimes/ios/native/liblua54.framework/Info.plist", "runtimes/ios/native/liblua54.framework/liblua54", "runtimes/linux-x64/native/liblua54.so", "runtimes/maccatalyst/native/liblua54.framework/Info.plist", "runtimes/maccatalyst/native/liblua54.framework/liblua54", "runtimes/osx/native/liblua54.dylib", "runtimes/tvos/native/liblua54.framework/Info.plist", "runtimes/tvos/native/liblua54.framework/liblua54", "runtimes/win-arm/native/lua54.dll", "runtimes/win-arm64/native/lua54.dll", "runtimes/win-x64/native/lua54.dll", "runtimes/win-x86/native/lua54.dll", "runtimes/win10-arm/nativeassets/uap10.0/lua54.dll", "runtimes/win10-arm64/nativeassets/uap10.0/lua54.dll", "runtimes/win10-x64/nativeassets/uap10.0/lua54.dll", "runtimes/win10-x86/nativeassets/uap10.0/lua54.dll"]}, "Microsoft.NETFramework.ReferenceAssemblies/1.0.3": {"sha512": "vUc9Npcs14QsyOD01tnv/m8sQUnGTGOw1BCmKcv77LBJY7OxhJ+zJF7UD/sCL3lYNFuqmQEVlkfS4Quif6FyYg==", "type": "package", "path": "microsoft.netframework.referenceassemblies/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "microsoft.netframework.referenceassemblies.1.0.3.nupkg.sha512", "microsoft.netframework.referenceassemblies.nuspec"]}, "Microsoft.NETFramework.ReferenceAssemblies.net481/1.0.3": {"sha512": "Vv/20vgHS7VglVOVh8J3Iz/MA+VYKVRp9f7r2qiKBMuzviTOmocG70yq0Q8T5OTmCONkEAIJwETD1zhEfLkAXQ==", "type": "package", "path": "microsoft.netframework.referenceassemblies.net481/1.0.3", "files": [".nupkg.metadata", ".signature.p7s", "build/.NETFramework/v4.8.1/Accessibility.dll", "build/.NETFramework/v4.8.1/Accessibility.xml", "build/.NETFramework/v4.8.1/CustomMarshalers.dll", "build/.NETFramework/v4.8.1/CustomMarshalers.xml", "build/.NETFramework/v4.8.1/Facades/Microsoft.Win32.Primitives.dll", "build/.NETFramework/v4.8.1/Facades/System.AppContext.dll", "build/.NETFramework/v4.8.1/Facades/System.Collections.Concurrent.dll", "build/.NETFramework/v4.8.1/Facades/System.Collections.NonGeneric.dll", "build/.NETFramework/v4.8.1/Facades/System.Collections.Specialized.dll", "build/.NETFramework/v4.8.1/Facades/System.Collections.dll", "build/.NETFramework/v4.8.1/Facades/System.ComponentModel.Annotations.dll", "build/.NETFramework/v4.8.1/Facades/System.ComponentModel.EventBasedAsync.dll", "build/.NETFramework/v4.8.1/Facades/System.ComponentModel.Primitives.dll", "build/.NETFramework/v4.8.1/Facades/System.ComponentModel.TypeConverter.dll", "build/.NETFramework/v4.8.1/Facades/System.ComponentModel.dll", "build/.NETFramework/v4.8.1/Facades/System.Console.dll", "build/.NETFramework/v4.8.1/Facades/System.Data.Common.dll", "build/.NETFramework/v4.8.1/Facades/System.Diagnostics.Contracts.dll", "build/.NETFramework/v4.8.1/Facades/System.Diagnostics.Debug.dll", "build/.NETFramework/v4.8.1/Facades/System.Diagnostics.FileVersionInfo.dll", "build/.NETFramework/v4.8.1/Facades/System.Diagnostics.Process.dll", "build/.NETFramework/v4.8.1/Facades/System.Diagnostics.StackTrace.dll", "build/.NETFramework/v4.8.1/Facades/System.Diagnostics.TextWriterTraceListener.dll", "build/.NETFramework/v4.8.1/Facades/System.Diagnostics.Tools.dll", "build/.NETFramework/v4.8.1/Facades/System.Diagnostics.TraceSource.dll", "build/.NETFramework/v4.8.1/Facades/System.Drawing.Primitives.dll", "build/.NETFramework/v4.8.1/Facades/System.Dynamic.Runtime.dll", "build/.NETFramework/v4.8.1/Facades/System.Globalization.Calendars.dll", "build/.NETFramework/v4.8.1/Facades/System.Globalization.Extensions.dll", "build/.NETFramework/v4.8.1/Facades/System.Globalization.dll", "build/.NETFramework/v4.8.1/Facades/System.IO.Compression.ZipFile.dll", "build/.NETFramework/v4.8.1/Facades/System.IO.FileSystem.DriveInfo.dll", "build/.NETFramework/v4.8.1/Facades/System.IO.FileSystem.Primitives.dll", "build/.NETFramework/v4.8.1/Facades/System.IO.FileSystem.Watcher.dll", "build/.NETFramework/v4.8.1/Facades/System.IO.FileSystem.dll", "build/.NETFramework/v4.8.1/Facades/System.IO.IsolatedStorage.dll", "build/.NETFramework/v4.8.1/Facades/System.IO.MemoryMappedFiles.dll", "build/.NETFramework/v4.8.1/Facades/System.IO.Pipes.dll", "build/.NETFramework/v4.8.1/Facades/System.IO.UnmanagedMemoryStream.dll", "build/.NETFramework/v4.8.1/Facades/System.IO.dll", "build/.NETFramework/v4.8.1/Facades/System.Linq.Expressions.dll", "build/.NETFramework/v4.8.1/Facades/System.Linq.Parallel.dll", "build/.NETFramework/v4.8.1/Facades/System.Linq.Queryable.dll", "build/.NETFramework/v4.8.1/Facades/System.Linq.dll", "build/.NETFramework/v4.8.1/Facades/System.Net.Http.Rtc.dll", "build/.NETFramework/v4.8.1/Facades/System.Net.NameResolution.dll", "build/.NETFramework/v4.8.1/Facades/System.Net.NetworkInformation.dll", "build/.NETFramework/v4.8.1/Facades/System.Net.Ping.dll", "build/.NETFramework/v4.8.1/Facades/System.Net.Primitives.dll", "build/.NETFramework/v4.8.1/Facades/System.Net.Requests.dll", "build/.NETFramework/v4.8.1/Facades/System.Net.Security.dll", "build/.NETFramework/v4.8.1/Facades/System.Net.Sockets.dll", "build/.NETFramework/v4.8.1/Facades/System.Net.WebHeaderCollection.dll", "build/.NETFramework/v4.8.1/Facades/System.Net.WebSockets.Client.dll", "build/.NETFramework/v4.8.1/Facades/System.Net.WebSockets.dll", "build/.NETFramework/v4.8.1/Facades/System.ObjectModel.dll", "build/.NETFramework/v4.8.1/Facades/System.Reflection.Emit.ILGeneration.dll", "build/.NETFramework/v4.8.1/Facades/System.Reflection.Emit.Lightweight.dll", "build/.NETFramework/v4.8.1/Facades/System.Reflection.Emit.dll", "build/.NETFramework/v4.8.1/Facades/System.Reflection.Extensions.dll", "build/.NETFramework/v4.8.1/Facades/System.Reflection.Primitives.dll", "build/.NETFramework/v4.8.1/Facades/System.Reflection.dll", "build/.NETFramework/v4.8.1/Facades/System.Resources.Reader.dll", "build/.NETFramework/v4.8.1/Facades/System.Resources.ResourceManager.dll", "build/.NETFramework/v4.8.1/Facades/System.Resources.Writer.dll", "build/.NETFramework/v4.8.1/Facades/System.Runtime.CompilerServices.VisualC.dll", "build/.NETFramework/v4.8.1/Facades/System.Runtime.Extensions.dll", "build/.NETFramework/v4.8.1/Facades/System.Runtime.Handles.dll", "build/.NETFramework/v4.8.1/Facades/System.Runtime.InteropServices.RuntimeInformation.dll", "build/.NETFramework/v4.8.1/Facades/System.Runtime.InteropServices.WindowsRuntime.dll", "build/.NETFramework/v4.8.1/Facades/System.Runtime.InteropServices.dll", "build/.NETFramework/v4.8.1/Facades/System.Runtime.Numerics.dll", "build/.NETFramework/v4.8.1/Facades/System.Runtime.Serialization.Formatters.dll", "build/.NETFramework/v4.8.1/Facades/System.Runtime.Serialization.Json.dll", "build/.NETFramework/v4.8.1/Facades/System.Runtime.Serialization.Primitives.dll", "build/.NETFramework/v4.8.1/Facades/System.Runtime.Serialization.Xml.dll", "build/.NETFramework/v4.8.1/Facades/System.Runtime.dll", "build/.NETFramework/v4.8.1/Facades/System.Security.Claims.dll", "build/.NETFramework/v4.8.1/Facades/System.Security.Cryptography.Algorithms.dll", "build/.NETFramework/v4.8.1/Facades/System.Security.Cryptography.Csp.dll", "build/.NETFramework/v4.8.1/Facades/System.Security.Cryptography.Encoding.dll", "build/.NETFramework/v4.8.1/Facades/System.Security.Cryptography.Primitives.dll", "build/.NETFramework/v4.8.1/Facades/System.Security.Cryptography.X509Certificates.dll", "build/.NETFramework/v4.8.1/Facades/System.Security.Principal.dll", "build/.NETFramework/v4.8.1/Facades/System.Security.SecureString.dll", "build/.NETFramework/v4.8.1/Facades/System.ServiceModel.Duplex.dll", "build/.NETFramework/v4.8.1/Facades/System.ServiceModel.Http.dll", "build/.NETFramework/v4.8.1/Facades/System.ServiceModel.NetTcp.dll", "build/.NETFramework/v4.8.1/Facades/System.ServiceModel.Primitives.dll", "build/.NETFramework/v4.8.1/Facades/System.ServiceModel.Security.dll", "build/.NETFramework/v4.8.1/Facades/System.Text.Encoding.Extensions.dll", "build/.NETFramework/v4.8.1/Facades/System.Text.Encoding.dll", "build/.NETFramework/v4.8.1/Facades/System.Text.RegularExpressions.dll", "build/.NETFramework/v4.8.1/Facades/System.Threading.Overlapped.dll", "build/.NETFramework/v4.8.1/Facades/System.Threading.Tasks.Parallel.dll", "build/.NETFramework/v4.8.1/Facades/System.Threading.Tasks.dll", "build/.NETFramework/v4.8.1/Facades/System.Threading.Thread.dll", "build/.NETFramework/v4.8.1/Facades/System.Threading.ThreadPool.dll", "build/.NETFramework/v4.8.1/Facades/System.Threading.Timer.dll", "build/.NETFramework/v4.8.1/Facades/System.Threading.dll", "build/.NETFramework/v4.8.1/Facades/System.ValueTuple.dll", "build/.NETFramework/v4.8.1/Facades/System.Xml.ReaderWriter.dll", "build/.NETFramework/v4.8.1/Facades/System.Xml.XDocument.dll", "build/.NETFramework/v4.8.1/Facades/System.Xml.XPath.XDocument.dll", "build/.NETFramework/v4.8.1/Facades/System.Xml.XPath.dll", "build/.NETFramework/v4.8.1/Facades/System.Xml.XmlDocument.dll", "build/.NETFramework/v4.8.1/Facades/System.Xml.XmlSerializer.dll", "build/.NETFramework/v4.8.1/Facades/netstandard.dll", "build/.NETFramework/v4.8.1/ISymWrapper.dll", "build/.NETFramework/v4.8.1/ISymWrapper.xml", "build/.NETFramework/v4.8.1/Microsoft.Activities.Build.dll", "build/.NETFramework/v4.8.1/Microsoft.Activities.Build.xml", "build/.NETFramework/v4.8.1/Microsoft.Build.Conversion.v4.0.dll", "build/.NETFramework/v4.8.1/Microsoft.Build.Conversion.v4.0.xml", "build/.NETFramework/v4.8.1/Microsoft.Build.Engine.dll", "build/.NETFramework/v4.8.1/Microsoft.Build.Engine.xml", "build/.NETFramework/v4.8.1/Microsoft.Build.Framework.dll", "build/.NETFramework/v4.8.1/Microsoft.Build.Framework.xml", "build/.NETFramework/v4.8.1/Microsoft.Build.Tasks.v4.0.dll", "build/.NETFramework/v4.8.1/Microsoft.Build.Tasks.v4.0.xml", "build/.NETFramework/v4.8.1/Microsoft.Build.Utilities.v4.0.dll", "build/.NETFramework/v4.8.1/Microsoft.Build.Utilities.v4.0.xml", "build/.NETFramework/v4.8.1/Microsoft.Build.dll", "build/.NETFramework/v4.8.1/Microsoft.Build.xml", "build/.NETFramework/v4.8.1/Microsoft.CSharp.dll", "build/.NETFramework/v4.8.1/Microsoft.CSharp.xml", "build/.NETFramework/v4.8.1/Microsoft.JScript.dll", "build/.NETFramework/v4.8.1/Microsoft.JScript.xml", "build/.NETFramework/v4.8.1/Microsoft.VisualBasic.Compatibility.Data.dll", "build/.NETFramework/v4.8.1/Microsoft.VisualBasic.Compatibility.Data.xml", "build/.NETFramework/v4.8.1/Microsoft.VisualBasic.Compatibility.dll", "build/.NETFramework/v4.8.1/Microsoft.VisualBasic.Compatibility.xml", "build/.NETFramework/v4.8.1/Microsoft.VisualBasic.dll", "build/.NETFramework/v4.8.1/Microsoft.VisualBasic.xml", "build/.NETFramework/v4.8.1/Microsoft.VisualC.STLCLR.dll", "build/.NETFramework/v4.8.1/Microsoft.VisualC.STLCLR.xml", "build/.NETFramework/v4.8.1/Microsoft.VisualC.dll", "build/.NETFramework/v4.8.1/Microsoft.VisualC.xml", "build/.NETFramework/v4.8.1/PermissionSets/FullTrust.xml", "build/.NETFramework/v4.8.1/PermissionSets/Internet.xml", "build/.NETFramework/v4.8.1/PermissionSets/LocalIntranet.xml", "build/.NETFramework/v4.8.1/PresentationBuildTasks.dll", "build/.NETFramework/v4.8.1/PresentationBuildTasks.xml", "build/.NETFramework/v4.8.1/PresentationCore.dll", "build/.NETFramework/v4.8.1/PresentationCore.xml", "build/.NETFramework/v4.8.1/PresentationFramework.Aero.dll", "build/.NETFramework/v4.8.1/PresentationFramework.Aero.xml", "build/.NETFramework/v4.8.1/PresentationFramework.Aero2.dll", "build/.NETFramework/v4.8.1/PresentationFramework.Aero2.xml", "build/.NETFramework/v4.8.1/PresentationFramework.AeroLite.dll", "build/.NETFramework/v4.8.1/PresentationFramework.AeroLite.xml", "build/.NETFramework/v4.8.1/PresentationFramework.Classic.dll", "build/.NETFramework/v4.8.1/PresentationFramework.Classic.xml", "build/.NETFramework/v4.8.1/PresentationFramework.Luna.dll", "build/.NETFramework/v4.8.1/PresentationFramework.Luna.xml", "build/.NETFramework/v4.8.1/PresentationFramework.Royale.dll", "build/.NETFramework/v4.8.1/PresentationFramework.Royale.xml", "build/.NETFramework/v4.8.1/PresentationFramework.dll", "build/.NETFramework/v4.8.1/PresentationFramework.xml", "build/.NETFramework/v4.8.1/ReachFramework.dll", "build/.NETFramework/v4.8.1/ReachFramework.xml", "build/.NETFramework/v4.8.1/RedistList/FrameworkList.xml", "build/.NETFramework/v4.8.1/System.Activities.Core.Presentation.dll", "build/.NETFramework/v4.8.1/System.Activities.Core.Presentation.xml", "build/.NETFramework/v4.8.1/System.Activities.DurableInstancing.dll", "build/.NETFramework/v4.8.1/System.Activities.DurableInstancing.xml", "build/.NETFramework/v4.8.1/System.Activities.Presentation.dll", "build/.NETFramework/v4.8.1/System.Activities.Presentation.xml", "build/.NETFramework/v4.8.1/System.Activities.dll", "build/.NETFramework/v4.8.1/System.Activities.xml", "build/.NETFramework/v4.8.1/System.AddIn.Contract.dll", "build/.NETFramework/v4.8.1/System.AddIn.Contract.xml", "build/.NETFramework/v4.8.1/System.AddIn.dll", "build/.NETFramework/v4.8.1/System.AddIn.xml", "build/.NETFramework/v4.8.1/System.ComponentModel.Composition.Registration.dll", "build/.NETFramework/v4.8.1/System.ComponentModel.Composition.Registration.xml", "build/.NETFramework/v4.8.1/System.ComponentModel.Composition.dll", "build/.NETFramework/v4.8.1/System.ComponentModel.Composition.xml", "build/.NETFramework/v4.8.1/System.ComponentModel.DataAnnotations.dll", "build/.NETFramework/v4.8.1/System.ComponentModel.DataAnnotations.xml", "build/.NETFramework/v4.8.1/System.Configuration.Install.dll", "build/.NETFramework/v4.8.1/System.Configuration.Install.xml", "build/.NETFramework/v4.8.1/System.Configuration.dll", "build/.NETFramework/v4.8.1/System.Configuration.xml", "build/.NETFramework/v4.8.1/System.Core.dll", "build/.NETFramework/v4.8.1/System.Core.xml", "build/.NETFramework/v4.8.1/System.Data.DataSetExtensions.dll", "build/.NETFramework/v4.8.1/System.Data.DataSetExtensions.xml", "build/.NETFramework/v4.8.1/System.Data.Entity.Design.dll", "build/.NETFramework/v4.8.1/System.Data.Entity.Design.xml", "build/.NETFramework/v4.8.1/System.Data.Entity.dll", "build/.NETFramework/v4.8.1/System.Data.Entity.xml", "build/.NETFramework/v4.8.1/System.Data.Linq.dll", "build/.NETFramework/v4.8.1/System.Data.Linq.xml", "build/.NETFramework/v4.8.1/System.Data.OracleClient.dll", "build/.NETFramework/v4.8.1/System.Data.OracleClient.xml", "build/.NETFramework/v4.8.1/System.Data.Services.Client.dll", "build/.NETFramework/v4.8.1/System.Data.Services.Client.xml", "build/.NETFramework/v4.8.1/System.Data.Services.Design.dll", "build/.NETFramework/v4.8.1/System.Data.Services.Design.xml", "build/.NETFramework/v4.8.1/System.Data.Services.dll", "build/.NETFramework/v4.8.1/System.Data.Services.xml", "build/.NETFramework/v4.8.1/System.Data.SqlXml.dll", "build/.NETFramework/v4.8.1/System.Data.SqlXml.xml", "build/.NETFramework/v4.8.1/System.Data.dll", "build/.NETFramework/v4.8.1/System.Data.xml", "build/.NETFramework/v4.8.1/System.Deployment.dll", "build/.NETFramework/v4.8.1/System.Deployment.xml", "build/.NETFramework/v4.8.1/System.Design.dll", "build/.NETFramework/v4.8.1/System.Design.xml", "build/.NETFramework/v4.8.1/System.Device.dll", "build/.NETFramework/v4.8.1/System.Device.xml", "build/.NETFramework/v4.8.1/System.Diagnostics.Tracing.dll", "build/.NETFramework/v4.8.1/System.Diagnostics.Tracing.xml", "build/.NETFramework/v4.8.1/System.DirectoryServices.AccountManagement.dll", "build/.NETFramework/v4.8.1/System.DirectoryServices.AccountManagement.xml", "build/.NETFramework/v4.8.1/System.DirectoryServices.Protocols.dll", "build/.NETFramework/v4.8.1/System.DirectoryServices.Protocols.xml", "build/.NETFramework/v4.8.1/System.DirectoryServices.dll", "build/.NETFramework/v4.8.1/System.DirectoryServices.xml", "build/.NETFramework/v4.8.1/System.Drawing.Design.dll", "build/.NETFramework/v4.8.1/System.Drawing.Design.xml", "build/.NETFramework/v4.8.1/System.Drawing.dll", "build/.NETFramework/v4.8.1/System.Drawing.xml", "build/.NETFramework/v4.8.1/System.Dynamic.dll", "build/.NETFramework/v4.8.1/System.EnterpriseServices.Thunk.dll", "build/.NETFramework/v4.8.1/System.EnterpriseServices.Wrapper.dll", "build/.NETFramework/v4.8.1/System.EnterpriseServices.dll", "build/.NETFramework/v4.8.1/System.EnterpriseServices.xml", "build/.NETFramework/v4.8.1/System.IO.Compression.FileSystem.dll", "build/.NETFramework/v4.8.1/System.IO.Compression.FileSystem.xml", "build/.NETFramework/v4.8.1/System.IO.Compression.dll", "build/.NETFramework/v4.8.1/System.IO.Compression.xml", "build/.NETFramework/v4.8.1/System.IO.Log.dll", "build/.NETFramework/v4.8.1/System.IO.Log.xml", "build/.NETFramework/v4.8.1/System.IdentityModel.Selectors.dll", "build/.NETFramework/v4.8.1/System.IdentityModel.Selectors.xml", "build/.NETFramework/v4.8.1/System.IdentityModel.Services.dll", "build/.NETFramework/v4.8.1/System.IdentityModel.Services.xml", "build/.NETFramework/v4.8.1/System.IdentityModel.dll", "build/.NETFramework/v4.8.1/System.IdentityModel.xml", "build/.NETFramework/v4.8.1/System.Linq.xml", "build/.NETFramework/v4.8.1/System.Management.Instrumentation.dll", "build/.NETFramework/v4.8.1/System.Management.Instrumentation.xml", "build/.NETFramework/v4.8.1/System.Management.dll", "build/.NETFramework/v4.8.1/System.Management.xml", "build/.NETFramework/v4.8.1/System.Messaging.dll", "build/.NETFramework/v4.8.1/System.Messaging.xml", "build/.NETFramework/v4.8.1/System.Net.Http.WebRequest.dll", "build/.NETFramework/v4.8.1/System.Net.Http.WebRequest.xml", "build/.NETFramework/v4.8.1/System.Net.Http.dll", "build/.NETFramework/v4.8.1/System.Net.Http.xml", "build/.NETFramework/v4.8.1/System.Net.dll", "build/.NETFramework/v4.8.1/System.Net.xml", "build/.NETFramework/v4.8.1/System.Numerics.dll", "build/.NETFramework/v4.8.1/System.Numerics.xml", "build/.NETFramework/v4.8.1/System.Printing.dll", "build/.NETFramework/v4.8.1/System.Printing.xml", "build/.NETFramework/v4.8.1/System.Reflection.Context.dll", "build/.NETFramework/v4.8.1/System.Reflection.Context.xml", "build/.NETFramework/v4.8.1/System.Runtime.Caching.dll", "build/.NETFramework/v4.8.1/System.Runtime.Caching.xml", "build/.NETFramework/v4.8.1/System.Runtime.DurableInstancing.dll", "build/.NETFramework/v4.8.1/System.Runtime.DurableInstancing.xml", "build/.NETFramework/v4.8.1/System.Runtime.Remoting.dll", "build/.NETFramework/v4.8.1/System.Runtime.Remoting.xml", "build/.NETFramework/v4.8.1/System.Runtime.Serialization.Formatters.Soap.dll", "build/.NETFramework/v4.8.1/System.Runtime.Serialization.Formatters.Soap.xml", "build/.NETFramework/v4.8.1/System.Runtime.Serialization.dll", "build/.NETFramework/v4.8.1/System.Runtime.Serialization.xml", "build/.NETFramework/v4.8.1/System.Security.dll", "build/.NETFramework/v4.8.1/System.Security.xml", "build/.NETFramework/v4.8.1/System.ServiceModel.Activation.dll", "build/.NETFramework/v4.8.1/System.ServiceModel.Activation.xml", "build/.NETFramework/v4.8.1/System.ServiceModel.Activities.dll", "build/.NETFramework/v4.8.1/System.ServiceModel.Activities.xml", "build/.NETFramework/v4.8.1/System.ServiceModel.Channels.dll", "build/.NETFramework/v4.8.1/System.ServiceModel.Channels.xml", "build/.NETFramework/v4.8.1/System.ServiceModel.Discovery.dll", "build/.NETFramework/v4.8.1/System.ServiceModel.Discovery.xml", "build/.NETFramework/v4.8.1/System.ServiceModel.Routing.dll", "build/.NETFramework/v4.8.1/System.ServiceModel.Routing.xml", "build/.NETFramework/v4.8.1/System.ServiceModel.Web.dll", "build/.NETFramework/v4.8.1/System.ServiceModel.Web.xml", "build/.NETFramework/v4.8.1/System.ServiceModel.dll", "build/.NETFramework/v4.8.1/System.ServiceModel.xml", "build/.NETFramework/v4.8.1/System.ServiceProcess.dll", "build/.NETFramework/v4.8.1/System.ServiceProcess.xml", "build/.NETFramework/v4.8.1/System.Speech.dll", "build/.NETFramework/v4.8.1/System.Speech.xml", "build/.NETFramework/v4.8.1/System.Threading.Tasks.Dataflow.xml", "build/.NETFramework/v4.8.1/System.Transactions.dll", "build/.NETFramework/v4.8.1/System.Transactions.xml", "build/.NETFramework/v4.8.1/System.Web.Abstractions.dll", "build/.NETFramework/v4.8.1/System.Web.ApplicationServices.dll", "build/.NETFramework/v4.8.1/System.Web.ApplicationServices.xml", "build/.NETFramework/v4.8.1/System.Web.DataVisualization.Design.dll", "build/.NETFramework/v4.8.1/System.Web.DataVisualization.dll", "build/.NETFramework/v4.8.1/System.Web.DataVisualization.xml", "build/.NETFramework/v4.8.1/System.Web.DynamicData.Design.dll", "build/.NETFramework/v4.8.1/System.Web.DynamicData.Design.xml", "build/.NETFramework/v4.8.1/System.Web.DynamicData.dll", "build/.NETFramework/v4.8.1/System.Web.DynamicData.xml", "build/.NETFramework/v4.8.1/System.Web.Entity.Design.dll", "build/.NETFramework/v4.8.1/System.Web.Entity.Design.xml", "build/.NETFramework/v4.8.1/System.Web.Entity.dll", "build/.NETFramework/v4.8.1/System.Web.Entity.xml", "build/.NETFramework/v4.8.1/System.Web.Extensions.Design.dll", "build/.NETFramework/v4.8.1/System.Web.Extensions.Design.xml", "build/.NETFramework/v4.8.1/System.Web.Extensions.dll", "build/.NETFramework/v4.8.1/System.Web.Extensions.xml", "build/.NETFramework/v4.8.1/System.Web.Mobile.dll", "build/.NETFramework/v4.8.1/System.Web.Mobile.xml", "build/.NETFramework/v4.8.1/System.Web.RegularExpressions.dll", "build/.NETFramework/v4.8.1/System.Web.RegularExpressions.xml", "build/.NETFramework/v4.8.1/System.Web.Routing.dll", "build/.NETFramework/v4.8.1/System.Web.Services.dll", "build/.NETFramework/v4.8.1/System.Web.Services.xml", "build/.NETFramework/v4.8.1/System.Web.dll", "build/.NETFramework/v4.8.1/System.Web.xml", "build/.NETFramework/v4.8.1/System.Windows.Controls.Ribbon.dll", "build/.NETFramework/v4.8.1/System.Windows.Controls.Ribbon.xml", "build/.NETFramework/v4.8.1/System.Windows.Forms.DataVisualization.Design.dll", "build/.NETFramework/v4.8.1/System.Windows.Forms.DataVisualization.dll", "build/.NETFramework/v4.8.1/System.Windows.Forms.DataVisualization.xml", "build/.NETFramework/v4.8.1/System.Windows.Forms.dll", "build/.NETFramework/v4.8.1/System.Windows.Forms.xml", "build/.NETFramework/v4.8.1/System.Windows.Input.Manipulations.dll", "build/.NETFramework/v4.8.1/System.Windows.Input.Manipulations.xml", "build/.NETFramework/v4.8.1/System.Windows.Presentation.dll", "build/.NETFramework/v4.8.1/System.Windows.Presentation.xml", "build/.NETFramework/v4.8.1/System.Windows.dll", "build/.NETFramework/v4.8.1/System.Workflow.Activities.dll", "build/.NETFramework/v4.8.1/System.Workflow.Activities.xml", "build/.NETFramework/v4.8.1/System.Workflow.ComponentModel.dll", "build/.NETFramework/v4.8.1/System.Workflow.ComponentModel.xml", "build/.NETFramework/v4.8.1/System.Workflow.Runtime.dll", "build/.NETFramework/v4.8.1/System.Workflow.Runtime.xml", "build/.NETFramework/v4.8.1/System.WorkflowServices.dll", "build/.NETFramework/v4.8.1/System.WorkflowServices.xml", "build/.NETFramework/v4.8.1/System.Xaml.dll", "build/.NETFramework/v4.8.1/System.Xaml.xml", "build/.NETFramework/v4.8.1/System.Xml.Linq.dll", "build/.NETFramework/v4.8.1/System.Xml.Linq.xml", "build/.NETFramework/v4.8.1/System.Xml.Serialization.dll", "build/.NETFramework/v4.8.1/System.Xml.dll", "build/.NETFramework/v4.8.1/System.Xml.xml", "build/.NETFramework/v4.8.1/System.dll", "build/.NETFramework/v4.8.1/System.xml", "build/.NETFramework/v4.8.1/UIAutomationClient.dll", "build/.NETFramework/v4.8.1/UIAutomationClient.xml", "build/.NETFramework/v4.8.1/UIAutomationClientsideProviders.dll", "build/.NETFramework/v4.8.1/UIAutomationClientsideProviders.xml", "build/.NETFramework/v4.8.1/UIAutomationProvider.dll", "build/.NETFramework/v4.8.1/UIAutomationProvider.xml", "build/.NETFramework/v4.8.1/UIAutomationTypes.dll", "build/.NETFramework/v4.8.1/UIAutomationTypes.xml", "build/.NETFramework/v4.8.1/WindowsBase.dll", "build/.NETFramework/v4.8.1/WindowsBase.xml", "build/.NETFramework/v4.8.1/WindowsFormsIntegration.dll", "build/.NETFramework/v4.8.1/WindowsFormsIntegration.xml", "build/.NETFramework/v4.8.1/XamlBuildTask.dll", "build/.NETFramework/v4.8.1/XamlBuildTask.xml", "build/.NETFramework/v4.8.1/mscorlib.dll", "build/.NETFramework/v4.8.1/mscorlib.xml", "build/.NETFramework/v4.8.1/namespaces.xml", "build/.NETFramework/v4.8.1/sysglobl.dll", "build/.NETFramework/v4.8.1/sysglobl.xml", "build/Microsoft.NETFramework.ReferenceAssemblies.net481.targets", "microsoft.netframework.referenceassemblies.net481.1.0.3.nupkg.sha512", "microsoft.netframework.referenceassemblies.net481.nuspec"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "NLua/1.7.3": {"sha512": "9GdqLw+DpB8ISI6LQlKqFHcsFel8iPnoAE/As6ElmRtJ7lqbVEJ2vD8EkHDuEgW0T8FSKidn6/lH7Uok/XnGMQ==", "type": "package", "path": "nlua/1.7.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/MonoAndroid/NLua.dll", "lib/MonoAndroid/NLua.pdb", "lib/net46/NLua.dll", "lib/net8.0-android28/NLua.dll", "lib/net8.0-ios11.0/NLua.dll", "lib/net8.0-maccatalyst13.0/NLua.dll", "lib/net8.0-macos10.13/NLua.dll", "lib/net8.0-tvos11.0/NLua.dll", "lib/net8.0/NLua.dll", "lib/netstandard2.0/NLua.dll", "lib/uap10.0/NLua.dll", "lib/uap10.0/NLua.pri", "lib/xamarinios/NLua.dll", "lib/xamarinios/NLua.pdb", "lib/xamarinmac/NLua.dll", "lib/xamarinmac/NLua.pdb", "lib/xamarintvos/NLua.dll", "lib/xamarintvos/NLua.pdb", "nlua.1.7.3.nupkg.sha512", "nlua.nuspec"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8.1": ["Microsoft.NETFramework.ReferenceAssemblies >= 1.0.3", "NLua >= 1.7.3", "Newtonsoft.Json >= 13.0.3"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\YulgangDev\\Real\\V24RealProject\\GS_RELOAD\\RxjhServer.csproj", "projectName": "RxjhServer", "projectPath": "E:\\YulgangDev\\Real\\V24RealProject\\GS_RELOAD\\RxjhServer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\YulgangDev\\Real\\V24RealProject\\GS_RELOAD\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["E:\\MovedDrive\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net481"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net481": {"targetAlias": "net481", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net481": {"targetAlias": "net481", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}, "NLua": {"target": "Package", "version": "[1.7.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}