<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="GameServerEntities" connectionString="metadata=res://*/RxjhServer.DbClss.Model1.csdl|res://*/RxjhServer.DbClss.Model1.ssdl|res://*/RxjhServer.DbClss.Model1.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=**************,1433;initial catalog=GameServer;user id=sa;password=*************************;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />
  </connectionStrings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
</configuration>