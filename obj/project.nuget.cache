{"version": 2, "dgSpecHash": "2EPFZYpjZmM=", "success": true, "projectFilePath": "E:\\YulgangDev\\Real\\V24RealProject\\GS_RELOAD\\RxjhServer.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\keralua\\1.4.1\\keralua.1.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netframework.referenceassemblies\\1.0.3\\microsoft.netframework.referenceassemblies.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netframework.referenceassemblies.net481\\1.0.3\\microsoft.netframework.referenceassemblies.net481.1.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nlua\\1.7.3\\nlua.1.7.3.nupkg.sha512"], "logs": []}