{"format": 1, "restore": {"E:\\YulgangDev\\Real\\V24RealProject\\GS_RELOAD\\RxjhServer.csproj": {}}, "projects": {"E:\\YulgangDev\\Real\\V24RealProject\\GS_RELOAD\\RxjhServer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\YulgangDev\\Real\\V24RealProject\\GS_RELOAD\\RxjhServer.csproj", "projectName": "RxjhServer", "projectPath": "E:\\YulgangDev\\Real\\V24RealProject\\GS_RELOAD\\RxjhServer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\YulgangDev\\Real\\V24RealProject\\GS_RELOAD\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["E:\\MovedDrive\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net481"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net481": {"targetAlias": "net481", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net481": {"targetAlias": "net481", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}, "NLua": {"target": "Package", "version": "[1.7.3, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}}