﻿"restore":{"projectUniqueName":"E:\\YulgangDev\\Real\\V24RealProject\\GS_RELOAD\\RxjhServer.csproj","projectName":"RxjhServer","projectPath":"E:\\YulgangDev\\Real\\V24RealProject\\GS_RELOAD\\RxjhServer.csproj","outputPath":"E:\\YulgangDev\\Real\\V24RealProject\\GS_RELOAD\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net481"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net481":{"targetAlias":"net481","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"net481":{"targetAlias":"net481","dependencies":{"Microsoft.NETFramework.ReferenceAssemblies":{"suppressParent":"All","target":"Package","version":"[1.0.3, )","autoReferenced":true},"NLua":{"target":"Package","version":"[1.7.3, )"},"Newtonsoft.Json":{"target":"Package","version":"[13.0.3, )"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\8.0.303\\RuntimeIdentifierGraph.json"}}"runtimes":{"win7-x86":{"#import":[]}}