﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)keralua\1.4.1\build\net46\KeraLua.targets" Condition="Exists('$(NuGetPackageRoot)keralua\1.4.1\build\net46\KeraLua.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.netframework.referenceassemblies.net481\1.0.3\build\Microsoft.NETFramework.ReferenceAssemblies.net481.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.netframework.referenceassemblies.net481\1.0.3\build\Microsoft.NETFramework.ReferenceAssemblies.net481.targets')" />
  </ImportGroup>
</Project>