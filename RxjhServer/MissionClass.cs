using System;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class MissionClass : IDisposable
{
    private Players Play;

    public MissionClass(Players Playr)
    {
        Play = Playr;
    }

    public void Dispose()
    {
        Play = null;
    }

    ~MissionClass()
    {
    }

    public void NhiemVu2(byte[] data, int length)
    {
        Play.PacketModification(data, length);
        int num = BitConverter.ToUInt16(data, 10);
        int num2 = BitConverter.ToUInt16(data, 12);
        int NhiemVuGiaiDoanID = BitConverter.ToInt16(data, 14);
        if (num <= 300)
        {
            switch (num)
            {
                case 12:
                    NhiemVu12(num, num2, NhiemVuGiaiDoanID);
                    return;
                case 11:
                    NhiemVu11(num, num2, NhiemVuGiaiDoanID);
                    return;
                case 45:
                    NhiemVu45(num, num2, NhiemVuGiaiDoanID);
                    return;
                case 18:
                    NhiemVu18(num, num2, NhiemVuGiaiDoanID);
                    return;
                case 73:
                    NhiemVu73(num, num2, NhiemVuGiaiDoanID);
                    return;
                case 46:
                    NhiemVu46(num, num2, NhiemVuGiaiDoanID);
                    return;
                case 300:
                    NhiemVu300(num, num2, NhiemVuGiaiDoanID);
                    return;
                case 178:
                    NhiemVu178(num, num2, NhiemVuGiaiDoanID);
                    return;
                case 74:
                    NhiemVu74(num, num2, NhiemVuGiaiDoanID);
                    return;
            }
        }
        else
        {
            if (num <= 617)
            {
                if (num <= 408)
                {
                    if (num == 301)
                    {
                        NhiemVu301(num, num2, NhiemVuGiaiDoanID);
                        return;
                    }

                    if ((uint)(num - 400) > 8u) goto IL_02d7;
                }
                else if (num != 410)
                {
                    if (num == 615 || num == 617) goto IL_031e;
                    goto IL_02d7;
                }

                NhiemVu八ChuyenChuc(num, num2, NhiemVuGiaiDoanID);
                return;
            }

            if (num <= 750)
            {
                if (num == 620) goto IL_031e;
                if ((uint)(num - 748) <= 2u)
                {
                    NhiemVu十ChuyenChuc(num, num2, NhiemVuGiaiDoanID);
                    return;
                }
            }
            else
            {
                switch (num)
                {
                    case 1002:
                        NhiemVu1002(num, num2, NhiemVuGiaiDoanID);
                        return;
                    case 1003:
                        NhiemVu1003(num, num2, NhiemVuGiaiDoanID);
                        return;
                    case 1004:
                        NhiemVu1004(num, num2, NhiemVuGiaiDoanID);
                        return;
                    case 1005:
                        NhiemVu1005(num, num2, NhiemVuGiaiDoanID);
                        return;
                    case 1006:
                        NhiemVu1006(num, num2, NhiemVuGiaiDoanID);
                        return;
                }

                switch (num)
                {
                    case 9998:
                        NhiemVu9998(num, num2, NhiemVuGiaiDoanID);
                        return;
                    case 9202:
                        NhiemVu9202(num, num2, NhiemVuGiaiDoanID);
                        return;
                }
            }
        }

        IL_02d7:
        if (num2 == 1) TaskReminder(num, 11, NhiemVuGiaiDoanID);
        if (num2 == 3) TaskReminder(num, 31, NhiemVuGiaiDoanID);
        if (num2 == 5) TaskReminder(num, 51, NhiemVuGiaiDoanID);
        return;
        IL_031e:
        NhiemVu九ChuyenChuc(num, num2, NhiemVuGiaiDoanID);
    }

    public void NhiemVu(byte[] data, int length)
    {
        Play.PacketModification(data, length);
        int num = BitConverter.ToUInt16(data, 10);
        int num2 = BitConverter.ToUInt16(data, 12);
        int NhiemVuGiaiDoanID = BitConverter.ToInt16(data, 14);
        switch (num)
        {
            case 12:
                NhiemVu12(num, num2, NhiemVuGiaiDoanID);
                return;
            case 11:
                NhiemVu11(num, num2, NhiemVuGiaiDoanID);
                return;
            case 45:
                NhiemVu45(num, num2, NhiemVuGiaiDoanID);
                return;
            case 18:
                NhiemVu18(num, num2, NhiemVuGiaiDoanID);
                return;
            case 73:
                NhiemVu73(num, num2, NhiemVuGiaiDoanID);
                return;
            case 46:
                NhiemVu46(num, num2, NhiemVuGiaiDoanID);
                return;
            case 300:
                NhiemVu300(num, num2, NhiemVuGiaiDoanID);
                return;
            case 178:
                NhiemVu178(num, num2, NhiemVuGiaiDoanID);
                return;
            case 74:
                NhiemVu74(num, num2, NhiemVuGiaiDoanID);
                return;
            case 301:
                NhiemVu301(num, num2, NhiemVuGiaiDoanID);
                return;
            case 400:
            case 401:
            case 402:
            case 403:
            case 404:
            case 405:
            case 406:
            case 407:
            case 408:
            case 410:
                NhiemVu八ChuyenChuc(num, num2, NhiemVuGiaiDoanID);
                return;
            case 699:
            case 748:
            case 749:
                NhiemVu十ChuyenChuc(num, num2, NhiemVuGiaiDoanID);
                return;
            case 615:
            case 617:
            case 620:
                NhiemVu九ChuyenChuc(num, num2, NhiemVuGiaiDoanID);
                return;
            case 9998:
                NhiemVu9998(num, num2, NhiemVuGiaiDoanID);
                return;
            case 9202:
                NhiemVu9202(num, num2, NhiemVuGiaiDoanID);
                return;
            case 1002:
                NhiemVu1002(num, num2, NhiemVuGiaiDoanID);
                return;
            case 1003:
                NhiemVu1003(num, num2, NhiemVuGiaiDoanID);
                return;
            case 1004:
                NhiemVu1004(num, num2, NhiemVuGiaiDoanID);
                return;
            case 1005:
                NhiemVu1005(num, num2, NhiemVuGiaiDoanID);
                return;
            case 1006:
                NhiemVu1006(num, num2, NhiemVuGiaiDoanID);
                return;
        }

        if (num2 == 1) TaskReminder(num, 11, NhiemVuGiaiDoanID);
        if (num2 == 3) TaskReminder(num, 31, NhiemVuGiaiDoanID);
        if (num2 == 5) TaskReminder(num, 51, NhiemVuGiaiDoanID);
    }

    public bool NhiemVu1002(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        while (true)
        {
            switch (Play.CharacterBeast == null ? 5 : 4)
            {
                case 4:
                    if (Play.CharacterBeast.FLD_LEVEL >= 15) goto case 0;
                    break;
                case 0:
                case 3:
                    if (Play.CharacterBeast.FLD_JOB_LEVEL == 0) goto case 1;
                    break;
                case 1:
                    Play.SpiritBeastToClass(0, 1);
                    Play.CharacterBeast.CalculateBasicData();
                    Play.UpdateSpiritBeastHP_MP_SP();
                    Play.UpdateSpiritBeastMartialArtsAndStatus();
                    Play.UpdateTheWeightOfTheBeast();
                    Play.SummonUpdateShowsEquippedItems(Play);
                    TaskReminder(NhiemVuID, 10, 0);
                    Play.UpdateCharacterTask();
                    return true;
                case 5:
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                    return false;
                default:
                    continue;
            }

            break;
        }

        TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
        return false;
    }

    public bool NhiemVu1003(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        while (true)
        {
            switch (Play.CharacterBeast == null ? 2 : 0)
            {
                case 0:
                    if (Play.CharacterBeast.FLD_LEVEL >= 50) goto case 3;
                    goto IL_0091;
                case 2:
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                    return false;
                case 3:
                case 4:
                    if (Play.CharacterBeast.FLD_JOB_LEVEL == 1) break;
                    goto IL_0091;
                default:
                    continue;
                case 5:
                    break;
                    IL_0091:
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                    return false;
            }

            break;
        }

        Play.SpiritBeastToClass(1, 2);
        Play.CharacterBeast.CalculateBasicData();
        Play.UpdateSpiritBeastHP_MP_SP();
        Play.UpdateSpiritBeastMartialArtsAndStatus();
        Play.UpdateTheWeightOfTheBeast();
        Play.SummonUpdateShowsEquippedItems(Play);
        TaskReminder(NhiemVuID, 10, 0);
        Play.UpdateCharacterTask();
        return true;
    }

    public bool NhiemVu1004(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        while (true)
        {
            switch (Play.CharacterBeast == null ? 2 : 0)
            {
                case 0:
                    if (Play.CharacterBeast.FLD_LEVEL >= 50) goto case 3;
                    goto IL_0091;
                case 2:
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                    return false;
                case 3:
                case 4:
                    if (Play.CharacterBeast.FLD_JOB_LEVEL == 1) break;
                    goto IL_0091;
                default:
                    continue;
                case 5:
                    break;
                    IL_0091:
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                    return false;
            }

            break;
        }

        Play.SpiritBeastToClass(2, 2);
        Play.CharacterBeast.CalculateBasicData();
        Play.UpdateSpiritBeastHP_MP_SP();
        Play.UpdateSpiritBeastMartialArtsAndStatus();
        Play.UpdateTheWeightOfTheBeast();
        Play.SummonUpdateShowsEquippedItems(Play);
        Play.UpdateCharacterTask();
        return true;
    }

    public bool NhiemVu1005(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        while (true)
        {
            switch (Play.CharacterBeast == null ? 2 : 0)
            {
                case 0:
                    if (Play.CharacterBeast.FLD_LEVEL >= 75) goto case 3;
                    goto IL_0091;
                case 2:
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                    return false;
                case 3:
                case 4:
                    if (Play.CharacterBeast.FLD_JOB_LEVEL == 2) break;
                    goto IL_0091;
                default:
                    continue;
                case 5:
                    break;
                    IL_0091:
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                    return false;
            }

            break;
        }

        Play.SpiritBeastToClass(1, 3);
        Play.CharacterBeast.CalculateBasicData();
        Play.UpdateSpiritBeastHP_MP_SP();
        Play.UpdateSpiritBeastMartialArtsAndStatus();
        Play.UpdateTheWeightOfTheBeast();
        Play.SummonUpdateShowsEquippedItems(Play);
        Play.UpdateCharacterTask();
        return true;
    }

    public bool NhiemVu1006(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        while (true)
        {
            switch (Play.CharacterBeast == null ? 2 : 0)
            {
                case 0:
                    if (Play.CharacterBeast.FLD_LEVEL >= 75) goto case 3;
                    goto IL_0091;
                case 2:
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                    return false;
                case 3:
                case 4:
                    if (Play.CharacterBeast.FLD_JOB_LEVEL == 2) break;
                    goto IL_0091;
                default:
                    continue;
                case 5:
                    break;
                    IL_0091:
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                    return false;
            }

            break;
        }

        Play.SpiritBeastToClass(2, 3);
        Play.CharacterBeast.CalculateBasicData();
        Play.UpdateSpiritBeastHP_MP_SP();
        Play.UpdateSpiritBeastMartialArtsAndStatus();
        Play.UpdateTheWeightOfTheBeast();
        Play.SummonUpdateShowsEquippedItems(Play);
        Play.UpdateCharacterTask();
        return true;
    }

    public void TaskReminder(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        var array = Converter.HexStringToByte("AA551400000084000600120033000000000000000000000055AA");
        System.Buffer.BlockCopy(BitConverter.GetBytes(NhiemVuID), 0, array, 10, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(ThaoTacD), 0, array, 12, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(NhiemVuGiaiDoanID), 0, array, 14, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(Play.CharacterFullServerID), 0, array, 4, 2);
        Play.Client?.Send_Map_Data(array, array.Length);
    }

    public void NhiemVu18(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        var num = 0;
        switch (ThaoTacD)
        {
            case 1:
                if (Play.Player_Level >= 10 && Play.Player_Job_level < 1)
                    TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
                else
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                break;
            case 2:
            {
                Play.CharacterToProfession(0, 1);
                TaskReminder(NhiemVuID, 21, 1);
                设置NhiemVu(NhiemVuID, 1);
                Play.NewLearningQigong(5, 0);
                var play = Play;
                play.Player_Money = play.Player_Money;
                Play.UpdateMoneyAndWeight();
                num = Play.GetParcelVacancy(Play);
                if (num != -1) Play.IncreaseItemWithAttributes(0, num, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                Play.UpdateCharacterData(Play);
                Play.UpdateMartialArtsAndStatus();
                break;
            }
            case 3:
                TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                break;
            case 4:
                if (Play.NhiemVu.ContainsKey(NhiemVuID)) Play.NhiemVu.Remove(NhiemVuID);
                TaskReminder(NhiemVuID, 41, NhiemVuGiaiDoanID);
                break;
            case 5:
                TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
                break;
        }
    }

    public void NhiemVu11(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        switch (ThaoTacD)
        {
            case 1:
                if (Play.Player_Level >= 35 && Play.Player_Job_level < 2 && Play.Player_Zx == 0)
                    TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
                else
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                break;
            case 2:
            {
                Play.CharacterToProfession(1, 2);
                TaskReminder(NhiemVuID, 21, 1);
                设置NhiemVu(NhiemVuID, 1);
                Play.NewLearningQigong(6, 0);
                var play = Play;
                play.Player_Money = play.Player_Money;
                Play.ThanNuVoCongDiemSo += 5;
                Play.UpdateMoneyAndWeight();
                Play.UpdateCharacterData(Play);
                Play.UpdateEquipmentEffectTo(Play, Play);
                Play.UpdateMartialArtsAndStatus();
                break;
            }
            case 3:
                TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                break;
            case 4:
                if (Play.NhiemVu.ContainsKey(NhiemVuID)) Play.NhiemVu.Remove(NhiemVuID);
                TaskReminder(NhiemVuID, 41, NhiemVuGiaiDoanID);
                break;
            case 5:
                TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
                break;
        }
    }

    public void NhiemVu12(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        switch (ThaoTacD)
        {
            case 1:
                if (Play.Player_Level >= 35 && Play.Player_Job_level < 2 && Play.Player_Zx == 0)
                    TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
                else
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                break;
            case 2:
            {
                Play.CharacterToProfession(2, 2);
                TaskReminder(NhiemVuID, 21, 1);
                设置NhiemVu(NhiemVuID, 1);
                Play.NewLearningQigong(6, 0);
                var play = Play;
                play.Player_Money = play.Player_Money;
                Play.ThanNuVoCongDiemSo += 5;
                Play.UpdateMoneyAndWeight();
                Play.UpdateCharacterData(Play);
                Play.UpdateEquipmentEffectTo(Play, Play);
                Play.UpdateMartialArtsAndStatus();
                break;
            }
            case 3:
                TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                break;
            case 4:
                if (Play.NhiemVu.ContainsKey(NhiemVuID)) Play.NhiemVu.Remove(NhiemVuID);
                TaskReminder(NhiemVuID, 41, NhiemVuGiaiDoanID);
                break;
            case 5:
                TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
                break;
        }
    }

    public void NhiemVu45(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        var num = 0;
        var num2 = 0;
        switch (ThaoTacD)
        {
            case 1:
                if (Play.Player_Level >= 60 && Play.Player_Job_level < 3 && Play.Player_Zx != 0)
                    TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
                else
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                break;
            case 2:
            {
                Play.CharacterToProfession(Play.Player_Zx, 3);
                TaskReminder(NhiemVuID, 21, 1);
                设置NhiemVu(NhiemVuID, 1);
                Play.NewLearningQigong(7, 0);
                var play = Play;
                play.Player_Money = play.Player_Money;
                Play.ThanNuVoCongDiemSo += 5;
                Play.UpdateMoneyAndWeight();
                Play.UpdateMartialArtsAndStatus();
                Play.LoadCharacterWearItem();
                Play.UpdateCharacterData(Play);
                num = Play.GetParcelVacancy(Play);
                if (num != -1) Play.IncreaseItemWithAttributes(0, num, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                num2 = Play.GetParcelVacancy(Play);
                if (num2 != -1) Play.IncreaseItemWithAttributes(0, num2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                break;
            }
            case 3:
                TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                break;
            case 4:
                if (Play.NhiemVu.ContainsKey(NhiemVuID)) Play.NhiemVu.Remove(NhiemVuID);
                TaskReminder(NhiemVuID, 41, NhiemVuGiaiDoanID);
                break;
            case 5:
                TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
                break;
        }
    }

    public void NhiemVu46(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        var num = 0;
        var num2 = 0;
        switch (ThaoTacD)
        {
            case 1:
                if (Play.Player_Level >= 60 && Play.Player_Job_level < 3 && Play.Player_Zx != 0)
                    TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
                else
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                break;
            case 2:
            {
                Play.CharacterToProfession(Play.Player_Zx, 3);
                TaskReminder(NhiemVuID, 21, 1);
                设置NhiemVu(NhiemVuID, 1);
                Play.NewLearningQigong(7, 0);
                var play = Play;
                play.Player_Money = play.Player_Money;
                Play.ThanNuVoCongDiemSo += 5;
                Play.UpdateMoneyAndWeight();
                Play.UpdateCharacterData(Play);
                Play.UpdateMartialArtsAndStatus();
                num = Play.GetParcelVacancy(Play);
                if (num != -1) Play.IncreaseItemWithAttributes(0, num, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                num2 = Play.GetParcelVacancy(Play);
                if (num2 != -1) Play.IncreaseItemWithAttributes(0, num2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                break;
            }
            case 3:
                TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                break;
            case 4:
                if (Play.NhiemVu.ContainsKey(NhiemVuID)) Play.NhiemVu.Remove(NhiemVuID);
                TaskReminder(NhiemVuID, 41, NhiemVuGiaiDoanID);
                break;
            case 5:
                TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
                break;
        }
    }

    public void NhiemVu73(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        var num = 0;
        var num12 = 0;
        var num13 = 0;
        var num14 = 0;
        var num2 = 0;
        if (ThaoTacD == 1)
        {
            if (Play.Player_Level >= 80 && Play.Player_Job_level < 4 && Play.Player_Zx != 0)
                TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
            else
                TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
        }

        switch (ThaoTacD)
        {
            case 2:
            {
                Play.CharacterToProfession(Play.Player_Zx, 4);
                TaskReminder(NhiemVuID, 21, 1);
                设置NhiemVu(NhiemVuID, 1);
                Play.NewLearningQigong(8, 0);
                Play.NewLearningQigong(9, 0);
                var play = Play;
                play.Player_Money = play.Player_Money;
                Play.ThanNuVoCongDiemSo += 5;
                Play.UpdateCharacterData(Play);
                Play.UpdateMoneyAndWeight();
                Play.UpdateMartialArtsAndStatus();
                num14 = Play.GetParcelVacancy(Play);
                if (num14 != -1)
                {
                    num13 = 0;
                    if (Play.Player_Job == 1)
                    {
                        var num3 = Play.Player_Zx != 1 ? 100202028 : 100201027;
                        num13 = num3;
                    }
                    else if (Play.Player_Job == 2)
                    {
                        var num4 = Play.Player_Zx != 1 ? 200202028 : 200201027;
                        num13 = num4;
                    }
                    else if (Play.Player_Job == 3)
                    {
                        var num5 = Play.Player_Zx != 1 ? 300202028 : 300201027;
                        num13 = num5;
                    }
                    else if (Play.Player_Job == 4)
                    {
                        var num6 = Play.Player_Zx != 1 ? 400202028 : 400201027;
                        num13 = num6;
                    }
                    else if (Play.Player_Job == 5)
                    {
                        var num7 = Play.Player_Zx != 1 ? 500202028 : 500201027;
                        num13 = num7;
                    }
                    else if (Play.Player_Job == 6)
                    {
                        var num8 = Play.Player_Zx != 1 ? 700202028 : 700201027;
                        num13 = num8;
                    }
                    else if (Play.Player_Job == 7)
                    {
                        var num9 = Play.Player_Zx != 1 ? 800202028 : 800201027;
                        num13 = num9;
                    }
                    else if (Play.Player_Job == 8)
                    {
                        num13 = 100204005;
                    }
                    else if (Play.Player_Job == 9)
                    {
                        num13 = 200204005;
                    }
                    else if (Play.Player_Job == 10)
                    {
                        var num10 = Play.Player_Zx != 1 ? 900202030 : 900201029;
                        num13 = num10;
                    }
                    else if (Play.Player_Job == 11)
                    {
                        var num11 = Play.Player_Zx != 1 ? 400204015 : 400204005;
                        num13 = num11;
                    }
                    else if (Play.Player_Job == 12)
                    {
                        num13 = 300204005;
                    }
                    else if (Play.Player_Job == 13)
                    {
                        num13 = 500204005;
                    }

                    if (Play.Player_Job != 4 && Play.Player_Job != 6)
                        Play.IncreaseItemWithAttributes(num13, num14, 1, 10000007, 70000023, 70000023, 70000023,
                            70000023, 0, 0, 0, 0, 0);
                    else
                        Play.IncreaseItemWithAttributes(num13, num14, 1, 10000007, 10000013, 10000013, 10000013,
                            10000013, 0, 0, 0, 0, 0);
                }

                num2 = Play.GetParcelVacancy(Play);
                if (num2 != -1) Play.IncreaseItemWithAttributes(0, num2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                num = Play.GetParcelVacancy(Play);
                if (num != -1) Play.IncreaseItemWithAttributes(0, num, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                num12 = Play.GetParcelVacancy(Play);
                if (num12 != -1) Play.IncreaseItemWithAttributes(0, num12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                break;
            }
            case 3:
                TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                break;
            case 4:
                if (Play.NhiemVu.ContainsKey(NhiemVuID)) Play.NhiemVu.Remove(NhiemVuID);
                TaskReminder(NhiemVuID, 41, NhiemVuGiaiDoanID);
                break;
            case 5:
                TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
                break;
        }
    }

    public void NhiemVu74(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        var num = 0;
        var num12 = 0;
        var num13 = 0;
        var num14 = 0;
        var num2 = 0;
        switch (ThaoTacD)
        {
            case 1:
                if (Play.Player_Level >= 80 && Play.Player_Job_level < 4 && Play.Player_Zx != 0)
                    TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
                else
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                break;
            case 2:
            {
                Play.CharacterToProfession(Play.Player_Zx, 4);
                TaskReminder(NhiemVuID, 21, 1);
                设置NhiemVu(NhiemVuID, 1);
                Play.NewLearningQigong(8, 0);
                Play.NewLearningQigong(9, 0);
                var play = Play;
                play.Player_Money = play.Player_Money;
                Play.ThanNuVoCongDiemSo += 5;
                Play.UpdateCharacterData(Play);
                Play.UpdateMoneyAndWeight();
                Play.UpdateMartialArtsAndStatus();
                num13 = Play.GetParcelVacancy(Play);
                if (num13 != -1)
                {
                    num2 = 0;
                    if (Play.Player_Job == 1)
                    {
                        var num3 = Play.Player_Zx != 1 ? 100202028 : 100201027;
                        num2 = num3;
                    }
                    else if (Play.Player_Job == 2)
                    {
                        var num4 = Play.Player_Zx != 1 ? 200202028 : 200201027;
                        num2 = num4;
                    }
                    else if (Play.Player_Job == 3)
                    {
                        var num5 = Play.Player_Zx != 1 ? 300202028 : 300201027;
                        num2 = num5;
                    }
                    else if (Play.Player_Job == 4)
                    {
                        var num6 = Play.Player_Zx != 1 ? 400202028 : 400201027;
                        num2 = num6;
                    }
                    else if (Play.Player_Job == 5)
                    {
                        var num7 = Play.Player_Zx != 1 ? 500202028 : 500201027;
                        num2 = num7;
                    }
                    else if (Play.Player_Job == 6)
                    {
                        var num8 = Play.Player_Zx != 1 ? 700202028 : 700201027;
                        num2 = num8;
                    }
                    else if (Play.Player_Job == 7)
                    {
                        var num9 = Play.Player_Zx != 1 ? 800202028 : 800201027;
                        num2 = num9;
                    }
                    else if (Play.Player_Job == 8)
                    {
                        num2 = 100204005;
                    }
                    else if (Play.Player_Job == 9)
                    {
                        num2 = 200204005;
                    }
                    else if (Play.Player_Job == 10)
                    {
                        var num10 = Play.Player_Zx != 1 ? 900202030 : 900201029;
                        num2 = num10;
                    }
                    else if (Play.Player_Job == 11)
                    {
                        var num11 = Play.Player_Zx != 1 ? 400204015 : 400204005;
                        num2 = num11;
                    }
                    else if (Play.Player_Job == 12)
                    {
                        num2 = 300204005;
                    }
                    else if (Play.Player_Job == 13)
                    {
                        num2 = 500204005;
                    }

                    if (Play.Player_Job != 4 && Play.Player_Job != 6)
                        Play.IncreaseItemWithAttributes(num2, num13, 1, 10000007, 70000023, 70000023, 70000023,
                            70000023, 0, 0, 0, 0, 0);
                    else
                        Play.IncreaseItemWithAttributes(num2, num13, 1, 10000007, 10000013, 10000013, 10000013,
                            10000013, 0, 0, 0, 0, 0);
                }

                num = Play.GetParcelVacancy(Play);
                if (num != -1) Play.IncreaseItemWithAttributes(0, num, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                num12 = Play.GetParcelVacancy(Play);
                if (num12 != -1) Play.IncreaseItemWithAttributes(0, num12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                num14 = Play.GetParcelVacancy(Play);
                if (num14 != -1) Play.IncreaseItemWithAttributes(0, num14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                break;
            }
            case 3:
                TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                break;
            case 4:
                if (Play.NhiemVu.ContainsKey(NhiemVuID)) Play.NhiemVu.Remove(NhiemVuID);
                TaskReminder(NhiemVuID, 41, NhiemVuGiaiDoanID);
                break;
            case 5:
                TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
                break;
        }
    }

    public void NhiemVu178(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        var num = 0;
        var num2 = 0;
        var num3 = 0;
        switch (ThaoTacD)
        {
            case 1:
                if (Play.Player_Level >= 100 && Play.Player_Job_level < 5 && Play.Player_Zx != 0)
                    TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
                else
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                break;
            case 2:
            {
                Play.CharacterToProfession(Play.Player_Zx, 5);
                TaskReminder(NhiemVuID, 21, 1);
                设置NhiemVu(NhiemVuID, 1);
                Play.NewLearningQigong(10, 0);
                Play.LearningSkills(0, 25);
                Play.LearningSkills(0, 26);
                Play.LearningSkills(0, 27);
                Play.UpdateCharacterData(Play);
                Play.UpdateKinhNghiemVaTraiNghiem();
                var play = Play;
                play.Player_Money = play.Player_Money;
                Play.ThanNuVoCongDiemSo += 5;
                Play.UpdateMoneyAndWeight();
                Play.UpdateMartialArtsAndStatus();
                num2 = Play.GetParcelVacancy(Play);
                if (num2 != -1) Play.IncreaseItemWithAttributes(0, num2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                num = Play.GetParcelVacancy(Play);
                if (num != -1) Play.IncreaseItemWithAttributes(0, num, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                num3 = Play.GetParcelVacancy(Play);
                if (num3 != -1) Play.IncreaseItemWithAttributes(0, num3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                break;
            }
            case 3:
                TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                break;
            case 4:
                if (Play.NhiemVu.ContainsKey(NhiemVuID)) Play.NhiemVu.Remove(NhiemVuID);
                TaskReminder(NhiemVuID, 41, NhiemVuGiaiDoanID);
                break;
            case 5:
                TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
                break;
        }
    }

    public void NhiemVu300(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        var num2 = 0;
        var num3 = 0;
        var num4 = 0;
        var num5 = 0;
        switch (ThaoTacD)
        {
            case 1:
                if (Play.Player_Level >= 115 && Play.Player_Job_level < 6 && Play.Player_Zx != 0)
                    TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
                else
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                break;
            case 2:
                num4 = Play.GetParcelVacancy(Play);
                if (num4 != -1)
                {
                    Play.CharacterToProfession(Play.Player_Zx, 6);
                    TaskReminder(NhiemVuID, 21, 1);
                    设置NhiemVu(NhiemVuID, 1);
                    Play.UpdateCharacterData(Play);
                    Play.UpdateKinhNghiemVaTraiNghiem();
                    Play.LoadCharacterWearItem();
                    var play = Play;
                    play.Player_Money = play.Player_Money;
                    Play.ThanNuVoCongDiemSo += 5;
                    Play.UpdateMoneyAndWeight();
                    Play.UpdateMartialArtsAndStatus();
                    num5 = Play.GetParcelVacancy(Play);
                    if (num5 != -1) Play.IncreaseItemWithAttributes(0, num5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                    num2 = Play.GetParcelVacancy(Play);
                    if (num2 != -1) Play.IncreaseItemWithAttributes(0, num2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                    num3 = Play.GetParcelVacancy(Play);
                    if (num3 != -1) Play.IncreaseItemWithAttributes(0, num3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                }
                else
                {
                    Play.HeThongNhacNho("Không còn chỗ trống", 10, "Túi đồ");
                    TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                }

                break;
            case 3:
                TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                break;
            case 4:
                if (Play.NhiemVu.ContainsKey(NhiemVuID)) Play.NhiemVu.Remove(NhiemVuID);
                TaskReminder(NhiemVuID, 41, NhiemVuGiaiDoanID);
                break;
            case 5:
                TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
                break;
        }
    }

    public void NhiemVu301(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        var num2 = 0;
        var num3 = 0;
        var num4 = 0;
        var num5 = 0;
        switch (ThaoTacD)
        {
            case 1:
                if (Play.Player_Level >= 120 && Play.Player_Job_level < 7 && Play.Player_Zx != 0)
                    TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
                else
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                break;
            case 2:
                num4 = Play.GetParcelVacancy(Play);
                if (num4 != -1)
                {
                    Play.CharacterToProfession(Play.Player_Zx, 7);
                    TaskReminder(NhiemVuID, 21, 1);
                    设置NhiemVu(NhiemVuID, 1);
                    Play.UpdateCharacterData(Play);
                    Play.UpdateKinhNghiemVaTraiNghiem();
                    Play.LoadCharacterWearItem();
                    var play = Play;
                    play.Player_Money = play.Player_Money;
                    Play.ThanNuVoCongDiemSo += 5;
                    Play.UpdateMoneyAndWeight();
                    Play.UpdateMartialArtsAndStatus();
                    num3 = Play.GetParcelVacancy(Play);
                    if (num3 != -1) Play.IncreaseItemWithAttributes(0, num3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                    num5 = Play.GetParcelVacancy(Play);
                    if (num5 != -1) Play.IncreaseItemWithAttributes(0, num5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                    num2 = Play.GetParcelVacancy(Play);
                    if (num2 != -1) Play.IncreaseItemWithAttributes(0, num2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                }
                else
                {
                    Play.HeThongNhacNho("Không còn chỗ trống", 10, "Túi đồ");
                    TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                }

                break;
            case 3:
                TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                break;
            case 4:
                if (Play.NhiemVu.ContainsKey(NhiemVuID)) Play.NhiemVu.Remove(NhiemVuID);
                TaskReminder(NhiemVuID, 41, NhiemVuGiaiDoanID);
                break;
            case 5:
                TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
                break;
        }
    }

    public void NhiemVu八ChuyenChuc(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        var num2 = 0;
        var num3 = 0;
        var num4 = 0;
        var num5 = 0;
        switch (ThaoTacD)
        {
            case 1:
                if (Play.Player_Level >= 130 && Play.Player_Job_level < 8 && Play.Player_Zx != 0)
                    TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
                else
                    TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
                break;
            case 2:
                num4 = Play.GetParcelVacancy(Play);
                if (num4 != -1)
                {
                    Play.CharacterToProfession(Play.Player_Zx, 8);
                    TaskReminder(NhiemVuID, 21, 1);
                    设置NhiemVu(NhiemVuID, 1);
                    Play.UpdateCharacterData(Play);
                    Play.UpdateKinhNghiemVaTraiNghiem();
                    Play.LoadCharacterWearItem();
                    var play = Play;
                    play.Player_Money = play.Player_Money;
                    Play.ThanNuVoCongDiemSo += 5;
                    Play.UpdateMoneyAndWeight();
                    Play.UpdateMartialArtsAndStatus();
                    num3 = Play.GetParcelVacancy(Play);
                    if (num3 != -1) Play.IncreaseItemWithAttributes(0, num3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                    num5 = Play.GetParcelVacancy(Play);
                    if (num5 != -1) Play.IncreaseItemWithAttributes(0, num5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                    num2 = Play.GetParcelVacancy(Play);
                    if (num2 != -1) Play.IncreaseItemWithAttributes(0, num2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                }
                else
                {
                    Play.HeThongNhacNho("Không còn chỗ trống", 10, "Túi đồ");
                    TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                }

                break;
            case 3:
                TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                break;
            case 4:
                if (Play.NhiemVu.ContainsKey(NhiemVuID)) Play.NhiemVu.Remove(NhiemVuID);
                TaskReminder(NhiemVuID, 41, NhiemVuGiaiDoanID);
                break;
            case 5:
                TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
                break;
        }
    }

    public void NhiemVu九ChuyenChuc(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        var num2 = 0;
        var num3 = 0;
        var num4 = 0;
        var num5 = 0;
        switch (ThaoTacD)
        {
            case 1:
                if (Play.Player_Level >= 140 && Play.Player_Job_level < 9 && Play.Player_Zx != 0)
                    TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
                else
                    TaskReminder(NhiemVuID, 10, NhiemVuGiaiDoanID);
                break;
            case 2:
                num4 = Play.GetParcelVacancy(Play);
                if (num4 != -1)
                {
                    Play.CharacterToProfession(Play.Player_Zx, 9);
                    TaskReminder(NhiemVuID, 21, 1);
                    设置NhiemVu(NhiemVuID, 1);
                    var play = Play;
                    play.Player_Money = play.Player_Money;
                    Play.ThanNuVoCongDiemSo += 5;
                    Play.UpdateCharacterData(Play);
                    Play.UpdateKinhNghiemVaTraiNghiem();
                    Play.LoadCharacterWearItem();
                    Play.UpdateMoneyAndWeight();
                    Play.UpdateMartialArtsAndStatus();
                    num3 = Play.GetParcelVacancy(Play);
                    if (num3 != -1) Play.IncreaseItemWithAttributes(0, num3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                    num5 = Play.GetParcelVacancy(Play);
                    if (num5 != -1) Play.IncreaseItemWithAttributes(0, num5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                    num2 = Play.GetParcelVacancy(Play);
                    if (num2 != -1) Play.IncreaseItemWithAttributes(0, num2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                }
                else
                {
                    Play.HeThongNhacNho("Không còn chỗ trống", 10, "Túi đồ");
                    TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                }

                break;
            case 3:
                TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                break;
            case 4:
                if (Play.NhiemVu.ContainsKey(NhiemVuID)) Play.NhiemVu.Remove(NhiemVuID);
                TaskReminder(NhiemVuID, 41, NhiemVuGiaiDoanID);
                break;
            case 5:
                TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
                break;
        }
    }

    public void NhiemVu十ChuyenChuc(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        var num2 = 0;
        var num3 = 0;
        var num4 = 0;
        switch (ThaoTacD)
        {
            case 1:
                if (Play.Player_Level >= 150 && Play.Player_Job_level < 10 && Play.Player_Zx != 0)
                    TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
                else
                    TaskReminder(NhiemVuID, 10, NhiemVuGiaiDoanID);
                break;
            case 2:
                if (Play.GetParcelVacancy(Play) != -1)
                {
                    Play.CharacterToProfession(Play.Player_Zx, 10);
                    TaskReminder(NhiemVuID, 21, 1);
                    设置NhiemVu(NhiemVuID, 1);
                    var play = Play;
                    play.Player_Money = play.Player_Money;
                    Play.ThanNuVoCongDiemSo += 5;
                    Play.UpdateCharacterData(Play);
                    Play.UpdateKinhNghiemVaTraiNghiem();
                    Play.LoadCharacterWearItem();
                    Play.UpdateMoneyAndWeight();
                    Play.UpdateMartialArtsAndStatus();
                    num4 = Play.GetParcelVacancy(Play);
                    if (num4 != -1) Play.IncreaseItemWithAttributes(0, num4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                    num2 = Play.GetParcelVacancy(Play);
                    if (num2 != -1) Play.IncreaseItemWithAttributes(0, num2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                    num3 = Play.GetParcelVacancy(Play);
                    if (num3 != -1) Play.IncreaseItemWithAttributes(0, num3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
                }
                else
                {
                    Play.HeThongNhacNho("Không còn chỗ trống", 10, "Túi đồ");
                    TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                }

                break;
            case 3:
                TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                break;
            case 4:
                if (Play.NhiemVu.ContainsKey(NhiemVuID)) Play.NhiemVu.Remove(NhiemVuID);
                TaskReminder(NhiemVuID, 41, NhiemVuGiaiDoanID);
                break;
            case 5:
                TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
                break;
        }
    }

    public void 设置NhiemVu(int NhiemVuID, int NhiemVuGiaiDoanID)
    {
        X_Nhiem_Vu_Loai NhiemVuClass = null;
        X_Nhiem_Vu_Loai value = null;
        if (Play.NhiemVu.TryGetValue(NhiemVuID, out value))
        {
            value.NhiemVuGiaiDoanID = NhiemVuGiaiDoanID;
            return;
        }

        NhiemVuClass = new X_Nhiem_Vu_Loai();
        NhiemVuClass.NhiemVuID = NhiemVuID;
        NhiemVuClass.NhiemVuGiaiDoanID = NhiemVuGiaiDoanID;
        if (!Play.NhiemVu.ContainsKey(NhiemVuID)) Play.NhiemVu.Add(NhiemVuID, NhiemVuClass);
    }

    public void NhiemVu9998(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        var num = 0;
        if (NhiemVuGiaiDoanID == 0)
        {
            switch (ThaoTacD)
            {
                case 4:
                    break;
                case 1:
                    TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
                    break;
                case 2:
                    TaskReminder(NhiemVuID, 21, NhiemVuGiaiDoanID);
                    设置NhiemVu(NhiemVuID, 1);
                    break;
                case 3:
                    TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                    break;
                case 5:
                    TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
                    break;
            }
        }
        else
        {
            num = Play.GetParcelVacancy(Play);
            if (num != -1)
            {
                Play.IncreaseItem2(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1700101), num,
                    BitConverter.GetBytes(1), new byte[56]);
                TaskReminder(NhiemVuID, 11, 2);
                设置NhiemVu(NhiemVuID, 2);
            }
            else
            {
                TaskReminder(NhiemVuID, 10, NhiemVuGiaiDoanID);
            }
        }
    }

    public void NhiemVu9202(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
    {
        var num = 0;
        var num2 = 0;
        var num3 = 0;
        var num4 = 0;
        var num5 = 0;
        var num6 = 0;
        var num7 = 0;
        if (NhiemVuGiaiDoanID == 0)
        {
            switch (ThaoTacD)
            {
                case 1:
                    TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
                    break;
                case 2:
                    TaskReminder(NhiemVuID, 21, NhiemVuGiaiDoanID);
                    设置NhiemVu(NhiemVuID, 1);
                    break;
                case 3:
                    TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
                    break;
            }

            return;
        }

        num = 0;
        num2 = 0;
        num6 = 0;
        num4 = 0;
        num7 = 0;
        for (num5 = 0; num5 < 36; num5++)
            if (BitConverter.ToInt32(Play.Item_In_Bag[num5].VatPham_ID, 0) == 1000000161)
                num = 1;
            else if (BitConverter.ToInt32(Play.Item_In_Bag[num5].VatPham_ID, 0) == 1000000162)
                num2 = 1;
            else if (BitConverter.ToInt32(Play.Item_In_Bag[num5].VatPham_ID, 0) == 1000000163)
                num6 = 1;
            else if (BitConverter.ToInt32(Play.Item_In_Bag[num5].VatPham_ID, 0) == 1000000164)
                num4 = 1;
            else if (BitConverter.ToInt32(Play.Item_In_Bag[num5].VatPham_ID, 0) == 1000000199) num7 = 1;
        if (num != 0 && num2 != 0 && num6 != 0 && num4 != 0 && num7 != 0)
        {
            for (num3 = 0; num3 < 36; num3++)
                if (BitConverter.ToInt32(Play.Item_In_Bag[num3].VatPham_ID, 0) == 1000000161)
                    Play.SubtractItems(num3, 1);
                else if (BitConverter.ToInt32(Play.Item_In_Bag[num3].VatPham_ID, 0) == 1000000162)
                    Play.SubtractItems(num3, 1);
                else if (BitConverter.ToInt32(Play.Item_In_Bag[num3].VatPham_ID, 0) == 1000000163)
                    Play.SubtractItems(num3, 1);
                else if (BitConverter.ToInt32(Play.Item_In_Bag[num3].VatPham_ID, 0) == 1000000164)
                    Play.SubtractItems(num3, 1);
                else if (BitConverter.ToInt32(Play.Item_In_Bag[num3].VatPham_ID, 0) == 1000000199)
                    Play.SubtractItems(num3, 1);
            Play.AddItems(BitConverter.GetBytes(RxjhClass.GetDBItmeId()), BitConverter.GetBytes(1000000365),
                Play.GetParcelVacancy(Play), BitConverter.GetBytes(1), new byte[56]);
            TaskReminder(NhiemVuID, 11, 2);
            设置NhiemVu(NhiemVuID, 3);
        }
        else
        {
            TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
        }
    }
}