using System;
using System.Net;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.Text;

namespace RxjhServer;

public class WGSockClient : IDisposable
{
    public Socket clientSocket;

    private readonly byte[] dataReceive;

    private bool disposed;

    private readonly RemoveWGClientDelegate removeFromTheServerList;

    public WGSockClient(Socket from, RemoveWGClientDelegate rftsl)
    {
        dataReceive = new byte[102400];
        disposed = false;
        removeFromTheServerList = rftsl;
        clientSocket = from;
    }

    public IPAddress IP
    {
        get
        {
            try
            {
                IPAddress result = null;
                while (true)
                {
                    object obj;
                    switch (!disposed ? 2 : 4)
                    {
                        case 0:
                        case 2:
                            obj = ((IPEndPoint)clientSocket.RemoteEndPoint).Address;
                            goto IL_0054;
                        case 4:
                            obj = null;
                            goto IL_0054;
                        default:
                            continue;
                        case 3:
                            break;
                            IL_0054:
                            result = (IPAddress)obj;
                            break;
                    }

                    break;
                }

                return result;
            }
            catch
            {
                Dispose();
            }

            return null;
        }
    }

    public void Dispose()
    {
        if (!disposed)
        {
            disposed = true;
            try
            {
                clientSocket.Shutdown(SocketShutdown.Both);
            }
            catch
            {
            }

            clientSocket?.Close();
            clientSocket = null;
            removeFromTheServerList?.Invoke(this);
        }
    }

    [DllImport("PkEnBk.dll", CharSet = CharSet.Ansi)]
    public static extern uint send_en(Socket socket_0, string astr1, int int_0, int flags);

    public virtual void OnReceiveData(IAsyncResult iasyncResult_0)
    {
        try
        {
            byte[] array = null;
            var num2 = 0;
            if (disposed) return;
            num2 = clientSocket.EndReceive(iasyncResult_0);
            if (num2 <= 0)
            {
                Dispose();
                return;
            }

            array = ProcessDataReceived(dataReceive, num2);
            if (array != null)
                clientSocket.BeginSend(array, 0, array.Length, SocketFlags.None, OnSended, this);
            else
                clientSocket.BeginReceive(dataReceive, 0, dataReceive.Length, SocketFlags.None, OnReceiveData, this);
        }
        catch (Exception ex)
        {
            Console.WriteLine("{0}", ex.Message);
            Dispose();
        }
    }

    public void OnSended(IAsyncResult iasyncResult_0)
    {
        try
        {
            if (!disposed)
            {
                clientSocket.EndSend(iasyncResult_0);
                clientSocket.BeginReceive((iasyncResult_0.AsyncState as WGSockClient).dataReceive, 0,
                    (iasyncResult_0.AsyncState as WGSockClient).dataReceive.Length, SocketFlags.None, OnReceiveData,
                    iasyncResult_0.AsyncState);
            }
        }
        catch
        {
        }
    }

    public void OnSended2(IAsyncResult iasyncResult_0)
    {
        try
        {
            if (!disposed) clientSocket.EndSend(iasyncResult_0);
        }
        catch (Exception)
        {
        }
    }

    public virtual byte[] ProcessDataReceived(byte[] data, int length)
    {
        return null;
    }

    public virtual byte[] DataReceived2(byte[] data, int length)
    {
        return null;
    }

    public virtual void Sendd(string string_0)
    {
        var bytes = Encoding.Default.GetBytes(string_0);
        发送(bytes, bytes.Length);
    }

    public virtual void 发送(byte[] toSendBuff, int int_0)
    {
        try
        {
            var array = new byte[int_0 + 6];
            array[0] = 170;
            array[1] = 102;
            System.Buffer.BlockCopy(Buffer.GetBytes(int_0), 0, array, 2, 4);
            System.Buffer.BlockCopy(toSendBuff, 0, array, 6, int_0);
            clientSocket.BeginSend(array, 0, int_0 + 6, SocketFlags.None, OnSended2, this);
        }
        catch (SocketException ex)
        {
            Form1.WriteLine(1, "Số tài khoản Server Gửi đi phạm sai lầm 333: " + ex.Message);
        }
        catch (Exception ex2)
        {
            Form1.WriteLine(1, "Số tài khoản Server Gửi đi phạm sai lầm 444: " + ex2.Message);
        }
    }

    public virtual void Send(byte[] toSendBuff, int int_0)
    {
        try
        {
            if (!disposed)
            {
                var array = new byte[int_0];
                System.Buffer.BlockCopy(toSendBuff, 0, array, 0, int_0);
                clientSocket.BeginSend(array, 0, int_0, SocketFlags.None, OnSended2, this);
            }
        }
        catch (Exception)
        {
        }
    }

    public virtual void Send(byte[] toSendBuff, int offset, int int_0)
    {
        try
        {
            byte[] array = null;
            if (!disposed)
            {
                array = new byte[int_0];
                System.Buffer.BlockCopy(toSendBuff, offset, array, 0, int_0);
                if (!disposed) clientSocket.BeginSend(array, 0, int_0, SocketFlags.None, OnSended2, this);
            }
        }
        catch (Exception)
        {
        }
    }

    public void Start()
    {
        clientSocket.BeginReceive(dataReceive, 0, dataReceive.Length, SocketFlags.None, OnReceiveData, this);
    }
}