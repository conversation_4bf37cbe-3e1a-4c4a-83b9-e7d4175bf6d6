using System;
using System.Timers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class X_Di_Thuong_Trang_Thai_Phong_Ngu_Loai : IDisposable
{
    public System.Timers.Timer NhanVatTrangThai;

    public Players Play;

    public DateTime time;

    public X_Di_Thuong_Trang_Thai_Phong_Ngu_Loai(Players Play_, int ThoiGian, int DiThuong_ID, int DiThuong_SoLuong)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường trạng thái loại -NEW 333");
        FLD_PID = DiThuong_ID;
        time = DateTime.Now;
        time = time.AddMilliseconds(ThoiGian);
        Play = Play_;
        NhanVatTrangThai = new System.Timers.Timer(ThoiGian);
        NhanVatTrangThai.Elapsed += TimeEndEvent1;
        NhanVatTrangThai.Enabled = true;
        NhanVatTrangThai.AutoReset = false;
        Trang_thai_hieu_qua(FLD_PID, 1, DiT<PERSON><PERSON>_SoLuong, ThoiGian / 1000);
    }

    public int FLD_PID { get; set; }

    public void Dispose()
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường trạng thái loại -Dispose 555");
        if (NhanVatTrangThai != null)
        {
            NhanVatTrangThai.Enabled = false;
            NhanVatTrangThai.Close();
            NhanVatTrangThai.Dispose();
            NhanVatTrangThai = null;
        }

        Play = null;
    }

    public void TimeEndEvent1(object sender, ElapsedEventArgs e)
    {
        var num2 = 0;
        while (true)
        {
            switch (num2)
            {
                default:
                    if (World.jlMsg == 1)
                    {
                        num2 = 2;
                        continue;
                    }

                    break;
                case 2:
                    Form1.WriteLine(0, "Thời gian kết thúc sự kiện 1 555");
                    num2 = 1;
                    continue;
                case 1:
                    break;
            }

            break;
        }

        ThoiGianKetThucSuKien();
    }

    public void ThoiGianKetThucSuKien()
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường trạng thái loại - Thời gian kết thúc sự kiện 666");
        try
        {
            if (Play != null)
            {
                if (FLD_PID == 14)
                {
                    Play.FLD_DuocPham_GiamBotPhongNgu += 0.1;
                    Play.UpdateMartialArtsAndStatus();
                }

                Play.TrangThai_PhongThu_BatThuong?.Remove(FLD_PID);
                Trang_thai_hieu_qua(FLD_PID, 0, 0, 0);
                Dispose();
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1,
                "Dị thường trạng thái loại Thời gian kết thúc sự kiệnPhạm sai lầm: 444 [" + FLD_PID + "]" + ex);
        }
        finally
        {
            Dispose();
        }
    }

    public void Trang_thai_hieu_qua(int 异常ID, int 开关, int 异常数量, int 时间)
    {
        byte[] array = null;
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường rơi lam trạng thái loại - Trạng thái hiệu quả 333");
        array = Converter.HexStringToByte(
            "AA5546003527401538008C0300002C0100000900000001000000000000006016A2496016A2492600000014000000000000008C030000E80300000900000001000000000000000000000055AA");
        System.Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 18, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 58, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(开关), 0, array, 22, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(开关), 0, array, 62, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(时间), 0, array, 38, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(异常数量), 0, array, 42, 4);
        if (Play != null)
        {
            System.Buffer.BlockCopy(BitConverter.GetBytes(Play.CharacterFullServerID), 0, array, 14, 2);
            System.Buffer.BlockCopy(BitConverter.GetBytes(Play.CharacterFullServerID), 0, array, 4, 2);
            Play.Client?.SendMultiplePackage(array, array.Length);
            Play.SendMultiplePacketsOfCurrentRangeBroadcastData(array, array.Length);
        }
    }
}