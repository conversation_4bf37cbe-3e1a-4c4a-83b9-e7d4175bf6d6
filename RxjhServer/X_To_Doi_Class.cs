using System;
using System.Collections.Generic;
using System.Linq;
using System.Timers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class X_To_Doi_Class : IDisposable
{
    public int _Slot_Give_Item_Count = 0;

    public System.Timers.Timer AutomaticDisplay;

    public int Check_Slot_In_Party = 1;

    public int DaoCuQuyTac_PhanPhoi;

    public int DoiNguDangCap;

    public Players DoiTruong;

    public string DoiTruongTen;

    private int GuiLoiMoi_ToDoi_DemSoLuong;

    public Players Moi_NguoiChoi;

    public int PhanBoHienTai;

    public bool RedPackage;

    public int RedPackageThoiGian;

    public int Slot_Give_Item_Random = 0;

    public int TeamID;
    public List<Players> tem = new();

    public ThreadSafeDictionary<int, Players> ToDoi_NguoiChoi;

    private ThreadSafeDictionary<string, X_Nguoi_Choi_Ngoai_Tuyen> ToDoi_NguoiChoi_Da_OffLine;

    public X_To_Doi_Class(Players Play)
    {
        AutomaticDisplay = new System.Timers.Timer(3000.0);
        AutomaticDisplay.Elapsed += AutomaticDisplayEvent;
        AutomaticDisplay.AutoReset = true;
        DoiTruongTen = Play.UserName;
        DoiTruong = Play;
        ToDoi_NguoiChoi = new ThreadSafeDictionary<int, Players>();
        ToDoi_NguoiChoi.Add(Play.CharacterFullServerID, Play);
        PhanBoHienTai = 0;
        DaoCuQuyTac_PhanPhoi = 1;
        RedPackage = false;
        RedPackageThoiGian = 0;
        DoiNguDangCap = Play.Player_Level;
        ToDoi_NguoiChoi_Da_OffLine = new ThreadSafeDictionary<string, X_Nguoi_Choi_Ngoai_Tuyen>();
    }

    public Players Leader => World.allConnectedChars.Values.Where(K => K.UserName == DoiTruongTen).FirstOrDefault();

    public void Dispose()
    {
        try
        {
            ToDoi_NguoiChoi_Da_OffLine?.Clear();
            if (World.WToDoi.TryGetValue(TeamID, out var _)) World.WToDoi.Remove(TeamID);
            if (ToDoi_NguoiChoi != null)
                foreach (var value2 in ToDoi_NguoiChoi.Values)
                {
                    value2.DisbandTeamTips();
                    value2.TeamID = 0;
                    value2.TeamingStage = 0;
                    value2.CoupleInTeam = false;
                }

            ToDoi_NguoiChoi?.Clear();
            if (AutomaticDisplay != null)
            {
                AutomaticDisplay.Enabled = false;
                AutomaticDisplay.Close();
                AutomaticDisplay.Dispose();
                AutomaticDisplay = null;
            }

            Moi_NguoiChoi = null;
            tem = null;
            DoiNguDangCap = 0;
            RedPackage = false;
            RedPackageThoiGian = 0;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "ToDoi类   Dispose   error!" + ex.Message);
        }
        finally
        {
            if (tem != null)
            {
                tem.Clear();
                tem = null;
            }

            if (ToDoi_NguoiChoi != null)
            {
                ToDoi_NguoiChoi.Dispose();
                ToDoi_NguoiChoi = null;
            }

            if (ToDoi_NguoiChoi_Da_OffLine != null)
            {
                ToDoi_NguoiChoi_Da_OffLine.Dispose();
                ToDoi_NguoiChoi_Da_OffLine = null;
            }

            if (World.WToDoi.ContainsKey(TeamID)) World.WToDoi.Remove(TeamID);
            if (AutomaticDisplay != null)
            {
                AutomaticDisplay.Enabled = false;
                AutomaticDisplay.Close();
                AutomaticDisplay.Dispose();
                AutomaticDisplay = null;
            }

            Moi_NguoiChoi = null;
            DoiNguDangCap = 0;
            RedPackage = false;
            RedPackageThoiGian = 0;
        }
    }

    ~X_To_Doi_Class()
    {
    }

    public Players ThanhVien_DatDuoc_TuongUng(int key)
    {
        try
        {
            var num = 0;
            foreach (var value in ToDoi_NguoiChoi.Values)
            {
                if (key == num) return value;
                num++;
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "ThanhVien_DatDuoc_TuongUng   error!" + ex.Message);
        }

        return null;
    }

    private void AutomaticDisplayEvent(object sender, ElapsedEventArgs e)
    {
        try
        {
            if (ToDoi_NguoiChoi != null && ToDoi_NguoiChoi.Count <= 1)
            {
                Dispose();
                return;
            }

            GuiLoiMoi_ToDoi_DemSoLuong++;
            if (GuiLoiMoi_ToDoi_DemSoLuong >= 15)
            {
                GuiLoiMoi_ToDoi_DemSoLuong = 0;
                if (ToDoi_NguoiChoi_Da_OffLine != null && ToDoi_NguoiChoi_Da_OffLine.Count > 0)
                {
                    var list = new List<string>();
                    foreach (var value3 in ToDoi_NguoiChoi_Da_OffLine.Values)
                    {
                        var players = World.KiemTra_Ten_NguoiChoi(value3.UserName);
                        if (players == null) continue;
                        if (value3.TeamID == TeamID)
                        {
                            if (DoiTruong.FindPlayers(1000, players))
                            {
                                if (players.TeamingStage == 0 && ToDoi_NguoiChoi.Count < 8 && players.TeamID == 0)
                                {
                                    var array = Converter.HexStringToByte(
                                        "AA5528002C0130000600010001002D010000000000000000000000000000000000000000000000000000000055AA");
                                    System.Buffer.BlockCopy(Buffer.GetBytes(DoiTruong.CharacterFullServerID), 0, array,
                                        4, 2);
                                    System.Buffer.BlockCopy(Buffer.GetBytes(players.CharacterFullServerID), 0, array,
                                        14, 2);
                                    DoiTruong.SendTeam(array, array.Length);
                                }
                                else if (players.TeamingStage == 1)
                                {
                                    var array2 =
                                        Converter.HexStringToByte("AA5512002C013200040001002C01000000000000000055AA");
                                    System.Buffer.BlockCopy(Buffer.GetBytes(DoiTruong.CharacterFullServerID), 0, array2,
                                        4, 2);
                                    System.Buffer.BlockCopy(Buffer.GetBytes(DoiTruong.CharacterFullServerID), 0, array2,
                                        12, 2);
                                    DoiTruong.ICancelTheTeam(array2, array2.Length);
                                }
                                else if (players.TeamingStage == 2 && !list.Contains(players.Userid))
                                {
                                    list.Add(players.Userid);
                                }
                            }
                        }
                        else if (!list.Contains(players.Userid))
                        {
                            list.Add(players.Userid);
                        }
                    }

                    if (list.Count > 0)
                        foreach (var item in list)
                            if (ToDoi_NguoiChoi_Da_OffLine.TryGetValue(item, out var _))
                                ToDoi_NguoiChoi_Da_OffLine.Remove(item);
                    list.Clear();
                }
            }

            if (RedPackage)
            {
                RedPackageThoiGian -= 3000;
                if (RedPackageThoiGian <= 0)
                {
                    RedPackage = false;
                    RedPackageThoiGian = 0;
                }
            }
            else
            {
                RedPackage = false;
                RedPackageThoiGian = 0;
            }

            Players value2;
            if (ToDoi_NguoiChoi != null)
                foreach (var value4 in ToDoi_NguoiChoi.Values)
                    if (World.allConnectedChars.TryGetValue(value4.CharacterFullServerID, out value2))
                    {
                        value4.ShowPlayers();
                        if (RedPackage && RedPackageThoiGian > 0)
                        {
                            if (value4.AppendStatusList != null && !value4.GetAddState(**********))
                            {
                                var ThemVaoTrangThaiClass =
                                    new X_Them_Vao_Trang_Thai_Loai(value4, RedPackageThoiGian, **********, 0);
                                value4.AppendStatusList.Add(ThemVaoTrangThaiClass.FLD_PID, ThemVaoTrangThaiClass);
                                value4.StatusEffect(Buffer.GetBytes(**********), 1, RedPackageThoiGian);
                            }
                        }
                        else if (value4.AppendStatusList != null && value4.GetAddState(**********))
                        {
                            value4.AppendStatusList[**********].ThoiGianKetThucSuKien();
                        }
                    }
                    else if (tem != null && !tem.Contains(value4))
                    {
                        tem.Add(value4);
                    }

            if (tem != null)
                foreach (var item2 in tem)
                    if (ToDoi_NguoiChoi != null && ToDoi_NguoiChoi.TryGetValue(item2.CharacterFullServerID, out value2))
                    {
                        ToDoi_NguoiChoi.Remove(item2.CharacterFullServerID);
                        item2.TeamID = 0;
                        item2.TeamingStage = 0;
                    }

            if (tem.Count > 0) tem.Clear();
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "ToDoi类   AutomaticDisplayEvent   error!" + ex.Message);
        }
    }

    public void UyNhiemDoiTruong(Players NguoiChoi, Players DoiTruong)
    {
        try
        {
            DoiTruongTen = DoiTruong.UserName;
            this.DoiTruong = DoiTruong;
            foreach (var value in ToDoi_NguoiChoi.Values)
            {
                value.TipsForAppointmentOfCaptain(NguoiChoi, DoiTruong);
                value.ShowPlayers();
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "UyNhiemDoiTruong   error!" + ex.Message);
        }
    }

    public void ThamGiaThanhVienNhom_NhacNho(Players NguoiChoi)
    {
        try
        {
            if (NguoiChoi.FLD_Couple.Length != 0)
                foreach (var value2 in ToDoi_NguoiChoi.Values)
                    if (value2.UserName == NguoiChoi.FLD_Couple)
                    {
                        NguoiChoi.CoupleInTeam = true;
                        value2.CoupleInTeam = true;
                        break;
                    }

            foreach (var value3 in ToDoi_NguoiChoi.Values)
            {
                if (NguoiChoi != value3)
                {
                    value3.TipsForJoiningATeam(NguoiChoi);
                    NguoiChoi.TipsForJoiningATeam(value3);
                }

                value3.ShowPlayers();
            }

            if (ToDoi_NguoiChoi.Count >= 2) AutomaticDisplay.Enabled = true;
            if (ToDoi_NguoiChoi_Da_OffLine.TryGetValue(NguoiChoi.Userid, out var _))
                ToDoi_NguoiChoi_Da_OffLine.Remove(NguoiChoi.Userid);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "ToDoi类   ThamGiaThanhVienNhom_NhacNho    error!" + ex.Message);
        }
    }

    public void DangXuatParty(Players NguoiChoi, int Exit_ID)
    {
        var num = 0;
        try
        {
            if (ToDoi_NguoiChoi != null && ToDoi_NguoiChoi.TryGetValue(NguoiChoi.CharacterFullServerID, out var _))
            {
                ToDoi_NguoiChoi.Remove(NguoiChoi.CharacterFullServerID);
                if (Exit_ID == 1 && ToDoi_NguoiChoi.Count >= 2 &&
                    !ToDoi_NguoiChoi_Da_OffLine.TryGetValue(NguoiChoi.Userid, out var _))
                    ToDoi_NguoiChoi_Da_OffLine.Add(NguoiChoi.Userid, new X_Nguoi_Choi_Ngoai_Tuyen
                    {
                        TeamID = NguoiChoi.TeamID,
                        UserName = NguoiChoi.UserName
                    });
            }

            if (NguoiChoi.GetAddState(**********)) NguoiChoi.AppendStatusList[**********].ThoiGianKetThucSuKien();
            if (World.PartyBonus_IconPID != 0 && NguoiChoi.GetAddState(World.PartyBonus_IconPID))
                NguoiChoi.AppendStatusList[World.PartyBonus_IconPID].ThoiGianKetThucSuKien();
            num = 1;
            if (NguoiChoi.FLD_Couple.Length != 0)
            {
                NguoiChoi.CoupleInTeam = false;
                num = 3;
                if (ToDoi_NguoiChoi != null)
                    foreach (var value3 in ToDoi_NguoiChoi.Values)
                    {
                        num = 4;
                        if (value3.UserName == NguoiChoi.FLD_Couple)
                        {
                            num = 5;
                            value3.CoupleInTeam = false;
                            num = 6;
                            break;
                        }
                    }
            }

            num = 7;
            if (ToDoi_NguoiChoi != null && ToDoi_NguoiChoi.Count >= 2)
            {
                num = 8;
                if (DoiTruongTen != NguoiChoi.UserName)
                {
                    foreach (var value4 in ToDoi_NguoiChoi.Values)
                    {
                        num = 9;
                        value4.TipsForLeavingTeam(NguoiChoi);
                        num = 10;
                        value4.ShowPlayers();
                        num = 11;
                    }
                }
                else
                {
                    var flag = true;
                    foreach (var value5 in ToDoi_NguoiChoi.Values)
                    {
                        if (flag)
                        {
                            num = 12;
                            UyNhiemDoiTruong(NguoiChoi, value5);
                            flag = false;
                        }

                        num = 15;
                        value5.HeThongNhacNho("Quyền đội trưởng chuyển sang " + DoiTruongTen, 8);
                        value5.TipsForLeavingTeam(NguoiChoi);
                        value5.ShowPlayers();
                        num = 16;
                    }
                }
            }
            else
            {
                num = 17;
                Dispose();
            }

            num = 18;
            NguoiChoi.ILeaveTheTeamPrompt();
            num = 19;
            NguoiChoi.TeamID = 0;
            num = 20;
            NguoiChoi.TeamingStage = 0;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "TodoiDangXuat   error!" + num + "|" + ex.Message);
            logo.FileBugTxtLog("TodoiDangXuat   error!" + num + "|" + ex.Message);
        }
        finally
        {
            NguoiChoi.TeamID = 0;
            NguoiChoi.TeamingStage = 0;
        }
    }
}