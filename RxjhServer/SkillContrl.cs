using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace RxjhServer;

public class SkillContrl : Form
{
    private Button button1;

    private ComboBox comboBox1;

    private IContainer components;

    private GroupBox groupBox1;

    private GroupBox groupBox2;

    private Label label1;

    private Label label10;

    private Label label11;

    private Label label12;

    private Label label13;

    private Label label14;

    private Label label15;

    private Label label16;

    private Label label17;

    private Label label18;

    private Label label19;

    private Label label2;

    private Label label20;

    private Label label21;

    private Label label22;

    private Label label23;

    private Label label24;

    private Label label25;

    private Label label26;

    private Label label27;

    private Label label28;

    private Label label29;

    private Label label3;

    private Label label4;

    private Label label5;

    private Label label6;

    private Label label7;

    private Label label8;

    private Label label9;

    private readonly Dictionary<int, 属性> qglist = new();

    private readonly Dictionary<int, 属性> stqglist = new();

    private TextBox textBox1;

    private TextBox textBox10;

    private TextBox textBox11;

    private TextBox textBox12;

    private TextBox textBox13;

    private TextBox textBox14;

    private TextBox textBox15;

    private TextBox textBox16;

    private TextBox textBox17;

    private TextBox textBox18;

    private TextBox textBox19;

    private TextBox textBox2;

    private TextBox textBox20;

    private TextBox textBox21;

    private TextBox textBox22;

    private TextBox textBox23;

    private TextBox textBox24;

    private TextBox textBox25;

    private TextBox textBox26;

    private TextBox textBox27;

    private TextBox textBox28;

    private TextBox textBox29;

    private TextBox textBox3;

    private TextBox textBox30;

    private TextBox textBox31;

    private TextBox textBox32;

    private TextBox textBox33;

    private TextBox textBox34;

    private TextBox textBox35;

    private TextBox textBox36;

    private TextBox textBox37;

    private TextBox textBox4;

    private TextBox textBox5;

    private TextBox textBox6;

    private TextBox textBox7;

    private TextBox textBox8;

    private TextBox textBox9;

    public SkillContrl()
    {
        InitializeComponent();
    }

    private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
    {
        select(comboBox1.SelectedIndex);
    }

    private void SkillContrl_Load(object sender, EventArgs e)
    {
        comboBox1.SelectedIndex = 0;
    }

    private void 初始化()
    {
        label12.Text = string.Empty;
        label13.Text = string.Empty;
        label14.Text = string.Empty;
        label15.Text = string.Empty;
        label16.Text = string.Empty;
        label17.Text = string.Empty;
        label18.Text = string.Empty;
        label19.Text = string.Empty;
        label20.Text = string.Empty;
        label21.Text = string.Empty;
        label22.Text = string.Empty;
        label23.Text = string.Empty;
        label24.Text = string.Empty;
        label25.Text = string.Empty;
        label26.Text = string.Empty;
        label12.Show();
        label13.Show();
        label14.Show();
        label15.Show();
        label16.Show();
        label17.Show();
        label18.Show();
        label19.Show();
        label20.Show();
        label21.Show();
        label22.Show();
        label23.Show();
        label24.Show();
        label25.Show();
        label26.Show();
        textBox23.Show();
        textBox24.Show();
        textBox25.Show();
        textBox26.Show();
        textBox27.Show();
        textBox28.Show();
        textBox29.Show();
        textBox30.Show();
        textBox31.Show();
        textBox32.Show();
        textBox33.Show();
        textBox34.Show();
        textBox35.Show();
        textBox36.Show();
        textBox37.Show();
    }

    private void select(int index)
    {
        try
        {
            var num2 = 0;
            X_Thang_Thien_Khi_Cong_Tong_Loai 升天气功总类 = null;
            var enumerator = default(Dictionary<int, X_Thang_Thien_Khi_Cong_Tong_Loai>.ValueCollection.Enumerator);
            X_Khi_Cong_Tang_Them_Thuoc_Tinh 气功加成属性 = null;
            string text = null;
            string text2 = null;
            string text3 = null;
            var num3 = 0;
            index++;
            var num4 = index;
            qglist.Clear();
            stqglist.Clear();
            初始化();
            var enumerator2 = World.KhiCongTangThem.Values.GetEnumerator();
            num2 = 0;
            try
            {
                num2 = 10;
                while (true)
                {
                    switch (num2)
                    {
                        case 6:
                            break;
                        case 19:
                            if (气功加成属性.FLD_JOB == num4)
                            {
                                num2 = 13;
                                continue;
                            }

                            goto default;
                        default:
                            num2 = 15;
                            continue;
                        case 15:
                            if (!enumerator2.MoveNext())
                            {
                                num2 = 0;
                                continue;
                            }

                            气功加成属性 = enumerator2.Current;
                            num2 = 19;
                            continue;
                        case 13:
                        {
                            text = 气功加成属性.FLD_NAME;
                            text2 = 气功加成属性.FLD_BonusRateValuePerPoint1.ToString();
                            text3 = 气功加成属性.FLD_BonusRateValuePerPoint2.ToString();
                            var value = new 属性(double.Parse(text2), double.Parse(text3));
                            qglist.Add(气功加成属性.FLD_INDEX, value);
                            num3 = 气功加成属性.FLD_INDEX;
                            num2 = 8;
                            continue;
                        }
                        case 8:
                            switch (num3)
                            {
                                default:
                                    num2 = 9;
                                    break;
                                case 0:
                                    label1.Text = text;
                                    textBox1.Text = text2;
                                    textBox2.Text = text3;
                                    num2 = 7;
                                    break;
                                case 1:
                                    label2.Text = text;
                                    textBox4.Text = text2;
                                    textBox3.Text = text3;
                                    num2 = 4;
                                    break;
                                case 2:
                                    label3.Text = text;
                                    textBox6.Text = text2;
                                    textBox5.Text = text3;
                                    num2 = 16;
                                    break;
                                case 3:
                                    label4.Text = text;
                                    textBox8.Text = text2;
                                    textBox7.Text = text3;
                                    num2 = 18;
                                    break;
                                case 4:
                                    label5.Text = text;
                                    textBox10.Text = text2;
                                    textBox9.Text = text3;
                                    num2 = 3;
                                    break;
                                case 5:
                                    label6.Text = text;
                                    textBox12.Text = text2;
                                    textBox11.Text = text3;
                                    num2 = 5;
                                    break;
                                case 6:
                                    label7.Text = text;
                                    textBox14.Text = text2;
                                    textBox13.Text = text3;
                                    num2 = 14;
                                    break;
                                case 7:
                                    label8.Text = text;
                                    textBox16.Text = text2;
                                    textBox15.Text = text3;
                                    num2 = 12;
                                    break;
                                case 8:
                                    label9.Text = text;
                                    textBox18.Text = text2;
                                    textBox17.Text = text3;
                                    num2 = 11;
                                    break;
                                case 9:
                                    label10.Text = text;
                                    textBox20.Text = text2;
                                    textBox19.Text = text3;
                                    num2 = 1;
                                    break;
                                case 10:
                                    label11.Text = text;
                                    textBox22.Text = text2;
                                    textBox21.Text = text3;
                                    num2 = 17;
                                    break;
                            }

                            continue;
                        case 9:
                            num2 = 2;
                            continue;
                        case 0:
                            num2 = 6;
                            continue;
                    }

                    break;
                }
            }
            finally
            {
                enumerator2.Dispose();
            }

            enumerator = World.ThangThienKhiCongList.Values.GetEnumerator();
            num2 = 1;
            try
            {
                num2 = 223;
                while (true)
                {
                    num2 = 8;
                    if (!enumerator.MoveNext()) break;
                    升天气功总类 = enumerator.Current;
                    num2 = 244;
                    switch (num4)
                    {
                        default:
                            num2 = 369;
                            num2 = 346;
                            break;
                        case 1:
                        {
                            num2 = 358;
                            if (升天气功总类.NhanVatNgheNghiep1 != 1) break;
                            num2 = 377;
                            var value2 = new 属性(升天气功总类.FLD_BonusRateValuePerPoint, 0.0);
                            stqglist.Add(升天气功总类.KhiCongID, value2);
                            label22.Show();
                            textBox33.Show();
                            num2 = 185;
                            if (label12.Text.Length <= 7)
                            {
                                num2 = 605;
                                label12.Text = 升天气功总类.KhiCongTen;
                                textBox23.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 576;
                                break;
                            }

                            num2 = 553;
                            if (label13.Text.Length <= 7)
                            {
                                num2 = 99;
                                label13.Text = 升天气功总类.KhiCongTen;
                                textBox24.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 581;
                                break;
                            }

                            num2 = 197;
                            if (label14.Text.Length <= 7)
                            {
                                num2 = 239;
                                label14.Text = 升天气功总类.KhiCongTen;
                                textBox25.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 312;
                                break;
                            }

                            num2 = 216;
                            if (label15.Text.Length <= 7)
                            {
                                num2 = 159;
                                label15.Text = 升天气功总类.KhiCongTen;
                                textBox26.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 63;
                                break;
                            }

                            num2 = 340;
                            if (label16.Text.Length <= 7)
                            {
                                num2 = 433;
                                label16.Text = 升天气功总类.KhiCongTen;
                                textBox27.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 69;
                                break;
                            }

                            num2 = 121;
                            if (label17.Text.Length <= 7)
                            {
                                num2 = 103;
                                label17.Text = 升天气功总类.KhiCongTen;
                                textBox28.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 564;
                                break;
                            }

                            num2 = 166;
                            if (label18.Text.Length <= 7)
                            {
                                num2 = 68;
                                label18.Text = 升天气功总类.KhiCongTen;
                                textBox29.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 208;
                                break;
                            }

                            num2 = 575;
                            if (label19.Text.Length <= 7)
                            {
                                num2 = 154;
                                label19.Text = 升天气功总类.KhiCongTen;
                                textBox30.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 603;
                                break;
                            }

                            num2 = 175;
                            if (label20.Text.Length <= 7)
                            {
                                num2 = 536;
                                label20.Text = 升天气功总类.KhiCongTen;
                                textBox31.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 299;
                                break;
                            }

                            num2 = 5;
                            if (label21.Text.Length <= 7)
                            {
                                num2 = 252;
                                label21.Text = 升天气功总类.KhiCongTen;
                                textBox32.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 50;
                                break;
                            }

                            num2 = 143;
                            if (label22.Text.Length <= 7)
                            {
                                num2 = 165;
                                label22.Text = 升天气功总类.KhiCongTen;
                                textBox33.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                label23.Hide();
                                label24.Hide();
                                label25.Hide();
                                label26.Hide();
                                textBox34.Hide();
                                textBox35.Hide();
                                textBox36.Hide();
                                textBox37.Hide();
                                num2 = 560;
                                break;
                            }

                            num2 = 294;
                            if (label23.Text.Length <= 7)
                            {
                                num2 = 425;
                                label23.Text = 升天气功总类.KhiCongTen;
                                textBox34.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 198;
                                break;
                            }

                            num2 = 111;
                            if (label24.Text.Length <= 7)
                            {
                                num2 = 232;
                                label24.Text = 升天气功总类.KhiCongTen;
                                textBox35.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 270;
                                break;
                            }

                            num2 = 106;
                            if (label25.Text.Length <= 7)
                            {
                                num2 = 388;
                                label25.Text = 升天气功总类.KhiCongTen;
                                textBox36.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 213;
                                break;
                            }

                            num2 = 277;
                            if (label26.Text.Length <= 7)
                            {
                                num2 = 544;
                                label26.Text = 升天气功总类.KhiCongTen;
                                textBox37.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 508;
                            }

                            break;
                        }
                        case 2:
                            num2 = 478;
                            if (升天气功总类.NhanVatNgheNghiep2 != 1) break;
                            num2 = 389;
                            num2 = 43;
                            if (label12.Text.Length <= 7)
                            {
                                num2 = 107;
                                label12.Text = 升天气功总类.KhiCongTen;
                                textBox23.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 507;
                                break;
                            }

                            num2 = 22;
                            if (label13.Text.Length <= 7)
                            {
                                num2 = 348;
                                label13.Text = 升天气功总类.KhiCongTen;
                                textBox24.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 482;
                                break;
                            }

                            num2 = 451;
                            if (label14.Text.Length <= 7)
                            {
                                num2 = 493;
                                label14.Text = 升天气功总类.KhiCongTen;
                                textBox25.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 193;
                                break;
                            }

                            num2 = 600;
                            if (label15.Text.Length <= 7)
                            {
                                num2 = 95;
                                label15.Text = 升天气功总类.KhiCongTen;
                                textBox26.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 489;
                                break;
                            }

                            num2 = 413;
                            if (label16.Text.Length <= 7)
                            {
                                num2 = 158;
                                label16.Text = 升天气功总类.KhiCongTen;
                                textBox27.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 179;
                                break;
                            }

                            num2 = 15;
                            if (label17.Text.Length <= 7)
                            {
                                num2 = 189;
                                label17.Text = 升天气功总类.KhiCongTen;
                                textBox28.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 168;
                                break;
                            }

                            num2 = 368;
                            if (label18.Text.Length <= 7)
                            {
                                num2 = 285;
                                label18.Text = 升天气功总类.KhiCongTen;
                                textBox29.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 281;
                                break;
                            }

                            num2 = 527;
                            if (label19.Text.Length <= 7)
                            {
                                num2 = 160;
                                label19.Text = 升天气功总类.KhiCongTen;
                                textBox30.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 496;
                                break;
                            }

                            num2 = 282;
                            if (label20.Text.Length <= 7)
                            {
                                num2 = 148;
                                label20.Text = 升天气功总类.KhiCongTen;
                                textBox31.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 259;
                                break;
                            }

                            num2 = 30;
                            if (label21.Text.Length <= 7)
                            {
                                num2 = 218;
                                label21.Text = 升天气功总类.KhiCongTen;
                                textBox32.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                label22.Hide();
                                label23.Hide();
                                label24.Hide();
                                label25.Hide();
                                label26.Hide();
                                textBox33.Hide();
                                textBox34.Hide();
                                textBox35.Hide();
                                textBox36.Hide();
                                textBox37.Hide();
                                num2 = 74;
                                break;
                            }

                            num2 = 520;
                            if (label22.Text.Length <= 7)
                            {
                                num2 = 390;
                                label22.Text = 升天气功总类.KhiCongTen;
                                textBox33.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 80;
                                break;
                            }

                            num2 = 249;
                            if (label23.Text.Length <= 7)
                            {
                                num2 = 481;
                                label23.Text = 升天气功总类.KhiCongTen;
                                textBox34.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 558;
                                break;
                            }

                            num2 = 25;
                            if (label24.Text.Length <= 7)
                            {
                                num2 = 510;
                                label24.Text = 升天气功总类.KhiCongTen;
                                textBox35.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 66;
                                break;
                            }

                            num2 = 535;
                            if (label25.Text.Length <= 7)
                            {
                                num2 = 55;
                                label25.Text = 升天气功总类.KhiCongTen;
                                textBox36.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 515;
                                break;
                            }

                            num2 = 376;
                            if (label26.Text.Length <= 7)
                            {
                                num2 = 254;
                                label26.Text = 升天气功总类.KhiCongTen;
                                textBox37.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 52;
                            }

                            break;
                        case 3:
                            num2 = 109;
                            if (升天气功总类.NhanVatNgheNghiep3 != 1) break;
                            num2 = 261;
                            num2 = 398;
                            if (label12.Text.Length <= 7)
                            {
                                num2 = 289;
                                label12.Text = 升天气功总类.KhiCongTen;
                                textBox23.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 332;
                                break;
                            }

                            num2 = 374;
                            if (label13.Text.Length <= 7)
                            {
                                num2 = 240;
                                label13.Text = 升天气功总类.KhiCongTen;
                                textBox24.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 543;
                                break;
                            }

                            num2 = 501;
                            if (label14.Text.Length <= 7)
                            {
                                num2 = 521;
                                label14.Text = 升天气功总类.KhiCongTen;
                                textBox25.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 428;
                                break;
                            }

                            num2 = 286;
                            if (label15.Text.Length <= 7)
                            {
                                num2 = 578;
                                label15.Text = 升天气功总类.KhiCongTen;
                                textBox26.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 357;
                                break;
                            }

                            num2 = 406;
                            if (label16.Text.Length <= 7)
                            {
                                num2 = 570;
                                label16.Text = 升天气功总类.KhiCongTen;
                                textBox27.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 434;
                                break;
                            }

                            num2 = 541;
                            if (label17.Text.Length <= 7)
                            {
                                num2 = 162;
                                label17.Text = 升天气功总类.KhiCongTen;
                                textBox28.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 251;
                                break;
                            }

                            num2 = 589;
                            if (label18.Text.Length <= 7)
                            {
                                num2 = 211;
                                label18.Text = 升天气功总类.KhiCongTen;
                                textBox29.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 301;
                                break;
                            }

                            num2 = 540;
                            if (label19.Text.Length <= 7)
                            {
                                num2 = 34;
                                label19.Text = 升天气功总类.KhiCongTen;
                                textBox30.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 614;
                                break;
                            }

                            num2 = 19;
                            if (label20.Text.Length <= 7)
                            {
                                num2 = 191;
                                label20.Text = 升天气功总类.KhiCongTen;
                                textBox31.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 138;
                                break;
                            }

                            num2 = 396;
                            if (label21.Text.Length <= 7)
                            {
                                num2 = 7;
                                label21.Text = 升天气功总类.KhiCongTen;
                                textBox32.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                label22.Hide();
                                label23.Hide();
                                label24.Hide();
                                label25.Hide();
                                label26.Hide();
                                textBox33.Hide();
                                textBox34.Hide();
                                textBox35.Hide();
                                textBox36.Hide();
                                textBox37.Hide();
                                num2 = 438;
                                break;
                            }

                            num2 = 136;
                            if (label22.Text.Length <= 7)
                            {
                                num2 = 437;
                                label22.Text = 升天气功总类.KhiCongTen;
                                textBox33.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 130;
                                break;
                            }

                            num2 = 290;
                            if (label23.Text.Length <= 7)
                            {
                                num2 = 424;
                                label23.Text = 升天气功总类.KhiCongTen;
                                textBox34.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 609;
                                break;
                            }

                            num2 = 262;
                            if (label24.Text.Length <= 7)
                            {
                                num2 = 509;
                                label24.Text = 升天气功总类.KhiCongTen;
                                textBox35.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 155;
                                break;
                            }

                            num2 = 456;
                            if (label25.Text.Length <= 7)
                            {
                                num2 = 411;
                                label25.Text = 升天气功总类.KhiCongTen;
                                textBox36.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 432;
                                break;
                            }

                            num2 = 292;
                            if (label26.Text.Length <= 7)
                            {
                                num2 = 302;
                                label26.Text = 升天气功总类.KhiCongTen;
                                textBox37.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 607;
                            }

                            break;
                        case 4:
                            num2 = 256;
                            if (升天气功总类.NhanVatNgheNghiep4 != 1) break;
                            num2 = 113;
                            num2 = 96;
                            if (label12.Text.Length <= 7)
                            {
                                num2 = 305;
                                label12.Text = 升天气功总类.KhiCongTen;
                                textBox23.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 442;
                                break;
                            }

                            num2 = 585;
                            if (label13.Text.Length <= 7)
                            {
                                num2 = 60;
                                label13.Text = 升天气功总类.KhiCongTen;
                                textBox24.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 85;
                                break;
                            }

                            num2 = 176;
                            if (label14.Text.Length <= 7)
                            {
                                num2 = 532;
                                label14.Text = 升天气功总类.KhiCongTen;
                                textBox25.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 100;
                                break;
                            }

                            num2 = 360;
                            if (label15.Text.Length <= 7)
                            {
                                num2 = 233;
                                label15.Text = 升天气功总类.KhiCongTen;
                                textBox26.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 274;
                                break;
                            }

                            num2 = 335;
                            if (label16.Text.Length <= 7)
                            {
                                num2 = 45;
                                label16.Text = 升天气功总类.KhiCongTen;
                                textBox27.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 257;
                                break;
                            }

                            num2 = 142;
                            if (label17.Text.Length <= 7)
                            {
                                num2 = 494;
                                label17.Text = 升天气功总类.KhiCongTen;
                                textBox28.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 338;
                                break;
                            }

                            num2 = 291;
                            if (label18.Text.Length <= 7)
                            {
                                num2 = 431;
                                label18.Text = 升天气功总类.KhiCongTen;
                                textBox29.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 219;
                                break;
                            }

                            num2 = 474;
                            if (label19.Text.Length <= 7)
                            {
                                num2 = 379;
                                label19.Text = 升天气功总类.KhiCongTen;
                                textBox30.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 194;
                                break;
                            }

                            num2 = 325;
                            if (label20.Text.Length <= 7)
                            {
                                num2 = 271;
                                label20.Text = 升天气功总类.KhiCongTen;
                                textBox31.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 552;
                                break;
                            }

                            num2 = 479;
                            if (label21.Text.Length <= 7)
                            {
                                num2 = 467;
                                label21.Text = 升天气功总类.KhiCongTen;
                                textBox32.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                label22.Hide();
                                label23.Hide();
                                label24.Hide();
                                label25.Hide();
                                label26.Hide();
                                textBox33.Hide();
                                textBox34.Hide();
                                textBox35.Hide();
                                textBox36.Hide();
                                textBox37.Hide();
                                num2 = 76;
                                break;
                            }

                            num2 = 149;
                            if (label22.Text.Length <= 7)
                            {
                                num2 = 44;
                                label22.Text = 升天气功总类.KhiCongTen;
                                textBox33.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 202;
                                break;
                            }

                            num2 = 124;
                            if (label23.Text.Length <= 7)
                            {
                                num2 = 517;
                                label23.Text = 升天气功总类.KhiCongTen;
                                textBox34.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 526;
                                break;
                            }

                            num2 = 235;
                            if (label24.Text.Length <= 7)
                            {
                                num2 = 498;
                                label24.Text = 升天气功总类.KhiCongTen;
                                textBox35.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 615;
                                break;
                            }

                            num2 = 51;
                            if (label25.Text.Length <= 7)
                            {
                                num2 = 598;
                                label25.Text = 升天气功总类.KhiCongTen;
                                textBox36.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 416;
                                break;
                            }

                            num2 = 49;
                            if (label26.Text.Length <= 7)
                            {
                                num2 = 593;
                                label26.Text = 升天气功总类.KhiCongTen;
                                textBox37.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 595;
                            }

                            break;
                        case 5:
                            num2 = 354;
                            if (升天气功总类.NhanVatNgheNghiep5 != 1) break;
                            num2 = 13;
                            num2 = 117;
                            if (label12.Text.Length <= 7)
                            {
                                num2 = 380;
                                label12.Text = 升天气功总类.KhiCongTen;
                                textBox23.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 264;
                                break;
                            }

                            num2 = 477;
                            if (label13.Text.Length <= 7)
                            {
                                num2 = 450;
                                label13.Text = 升天气功总类.KhiCongTen;
                                textBox24.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 469;
                                break;
                            }

                            num2 = 547;
                            if (label14.Text.Length <= 7)
                            {
                                num2 = 77;
                                label14.Text = 升天气功总类.KhiCongTen;
                                textBox25.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 484;
                                break;
                            }

                            num2 = 73;
                            if (label15.Text.Length <= 7)
                            {
                                num2 = 601;
                                label15.Text = 升天气功总类.KhiCongTen;
                                textBox26.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 529;
                                break;
                            }

                            num2 = 531;
                            if (label16.Text.Length <= 7)
                            {
                                num2 = 612;
                                label16.Text = 升天气功总类.KhiCongTen;
                                textBox27.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 10;
                                break;
                            }

                            num2 = 237;
                            if (label17.Text.Length <= 7)
                            {
                                num2 = 188;
                                label17.Text = 升天气功总类.KhiCongTen;
                                textBox28.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 572;
                                break;
                            }

                            num2 = 284;
                            if (label18.Text.Length <= 7)
                            {
                                num2 = 414;
                                label18.Text = 升天气功总类.KhiCongTen;
                                textBox29.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 236;
                                break;
                            }

                            num2 = 351;
                            if (label19.Text.Length <= 7)
                            {
                                num2 = 421;
                                label19.Text = 升天气功总类.KhiCongTen;
                                textBox30.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 453;
                                break;
                            }

                            num2 = 410;
                            if (label20.Text.Length <= 7)
                            {
                                num2 = 122;
                                label20.Text = 升天气功总类.KhiCongTen;
                                textBox31.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 123;
                                break;
                            }

                            num2 = 57;
                            if (label21.Text.Length <= 7)
                            {
                                num2 = 562;
                                label21.Text = 升天气功总类.KhiCongTen;
                                textBox32.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 567;
                                break;
                            }

                            num2 = 554;
                            if (label22.Text.Length <= 7)
                            {
                                num2 = 400;
                                label22.Text = 升天气功总类.KhiCongTen;
                                textBox33.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 98;
                                break;
                            }

                            num2 = 407;
                            if (label23.Text.Length <= 7)
                            {
                                num2 = 23;
                                label23.Text = 升天气功总类.KhiCongTen;
                                textBox34.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                label24.Hide();
                                label25.Hide();
                                label26.Hide();
                                textBox35.Hide();
                                textBox36.Hide();
                                textBox37.Hide();
                                num2 = 200;
                                break;
                            }

                            num2 = 577;
                            if (label24.Text.Length <= 7)
                            {
                                num2 = 349;
                                label24.Text = 升天气功总类.KhiCongTen;
                                textBox35.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 439;
                                break;
                            }

                            num2 = 177;
                            if (label25.Text.Length <= 7)
                            {
                                num2 = 266;
                                label25.Text = 升天气功总类.KhiCongTen;
                                textBox36.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 40;
                                break;
                            }

                            num2 = 590;
                            if (label26.Text.Length <= 7)
                            {
                                num2 = 181;
                                label26.Text = 升天气功总类.KhiCongTen;
                                textBox37.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 462;
                            }

                            break;
                        case 6:
                            num2 = 56;
                            if (升天气功总类.NhanVatNgheNghiep6 != 1) break;
                            num2 = 336;
                            num2 = 370;
                            if (label12.Text.Length <= 7)
                            {
                                num2 = 221;
                                label12.Text = 升天气功总类.KhiCongTen;
                                textBox23.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 135;
                                break;
                            }

                            num2 = 89;
                            if (label13.Text.Length <= 7)
                            {
                                num2 = 384;
                                label13.Text = 升天气功总类.KhiCongTen;
                                textBox24.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 204;
                                break;
                            }

                            num2 = 169;
                            if (label14.Text.Length <= 7)
                            {
                                num2 = 293;
                                label14.Text = 升天气功总类.KhiCongTen;
                                textBox25.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 65;
                                break;
                            }

                            num2 = 610;
                            if (label15.Text.Length <= 7)
                            {
                                num2 = 392;
                                label15.Text = 升天气功总类.KhiCongTen;
                                textBox26.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 449;
                                break;
                            }

                            num2 = 444;
                            if (label16.Text.Length <= 7)
                            {
                                num2 = 566;
                                label16.Text = 升天气功总类.KhiCongTen;
                                textBox27.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 151;
                                break;
                            }

                            num2 = 355;
                            if (label17.Text.Length <= 7)
                            {
                                num2 = 519;
                                label17.Text = 升天气功总类.KhiCongTen;
                                textBox28.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 387;
                                break;
                            }

                            num2 = 78;
                            if (label18.Text.Length <= 7)
                            {
                                num2 = 167;
                                label18.Text = 升天气功总类.KhiCongTen;
                                textBox29.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 611;
                                break;
                            }

                            num2 = 2;
                            if (label19.Text.Length <= 7)
                            {
                                num2 = 569;
                                label19.Text = 升天气功总类.KhiCongTen;
                                textBox30.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 347;
                                break;
                            }

                            num2 = 287;
                            if (label20.Text.Length <= 7)
                            {
                                num2 = 41;
                                label20.Text = 升天气功总类.KhiCongTen;
                                textBox31.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 131;
                                break;
                            }

                            num2 = 446;
                            if (label21.Text.Length <= 7)
                            {
                                num2 = 455;
                                label21.Text = 升天气功总类.KhiCongTen;
                                textBox32.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 466;
                                break;
                            }

                            num2 = 42;
                            if (label22.Text.Length <= 7)
                            {
                                num2 = 263;
                                label22.Text = 升天气功总类.KhiCongTen;
                                textBox33.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                label23.Hide();
                                label24.Hide();
                                label25.Hide();
                                label26.Hide();
                                textBox34.Hide();
                                textBox35.Hide();
                                textBox36.Hide();
                                textBox37.Hide();
                                num2 = 128;
                                break;
                            }

                            num2 = 229;
                            if (label23.Text.Length <= 7)
                            {
                                num2 = 480;
                                label23.Text = 升天气功总类.KhiCongTen;
                                textBox34.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 228;
                                break;
                            }

                            num2 = 220;
                            if (label24.Text.Length <= 7)
                            {
                                num2 = 146;
                                label24.Text = 升天气功总类.KhiCongTen;
                                textBox35.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 440;
                                break;
                            }

                            num2 = 461;
                            if (label25.Text.Length <= 7)
                            {
                                num2 = 408;
                                label25.Text = 升天气功总类.KhiCongTen;
                                textBox36.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 596;
                                break;
                            }

                            num2 = 465;
                            if (label26.Text.Length <= 7)
                            {
                                num2 = 54;
                                label26.Text = 升天气功总类.KhiCongTen;
                                textBox37.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 1;
                            }

                            break;
                        case 7:
                            num2 = 506;
                            if (升天气功总类.NhanVatNgheNghiep7 != 1) break;
                            num2 = 337;
                            num2 = 58;
                            if (label12.Text.Length <= 7)
                            {
                                num2 = 180;
                                label12.Text = 升天气功总类.KhiCongTen;
                                textBox23.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 283;
                                break;
                            }

                            num2 = 152;
                            if (label13.Text.Length <= 7)
                            {
                                num2 = 16;
                                label13.Text = 升天气功总类.KhiCongTen;
                                textBox24.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 83;
                                break;
                            }

                            num2 = 339;
                            if (label14.Text.Length <= 7)
                            {
                                num2 = 382;
                                label14.Text = 升天气功总类.KhiCongTen;
                                textBox25.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 441;
                                break;
                            }

                            num2 = 67;
                            if (label15.Text.Length <= 7)
                            {
                                num2 = 591;
                                label15.Text = 升天气功总类.KhiCongTen;
                                textBox26.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 86;
                                break;
                            }

                            num2 = 79;
                            if (label16.Text.Length <= 7)
                            {
                                num2 = 36;
                                label16.Text = 升天气功总类.KhiCongTen;
                                textBox27.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 140;
                                break;
                            }

                            num2 = 539;
                            if (label17.Text.Length <= 7)
                            {
                                num2 = 599;
                                label17.Text = 升天气功总类.KhiCongTen;
                                textBox28.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 573;
                                break;
                            }

                            num2 = 371;
                            if (label18.Text.Length <= 7)
                            {
                                num2 = 3;
                                label18.Text = 升天气功总类.KhiCongTen;
                                textBox29.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 587;
                                break;
                            }

                            num2 = 511;
                            if (label19.Text.Length <= 7)
                            {
                                num2 = 62;
                                label19.Text = 升天气功总类.KhiCongTen;
                                textBox30.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 269;
                                break;
                            }

                            num2 = 512;
                            if (label20.Text.Length <= 7)
                            {
                                num2 = 497;
                                label20.Text = 升天气功总类.KhiCongTen;
                                textBox31.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 359;
                                break;
                            }

                            num2 = 557;
                            if (label21.Text.Length <= 7)
                            {
                                num2 = 429;
                                label21.Text = 升天气功总类.KhiCongTen;
                                textBox32.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                label22.Hide();
                                label23.Hide();
                                label24.Hide();
                                label25.Hide();
                                label26.Hide();
                                textBox33.Hide();
                                textBox34.Hide();
                                textBox35.Hide();
                                textBox36.Hide();
                                textBox37.Hide();
                                num2 = 323;
                                break;
                            }

                            num2 = 546;
                            if (label22.Text.Length <= 7)
                            {
                                num2 = 592;
                                label22.Text = 升天气功总类.KhiCongTen;
                                textBox33.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 333;
                                break;
                            }

                            num2 = 404;
                            if (label23.Text.Length <= 7)
                            {
                                num2 = 423;
                                label23.Text = 升天气功总类.KhiCongTen;
                                textBox34.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 61;
                                break;
                            }

                            num2 = 153;
                            if (label24.Text.Length <= 7)
                            {
                                num2 = 327;
                                label24.Text = 升天气功总类.KhiCongTen;
                                textBox35.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 82;
                                break;
                            }

                            num2 = 206;
                            if (label25.Text.Length <= 7)
                            {
                                num2 = 473;
                                label25.Text = 升天气功总类.KhiCongTen;
                                textBox36.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 230;
                                break;
                            }

                            num2 = 385;
                            if (label26.Text.Length <= 7)
                            {
                                num2 = 297;
                                label26.Text = 升天气功总类.KhiCongTen;
                                textBox37.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 209;
                            }

                            break;
                        case 8:
                            num2 = 363;
                            if (升天气功总类.NhanVatNgheNghiep8 != 1) break;
                            num2 = 114;
                            num2 = 452;
                            if (label12.Text.Length <= 7)
                            {
                                num2 = 133;
                                label12.Text = 升天气功总类.KhiCongTen;
                                textBox23.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 126;
                                break;
                            }

                            num2 = 402;
                            if (label13.Text.Length <= 7)
                            {
                                num2 = 241;
                                label13.Text = 升天气功总类.KhiCongTen;
                                textBox24.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 253;
                                break;
                            }

                            num2 = 344;
                            if (label14.Text.Length <= 7)
                            {
                                num2 = 426;
                                label14.Text = 升天气功总类.KhiCongTen;
                                textBox25.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 187;
                                break;
                            }

                            num2 = 279;
                            if (label15.Text.Length <= 7)
                            {
                                num2 = 88;
                                label15.Text = 升天气功总类.KhiCongTen;
                                textBox26.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 331;
                                break;
                            }

                            num2 = 550;
                            if (label16.Text.Length <= 7)
                            {
                                num2 = 471;
                                label16.Text = 升天气功总类.KhiCongTen;
                                textBox27.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 150;
                                break;
                            }

                            num2 = 317;
                            if (label17.Text.Length <= 7)
                            {
                                num2 = 132;
                                label17.Text = 升天气功总类.KhiCongTen;
                                textBox28.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 182;
                                break;
                            }

                            num2 = 33;
                            if (label18.Text.Length <= 7)
                            {
                                num2 = 0;
                                label18.Text = 升天气功总类.KhiCongTen;
                                textBox29.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 105;
                                break;
                            }

                            num2 = 31;
                            if (label19.Text.Length <= 7)
                            {
                                num2 = 488;
                                label19.Text = 升天气功总类.KhiCongTen;
                                textBox30.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 242;
                                break;
                            }

                            num2 = 586;
                            if (label20.Text.Length <= 7)
                            {
                                num2 = 295;
                                label20.Text = 升天气功总类.KhiCongTen;
                                textBox31.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 321;
                                break;
                            }

                            num2 = 395;
                            if (label21.Text.Length <= 7)
                            {
                                num2 = 92;
                                label21.Text = 升天气功总类.KhiCongTen;
                                textBox32.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                label22.Hide();
                                label23.Hide();
                                label24.Hide();
                                label25.Hide();
                                label26.Hide();
                                textBox33.Hide();
                                textBox34.Hide();
                                textBox35.Hide();
                                textBox36.Hide();
                                textBox37.Hide();
                                num2 = 353;
                                break;
                            }

                            num2 = 459;
                            if (label22.Text.Length <= 7)
                            {
                                num2 = 391;
                                label22.Text = 升天气功总类.KhiCongTen;
                                textBox33.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 145;
                                break;
                            }

                            num2 = 300;
                            if (label23.Text.Length <= 7)
                            {
                                num2 = 12;
                                label23.Text = 升天气功总类.KhiCongTen;
                                textBox34.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 499;
                                break;
                            }

                            num2 = 258;
                            if (label24.Text.Length <= 7)
                            {
                                num2 = 315;
                                label24.Text = 升天气功总类.KhiCongTen;
                                textBox35.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 551;
                                break;
                            }

                            num2 = 238;
                            if (label25.Text.Length <= 7)
                            {
                                num2 = 561;
                                label25.Text = 升天气功总类.KhiCongTen;
                                textBox36.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 435;
                                break;
                            }

                            num2 = 47;
                            if (label26.Text.Length <= 7)
                            {
                                num2 = 528;
                                label26.Text = 升天气功总类.KhiCongTen;
                                textBox37.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 328;
                            }

                            break;
                        case 9:
                            num2 = 375;
                            if (升天气功总类.NhanVatNgheNghiep9 != 1) break;
                            num2 = 39;
                            num2 = 245;
                            if (label12.Text.Length <= 7)
                            {
                                num2 = 422;
                                label12.Text = 升天气功总类.KhiCongTen;
                                textBox23.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 500;
                                break;
                            }

                            num2 = 324;
                            if (label13.Text.Length <= 7)
                            {
                                num2 = 458;
                                label13.Text = 升天气功总类.KhiCongTen;
                                textBox24.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 32;
                                break;
                            }

                            num2 = 487;
                            if (label14.Text.Length <= 7)
                            {
                                num2 = 361;
                                label14.Text = 升天气功总类.KhiCongTen;
                                textBox25.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 617;
                                break;
                            }

                            num2 = 436;
                            if (label15.Text.Length <= 7)
                            {
                                num2 = 419;
                                label15.Text = 升天气功总类.KhiCongTen;
                                textBox26.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 260;
                                break;
                            }

                            num2 = 342;
                            if (label16.Text.Length <= 7)
                            {
                                num2 = 334;
                                label16.Text = 升天气功总类.KhiCongTen;
                                textBox27.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 409;
                                break;
                            }

                            num2 = 464;
                            if (label17.Text.Length <= 7)
                            {
                                num2 = 21;
                                label17.Text = 升天气功总类.KhiCongTen;
                                textBox28.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 120;
                                break;
                            }

                            num2 = 503;
                            if (label18.Text.Length <= 7)
                            {
                                num2 = 505;
                                label18.Text = 升天气功总类.KhiCongTen;
                                textBox29.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 207;
                                break;
                            }

                            num2 = 492;
                            if (label19.Text.Length <= 7)
                            {
                                num2 = 272;
                                label19.Text = 升天气功总类.KhiCongTen;
                                textBox30.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 87;
                                break;
                            }

                            num2 = 405;
                            if (label20.Text.Length <= 7)
                            {
                                num2 = 602;
                                label20.Text = 升天气功总类.KhiCongTen;
                                textBox31.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 530;
                                break;
                            }

                            num2 = 579;
                            if (label21.Text.Length <= 7)
                            {
                                num2 = 231;
                                label21.Text = 升天气功总类.KhiCongTen;
                                textBox32.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                label22.Hide();
                                label23.Hide();
                                label24.Hide();
                                label25.Hide();
                                label26.Hide();
                                textBox33.Hide();
                                textBox34.Hide();
                                textBox35.Hide();
                                textBox36.Hide();
                                textBox37.Hide();
                                num2 = 468;
                                break;
                            }

                            num2 = 20;
                            if (label22.Text.Length <= 7)
                            {
                                num2 = 555;
                                label22.Text = 升天气功总类.KhiCongTen;
                                textBox33.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 268;
                                break;
                            }

                            num2 = 172;
                            if (label23.Text.Length <= 7)
                            {
                                num2 = 445;
                                label23.Text = 升天气功总类.KhiCongTen;
                                textBox34.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 97;
                                break;
                            }

                            num2 = 183;
                            if (label24.Text.Length <= 7)
                            {
                                num2 = 457;
                                label24.Text = 升天气功总类.KhiCongTen;
                                textBox35.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 362;
                                break;
                            }

                            num2 = 412;
                            if (label25.Text.Length <= 7)
                            {
                                num2 = 401;
                                label25.Text = 升天气功总类.KhiCongTen;
                                textBox36.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 443;
                                break;
                            }

                            num2 = 11;
                            if (label26.Text.Length <= 7)
                            {
                                num2 = 306;
                                label26.Text = 升天气功总类.KhiCongTen;
                                textBox37.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 227;
                            }

                            break;
                        case 10:
                            num2 = 215;
                            if (升天气功总类.NhanVatNgheNghiep10 != 1) break;
                            num2 = 345;
                            num2 = 571;
                            if (label12.Text.Length <= 7)
                            {
                                num2 = 460;
                                label12.Text = 升天气功总类.KhiCongTen;
                                textBox23.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 397;
                                break;
                            }

                            num2 = 475;
                            if (label13.Text.Length <= 7)
                            {
                                num2 = 127;
                                label13.Text = 升天气功总类.KhiCongTen;
                                textBox24.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 381;
                                break;
                            }

                            num2 = 418;
                            if (label14.Text.Length <= 7)
                            {
                                num2 = 523;
                                label14.Text = 升天气功总类.KhiCongTen;
                                textBox25.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 163;
                                break;
                            }

                            num2 = 542;
                            if (label15.Text.Length <= 7)
                            {
                                num2 = 565;
                                label15.Text = 升天气功总类.KhiCongTen;
                                textBox26.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 129;
                                break;
                            }

                            num2 = 217;
                            if (label16.Text.Length <= 7)
                            {
                                num2 = 275;
                                label16.Text = 升天气功总类.KhiCongTen;
                                textBox27.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 18;
                                break;
                            }

                            num2 = 556;
                            if (label17.Text.Length <= 7)
                            {
                                num2 = 319;
                                label17.Text = 升天气功总类.KhiCongTen;
                                textBox28.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 192;
                                break;
                            }

                            num2 = 350;
                            if (label18.Text.Length <= 7)
                            {
                                num2 = 224;
                                label18.Text = 升天气功总类.KhiCongTen;
                                textBox29.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 383;
                                break;
                            }

                            num2 = 210;
                            if (label19.Text.Length <= 7)
                            {
                                num2 = 504;
                                label19.Text = 升天气功总类.KhiCongTen;
                                textBox30.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 93;
                                break;
                            }

                            num2 = 582;
                            if (label20.Text.Length <= 7)
                            {
                                num2 = 139;
                                label20.Text = 升天气功总类.KhiCongTen;
                                textBox31.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 311;
                                break;
                            }

                            num2 = 533;
                            if (label21.Text.Length <= 7)
                            {
                                num2 = 101;
                                label21.Text = 升天气功总类.KhiCongTen;
                                textBox32.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                label22.Hide();
                                label23.Hide();
                                label24.Hide();
                                label25.Hide();
                                label26.Hide();
                                textBox33.Hide();
                                textBox34.Hide();
                                textBox35.Hide();
                                textBox36.Hide();
                                textBox37.Hide();
                                num2 = 454;
                                break;
                            }

                            num2 = 24;
                            if (label22.Text.Length <= 7)
                            {
                                num2 = 316;
                                label22.Text = 升天气功总类.KhiCongTen;
                                textBox33.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 352;
                                break;
                            }

                            num2 = 307;
                            if (label23.Text.Length <= 7)
                            {
                                num2 = 28;
                                label23.Text = 升天气功总类.KhiCongTen;
                                textBox34.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 170;
                                break;
                            }

                            num2 = 524;
                            if (label24.Text.Length <= 7)
                            {
                                num2 = 46;
                                label24.Text = 升天气功总类.KhiCongTen;
                                textBox35.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 190;
                                break;
                            }

                            num2 = 70;
                            if (label25.Text.Length <= 7)
                            {
                                num2 = 403;
                                label25.Text = 升天气功总类.KhiCongTen;
                                textBox36.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 178;
                                break;
                            }

                            num2 = 246;
                            if (label26.Text.Length <= 7)
                            {
                                num2 = 616;
                                label26.Text = 升天气功总类.KhiCongTen;
                                textBox37.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 303;
                            }

                            break;
                        case 11:
                            num2 = 393;
                            if (升天气功总类.NhanVatNgheNghiep10 != 1) break;
                            num2 = 538;
                            num2 = 568;
                            if (label12.Text.Length <= 7)
                            {
                                num2 = 201;
                                label12.Text = 升天气功总类.KhiCongTen;
                                textBox23.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 164;
                                break;
                            }

                            num2 = 495;
                            if (label13.Text.Length <= 7)
                            {
                                num2 = 156;
                                label13.Text = 升天气功总类.KhiCongTen;
                                textBox24.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 110;
                                break;
                            }

                            num2 = 485;
                            if (label14.Text.Length <= 7)
                            {
                                num2 = 248;
                                label14.Text = 升天气功总类.KhiCongTen;
                                textBox25.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 514;
                                break;
                            }

                            num2 = 265;
                            if (label15.Text.Length <= 7)
                            {
                                num2 = 486;
                                label15.Text = 升天气功总类.KhiCongTen;
                                textBox26.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 71;
                                break;
                            }

                            num2 = 91;
                            if (label16.Text.Length <= 7)
                            {
                                num2 = 430;
                                label16.Text = 升天气功总类.KhiCongTen;
                                textBox27.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 296;
                                break;
                            }

                            num2 = 134;
                            if (label17.Text.Length <= 7)
                            {
                                num2 = 112;
                                label17.Text = 升天气功总类.KhiCongTen;
                                textBox28.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 212;
                                break;
                            }

                            num2 = 378;
                            if (label18.Text.Length <= 7)
                            {
                                num2 = 415;
                                label18.Text = 升天气功总类.KhiCongTen;
                                textBox29.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 29;
                                break;
                            }

                            num2 = 367;
                            if (label19.Text.Length <= 7)
                            {
                                num2 = 330;
                                label19.Text = 升天气功总类.KhiCongTen;
                                textBox30.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 196;
                                break;
                            }

                            num2 = 125;
                            if (label20.Text.Length <= 7)
                            {
                                num2 = 394;
                                label20.Text = 升天气功总类.KhiCongTen;
                                textBox31.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 205;
                                break;
                            }

                            num2 = 522;
                            if (label21.Text.Length <= 7)
                            {
                                num2 = 417;
                                label21.Text = 升天气功总类.KhiCongTen;
                                textBox32.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                label22.Hide();
                                label23.Hide();
                                label24.Hide();
                                label25.Hide();
                                label26.Hide();
                                textBox33.Hide();
                                textBox34.Hide();
                                textBox35.Hide();
                                textBox36.Hide();
                                textBox37.Hide();
                                num2 = 516;
                                break;
                            }

                            num2 = 476;
                            if (label22.Text.Length <= 7)
                            {
                                num2 = 247;
                                label22.Text = 升天气功总类.KhiCongTen;
                                textBox33.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 255;
                                break;
                            }

                            num2 = 137;
                            if (label23.Text.Length <= 7)
                            {
                                num2 = 199;
                                label23.Text = 升天气功总类.KhiCongTen;
                                textBox34.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 6;
                                break;
                            }

                            num2 = 225;
                            if (label24.Text.Length <= 7)
                            {
                                num2 = 584;
                                label24.Text = 升天气功总类.KhiCongTen;
                                textBox35.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 276;
                                break;
                            }

                            num2 = 597;
                            if (label25.Text.Length <= 7)
                            {
                                num2 = 64;
                                label25.Text = 升天气功总类.KhiCongTen;
                                textBox36.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 84;
                                break;
                            }

                            num2 = 141;
                            if (label26.Text.Length <= 7)
                            {
                                num2 = 537;
                                label26.Text = 升天气功总类.KhiCongTen;
                                textBox37.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 490;
                            }

                            break;
                        case 12:
                            num2 = 604;
                            if (升天气功总类.NhanVatNgheNghiep10 != 1) break;
                            num2 = 104;
                            num2 = 35;
                            if (label12.Text.Length <= 7)
                            {
                                num2 = 273;
                                label12.Text = 升天气功总类.KhiCongTen;
                                textBox23.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 308;
                                break;
                            }

                            num2 = 343;
                            if (label13.Text.Length <= 7)
                            {
                                num2 = 606;
                                label13.Text = 升天气功总类.KhiCongTen;
                                textBox24.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 447;
                                break;
                            }

                            num2 = 329;
                            if (label14.Text.Length <= 7)
                            {
                                num2 = 548;
                                label14.Text = 升天气功总类.KhiCongTen;
                                textBox25.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 186;
                                break;
                            }

                            num2 = 322;
                            if (label15.Text.Length <= 7)
                            {
                                num2 = 72;
                                label15.Text = 升天气功总类.KhiCongTen;
                                textBox26.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 559;
                                break;
                            }

                            num2 = 470;
                            if (label16.Text.Length <= 7)
                            {
                                num2 = 594;
                                label16.Text = 升天气功总类.KhiCongTen;
                                textBox27.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 549;
                                break;
                            }

                            num2 = 318;
                            if (label17.Text.Length <= 7)
                            {
                                num2 = 386;
                                label17.Text = 升天气功总类.KhiCongTen;
                                textBox28.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 90;
                                break;
                            }

                            num2 = 545;
                            if (label18.Text.Length <= 7)
                            {
                                num2 = 326;
                                label18.Text = 升天气功总类.KhiCongTen;
                                textBox29.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 613;
                                break;
                            }

                            num2 = 365;
                            if (label19.Text.Length <= 7)
                            {
                                num2 = 9;
                                label19.Text = 升天气功总类.KhiCongTen;
                                textBox30.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 502;
                                break;
                            }

                            num2 = 483;
                            if (label20.Text.Length <= 7)
                            {
                                num2 = 94;
                                label20.Text = 升天气功总类.KhiCongTen;
                                textBox31.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 513;
                                break;
                            }

                            num2 = 580;
                            if (label21.Text.Length <= 7)
                            {
                                num2 = 17;
                                label21.Text = 升天气功总类.KhiCongTen;
                                textBox32.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                label22.Hide();
                                label23.Hide();
                                label24.Hide();
                                label25.Hide();
                                label26.Hide();
                                textBox33.Hide();
                                textBox34.Hide();
                                textBox35.Hide();
                                textBox36.Hide();
                                textBox37.Hide();
                                num2 = 448;
                                break;
                            }

                            num2 = 399;
                            if (label22.Text.Length <= 7)
                            {
                                num2 = 53;
                                label22.Text = 升天气功总类.KhiCongTen;
                                textBox33.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 534;
                                break;
                            }

                            num2 = 174;
                            if (label23.Text.Length <= 7)
                            {
                                num2 = 214;
                                label23.Text = 升天气功总类.KhiCongTen;
                                textBox34.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 491;
                                break;
                            }

                            num2 = 26;
                            if (label24.Text.Length <= 7)
                            {
                                num2 = 14;
                                label24.Text = 升天气功总类.KhiCongTen;
                                textBox35.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 144;
                                break;
                            }

                            num2 = 364;
                            if (label25.Text.Length <= 7)
                            {
                                num2 = 427;
                                label25.Text = 升天气功总类.KhiCongTen;
                                textBox36.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 518;
                                break;
                            }

                            num2 = 48;
                            if (label26.Text.Length <= 7)
                            {
                                num2 = 304;
                                label26.Text = 升天气功总类.KhiCongTen;
                                textBox37.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 118;
                            }

                            break;
                        case 13:
                            num2 = 574;
                            if (升天气功总类.NhanVatNgheNghiep10 != 1) break;
                            num2 = 234;
                            num2 = 298;
                            if (label12.Text.Length <= 7)
                            {
                                num2 = 472;
                                label12.Text = 升天气功总类.KhiCongTen;
                                textBox23.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 222;
                                break;
                            }

                            num2 = 147;
                            if (label13.Text.Length <= 7)
                            {
                                num2 = 525;
                                label13.Text = 升天气功总类.KhiCongTen;
                                textBox24.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 173;
                                break;
                            }

                            num2 = 314;
                            if (label14.Text.Length <= 7)
                            {
                                num2 = 243;
                                label14.Text = 升天气功总类.KhiCongTen;
                                textBox25.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 310;
                                break;
                            }

                            num2 = 366;
                            if (label15.Text.Length <= 7)
                            {
                                num2 = 27;
                                label15.Text = 升天气功总类.KhiCongTen;
                                textBox26.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 161;
                                break;
                            }

                            num2 = 81;
                            if (label16.Text.Length <= 7)
                            {
                                num2 = 37;
                                label16.Text = 升天气功总类.KhiCongTen;
                                textBox27.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 420;
                                break;
                            }

                            num2 = 250;
                            if (label17.Text.Length <= 7)
                            {
                                num2 = 463;
                                label17.Text = 升天气功总类.KhiCongTen;
                                textBox28.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 583;
                                break;
                            }

                            num2 = 38;
                            if (label18.Text.Length <= 7)
                            {
                                num2 = 320;
                                label18.Text = 升天气功总类.KhiCongTen;
                                textBox29.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 115;
                                break;
                            }

                            num2 = 157;
                            if (label19.Text.Length <= 7)
                            {
                                num2 = 184;
                                label19.Text = 升天气功总类.KhiCongTen;
                                textBox30.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 226;
                                break;
                            }

                            num2 = 195;
                            if (label20.Text.Length <= 7)
                            {
                                num2 = 59;
                                label20.Text = 升天气功总类.KhiCongTen;
                                textBox31.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 373;
                                break;
                            }

                            num2 = 341;
                            if (label21.Text.Length <= 7)
                            {
                                num2 = 102;
                                label21.Text = 升天气功总类.KhiCongTen;
                                textBox32.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                label22.Hide();
                                label23.Hide();
                                label24.Hide();
                                label25.Hide();
                                label26.Hide();
                                textBox33.Hide();
                                textBox34.Hide();
                                textBox35.Hide();
                                textBox36.Hide();
                                textBox37.Hide();
                                num2 = 563;
                                break;
                            }

                            num2 = 171;
                            if (label22.Text.Length <= 7)
                            {
                                num2 = 4;
                                label22.Text = 升天气功总类.KhiCongTen;
                                textBox33.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 116;
                                break;
                            }

                            num2 = 75;
                            if (label23.Text.Length <= 7)
                            {
                                num2 = 313;
                                label23.Text = 升天气功总类.KhiCongTen;
                                textBox34.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 356;
                                break;
                            }

                            num2 = 309;
                            if (label24.Text.Length <= 7)
                            {
                                num2 = 608;
                                label24.Text = 升天气功总类.KhiCongTen;
                                textBox35.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 278;
                                break;
                            }

                            num2 = 108;
                            if (label25.Text.Length <= 7)
                            {
                                num2 = 588;
                                label25.Text = 升天气功总类.KhiCongTen;
                                textBox36.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 372;
                                break;
                            }

                            num2 = 280;
                            if (label26.Text.Length <= 7)
                            {
                                num2 = 267;
                                label26.Text = 升天气功总类.KhiCongTen;
                                textBox37.Text = 升天气功总类.FLD_BonusRateValuePerPoint.ToString();
                                num2 = 288;
                            }

                            break;
                    }
                }

                num2 = 203;
                num2 = 119;
            }
            finally
            {
                enumerator.Dispose();
            }

            num2 = 2;
        }
        catch (Exception ex)
        {
            MessageBox.Show("加载气功数据出错 " + ex.Message);
        }
    }

    private void button1_Click(object sender, EventArgs e)
    {
        var num = comboBox1.SelectedIndex + 1;
        qglist.OrderByDescending(keyValuePair_0 => keyValuePair_0.Key);
        using var enumerator = World.KhiCongTangThem.Values.GetEnumerator();
        X_Khi_Cong_Tang_Them_Thuoc_Tinh 气功加成属性 = null;
        while (enumerator.MoveNext())
        {
            气功加成属性 = enumerator.Current;
            if (气功加成属性.FLD_JOB == num && qglist.TryGetValue(气功加成属性.FLD_INDEX, out var _))
            {
                if (气功加成属性.FLD_BonusRateValuePerPoint1 != qglist[气功加成属性.FLD_INDEX].FLD_加成1)
                    气功加成属性.FLD_BonusRateValuePerPoint1 = qglist[气功加成属性.FLD_INDEX].FLD_加成1;
                if (气功加成属性.FLD_BonusRateValuePerPoint2 != qglist[气功加成属性.FLD_INDEX].FLD_加成2)
                    气功加成属性.FLD_BonusRateValuePerPoint2 = qglist[气功加成属性.FLD_INDEX].FLD_加成2;
            }
        }
    }

    private 属性 getshuxing(int index, int int_0)
    {
        return null;
    }

    private 属性 getstshuxing()
    {
        return null;
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        comboBox1 = new ComboBox();
        groupBox1 = new GroupBox();
        label28 = new Label();
        label27 = new Label();
        textBox21 = new TextBox();
        textBox22 = new TextBox();
        textBox19 = new TextBox();
        textBox20 = new TextBox();
        textBox17 = new TextBox();
        textBox18 = new TextBox();
        textBox15 = new TextBox();
        textBox16 = new TextBox();
        textBox13 = new TextBox();
        textBox14 = new TextBox();
        textBox11 = new TextBox();
        textBox12 = new TextBox();
        textBox9 = new TextBox();
        textBox10 = new TextBox();
        textBox7 = new TextBox();
        textBox8 = new TextBox();
        textBox5 = new TextBox();
        textBox6 = new TextBox();
        textBox3 = new TextBox();
        textBox4 = new TextBox();
        textBox2 = new TextBox();
        textBox1 = new TextBox();
        label11 = new Label();
        label10 = new Label();
        label9 = new Label();
        label8 = new Label();
        label7 = new Label();
        label6 = new Label();
        label5 = new Label();
        label4 = new Label();
        label3 = new Label();
        label2 = new Label();
        label1 = new Label();
        groupBox2 = new GroupBox();
        label29 = new Label();
        textBox37 = new TextBox();
        textBox36 = new TextBox();
        textBox35 = new TextBox();
        textBox34 = new TextBox();
        textBox33 = new TextBox();
        textBox32 = new TextBox();
        textBox31 = new TextBox();
        textBox30 = new TextBox();
        textBox29 = new TextBox();
        textBox28 = new TextBox();
        textBox27 = new TextBox();
        textBox26 = new TextBox();
        textBox25 = new TextBox();
        textBox24 = new TextBox();
        textBox23 = new TextBox();
        label26 = new Label();
        label19 = new Label();
        label20 = new Label();
        label21 = new Label();
        label22 = new Label();
        label23 = new Label();
        label24 = new Label();
        label25 = new Label();
        label18 = new Label();
        label17 = new Label();
        label16 = new Label();
        label15 = new Label();
        label14 = new Label();
        label13 = new Label();
        label12 = new Label();
        button1 = new Button();
        groupBox1.SuspendLayout();
        groupBox2.SuspendLayout();
        SuspendLayout();
        comboBox1.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox1.FormattingEnabled = true;
        comboBox1.Items.AddRange(new object[13]
        {
            "Đao", "Kiếm", "Thương", "Cung Thủ", "Đại Phu", "Thích Khách", "Cầm Sư", "Hàn Bảo Quân", "Ghệ Hàn Bảo Quân",
            "Quyền Sư",
            "Mai Liễu Chân", "Tử Hào", "Ghệ Tử Hào"
        });
        comboBox1.Location = new Point(14, 13);
        comboBox1.Name = "comboBox1";
        comboBox1.Size = new Size(121, 21);
        comboBox1.TabIndex = 1;
        comboBox1.SelectedIndexChanged += comboBox1_SelectedIndexChanged;
        groupBox1.Controls.Add(label28);
        groupBox1.Controls.Add(label27);
        groupBox1.Controls.Add(textBox21);
        groupBox1.Controls.Add(textBox22);
        groupBox1.Controls.Add(textBox19);
        groupBox1.Controls.Add(textBox20);
        groupBox1.Controls.Add(textBox17);
        groupBox1.Controls.Add(textBox18);
        groupBox1.Controls.Add(textBox15);
        groupBox1.Controls.Add(textBox16);
        groupBox1.Controls.Add(textBox13);
        groupBox1.Controls.Add(textBox14);
        groupBox1.Controls.Add(textBox11);
        groupBox1.Controls.Add(textBox12);
        groupBox1.Controls.Add(textBox9);
        groupBox1.Controls.Add(textBox10);
        groupBox1.Controls.Add(textBox7);
        groupBox1.Controls.Add(textBox8);
        groupBox1.Controls.Add(textBox5);
        groupBox1.Controls.Add(textBox6);
        groupBox1.Controls.Add(textBox3);
        groupBox1.Controls.Add(textBox4);
        groupBox1.Controls.Add(textBox2);
        groupBox1.Controls.Add(textBox1);
        groupBox1.Controls.Add(label11);
        groupBox1.Controls.Add(label10);
        groupBox1.Controls.Add(label9);
        groupBox1.Controls.Add(label8);
        groupBox1.Controls.Add(label7);
        groupBox1.Controls.Add(label6);
        groupBox1.Controls.Add(label5);
        groupBox1.Controls.Add(label4);
        groupBox1.Controls.Add(label3);
        groupBox1.Controls.Add(label2);
        groupBox1.Controls.Add(label1);
        groupBox1.Location = new Point(14, 41);
        groupBox1.Name = "groupBox1";
        groupBox1.Size = new Size(287, 493);
        groupBox1.TabIndex = 3;
        groupBox1.TabStop = false;
        groupBox1.Text = "Normal QIgong";
        groupBox1.Enter += groupBox1_Enter;
        label28.AutoSize = true;
        label28.Location = new Point(195, 31);
        label28.Name = "label28";
        label28.Size = new Size(62, 13);
        label28.TabIndex = 34;
        label28.Text = "Tác dụng 2";
        label27.AutoSize = true;
        label27.Location = new Point(92, 31);
        label27.Name = "label27";
        label27.Size = new Size(62, 13);
        label27.TabIndex = 33;
        label27.Text = "Tác dụng 1";
        textBox21.Location = new Point(197, 414);
        textBox21.Name = "textBox21";
        textBox21.Size = new Size(71, 20);
        textBox21.TabIndex = 32;
        textBox22.Location = new Point(94, 414);
        textBox22.Name = "textBox22";
        textBox22.Size = new Size(68, 20);
        textBox22.TabIndex = 31;
        textBox19.Location = new Point(197, 378);
        textBox19.Name = "textBox19";
        textBox19.Size = new Size(71, 20);
        textBox19.TabIndex = 30;
        textBox20.Location = new Point(94, 378);
        textBox20.Name = "textBox20";
        textBox20.Size = new Size(68, 20);
        textBox20.TabIndex = 29;
        textBox17.Location = new Point(197, 342);
        textBox17.Name = "textBox17";
        textBox17.Size = new Size(71, 20);
        textBox17.TabIndex = 28;
        textBox18.Location = new Point(94, 342);
        textBox18.Name = "textBox18";
        textBox18.Size = new Size(68, 20);
        textBox18.TabIndex = 27;
        textBox15.Location = new Point(197, 307);
        textBox15.Name = "textBox15";
        textBox15.Size = new Size(71, 20);
        textBox15.TabIndex = 26;
        textBox16.Location = new Point(94, 307);
        textBox16.Name = "textBox16";
        textBox16.Size = new Size(68, 20);
        textBox16.TabIndex = 25;
        textBox13.Location = new Point(197, 271);
        textBox13.Name = "textBox13";
        textBox13.Size = new Size(71, 20);
        textBox13.TabIndex = 24;
        textBox14.Location = new Point(94, 271);
        textBox14.Name = "textBox14";
        textBox14.Size = new Size(68, 20);
        textBox14.TabIndex = 23;
        textBox11.Location = new Point(197, 235);
        textBox11.Name = "textBox11";
        textBox11.Size = new Size(71, 20);
        textBox11.TabIndex = 22;
        textBox12.Location = new Point(94, 235);
        textBox12.Name = "textBox12";
        textBox12.Size = new Size(68, 20);
        textBox12.TabIndex = 21;
        textBox9.Location = new Point(197, 199);
        textBox9.Name = "textBox9";
        textBox9.Size = new Size(71, 20);
        textBox9.TabIndex = 20;
        textBox10.Location = new Point(94, 199);
        textBox10.Name = "textBox10";
        textBox10.Size = new Size(68, 20);
        textBox10.TabIndex = 19;
        textBox7.Location = new Point(197, 164);
        textBox7.Name = "textBox7";
        textBox7.Size = new Size(71, 20);
        textBox7.TabIndex = 18;
        textBox8.Location = new Point(94, 164);
        textBox8.Name = "textBox8";
        textBox8.Size = new Size(68, 20);
        textBox8.TabIndex = 17;
        textBox5.Location = new Point(199, 128);
        textBox5.Name = "textBox5";
        textBox5.Size = new Size(71, 20);
        textBox5.TabIndex = 16;
        textBox6.Location = new Point(94, 128);
        textBox6.Name = "textBox6";
        textBox6.Size = new Size(68, 20);
        textBox6.TabIndex = 15;
        textBox3.Location = new Point(197, 92);
        textBox3.Name = "textBox3";
        textBox3.Size = new Size(71, 20);
        textBox3.TabIndex = 14;
        textBox4.Location = new Point(94, 92);
        textBox4.Name = "textBox4";
        textBox4.Size = new Size(68, 20);
        textBox4.TabIndex = 13;
        textBox2.Location = new Point(197, 56);
        textBox2.Name = "textBox2";
        textBox2.Size = new Size(71, 20);
        textBox2.TabIndex = 12;
        textBox1.Location = new Point(94, 56);
        textBox1.Name = "textBox1";
        textBox1.Size = new Size(68, 20);
        textBox1.TabIndex = 11;
        label11.AutoSize = true;
        label11.Location = new Point(6, 417);
        label11.Name = "label11";
        label11.Size = new Size(41, 13);
        label11.TabIndex = 10;
        label11.Text = "label11";
        label10.AutoSize = true;
        label10.Location = new Point(6, 381);
        label10.Name = "label10";
        label10.Size = new Size(41, 13);
        label10.TabIndex = 9;
        label10.Text = "label10";
        label9.AutoSize = true;
        label9.Location = new Point(6, 345);
        label9.Name = "label9";
        label9.Size = new Size(35, 13);
        label9.TabIndex = 8;
        label9.Text = "label9";
        label8.AutoSize = true;
        label8.Location = new Point(6, 310);
        label8.Name = "label8";
        label8.Size = new Size(35, 13);
        label8.TabIndex = 7;
        label8.Text = "label8";
        label7.AutoSize = true;
        label7.Location = new Point(6, 274);
        label7.Name = "label7";
        label7.Size = new Size(35, 13);
        label7.TabIndex = 6;
        label7.Text = "label7";
        label6.AutoSize = true;
        label6.Location = new Point(6, 238);
        label6.Name = "label6";
        label6.Size = new Size(35, 13);
        label6.TabIndex = 5;
        label6.Text = "label6";
        label5.AutoSize = true;
        label5.Location = new Point(6, 204);
        label5.Name = "label5";
        label5.Size = new Size(35, 13);
        label5.TabIndex = 4;
        label5.Text = "label5";
        label4.AutoSize = true;
        label4.Location = new Point(6, 170);
        label4.Name = "label4";
        label4.Size = new Size(35, 13);
        label4.TabIndex = 3;
        label4.Text = "label4";
        label3.AutoSize = true;
        label3.Location = new Point(6, 131);
        label3.Name = "label3";
        label3.Size = new Size(35, 13);
        label3.TabIndex = 2;
        label3.Text = "label3";
        label2.AutoSize = true;
        label2.Location = new Point(6, 95);
        label2.Name = "label2";
        label2.Size = new Size(35, 13);
        label2.TabIndex = 1;
        label2.Text = "label2";
        label1.AutoSize = true;
        label1.Location = new Point(6, 60);
        label1.Name = "label1";
        label1.Size = new Size(35, 13);
        label1.TabIndex = 0;
        label1.Text = "label1";
        groupBox2.Controls.Add(label29);
        groupBox2.Controls.Add(textBox37);
        groupBox2.Controls.Add(textBox36);
        groupBox2.Controls.Add(textBox35);
        groupBox2.Controls.Add(textBox34);
        groupBox2.Controls.Add(textBox33);
        groupBox2.Controls.Add(textBox32);
        groupBox2.Controls.Add(textBox31);
        groupBox2.Controls.Add(textBox30);
        groupBox2.Controls.Add(textBox29);
        groupBox2.Controls.Add(textBox28);
        groupBox2.Controls.Add(textBox27);
        groupBox2.Controls.Add(textBox26);
        groupBox2.Controls.Add(textBox25);
        groupBox2.Controls.Add(textBox24);
        groupBox2.Controls.Add(textBox23);
        groupBox2.Controls.Add(label26);
        groupBox2.Controls.Add(label19);
        groupBox2.Controls.Add(label20);
        groupBox2.Controls.Add(label21);
        groupBox2.Controls.Add(label22);
        groupBox2.Controls.Add(label23);
        groupBox2.Controls.Add(label24);
        groupBox2.Controls.Add(label25);
        groupBox2.Controls.Add(label18);
        groupBox2.Controls.Add(label17);
        groupBox2.Controls.Add(label16);
        groupBox2.Controls.Add(label15);
        groupBox2.Controls.Add(label14);
        groupBox2.Controls.Add(label13);
        groupBox2.Controls.Add(label12);
        groupBox2.Location = new Point(307, 41);
        groupBox2.Name = "groupBox2";
        groupBox2.Size = new Size(306, 493);
        groupBox2.TabIndex = 4;
        groupBox2.TabStop = false;
        groupBox2.Text = "Ac Qigong";
        label29.AutoSize = true;
        label29.Location = new Point(158, 31);
        label29.Name = "label29";
        label29.Size = new Size(55, 13);
        label29.TabIndex = 30;
        label29.Text = "Thêm vào";
        textBox37.Location = new Point(160, 451);
        textBox37.Name = "textBox37";
        textBox37.Size = new Size(100, 20);
        textBox37.TabIndex = 29;
        textBox36.Location = new Point(160, 423);
        textBox36.Name = "textBox36";
        textBox36.Size = new Size(100, 20);
        textBox36.TabIndex = 28;
        textBox35.Location = new Point(160, 394);
        textBox35.Name = "textBox35";
        textBox35.Size = new Size(100, 20);
        textBox35.TabIndex = 27;
        textBox34.Location = new Point(160, 366);
        textBox34.Name = "textBox34";
        textBox34.Size = new Size(100, 20);
        textBox34.TabIndex = 26;
        textBox33.Location = new Point(160, 338);
        textBox33.Name = "textBox33";
        textBox33.Size = new Size(100, 20);
        textBox33.TabIndex = 25;
        textBox32.Location = new Point(160, 310);
        textBox32.Name = "textBox32";
        textBox32.Size = new Size(100, 20);
        textBox32.TabIndex = 24;
        textBox31.Location = new Point(160, 282);
        textBox31.Name = "textBox31";
        textBox31.Size = new Size(100, 20);
        textBox31.TabIndex = 23;
        textBox30.Location = new Point(160, 254);
        textBox30.Name = "textBox30";
        textBox30.Size = new Size(100, 20);
        textBox30.TabIndex = 22;
        textBox29.Location = new Point(160, 225);
        textBox29.Name = "textBox29";
        textBox29.Size = new Size(100, 20);
        textBox29.TabIndex = 21;
        textBox28.Location = new Point(160, 197);
        textBox28.Name = "textBox28";
        textBox28.Size = new Size(100, 20);
        textBox28.TabIndex = 20;
        textBox27.Location = new Point(160, 169);
        textBox27.Name = "textBox27";
        textBox27.Size = new Size(100, 20);
        textBox27.TabIndex = 19;
        textBox26.Location = new Point(160, 141);
        textBox26.Name = "textBox26";
        textBox26.Size = new Size(100, 20);
        textBox26.TabIndex = 18;
        textBox25.Location = new Point(160, 113);
        textBox25.Name = "textBox25";
        textBox25.Size = new Size(100, 20);
        textBox25.TabIndex = 17;
        textBox24.Location = new Point(160, 85);
        textBox24.Name = "textBox24";
        textBox24.Size = new Size(100, 20);
        textBox24.TabIndex = 16;
        textBox23.Location = new Point(160, 56);
        textBox23.Name = "textBox23";
        textBox23.Size = new Size(100, 20);
        textBox23.TabIndex = 15;
        label26.AutoSize = true;
        label26.Location = new Point(6, 454);
        label26.Name = "label26";
        label26.Size = new Size(41, 13);
        label26.TabIndex = 14;
        label26.Text = "label26";
        label19.AutoSize = true;
        label19.Location = new Point(6, 257);
        label19.Name = "label19";
        label19.Size = new Size(41, 13);
        label19.TabIndex = 13;
        label19.Text = "label19";
        label20.AutoSize = true;
        label20.Location = new Point(6, 285);
        label20.Name = "label20";
        label20.Size = new Size(41, 13);
        label20.TabIndex = 12;
        label20.Text = "label20";
        label21.AutoSize = true;
        label21.Location = new Point(6, 313);
        label21.Name = "label21";
        label21.Size = new Size(41, 13);
        label21.TabIndex = 11;
        label21.Text = "label21";
        label22.AutoSize = true;
        label22.Location = new Point(6, 341);
        label22.Name = "label22";
        label22.Size = new Size(41, 13);
        label22.TabIndex = 10;
        label22.Text = "label22";
        label23.AutoSize = true;
        label23.Location = new Point(6, 369);
        label23.Name = "label23";
        label23.Size = new Size(41, 13);
        label23.TabIndex = 9;
        label23.Text = "label23";
        label24.AutoSize = true;
        label24.Location = new Point(6, 397);
        label24.Name = "label24";
        label24.Size = new Size(41, 13);
        label24.TabIndex = 8;
        label24.Text = "label24";
        label25.AutoSize = true;
        label25.Location = new Point(6, 426);
        label25.Name = "label25";
        label25.Size = new Size(41, 13);
        label25.TabIndex = 7;
        label25.Text = "label25";
        label18.AutoSize = true;
        label18.Location = new Point(6, 228);
        label18.Name = "label18";
        label18.Size = new Size(41, 13);
        label18.TabIndex = 6;
        label18.Text = "label18";
        label17.AutoSize = true;
        label17.Location = new Point(6, 199);
        label17.Name = "label17";
        label17.Size = new Size(41, 13);
        label17.TabIndex = 5;
        label17.Text = "label17";
        label16.AutoSize = true;
        label16.Location = new Point(6, 172);
        label16.Name = "label16";
        label16.Size = new Size(41, 13);
        label16.TabIndex = 4;
        label16.Text = "label16";
        label15.AutoSize = true;
        label15.Location = new Point(6, 144);
        label15.Name = "label15";
        label15.Size = new Size(41, 13);
        label15.TabIndex = 3;
        label15.Text = "label15";
        label14.AutoSize = true;
        label14.Location = new Point(6, 116);
        label14.Name = "label14";
        label14.Size = new Size(41, 13);
        label14.TabIndex = 2;
        label14.Text = "label14";
        label13.AutoSize = true;
        label13.Location = new Point(6, 88);
        label13.Name = "label13";
        label13.Size = new Size(41, 13);
        label13.TabIndex = 1;
        label13.Text = "label13";
        label12.AutoSize = true;
        label12.Location = new Point(6, 59);
        label12.Name = "label12";
        label12.Size = new Size(41, 13);
        label12.TabIndex = 0;
        label12.Text = "label12";
        button1.Location = new Point(159, 11);
        button1.Name = "button1";
        button1.Size = new Size(142, 25);
        button1.TabIndex = 5;
        button1.Text = "Save";
        button1.UseVisualStyleBackColor = true;
        button1.Click += button1_Click;
        AutoScaleDimensions = new SizeF(6f, 13f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(631, 558);
        Controls.Add(button1);
        Controls.Add(groupBox2);
        Controls.Add(groupBox1);
        Controls.Add(comboBox1);
        Name = "SkillContrl";
        StartPosition = FormStartPosition.CenterScreen;
        Text = "Qigong Setting";
        Load += SkillContrl_Load;
        groupBox1.ResumeLayout(false);
        groupBox1.PerformLayout();
        groupBox2.ResumeLayout(false);
        groupBox2.PerformLayout();
        ResumeLayout(false);
    }

    private void groupBox1_Enter(object sender, EventArgs e)
    {
    }

    [Serializable]
    [CompilerGenerated]
    private sealed class Class1
    {
        public static readonly Class1 class1_0 = new();

        public static Func<KeyValuePair<int, 属性>, int> func_0;

        internal int method_0(KeyValuePair<int, 属性> keyValuePair_0)
        {
            return keyValuePair_0.Key;
        }
    }
}