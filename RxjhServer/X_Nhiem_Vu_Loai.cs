using System.Collections.Generic;

namespace RxjhServer;

public class X_Nhiem_Vu_Loai
{
    public X_Nhiem_Vu_Loai(byte[] byte_0)
    {
        任务_byte = byte_0;
    }

    public X_Nhiem_Vu_Loai()
    {
        任务_byte = new byte[30];
    }

    public byte[] 任务_byte { get; set; }

    public int NhiemVuID
    {
        get
        {
            var array = new byte[2];
            System.Buffer.BlockCopy(任务_byte, 0, array, 0, 2);
            return Buffer.ToInt16(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, 任务_byte, 0, 2);
    }

    public int NhiemVuGiaiDoanID
    {
        get
        {
            var array = new byte[2];
            System.Buffer.BlockCopy(任务_byte, 2, array, 0, 2);
            return Buffer.ToInt16(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, 任务_byte, 2, 2);
    }

    public byte[] 执行阶段数据 { get; set; }

    public int 任务开关 { get; set; }

    public int NhiemVu_Type { get; set; }

    public string 任务传书 { get; set; }

    public int RwID { get; set; }

    public string 任务名 { get; set; } = string.Empty;

    public int 任务等级 { get; set; }

    public int 任务正邪 { get; set; }

    public int 职业 { get; set; }

    public 坐标类 Npc坐标 { get; set; } = new();

    public int NpcID { get; set; }

    public string NPCNAME { get; set; }

    public List<任务需要物品类> 任务需要物品 { get; set; } = new();

    public List<任务获得物品类> 任务获得物品 { get; set; } = new();

    public int NhiemVuGiaiDoan_SoLuong { get; set; }

    public List<X_Nhiem_Vu_Giai_Doan_Loai> NhiemVu_GiaiDoan { get; set; } = new();

    public X_Nhiem_Vu_Loai GetRW(int int_8)
    {
        using (var enumerator = World.NhiemVulist.Values.GetEnumerator())
        {
            X_Nhiem_Vu_Loai 任务类 = null;
            while (enumerator.MoveNext())
            {
                任务类 = enumerator.Current;
                if (任务类.RwID == int_8) return 任务类;
            }
        }

        return null;
    }
}