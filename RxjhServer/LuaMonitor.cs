using System;
using System.Collections.Generic;
using System.Threading;
using System.Diagnostics;

namespace RxjhServer
{
    public static class LuaMonitor
    {
        private static readonly Dictionary<string, LuaExecutionStats> ExecutionStats = new Dictionary<string, LuaExecutionStats>();
        private static readonly object StatsLock = new object();
        private static Timer MonitorTimer;
        
        public static void StartMonitoring()
        {
            Form1.WriteLine(2, "Starting Lua execution monitoring...");
            
            // Start monitoring timer (every 30 seconds)
            MonitorTimer = new Timer(ReportStats, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }
        
        public static void StopMonitoring()
        {
            MonitorTimer?.Dispose();
            Form1.WriteLine(2, "Lua execution monitoring stopped");
        }
        
        public static void RecordExecution(string functionName, long executionTimeMs, bool success, string error = null)
        {
            lock (StatsLock)
            {
                if (!ExecutionStats.ContainsKey(functionName))
                {
                    ExecutionStats[functionName] = new LuaExecutionStats(functionName);
                }
                
                var stats = ExecutionStats[functionName];
                stats.TotalExecutions++;
                stats.TotalExecutionTime += executionTimeMs;
                
                if (success)
                {
                    stats.SuccessfulExecutions++;
                }
                else
                {
                    stats.FailedExecutions++;
                    stats.LastError = error;
                    stats.LastErrorTime = DateTime.Now;
                }
                
                if (executionTimeMs > stats.MaxExecutionTime)
                {
                    stats.MaxExecutionTime = executionTimeMs;
                }
                
                if (stats.MinExecutionTime == 0 || executionTimeMs < stats.MinExecutionTime)
                {
                    stats.MinExecutionTime = executionTimeMs;
                }
            }
        }
        
        public static void RecordLuaCall(string functionName, Action luaCall)
        {
            var stopwatch = Stopwatch.StartNew();
            bool success = false;
            string error = null;
            
            try
            {
                luaCall();
                success = true;
            }
            catch (Exception ex)
            {
                success = false;
                error = ex.Message;
                throw; // Re-throw the exception
            }
            finally
            {
                stopwatch.Stop();
                RecordExecution(functionName, stopwatch.ElapsedMilliseconds, success, error);
            }
        }
        
        private static void ReportStats(object state)
        {
            try
            {
                lock (StatsLock)
                {
                    if (ExecutionStats.Count == 0)
                    {
                        return;
                    }
                    
                    Form1.WriteLine(2, "=== Lua Execution Statistics ===");
                    
                    var problematicFunctions = new List<string>();
                    
                    foreach (var kvp in ExecutionStats)
                    {
                        var stats = kvp.Value;
                        var avgTime = stats.TotalExecutions > 0 ? stats.TotalExecutionTime / stats.TotalExecutions : 0;
                        var successRate = stats.TotalExecutions > 0 ? (stats.SuccessfulExecutions * 100.0 / stats.TotalExecutions) : 0;
                        
                        Form1.WriteLine(2, $"Function: {stats.FunctionName}");
                        Form1.WriteLine(2, $"  Total calls: {stats.TotalExecutions}");
                        Form1.WriteLine(2, $"  Success rate: {successRate:F1}%");
                        Form1.WriteLine(2, $"  Avg time: {avgTime}ms");
                        Form1.WriteLine(2, $"  Max time: {stats.MaxExecutionTime}ms");
                        
                        if (stats.FailedExecutions > 0)
                        {
                            Form1.WriteLine(1, $"  Failed calls: {stats.FailedExecutions}");
                            Form1.WriteLine(1, $"  Last error: {stats.LastError}");
                            Form1.WriteLine(1, $"  Last error time: {stats.LastErrorTime}");
                            problematicFunctions.Add(stats.FunctionName);
                        }
                        
                        // Check for performance issues
                        if (avgTime > 1000) // More than 1 second average
                        {
                            Form1.WriteLine(1, $"  WARNING: Slow execution detected for {stats.FunctionName}");
                            if (!problematicFunctions.Contains(stats.FunctionName))
                            {
                                problematicFunctions.Add(stats.FunctionName);
                            }
                        }
                    }
                    
                    if (problematicFunctions.Count > 0)
                    {
                        Form1.WriteLine(1, $"Problematic functions detected: {string.Join(", ", problematicFunctions)}");
                        Form1.WriteLine(1, "Consider reviewing these scripts for optimization or error handling");
                    }
                    
                    Form1.WriteLine(2, "=== End Statistics ===");
                }
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"LuaMonitor.ReportStats error: {ex.Message}");
            }
        }
        
        public static void ClearStats()
        {
            lock (StatsLock)
            {
                ExecutionStats.Clear();
                Form1.WriteLine(2, "Lua execution statistics cleared");
            }
        }
        
        public static LuaExecutionStats GetStats(string functionName)
        {
            lock (StatsLock)
            {
                return ExecutionStats.ContainsKey(functionName) ? ExecutionStats[functionName] : null;
            }
        }
    }
    
    public class LuaExecutionStats
    {
        public string FunctionName { get; set; }
        public long TotalExecutions { get; set; }
        public long SuccessfulExecutions { get; set; }
        public long FailedExecutions { get; set; }
        public long TotalExecutionTime { get; set; }
        public long MaxExecutionTime { get; set; }
        public long MinExecutionTime { get; set; }
        public string LastError { get; set; }
        public DateTime LastErrorTime { get; set; }
        
        public LuaExecutionStats(string functionName)
        {
            FunctionName = functionName;
            TotalExecutions = 0;
            SuccessfulExecutions = 0;
            FailedExecutions = 0;
            TotalExecutionTime = 0;
            MaxExecutionTime = 0;
            MinExecutionTime = 0;
            LastError = null;
            LastErrorTime = DateTime.MinValue;
        }
    }
}
