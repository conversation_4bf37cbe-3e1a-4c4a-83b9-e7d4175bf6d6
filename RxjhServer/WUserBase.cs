using System;
using System.Runtime.CompilerServices;
using System.Threading;

namespace RxjhServer;

public class WUserBase<TContext> : IWUser<TContext>
{
    [NonSerialized]
    private readonly ReaderWriterLockSlim _objLock = Locks.GetLockInstance(LockRecursionPolicy.NoRecursion);

    [CompilerGenerated] private bool bool_0;

    [CompilerGenerated] private DateTime dateTime_0;

    [CompilerGenerated] private TContext gparam_0;

    [CompilerGenerated] private int int_0;

    public WUserBase()
    {
        Index = -1;
        Timestamp = DateTime.MinValue;
        Invalid = true;
    }

    public DateTime Timestamp { get; set; }

    public int Index { get; set; }

    public bool Invalid { get; set; }

    public TContext Context { get; set; }

    public string Credentials { get; private set; } = string.Empty;

    public IDisposable ReadLock => new ReadOnlyLock(_objLock);

    public IDisposable UpdateLock => new ReadLock(_objLock);

    public IDisposable WriteLock => new WriteLock(_objLock);

    public virtual void Reset()
    {
        Credentials = Guid.NewGuid().ToString();
        Context = default;
    }

    public bool Authentication(string credentials)
    {
        if (!string.IsNullOrEmpty(Credentials) && !string.IsNullOrEmpty(credentials))
            return !(Credentials != credentials);
        return false;
    }

    public override string ToString()
    {
        return $"{Index}-{Timestamp}";
    }
}