using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class UserList : Form
{
    private ListView eval_a_a;

    private ColumnHeader eval_b_b;

    private ColumnHeader eval_c_c;

    private ColumnHeader eval_d_d;

    private ColumnHeader eval_e_e;

    private ColumnHeader eval_f_f;

    private ColumnHeader eval_g_g;

    private ColumnHeader eval_h;

    private ColumnHeader eval_i;

    private ContextMenu eval_j;

    private MenuItem eval_k;

    private MenuItem eval_l;

    private MenuItem eval_m;

    private ColumnHeader eval_n;

    private MenuItem eval_o;

    private ColumnHeader eval_p;

    private ColumnHeader eval_q;

    private ColumnHeader eval_r;

    private ColumnHeader eval_s;

    private ColumnHeader eval_t;

    private ColumnHeader eval_u;

    private ColumnHeader eval_v;

    private ColumnHeader eval_w;

    private MenuItem eval_x;

    public UserList()
    {
        InitializeComponent();
    }

    protected override void Dispose(bool disposing)
    {
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        eval_a_a = new ListView();
        eval_b_b = new ColumnHeader();
        eval_c_c = new ColumnHeader();
        eval_d_d = new ColumnHeader();
        eval_e_e = new ColumnHeader();
        eval_n = new ColumnHeader();
        eval_f_f = new ColumnHeader();
        eval_g_g = new ColumnHeader();
        eval_h = new ColumnHeader();
        eval_i = new ColumnHeader();
        eval_p = new ColumnHeader();
        eval_t = new ColumnHeader();
        eval_q = new ColumnHeader();
        eval_u = new ColumnHeader();
        eval_s = new ColumnHeader();
        eval_r = new ColumnHeader();
        eval_v = new ColumnHeader();
        eval_w = new ColumnHeader();
        eval_j = new ContextMenu();
        eval_k = new MenuItem();
        eval_l = new MenuItem();
        eval_m = new MenuItem();
        eval_o = new MenuItem();
        eval_x = new MenuItem();
        SuspendLayout();
        eval_a_a.Columns.AddRange(new ColumnHeader[17]
        {
            eval_b_b, eval_c_c, eval_d_d, eval_e_e, eval_n, eval_f_f, eval_g_g, eval_h, eval_i, eval_p,
            eval_t, eval_q, eval_u, eval_s, eval_r, eval_v, eval_w
        });
        eval_a_a.ContextMenu = eval_j;
        eval_a_a.Dock = DockStyle.Fill;
        eval_a_a.ForeColor = SystemColors.WindowText;
        eval_a_a.FullRowSelect = true;
        eval_a_a.GridLines = true;
        eval_a_a.Location = new Point(0, 0);
        eval_a_a.Name = "eval_a_a";
        eval_a_a.Size = new Size(819, 406);
        eval_a_a.TabIndex = 0;
        eval_a_a.UseCompatibleStateImageBehavior = false;
        eval_a_a.View = View.Details;
        eval_a_a.SelectedIndexChanged += eval_a;
        eval_b_b.Text = "序号";
        eval_b_b.Width = 36;
        eval_c_c.Text = "ID";
        eval_c_c.Width = 34;
        eval_d_d.Text = "名字";
        eval_d_d.Width = 78;
        eval_e_e.Text = "等级";
        eval_e_e.Width = 37;
        eval_n.Text = "HP";
        eval_n.Width = 45;
        eval_f_f.Text = "IP";
        eval_f_f.Width = 116;
        eval_g_g.Text = "地图";
        eval_g_g.Width = 41;
        eval_h.Text = "X";
        eval_i.Text = "Y";
        eval_i.Width = 62;
        eval_p.Text = "攻";
        eval_p.Width = 36;
        eval_t.Text = "攻加";
        eval_t.Width = 39;
        eval_q.Text = "防";
        eval_q.Width = 38;
        eval_u.Text = "防加";
        eval_u.Width = 39;
        eval_s.Text = "气";
        eval_s.Width = 34;
        eval_r.Text = "ping";
        eval_r.Width = 37;
        eval_v.Text = "攻强";
        eval_v.Width = 42;
        eval_w.Text = "防强";
        eval_w.Width = 36;
        eval_j.MenuItems.AddRange(new MenuItem[5] { eval_k, eval_l, eval_m, eval_o, eval_x });
        eval_k.Index = 0;
        eval_k.Text = "Kick Char";
        eval_k.Click += eval_f;
        eval_l.Index = 1;
        eval_l.Text = "Kick ID";
        eval_l.Click += eval_e;
        eval_m.Index = 2;
        eval_m.Text = "Ban IP";
        eval_m.Click += eval_d;
        eval_o.Index = 3;
        eval_o.Text = "Ban ID";
        eval_o.Click += eval_c;
        eval_x.Index = 4;
        eval_x.Text = "View more";
        eval_x.Click += eval_b;
        ClientSize = new Size(819, 406);
        Controls.Add(eval_a_a);
        Name = "UserList";
        Text = "UserList";
        Load += UserList_Load;
        ResumeLayout(false);
    }

    private void eval_a(object sender, EventArgs e)
    {
    }

    private void eval_b(object sender, EventArgs e)
    {
        if (eval_a_a.SelectedItems.Count > 0)
        {
            var text = eval_a_a.SelectedItems[0].SubItems[2].Text;
            var list = new UserIdList();
            list.username = text;
            list.ShowDialog();
        }
    }

    private void eval_c(object sender, EventArgs e)
    {
        if (eval_a_a.SelectedItems.Count > 0)
        {
            var text = eval_a_a.SelectedItems[0].SubItems[1].Text;
            DBA.ExeSqlCommand($"UPDATE TBL_ACCOUNT SET FLD_ZT=1 WHERE FLD_ID='{text}'", "rxjhaccount");
        }
    }

    private void eval_d(object sender, EventArgs e)
    {
        if (eval_a_a.SelectedItems.Count > 0)
        {
            var text = eval_a_a.SelectedItems[0].SubItems[5].Text;
            DBA.ExeSqlCommand(
                string.Format(" Insert into TBL_BANED (FLD_BANEDIP,FLD_BANEDNIP) values ( '{0}','{0}')", text),
                "rxjhaccount");
        }
    }

    private void eval_e(object sender, EventArgs e)
    {
        if (eval_a_a.SelectedItems.Count > 0)
        {
            var players = World.KiemTraNguoiChoi(eval_a_a.SelectedItems[0].SubItems[1].Text);
            if (players != null && players.Client != null) players.Client.Dispose();
        }
    }

    private void eval_f(object sender, EventArgs e)
    {
        if (eval_a_a.SelectedItems.Count > 0)
        {
            var players = World.FindPlayerbyName(eval_a_a.SelectedItems[0].SubItems[2].Text);
            if (players != null && players.Client != null) players.Client.Dispose();
        }
    }

    private void UserList_Load(object sender, EventArgs e)
    {
        try
        {
            eval_a_a.ListViewItemSorter = new ListViewColumnSorter();
            eval_a_a.ColumnClick += ListViewHelper.ListView_ColumnClick;
            var cntOffline = 0;
            var cntIP = new List<string>();
            foreach (var players in World.allConnectedChars.Values)
            {
                if (players == null) continue;
                var playerIP = players.Client.ToString();
                if (!cntIP.Contains(playerIP)) cntIP.Add(playerIP);
                var items = new string[17];
                try
                {
                    items[0] = players.CharacterFullServerID.ToString();
                    items[1] = players.Userid;
                    if (players.Client.TreoMay)
                    {
                        cntOffline++;
                        items[2] = "[OFF]" + players.UserName;
                    }
                    else
                    {
                        items[2] = players.UserName;
                    }

                    items[3] = players.Player_Level.ToString();
                    items[4] = players.NhanVat_HP.ToString();
                    if (players.Client != null) items[5] = playerIP;
                    items[6] = players.NhanVatToaDo_BanDo.ToString();
                    items[7] = players.NhanVatToaDo_X.ToString();
                    items[8] = players.NhanVatToaDo_Y.ToString();
                    items[9] = players.FLD_NhanVatCoBan_CongKich.ToString();
                    items[10] = players.FLD_ThemVaoTiLePhanTram_CongKich.ToString();
                    items[11] = players.FLD_NhanVatCoBan_PhongNgu.ToString();
                    items[12] = players.FLD_ThemVaoTiLePhanTram_PhongNgu.ToString();
                    items[13] = players.FLD_TrangBi_ThemVao_KhiCong.ToString();
                    items[14] = players.Client.dwStop.ToString();
                    items[15] = players.CuongHoaVK.ToString();
                    items[16] = players.CuongHoaTB.ToString();
                }
                catch
                {
                    items[0] = players.CharacterFullServerID.ToString();
                    items[1] = players.Userid;
                    items[2] = players.UserName;
                    items[3] = players.Player_Level.ToString();
                }

                eval_a_a.Items.Insert(eval_a_a.Items.Count, new ListViewItem(items));
            }

            Text = "UserList (" + (World.allConnectedChars.Count - cntOffline) + ") (" + cntIP.Count + ")";
        }
        catch (Exception exception)
        {
            Form1.WriteLine(1, "人物列表出错" + exception.Message);
        }
    }
}