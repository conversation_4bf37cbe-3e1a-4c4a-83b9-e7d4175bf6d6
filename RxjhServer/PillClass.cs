namespace RxjhServer;

public class PillClass
{
    public int ID { get; set; }

    public int Pill_ID { get; set; }

    public string Pill_Name { get; set; }

    public string Level_Use { get; set; }

    public int Bonus_HP { get; set; }

    public int Bonus_HpPercent { get; set; }

    public int Bonus_MP { get; set; }

    public int Bonus_MpPercent { get; set; }

    public int Bonus_ATK { get; set; }

    public int Bonus_AtkPercent { get; set; }

    public int Bonus_DF { get; set; }

    public int Bonus_DfPercent { get; set; }

    public int Bonus_Evasion { get; set; }

    public int Bonus_EvaPercent { get; set; }

    public int Bonus_Accuracy { get; set; }

    public int Bonus_AccuPercent { get; set; }

    public int Bonus_AtkSkillPercent { get; set; }

    public int Bonus_DfSkill { get; set; }

    public int Bonus_Lucky { get; set; }

    public int Bonus_GoldPercent { get; set; }

    public int Bonus_Abilities { get; set; }

    public int Bonus_DiemHoangKim { get; set; }

    public int Public_Pill { get; set; }

    public int Bonus_ExpPercent { get; set; }

    public int Pill_Time { get; set; }

    public int Bonus_DfSkillPercent { get; set; }

    public int Upgrade_Weapon { get; set; }

    public int Upgrade_Armor { get; set; }

    public int Bonus_DropPercent { get; set; }

    public int Pill_Merge { get; set; }

    public string Cant_Use { get; set; }

    public int Pill_Days { get; set; }

    public int On_Off { get; set; }

    public int Hatch_Item { get; set; }

    public int TangHoa { get; set; }

    public static PillClass GetPillTinhNhan(int level)
    {
        foreach (var Value in World.List_Pill.Values)
            if (Value.TangHoa != 0 && Value.TangHoa == level)
                return Value;
        return null;
    }

    public static bool Check_Pill_Cant_Use(Players play, int itemid)
    {
        if (World.List_Pill.TryGetValue(itemid, out var cantuse))
        {
            if (!(cantuse.Cant_Use != "")) return false;
            var strArray = cantuse.Cant_Use.Split(',');
            if (strArray.Length == 0) return false;
            for (var i = 0; i < strArray.Length; i++)
                try
                {
                    var itemid_cantuse = int.Parse(strArray[i]);
                    if (play.AppendStatusList.ContainsKey(itemid_cantuse)) return true;
                    if (play.PublicDrugs.ContainsKey(itemid_cantuse)) return true;
                    if (play.TitleDrug.ContainsKey(itemid_cantuse)) return true;
                }
                catch
                {
                    return false;
                }
        }

        return false;
    }
}