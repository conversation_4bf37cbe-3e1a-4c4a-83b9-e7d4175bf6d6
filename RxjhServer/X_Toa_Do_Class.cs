using System;

namespace RxjhServer;

public class X_Toa_Do_Class : IDisposable
{
    public X_Toa_Do_Class(float Rxjh__X, float Rxjh__Y, float Rxjh__Z, int Rxjh__Map)
    {
        Rxjh_X = Rxjh__X;
        Rxjh_Y = Rxjh__Y;
        Rxjh_Z = Rxjh__Z;
        Rxjh_Map = Rxjh__Map;
    }

    public X_Toa_Do_Class()
    {
    }

    public float Rxjh_X { get; set; }

    public float Rxjh_Y { get; set; }

    public float Rxjh_Z { get; set; }

    public int Rxjh_Map { get; set; }

    public int FLD_PhiDiChuyen { get; set; }

    public string Rxjh_name { get; set; }

    public void Dispose()
    {
    }

    public static string getmapname(int int_0)
    {
        using (var enumerator = World.DiDong.GetEnumerator())
        {
            X_Toa_Do_Class 坐标Class = null;
            while (enumerator.MoveNext())
            {
                坐标Class = enumerator.Current;
                if (坐标Class.Rxjh_Map == int_0) return 坐标Class.Rxjh_name;
            }
        }

        return string.Empty;
    }

    public static int getmapid(string mapname)
    {
        using (var enumerator = World.DiDong.GetEnumerator())
        {
            X_Toa_Do_Class 坐标Class = null;
            while (enumerator.MoveNext())
            {
                坐标Class = enumerator.Current;
                if (坐标Class.Rxjh_name == mapname) return 坐标Class.Rxjh_Map;
            }
        }

        return 0;
    }

    public static string GetMapName(int int_0)
    {
        if (World.Maplist.TryGetValue(int_0, out var value)) return value;
        return string.Empty;
    }

    public static string getname1258(int mapid)
    {
        return mapid switch
        {
            201 => "Tam TaÌ Quan",
            101 => "HuyêÌn Bôòt Phaìi",
            401 => "无天阁1层",
            402 => "无天阁2层",
            403 => "无天阁3层",
            301 => "LiêÞu Chiình Quan",
            601 => "渊竹林",
            501 => "万寿阁1层",
            502 => "万寿阁2层",
            503 => "万寿阁3层",
            901 => "荤捧Package",
            801 => "Thêì Lýòc Chiêìn",
            701 => "竹火林",
            1101 => "柳善提督府",
            1001 => "神武门",
            1301 => "南明湖",
            1201 => "银币广场",
            1501 => "血魔洞2层",
            1401 => "血魔洞1层",
            1801 => "地灵洞2层",
            1701 => "地灵洞1层",
            1601 => "血魔洞3层",
            2001 => "南明洞",
            1901 => "地灵洞3层",
            2201 => "百武关",
            2101 => "松月关",
            2501 => "失落之地",
            2401 => "MaiLieuChan花迷宫",
            2711 => "迷宫第二层",
            2701 => "迷宫第一层",
            2601 => "钥匙房",
            2801 => "三界玄门",
            2721 => "迷宫第三层",
            5001 => "Bãìc HaÒi Bãng Cung",
            6001 => "Nam Lâm",
            3201 => "女王宫殿",
            2901 => "修炼之地",
            5201 => "北海玄冰宫",
            5101 => "北海水宫",
            5601 => "植物大战僵尸",
            5501 => "北海冰宫幻影",
            5401 => "玄冰地宫",
            25100 => "HôÒ Haòp Côìc",
            26000 => "Xiìch Thiên Giõìi",
            26100 => "Yêìn Phi Gia",
            26200 => "LaÞnh Ðiòa Kiêìm HoaÌng",
            _ => string.Empty
        };
    }

    public static string getname(int mapid)
    {
        return mapid switch
        {
            201 => "Tam Tà Quan",
            101 => "Huyền Bột Phái",
            401 => "无天阁1层",
            402 => "无天阁2层",
            403 => "无天阁3层",
            301 => "Liễu Chính Quan",
            601 => "渊竹林",
            501 => "万寿阁1层",
            502 => "万寿阁2层",
            503 => "万寿阁3层",
            901 => "荤捧Package",
            801 => "Thế Lực Chiến",
            701 => "竹火林",
            1101 => "柳善提督府",
            1001 => "神武门",
            1301 => "南明湖",
            1201 => "银币广场",
            1501 => "血魔洞2层",
            1401 => "血魔洞1层",
            1801 => "地灵洞2层",
            1701 => "地灵洞1层",
            1601 => "血魔洞3层",
            2001 => "南明洞",
            1901 => "地灵洞3层",
            2201 => "百武关",
            2101 => "松月关",
            2501 => "失落之地",
            2401 => "MaiLieuChan花迷宫",
            2711 => "迷宫第二层",
            2701 => "迷宫第一层",
            2601 => "钥匙房",
            2801 => "三界玄门",
            2721 => "迷宫第三层",
            5001 => "Bắc Hải Băng Cung",
            6001 => "Nam Lâm",
            3201 => "女王宫殿",
            2901 => "修炼之地",
            5201 => "北海玄冰宫",
            5101 => "北海水宫",
            5601 => "植物大战僵尸",
            5501 => "北海冰宫幻影",
            5401 => "玄冰地宫",
            25100 => "Hổ Hạp Cốc",
            26000 => "Xích Thiên Giới",
            26100 => "Yến Phi Gia",
            _ => string.Empty
        };
    }
}