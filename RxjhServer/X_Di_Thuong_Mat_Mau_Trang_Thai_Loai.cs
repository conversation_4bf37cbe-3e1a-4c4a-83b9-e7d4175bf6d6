using System;
using System.Timers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class X_Di_Thuong_Mat_Mau_Trang_Thai_Loai : IDisposable
{
    public NpcClass Npc;

    public int NpcPlayId;

    public Players Play;

    public DateTime time;

    public double ycztsl;

    public System.Timers.Timer 人物状态;

    public System.Timers.Timer 异常掉血时间;
    public System.Timers.Timer 怪物状态;

    public X_Di_Thuong_Mat_Mau_Trang_Thai_Loai(Players Play_, int 时间, int 异常ID, int 异常数量)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường mất máu trạng thái loại -NEW");
        FLD_PID = 异常ID;
        time = DateTime.Now;
        time = time.AddMilliseconds(时间);
        Play = Play_;
        人物状态 = new System.Timers.Timer(时间);
        人物状态.Elapsed += 时间结束事件1;
        人物状态.Enabled = true;
        人物状态.AutoReset = false;
        Trang_thai_hieu_qua(FLD_PID, 1, 异常数量, 时间 / 1000);
    }

    public X_Di_Thuong_Mat_Mau_Trang_Thai_Loai(NpcClass Play_, int _NpcPlayId, int 时间, int 异常ID, int 异常数量)
    {
        NpcPlayId = _NpcPlayId;
        FLD_PID = 异常ID;
        time = DateTime.Now;
        time = time.AddMilliseconds(时间);
        Npc = Play_;
        怪物状态 = new System.Timers.Timer(时间);
        怪物状态.Elapsed += 时间结束事件1;
        怪物状态.Enabled = true;
        怪物状态.AutoReset = false;
    }

    public int FLD_PID { get; set; }

    public void Dispose()
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường trạng thái loại -Dispose 222");
        if (怪物状态 != null)
        {
            怪物状态.Enabled = false;
            怪物状态.Close();
            怪物状态.Dispose();
            怪物状态 = null;
        }

        if (人物状态 != null)
        {
            人物状态.Enabled = false;
            人物状态.Close();
            人物状态.Dispose();
            人物状态 = null;
        }

        if (异常掉血时间 != null)
        {
            异常掉血时间.Enabled = false;
            异常掉血时间.Close();
            异常掉血时间.Dispose();
            异常掉血时间 = null;
        }

        Play = null;
        Npc = null;
    }

    public void TrangThai_BatThuongLoai_MatMau(double ycztsll)
    {
        var num2 = 1;
        while (true)
        {
            switch (num2)
            {
                default:
                    if (World.jlMsg == 1)
                    {
                        num2 = 2;
                        continue;
                    }

                    break;
                case 2:
                    Form1.WriteLine(0,
                        "Dị thường mất máu trạng thái loại - Dị thường mất máu trạng thái loại chảy máu");
                    num2 = 0;
                    continue;
                case 0:
                    break;
            }

            break;
        }

        ycztsl = ycztsll;
        异常掉血时间 = new System.Timers.Timer(1000.0);
        异常掉血时间.Elapsed += 异常掉血事件;
        异常掉血时间.Enabled = true;
        异常掉血时间.AutoReset = true;
    }

    public void 异常掉血事件(object sender, ElapsedEventArgs e)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường mất máu sự kiện");
        if (Play == null) return;
        Play.NhanVat_HP -= (int)ycztsl;
        if (Play.NhanVat_HP <= 0)
        {
            Play.Death();
            if (异常掉血时间 != null)
            {
                异常掉血时间.Enabled = false;
                异常掉血时间.Close();
                异常掉血时间.Dispose();
                异常掉血时间 = null;
            }
        }

        Play.CapNhat_HP_MP_SP();
    }

    public void 时间结束事件1(object sender, ElapsedEventArgs e)
    {
        var num2 = 1;
        while (true)
        {
            switch (num2)
            {
                default:
                    if (World.jlMsg == 1)
                    {
                        num2 = 2;
                        continue;
                    }

                    break;
                case 2:
                    Form1.WriteLine(0, "Thời gian kết thúc sự kiện 1 222");
                    num2 = 0;
                    continue;
                case 0:
                    break;
            }

            break;
        }

        ThoiGianKetThucSuKien();
    }

    public void ThoiGianKetThucSuKien()
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường trạng thái loại - Thời gian kết thúc sự kiện 222");
        try
        {
            var flag = true;
            while (true)
            {
                if (怪物状态 != null) goto IL_0036;
                IL_0190:
                while (true)
                {
                    IL_0190_2:
                    var flag2 = true;
                    while (true)
                    {
                        switch (Npc != null ? 11 : 8)
                        {
                            case 14:
                                goto IL_0036;
                            case 13:
                                return;
                            case 8:
                                if (Play != null) goto case 1;
                                return;
                            case 6:
                            case 11:
                                if (Npc.TrangThai_BatThuong != null) goto case 10;
                                goto case 9;
                            case 10:
                                Npc.TrangThai_BatThuong.Remove(FLD_PID);
                                goto case 9;
                            case 9:
                                Dispose();
                                return;
                            case 1:
                            case 15:
                                if (Play.TrangThai_MatMau_BatThuong != null) goto case 7;
                                goto case 2;
                            case 7:
                                Play.TrangThai_MatMau_BatThuong.Remove(FLD_PID);
                                goto case 2;
                            case 2:
                                Trang_thai_hieu_qua(FLD_PID, 0, 0, 0);
                                Dispose();
                                return;
                            case 4:
                            case 12:
                                return;
                            case 5:
                                continue;
                            case 3:
                                goto IL_0190_2;
                        }

                        break;
                    }

                    break;
                }

                continue;
                IL_0036:
                怪物状态.Enabled = false;
                怪物状态.Close();
                怪物状态.Dispose();
                怪物状态 = null;
                goto IL_0190;
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1,
                "Dị thường mất máu trạng thái loại Thời gian kết thúc sự kiệnPhạm sai lầm: [" + FLD_PID + "]" + ex);
        }
        finally
        {
            Dispose();
        }
    }

    public void Trang_thai_hieu_qua(int 异常ID, int 开关, int 异常数量, int 时间)
    {
        byte[] array = null;
        var flag = true;
        while (true)
        {
            if (World.jlMsg == 1) goto IL_001b;
            IL_01e8:
            while (true)
            {
                IL_01e8_2:
                array = Converter.HexStringToByte(
                    "AA5546003527401538008C0300002C0100000900000001000000000000006016A2496016A2492600000014000000000000008C030000E80300000900000001000000000000000000000055AA");
                System.Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 18, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 58, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(开关), 0, array, 22, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(开关), 0, array, 62, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(时间), 0, array, 38, 4);
                System.Buffer.BlockCopy(BitConverter.GetBytes(异常数量), 0, array, 42, 4);
                while (true)
                {
                    switch (Play != null ? 7 : 8)
                    {
                        case 6:
                            goto IL_001b;
                        case 5:
                            return;
                        case 7:
                            System.Buffer.BlockCopy(BitConverter.GetBytes(Play.CharacterFullServerID), 0, array, 14, 2);
                            System.Buffer.BlockCopy(BitConverter.GetBytes(Play.CharacterFullServerID), 0, array, 4, 2);
                            goto case 4;
                        case 4:
                            if (Play.Client != null) goto case 1;
                            goto case 2;
                        case 1:
                            Play.Client.SendMultiplePackage(array, array.Length);
                            goto case 2;
                        case 2:
                            Play.SendMultiplePacketsOfCurrentRangeBroadcastData(array, array.Length);
                            return;
                        case 8:
                            if (Npc != null) goto case 10;
                            return;
                        case 10:
                            System.Buffer.BlockCopy(BitConverter.GetBytes(Npc.FLD_INDEX), 0, array, 14, 2);
                            System.Buffer.BlockCopy(BitConverter.GetBytes(Npc.FLD_INDEX), 0, array, 4, 2);
                            Npc.QuangBaSoLieu(array, array.Length);
                            return;
                        case 0:
                            continue;
                        case 3:
                            goto IL_01e8_2;
                    }

                    break;
                }

                break;
            }

            continue;
            IL_001b:
            Form1.WriteLine(0, "Dị thường rơi lam trạng thái loại - Trạng thái hiệu quả 222");
            goto IL_01e8;
        }
    }
}