using System.Runtime.InteropServices;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class Buffer
{
    [DllImport("HelpTool.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
    public static extern void iCopyMemory(int int_0, int int_1, int int_2);

    [DllImport("HelpTool.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
    public static extern void Decrypt(int int_0, byte[] g_cur_key,
        [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 1)] byte[] lpBuffer);

    [DllImport("HelpTool.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
    public static extern void String2hex(string Str,
        [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 1)] byte[] lpBuffer);

    public static byte[] GetBytes(int value)
    {
        var array = new byte[4];
        array[3] = (byte)((uint)(value >> 24) & 0xFFu);
        array[2] = (byte)((uint)(value >> 16) & 0xFFu);
        array[1] = (byte)((uint)(value >> 8) & 0xFFu);
        array[0] = (byte)((uint)value & 0xFFu);
        return array;
    }

    public static byte[] GetBytes(long value)
    {
        var array = new byte[8];
        array[7] = (byte)((value >> 56) & 0xFF);
        array[6] = (byte)((value >> 48) & 0xFF);
        array[5] = (byte)((value >> 40) & 0xFF);
        array[4] = (byte)((value >> 32) & 0xFF);
        array[3] = (byte)((value >> 24) & 0xFF);
        array[2] = (byte)((value >> 16) & 0xFF);
        array[1] = (byte)((value >> 8) & 0xFF);
        array[0] = (byte)(value & 0xFF);
        return array;
    }

    public static int ToInt32(byte[] byte_0, int offset)
    {
        return (byte_0[offset] & 0xFF) | ((byte_0[offset + 1] & 0xFF) << 8) | ((byte_0[offset + 2] & 0xFF) << 16) |
               ((byte_0[offset + 3] & 0xFF) << 24);
    }

    public static short ToInt16(byte[] byte_0, int offset)
    {
        return (short)((byte_0[offset] & 0xFF) | ((byte_0[offset + 1] & 0xFF) << 8));
    }

    public static unsafe bool IsEquals(string str1, string str2)
    {
        var result = true;
        if (str1.Length != str2.Length) return false;
        var num = str1.Length % 4 != 0 ? 4 - str1.Length % 4 : 0;
        var num2 = str2.Length % 4 != 0 ? 4 - str1.Length % 4 : 0;
        var num3 = num > num2 ? num : num2;
        int i;
        for (i = 0; i < num3; i++)
        {
            if (i < num) str1 += "   ";
            if (i < num2) str2 += "   ";
        }

        fixed (char* ptr = str1)
        {
            fixed (char* ptr3 = str2)
            {
                var ptr2 = ptr;
                var ptr4 = ptr3;
                while (i < str1.Length)
                {
                    if (*(long*)ptr2 == *(long*)ptr4)
                    {
                        i += 4;
                        ptr2 += 8;
                        ptr4 += 8;
                        continue;
                    }

                    result = false;
                    break;
                }
            }
        }

        return result;
    }

    public static unsafe void BlockCopy(byte[] src, int srcOffset, byte[] dst, int dstOffset, int count)
    {
        var num = src.Length;
        var num2 = dst.Length;
        if (num - srcOffset < count || num2 - dstOffset < count)
        {
            Form1.WriteLine(1,
                "Hướng dẫn tra cứu vượt qua mấy tổ lớn nhỏ Mục tiêu mấy tổ [" + Converter.ToString(dst) + "]" +
                Converter.ToString(src));
            return;
        }

        fixed (byte* ptr = src)
        {
            fixed (byte* ptr3 = dst)
            {
                var ptr2 = ptr + srcOffset;
                iCopyMemory((int)(ptr3 + dstOffset), (int)ptr2, count);
            }
        }
    }
}