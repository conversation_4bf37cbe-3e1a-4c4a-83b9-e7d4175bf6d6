using System.Collections.Generic;

namespace RxjhServer;

public class ShopClass
{
    public int FLD_NID { get; set; }

    public int FLD_INDEX { get; set; }

    public int FLD_PID { get; set; }

    public long FLD_MONEY { get; set; }

    public int FLD_MAGIC0 { get; set; }

    public int FLD_MAGIC1 { get; set; }

    public int FLD_MAGIC2 { get; set; }

    public int FLD_MAGIC3 { get; set; }

    public int FLD_MAGIC4 { get; set; }

    public int CanVoHoang { get; set; }

    public int FLD_MAGICZh
    {
        get
        {
            var num = 0;
            if (FLD_MAGIC1 != 0) num++;
            if (FLD_MAGIC2 != 0) num++;
            if (FLD_MAGIC3 != 0) num++;
            if (FLD_MAGIC4 != 0) num++;
            return num;
        }
    }

    public static List<ShopClass> GetShopListAll(int int_9)
    {
        var list = new List<ShopClass>();
        using var enumerator = World.Shop.GetEnumerator();
        ShopClass shopClass = null;
        while (enumerator.MoveNext())
        {
            shopClass = enumerator.Current;
            if (shopClass.FLD_NID == int_9) list.Add(shopClass);
        }

        return list;
    }

    public static List<ShopClass> GetShopList(int int_9, int int_10)
    {
        var num = 0;
        var list2 = new List<ShopClass>();
        var list3 = new List<ShopClass>();
        if (int_10 == 0)
        {
            foreach (var item in World.Shop)
            {
                if (item.FLD_NID == int_9) list2.Add(item);
                if (list2.Count == 60) return list2;
            }

            return list2;
        }

        foreach (var item2 in World.Shop)
            if (item2.FLD_NID == int_9)
                list2.Add(item2);
        for (num = int_10 * 60; num < list2.Count; num++)
        {
            list3.Add(list2[num]);
            if (list3.Count == 60) break;
        }

        return list3;
    }

    public static ShopClass Getwp(int int_9)
    {
        using (var enumerator = World.Shop.GetEnumerator())
        {
            ShopClass shopClass = null;
            while (enumerator.MoveNext())
            {
                shopClass = enumerator.Current;
                if (shopClass.FLD_PID == int_9) return shopClass;
            }
        }

        return null;
    }

    public static int GetShop(int int_9)
    {
        var num = 0;
        using var enumerator = World.Shop.GetEnumerator();
        ShopClass shopClass = null;
        while (enumerator.MoveNext())
        {
            shopClass = enumerator.Current;
            if (shopClass.FLD_NID == int_9) num++;
        }

        return num;
    }
}