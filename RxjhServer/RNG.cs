using System.Security.Cryptography;

namespace RxjhServer;

public static class RNG
{
    private static readonly RNGCryptoServiceProvider rngcryptoServiceProvider_0 = new();

    private static readonly byte[] byte_0 = new byte[4];

    public static int Next()
    {
        rngcryptoServiceProvider_0.GetBytes(byte_0);
        var num = Buffer.ToInt32(byte_0, 0);
        if (num < 0) num = -num;
        return num;
    }

    public static int Next(int int_0)
    {
        rngcryptoServiceProvider_0.GetBytes(byte_0);
        var num = Buffer.ToInt32(byte_0, 0) % (int_0 + 1);
        if (num < 0) num = -num;
        return num;
    }

    public static int Next(int int_0, int int_1)
    {
        return Next(int_1 - int_0) + int_0;
    }
}