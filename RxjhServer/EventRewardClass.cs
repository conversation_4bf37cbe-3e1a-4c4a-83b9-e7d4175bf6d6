namespace RxjhServer;

public class EventRewardClass
{
    public int ID { get; set; }

    public string FLD_NAME { get; set; }

    public int FLD_RANK { get; set; }

    public int FLD_RANKX { get; set; }

    public int FLD_GOLD { get; set; }

    public int FLD_CASH { get; set; }

    public int FLD_VoHuan { get; set; }

    public int FLD_VoHoang { get; set; }

    public string FLD_Item { get; set; }

    public string FLD_EventCode { get; set; }

    public static EventRewardClass GetReward(int rank)
    {
        foreach (var Reward in World.List_EventReward.Values)
            if (rank >= Reward.FLD_RANK && rank <= Reward.FLD_RANKX && Reward.FLD_EventCode == "WorldBoss")
                return Reward;
        return null;
    }
}