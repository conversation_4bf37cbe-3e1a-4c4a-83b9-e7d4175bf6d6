using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;

namespace RxjhServer;

[Serializable]
public class ThreadSafeDictionary<TKey, TValue> : IDictionary<TKey, TValue>, ICollection<KeyValuePair<TKey, TValue>>,
    IEnumerable<KeyValuePair<TKey, TValue>>, IEnumerable, IThreadSafeDictionary<TKey, TValue>, IDisposable
{
    private IDictionary<TKey, TValue> dict = new Dictionary<TKey, TValue>();

    [NonSerialized]
    private ReaderWriterLockSlim dictionaryLock = Locks.GetLockInstance(LockRecursionPolicy.NoRecursion);

    public virtual TValue this[TKey gparam_0]
    {
        get
        {
            var readOnlyLock = new ReadOnlyLock(dictionaryLock);
            try
            {
                return dict[gparam_0];
            }
            finally
            {
                var num = 2;
                while (true)
                {
                    switch (num)
                    {
                        case 1:
                            break;
                        case 0:
                            ((IDisposable)readOnlyLock).Dispose();
                            num = 1;
                            continue;
                        default:
                            if (readOnlyLock != null)
                            {
                                num = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }
        }
        set
        {
            var writeLock = new WriteLock(dictionaryLock);
            try
            {
                dict[gparam_0] = value;
            }
            finally
            {
                var num = 2;
                while (true)
                {
                    switch (num)
                    {
                        case 1:
                            break;
                        case 0:
                            ((IDisposable)writeLock).Dispose();
                            num = 1;
                            continue;
                        default:
                            if (writeLock != null)
                            {
                                num = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }
        }
    }

    public virtual ICollection<TKey> Keys
    {
        get
        {
            var readOnlyLock = new ReadOnlyLock(dictionaryLock);
            try
            {
                return new List<TKey>(dict.Keys);
            }
            finally
            {
                var num = 2;
                while (true)
                {
                    switch (num)
                    {
                        case 1:
                            break;
                        case 0:
                            ((IDisposable)readOnlyLock).Dispose();
                            num = 1;
                            continue;
                        default:
                            if (readOnlyLock != null)
                            {
                                num = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }
        }
    }

    public virtual ICollection<TValue> Values
    {
        get
        {
            var readOnlyLock = new ReadOnlyLock(dictionaryLock);
            try
            {
                return new List<TValue>(dict.Values);
            }
            finally
            {
                var num = 0;
                while (true)
                {
                    switch (num)
                    {
                        case 2:
                            break;
                        case 1:
                            ((IDisposable)readOnlyLock).Dispose();
                            num = 2;
                            continue;
                        default:
                            if (readOnlyLock != null)
                            {
                                num = 1;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }
        }
    }

    public virtual int Count
    {
        get
        {
            var readOnlyLock = new ReadOnlyLock(dictionaryLock);
            try
            {
                return dict.Count;
            }
            finally
            {
                var num = 0;
                while (true)
                {
                    switch (num)
                    {
                        case 2:
                            break;
                        case 1:
                            ((IDisposable)readOnlyLock).Dispose();
                            num = 2;
                            continue;
                        default:
                            if (readOnlyLock != null)
                            {
                                num = 1;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }
        }
    }

    public virtual bool IsReadOnly
    {
        get
        {
            var readOnlyLock = new ReadOnlyLock(dictionaryLock);
            try
            {
                return dict.IsReadOnly;
            }
            finally
            {
                var num = 0;
                while (true)
                {
                    switch (num)
                    {
                        case 2:
                            break;
                        case 1:
                            ((IDisposable)readOnlyLock).Dispose();
                            num = 2;
                            continue;
                        default:
                            if (readOnlyLock != null)
                            {
                                num = 1;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }
        }
    }

    public virtual bool Remove(TKey key)
    {
        using (new WriteLock(dictionaryLock))
        {
            return dict.Remove(key);
        }
    }

    public virtual bool ContainsKey(TKey gparam_0)
    {
        var readOnlyLock = new ReadOnlyLock(dictionaryLock);
        try
        {
            return dict.ContainsKey(gparam_0);
        }
        finally
        {
            var num = 2;
            while (true)
            {
                switch (num)
                {
                    case 1:
                        break;
                    case 0:
                        ((IDisposable)readOnlyLock).Dispose();
                        num = 1;
                        continue;
                    default:
                        if (readOnlyLock != null)
                        {
                            num = 0;
                            continue;
                        }

                        break;
                }

                break;
            }
        }
    }

    public virtual bool TryGetValue(TKey gparam_0, out TValue value)
    {
        var readOnlyLock = new ReadOnlyLock(dictionaryLock);
        try
        {
            return dict.TryGetValue(gparam_0, out value);
        }
        finally
        {
            var num = 2;
            while (true)
            {
                switch (num)
                {
                    case 1:
                        break;
                    case 0:
                        ((IDisposable)readOnlyLock).Dispose();
                        num = 1;
                        continue;
                    default:
                        if (readOnlyLock != null)
                        {
                            num = 0;
                            continue;
                        }

                        break;
                }

                break;
            }
        }
    }

    public virtual void Clear()
    {
        var writeLock = new WriteLock(dictionaryLock);
        try
        {
            dict.Clear();
        }
        finally
        {
            var num = 2;
            while (true)
            {
                switch (num)
                {
                    case 0:
                        break;
                    default:
                        if (writeLock != null)
                        {
                            num = 1;
                            continue;
                        }

                        break;
                    case 1:
                        ((IDisposable)writeLock).Dispose();
                        num = 0;
                        continue;
                }

                break;
            }
        }
    }

    public virtual bool Contains(KeyValuePair<TKey, TValue> item)
    {
        var readOnlyLock = new ReadOnlyLock(dictionaryLock);
        try
        {
            return dict.Contains(item);
        }
        finally
        {
            var num = 2;
            while (true)
            {
                switch (num)
                {
                    case 0:
                        break;
                    default:
                        if (readOnlyLock != null)
                        {
                            num = 1;
                            continue;
                        }

                        break;
                    case 1:
                        ((IDisposable)readOnlyLock).Dispose();
                        num = 0;
                        continue;
                }

                break;
            }
        }
    }

    public virtual void Add(KeyValuePair<TKey, TValue> item)
    {
        var writeLock = new WriteLock(dictionaryLock);
        try
        {
            dict.Add(item);
        }
        finally
        {
            var num = 2;
            while (true)
            {
                switch (num)
                {
                    case 0:
                        break;
                    default:
                        if (writeLock != null)
                        {
                            num = 1;
                            continue;
                        }

                        break;
                    case 1:
                        ((IDisposable)writeLock).Dispose();
                        num = 0;
                        continue;
                }

                break;
            }
        }
    }

    public virtual void Add(TKey gparam_0, TValue value)
    {
        var writeLock = new WriteLock(dictionaryLock);
        try
        {
            dict.Add(gparam_0, value);
        }
        finally
        {
            var num = 0;
            while (true)
            {
                switch (num)
                {
                    case 1:
                        break;
                    default:
                        if (writeLock != null)
                        {
                            num = 2;
                            continue;
                        }

                        break;
                    case 2:
                        ((IDisposable)writeLock).Dispose();
                        num = 1;
                        continue;
                }

                break;
            }
        }
    }

    public virtual bool Remove(KeyValuePair<TKey, TValue> item)
    {
        var writeLock = new WriteLock(dictionaryLock);
        try
        {
            return dict.Remove(item);
        }
        finally
        {
            var num = 0;
            while (true)
            {
                switch (num)
                {
                    case 1:
                        break;
                    default:
                        if (writeLock != null)
                        {
                            num = 2;
                            continue;
                        }

                        break;
                    case 2:
                        ((IDisposable)writeLock).Dispose();
                        num = 1;
                        continue;
                }

                break;
            }
        }
    }

    public virtual void CopyTo(KeyValuePair<TKey, TValue>[] array, int arrayIndex)
    {
        var readOnlyLock = new ReadOnlyLock(dictionaryLock);
        try
        {
            dict.CopyTo(array, arrayIndex);
        }
        finally
        {
            var num = 0;
            while (true)
            {
                switch (num)
                {
                    case 1:
                        break;
                    default:
                        if (readOnlyLock != null)
                        {
                            num = 2;
                            continue;
                        }

                        break;
                    case 2:
                        ((IDisposable)readOnlyLock).Dispose();
                        num = 1;
                        continue;
                }

                break;
            }
        }
    }

    public virtual IEnumerator<KeyValuePair<TKey, TValue>> GetEnumerator()
    {
        throw new NotSupportedException(
            "Cannot enumerate a threadsafe dictionary.  Instead, enumerate the keys or values collection");
    }

    IEnumerator IEnumerable.GetEnumerator()
    {
        throw new NotSupportedException(
            "Cannot enumerate a threadsafe dictionary.  Instead, enumerate the keys or values collection");
    }

    public void Dispose()
    {
        if (dictionaryLock != null)
        {
            dictionaryLock.Dispose();
            dictionaryLock = null;
        }

        if (dict != null)
        {
            dict.Clear();
            dict = null;
        }
    }

    public void RemoveSafe(TKey key)
    {
        using (new ReadLock(dictionaryLock))
        {
            if (dict.ContainsKey(key))
                using (new WriteLock(dictionaryLock))
                {
                    dict.Remove(key);
                }
        }
    }

    public void MergeSafe(TKey gparam_0, TValue newValue)
    {
        var writeLock = new WriteLock(dictionaryLock);
        try
        {
            if (dict.ContainsKey(gparam_0)) dict.Remove(gparam_0);
            dict.Add(gparam_0, newValue);
        }
        finally
        {
            var num = 2;
            while (true)
            {
                switch (num)
                {
                    case 1:
                        break;
                    case 0:
                        ((IDisposable)writeLock).Dispose();
                        num = 1;
                        continue;
                    default:
                        if (writeLock != null)
                        {
                            num = 0;
                            continue;
                        }

                        break;
                }

                break;
            }
        }
    }
}