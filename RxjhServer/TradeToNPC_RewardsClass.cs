using System;

namespace RxjhServer;

public class TradeToNPC_RewardsClass
{
    public int ID { get; set; }

    public int NPC_PID { get; set; }

    public string NPC_Name { get; set; }

    public int Reward_PID { get; set; }

    public string Reward_Name { get; set; }

    public int Amount { get; set; }

    public int Magic0 { get; set; }

    public int Magic0_X { get; set; }

    public int FLD_PP { get; set; }

    public int FLD_Lock { get; set; }

    public int Trade_Times { get; set; }

    public static TradeToNPC_RewardsClass GetTrade(int Npc_pid)
    {
        var max = 0;
        var num = 0;
        var num2 = 0;
        foreach (var class3 in World.ListTradeToNPC_Rewards.Values)
            if (class3.NPC_PID == Npc_pid)
                max += class3.FLD_PP;
        num = new Random(World.GetRandomSeed()).Next(1, max + 1);
        foreach (var class2 in World.ListTradeToNPC_Rewards.Values)
            if (class2.NPC_PID == Npc_pid)
            {
                num2 += class2.FLD_PP;
                if (num2 >= num) return class2;
            }

        return null;
    }

    public static int GetTradeTimesByNPCID(int npcid)
    {
        foreach (var value in World.ListTradeToNPC_Rewards.Values)
            if (value.NPC_PID == npcid)
                return value.Trade_Times;
        return 0;
    }

    public static TradeToNPC_RewardsClass GetTradeToNPCByNPCID(int npcid)
    {
        foreach (var value in World.ListTradeToNPC_Rewards.Values)
            if (value.NPC_PID == npcid)
                return value;
        return null;
    }
}