using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class GMGJ : Form
{
    private Button button1;

    private Button button10;

    private Button button11;

    private Button button12;

    private Button button13;

    private Button button14;

    private Button button15;

    private Button button16;

    private Button button17;

    private Button button18;

    private Button button19;

    private Button button2;

    private Button button20;

    private Button button21;

    private Button button22;

    private Button button23;

    private Button button24;

    private Button button25;

    private Button button26;

    private Button button27;

    private Button button28;

    private Button button29;

    private Button button3;

    private Button button30;

    private Button button31;

    private Button button32;

    private But<PERSON> button33;

    private Button button34;

    private Button button35;

    private Button button36;

    private Button button37;

    private Button button38;

    private Button button39;

    private Button button4;

    private Button button40;

    private Button button41;

    private Button button5;

    private Button button6;

    private Button button7;

    private Button button8;

    private Button button9;

    private ComboBox comboBox1;

    private ComboBox comboBox11;

    private ComboBox comboBox12;

    private ComboBox comboBox13;

    private ComboBox comboBox14;

    private ComboBox comboBox15;

    private ComboBox comboBox16;

    private ComboBox comboBox17;

    private ComboBox comboBox18;

    private ComboBox comboBox19;

    private IContainer components;

    private GroupBox groupBox1;

    private GroupBox groupBox2;

    private Label label1;

    private Label label10;

    private Label label11;

    private Label label12;

    private Label label13;

    private Label label14;

    private Label label16;

    private Label label17;

    private Label label18;

    private Label label19;

    private Label label2;

    private Label label20;

    private Label label21;

    private Label label22;

    private Label label3;

    private Label label4;

    private Label label41;

    private Label label42;

    private Label label43;

    private Label label44;

    private Label label45;

    private Label label46;

    private Label label5;

    private Label label6;

    private Label label7;

    private Label label8;

    private Label label9;

    private ListBox listBox3;

    private StatusStrip statusStrip1;

    private TextBox textBox1;

    private TextBox textBox10;

    private TextBox textBox11;

    private TextBox textBox12;

    private TextBox textBox13;

    private TextBox textBox14;

    private TextBox textBox2;

    private TextBox textBox3;

    private TextBox textBox4;

    private TextBox textBox5;

    private TextBox textBox6;

    private TextBox textBox63;

    private TextBox textBox64;

    private TextBox textBox65;

    private TextBox textBox66;

    private TextBox textBox67;

    private TextBox textBox68;

    private TextBox textBox7;

    private TextBox textBox8;

    private TextBox textBox9;

    private ToolStripStatusLabel tishi;

    private ToolStripStatusLabel toolStripStatusLabel1;

    public GMGJ()
    {
        InitializeComponent();
        textBox1.Text = username;
        textBox6.Text = Player_Level;
        textBox8.Text = Player_Job;
        textBox4.Text = Player_Zx;
        textBox5.Text = Player_Job_leve;
        textBox7.Text = ChuyenSinh;
        textBox3.Text = Player_Sex;
        textBox9.Text = Player_Exp;
        tishi.Text = "Vui lòng nhập chính xác!";
        comboBox13.Text = "Không";
        comboBox14.Text = "Không";
        comboBox15.Text = "Không";
        comboBox16.Text = "Không";
        comboBox17.Text = "Không";
        comboBox18.Text = "Không";
        comboBox1.Text = "Không";
        comboBox19.Text = "Khoa khoá";
    }

    public string username { get; set; }

    public string Player_Level { get; set; }

    public string Player_Job { get; set; }

    public string Player_Zx { get; set; }

    public string Player_Job_leve { get; set; }

    public string ChuyenSinh { get; set; }

    public string Player_Sex { get; set; }

    public string Player_Exp { get; set; }

    internal static uint ComputeStringHash(string string_0)
    {
        var num = 0;
        var num2 = 0u;
        if (string_0 != null)
        {
            num2 = 2166136261u;
            for (num = 0; num < string_0.Length; num++) num2 = (string_0[num] ^ num2) * 16777619;
        }

        return num2;
    }

    private string cp1258tounicode(string nguon)
    {
        var vietmahoa =
            "AÌAìÂAÞEÌEìÊIÌIìOÌOìÔOÞUÌYìYìaÌaìaòaÞeÌeìêiÌiìoìoìôoÞuÌuìyìÃãÐðIÞiÞUÞuÞÕõÝýAòaòAÒaÒÂìâìÂÌâÌÂÒâÒÂÞâÞÂòâòÃìãìÃÌãÌÃÒãÒÃÞãÞÃòãòEòeòEÒeÒEÞeÞÊìêìÊÌêÌÊÒêÒÊÞêÞÊòêòIÒiÒIòiòOòoòOÒoÒÔìôìÔÌôÌÔÒôÒÔÞôÞÔòôòÕìõìÕÌõÌÕÒõÒÕÞõÞÕòõòUòuòUÒuÒÝìýìÝÌýÌÝÒýÒÝÞýÞÝòýòYÌyÌYòyòyÒyÒYÞyÞ";
        var unicode = new string[134]
        {
            "À", "Á", "Â", "Ã", "È", "É", "Ê", "Ì", "Í", "Ò",
            "Ó", "Ô", "Õ", "Ù", "Ú", "Ý", "à", "á", "â", "ã",
            "è", "é", "ê", "ì", "í", "ò", "ó", "ô", "õ", "ù",
            "ú", "ý", "Ă", "ă", "Đ", "đ", "Ĩ", "ĩ", "Ũ", "ũ",
            "Ơ", "ơ", "Ư", "ư", "Ạ", "ạ", "Ả", "ả", "Ấ", "ấ",
            "Ầ", "ầ", "Ẩ", "ẩ", "Ẫ", "ẫ", "Ậ", "ậ", "Ắ", "ắ",
            "Ằ", "ằ", "Ẳ", "ẳ", "Ẵ", "ẵ", "Ặ", "ặ", "Ẹ", "ẹ",
            "Ẻ", "ẻ", "Ẽ", "ẽ", "Ế", "ế", "Ề", "ề", "Ể", "ể",
            "Ễ", "ễ", "Ệ", "ệ", "Ỉ", "ỉ", "Ị", "ị", "Ọ", "ọ",
            "Ỏ", "ỏ", "Ố", "ố", "Ồ", "ồ", "Ổ", "ổ", "Ỗ", "ỗ",
            "Ộ", "ộ", "Ớ", "ớ", "Ờ", "ờ", "Ở", "ở", "Ỡ", "ỡ",
            "Ợ", "ợ", "Ụ", "ụ", "Ủ", "ủ", "Ứ", "ứ", "Ừ", "ừ",
            "Ử", "ử", "Ữ", "ữ", "Ự", "ự", "Ỳ", "ỳ", "Ỵ", "ỵ",
            "Ỷ", "ỷ", "Ỹ", "ỹ"
        };
        var newstring = "";
        for (var i = 0; i < nguon.Length; i++)
        {
            var index = vietmahoa.IndexOf(nguon[i]);
            newstring = index <= 0 ? newstring + nguon[i] : newstring + unicode[index];
        }

        return newstring;
    }

    private void comboBox11_SelectedIndexChanged(object sender, EventArgs e)
    {
        DataTable dataTable = null;
        var num2 = 0;
        var str = "";
        var text = comboBox11.Text;
        if (text == "Sách /Mũi tên") str = "13";
        if (text == "Áo choàng bang") str = "14";
        if (text == "Khác") str = "0";
        if (text == "Thú Nuôi") str = "15";
        if (text == "Vũ Khí") str = "4";
        if (text == "Y Phục") str = "1";
        if (text == "Dây Chuyền / Vòng Cổ") str = "7";
        if (text == "Tảng Đá / Ngọc") str = "16";
        if (text == "Bùa may mắn") str = "18";
        if (text == "Hộ Thủ") str = "2";
        if (text == "Khuyên Tai") str = "8";
        if (text == "Nhẫn") str = "10";
        if (text == "Hộp / Bảo Rương") str = "17";
        if (text == "Ủng / Giầy") str = "5";
        if (text == "Áo choàng") str = "12";
        if (text == "Nội Giáp") str = "6";
        if (text == "Gói Script") str = "20";
        listBox3.Items.Clear();
        comboBox12.Items.Clear();
        var string_ = "select * from TBL_XWWL_ITEM where FLD_RESIDE2='" + str + "'";
        dataTable = DBA.GetDBToDataTable(string_, "PublicDb");
        var textconver = listBox3.Text;
        for (num2 = 0; num2 < dataTable.Rows.Count; num2++)
        {
            textconver = FontVietName.Convert(dataTable.Rows[num2]["FLD_NAME"].ToString());
            var wind1258 = Encoding.GetEncoding(1258);
            var utf8 = Encoding.UTF8;
            var wind1258Bytes = wind1258.GetBytes(textconver);
            var utf8Bytes = Encoding.Convert(wind1258, utf8, wind1258Bytes);
            var utf8String = Encoding.UTF8.GetString(utf8Bytes);
            listBox3.Items.Add(utf8String);
            comboBox12.Items.Add(dataTable.Rows[num2]["FLD_PID"].ToString());
        }
    }

    private void listBox3_SelectedIndexChanged(object sender, EventArgs e)
    {
        comboBox12.SelectedIndex = listBox3.SelectedIndex;
    }

    private int AttributeSelection(string string_0, int int_0)
    {
        try
        {
            var num = ComputeStringHash(string_0);
            var num2 = num;
            return string_0 switch
            {
                "Tỷ lệ cường hoá/ hợp thành %" => 90000000 + int_0,
                "Công lực tăng" => 10000000 + int_0,
                "Kinh nghiệm khi chết giảm %" => 130000000 + int_0,
                "X.Suất nhận tiền %" => 120000000 + int_0,
                "Chính xác tăng" => 50000000 + int_0,
                "Lực phòng ngự tăng" => 20000000 + int_0,
                "Lực phòng ngự  võ công tăng" => 110000000 + int_0,
                "Số lượng sử dụng" => 2000000000 + int_0,
                "Red Blue Refining" => 2010000000 + int_0,
                "New recovery item capacity" => 1110000000 + int_0,
                "Né tránh tăng" => 60000000 + int_0,
                "Lực đã kích tăng" => 100000000 + int_0,
                "Khí công tăng" => 80000000 + int_0,
                "Nội công tăng" => 40000000 + int_0,
                "Công kích võ công %" => 70000000 + int_0,
                "Kinh nghiệm tăng %" => 150000000 + int_0,
                "Not Know" => 1020000000 + int_0,
                "Sinh mệnh tăng" => 30000000 + int_0,
                _ => 0
            };
        }
        catch
        {
            return 0;
        }
    }

    private void textBox1_MouseClick(object sender, MouseEventArgs e)
    {
        textBox1.Text = username;
    }

    private void textBox3_MouseClick(object sender, MouseEventArgs e)
    {
        textBox3.Text = Player_Sex;
    }

    private void textBox4_MouseClick(object sender, MouseEventArgs e)
    {
        textBox4.Text = Player_Zx;
    }

    private void textBox5_MouseClick(object sender, MouseEventArgs e)
    {
        textBox5.Text = Player_Job_leve;
    }

    private void textBox6_MouseClick(object sender, MouseEventArgs e)
    {
        textBox6.Text = Player_Level;
    }

    private void textBox7_MouseClick(object sender, MouseEventArgs e)
    {
        textBox7.Text = ChuyenSinh;
    }

    private void textBox8_MouseClick(object sender, MouseEventArgs e)
    {
        textBox8.Text = Player_Job;
    }

    private void textBox9_MouseClick(object sender, MouseEventArgs e)
    {
        textBox9.Text = Player_Exp;
    }

    private void button1_Click(object sender, EventArgs e)
    {
        var num12 = 0;
        Players players = null;
        var num13 = 0;
        var ThoiGian = 0;
        var num14 = 0;
        var num15 = 0;
        var num16 = 0;
        var num17 = 0;
        var num10 = 0;
        var KhoaItem = 0;
        var PhamChat = 0;
        var PhuHon = 0;
        var num11 = 0;
        num14 = comboBox13.Text switch
        {
            "Số lượng" => 2000000000 + int.Parse(textBox63.Text),
            "Tăng cường trang sức" => 30000000 + int.Parse(textBox63.Text),
            "Tăng cường áo giáp" => 20000000 + int.Parse(textBox63.Text),
            "Tăng cường Vũ Khí" => 10000000 + int.Parse(textBox63.Text),
            _ => 0
        };
        num13 = 0;
        num13 = int.Parse(textBox64.Text) * 100;
        switch (comboBox14.Text)
        {
            case "Độc":
                num14 += 1000006000 + num13;
                break;
            case "Ngoại công":
                num14 += 1000005000 + num13;
                break;
            case "Nội công":
                num14 += 1000004000 + num13;
                break;
            case "Phong":
                num14 += 1000003000 + num13;
                break;
            case "Thuỷ":
                num14 += 1000002000 + num13;
                break;
            case "Hoả":
                num14 += 1000001000 + num13;
                break;
        }

        num15 = AttributeSelection(comboBox15.Text, int.Parse(textBox65.Text));
        num16 = AttributeSelection(comboBox16.Text, int.Parse(textBox66.Text));
        num17 = AttributeSelection(comboBox17.Text, int.Parse(textBox67.Text));
        num10 = AttributeSelection(comboBox18.Text, int.Parse(textBox68.Text));
        num11 = AttributeSelection(comboBox1.Text, int.Parse(textBox10.Text));
        if (comboBox19.Text == "Không buộc")
        {
            KhoaItem = 0;
        }
        else if (comboBox19.Text == "Khóa lại")
        {
            KhoaItem = 1;
        }
        else if (comboBox19.Text == "Khá tốt")
        {
            PhamChat = 1;
            ThoiGian = 0;
        }
        else if (comboBox19.Text == "Cao Cấp")
        {
            PhamChat = 2;
            ThoiGian = 0;
        }
        else
        {
            ThoiGian = 0;
        }

        if (comboBox12.Text == string.Empty)
        {
            tishi.Text = "Vui long chon vat pham";
            return;
        }

        players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            num12 = players.GetParcelVacancy(players);
            if (num12 == -1)
            {
                tishi.Text = "Nhan vat, khong du o trong";
                return;
            }

            players.IncreaseItemWithAttributes(int.Parse(comboBox12.Text), num12, 1, num14, num15, num16, num17, num10,
                num11, PhuHon, PhamChat, KhoaItem, ThoiGian);
            players.HeThongNhacNho("Nhận được: " + ItmeClass.DatDuocVatPhamTen_XungHao(int.Parse(comboBox12.Text)), 10,
                "GM");
            tishi.Text = "[" + players.UserName + "] đã nhận được món hàng";
        }
        else
        {
            tishi.Text = "Người chơi không Online";
        }
    }

    private void button2_Click(object sender, EventArgs e)
    {
        var int_ = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.KiemSoatNguyenBao_SoLuong(int_, 1);
            players.Save_NguyenBaoData();
            tishi.Text = "Add Cash thành công";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.Save_NguyenBaoData();
    }

    private void button3_Click(object sender, EventArgs e)
    {
    }

    private void button3_Click_1(object sender, EventArgs e)
    {
        var num2 = long.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.Player_Money += num2;
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("Nhận được " + num2 + " lượng", 50);
            tishi.Text = "Hoàn tất";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }
    }

    private void button4_Click(object sender, EventArgs e)
    {
        var num2 = long.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.CharacterExperience += num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            tishi.Text = "Gia tăng kinh nghiệm thành công";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.UpdateMoneyAndWeight();
        players.SaveCharacterData();
    }

    private void button5_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.Player_ExpErience += num2;
            players.UpdateKinhNghiemVaTraiNghiem();
            players.SaveCharacterData();
            tishi.Text = "Player background brushing increases experience success ";
        }
        else
        {
            tishi.Text =
                "The player must be online when retrieving item attributes, please check whether the player is online";
        }

        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.SaveCharacterData();
    }

    private void button6_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.Player_WuXun += num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.SaveCharacterData();
            players.HeThongNhacNho("Nhận được Võ Huân" + num2, 50);
            tishi.Text = "Add võ huân thành công";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.SaveCharacterData();
    }

    private void label12_Click(object sender, EventArgs e)
    {
    }

    private void button12_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.FLD_NhanVat_ThemVao_CongKich += num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.SaveCharacterData();
            players.HeThongNhacNho("获得攻击" + num2, 50);
            tishi.Text = "玩家后台刷取增加攻击成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.SaveCharacterData();
    }

    private void button13_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.FLD_NhanVat_ThemVao_PhongNgu += num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("获得防御" + num2, 50);
            tishi.Text = "玩家后台刷取增加防御成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.UpdateMoneyAndWeight();
        players.SaveCharacterData();
    }

    private void button14_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.FLD_NhanVat_ThemVao_LucPhongNguVoCong += num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("获得武功防御" + num2, 50);
            tishi.Text = "玩家后台刷取增加武功防御成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.UpdateMoneyAndWeight();
        players.SaveCharacterData();
    }

    private void button15_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.CharactersToAddMax_HP += num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("获得生命" + num2, 50);
            tishi.Text = "玩家后台刷取增加生命成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }
    }

    private void button16_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.CharactersToAddMax_MP += num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("获得内功" + num2, 50);
            tishi.Text = "玩家后台刷取增加内功成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }
    }

    private void button17_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.FLD_NhanVat_ThemVao_TrungDich += num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("获得命中" + num2, 50);
            tishi.Text = "玩家后台刷取增加命中成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }
    }

    private void button18_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.FLD_NhanVat_ThemVao_NeTranh += num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("获得回避" + num2, 50);
            tishi.Text = "玩家后台刷取增加回避成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }
    }

    private void button19_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox8.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
            switch (num2)
            {
                case 1:
                    players.Player_Job = 1;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("[도캐릭터]로 클래스를 성공적으로 변경했습니다. 뒤로 물러나 다시 시작하세요!", 9, "GM");
                    tishi.Text = "玩家后台刷取职业修改成功";
                    break;
                case 2:
                    players.Player_Job = 2;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("[검캐릭터]로 클래스를 성공적으로 변경했습니다. 뒤로 물러나 다시 시작하세요!", 9, "GM");
                    tishi.Text = "玩家后台刷取职业修改成功";
                    break;
                case 3:
                    players.Player_Job = 3;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("[창캐릭터]로 클래스를 성공적으로 변경했습니다. 뒤로 물러나 다시 시작하세요!", 9, "GM");
                    tishi.Text = "玩家后台刷取职业修改成功";
                    break;
                case 4:
                    players.Player_Job = 4;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("[궁캐릭터]로 클래스를 성공적으로 변경했습니다. 뒤로 물러나 다시 시작하세요!", 9, "GM");
                    tishi.Text = "玩家后台刷取职业修改成功";
                    break;
                case 5:
                    players.Player_Job = 5;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("[의원캐릭터]로 클래스를 성공적으로 변경했습니다. 뒤로 물러나 다시 시작하세요!", 9, "GM");
                    tishi.Text = "玩家后台刷取职业修改成功";
                    break;
                case 6:
                    players.Player_Job = 6;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("[단캐릭터]로 클래스를 성공적으로 변경했습니다. 뒤로 물러나 다시 시작하세요!", 9, "GM");
                    tishi.Text = "玩家后台刷取职业修改成功";
                    break;
                case 7:
                    players.Player_Job = 7;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("[악사캐릭터]로 클래스를 성공적으로 변경했습니다. 뒤로 물러나 다시 시작하세요!", 9, "GM");
                    tishi.Text = "玩家后台刷取职业修改成功";
                    break;
                case 8:
                    players.Player_Job = 8;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("[한비광캐릭터]로 클래스를 성공적으로 변경했습니다. 뒤로 물러나 다시 시작하세요!", 9, "GM");
                    tishi.Text = "玩家后台刷取职业修改成功";
                    break;
                case 9:
                    players.Player_Job = 9;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("[담화린캐릭터]로 클래스를 성공적으로 변경했습니다. 뒤로 물러나 다시 시작하세요!", 9, "GM");
                    tishi.Text = "玩家后台刷取职业修改成功";
                    break;
                case 10:
                    players.Player_Job = 10;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("[격투가캐릭터]로 클래스를 성공적으로 변경했습니다. 뒤로 물러나 다시 시작하세요!", 9, "GM");
                    tishi.Text = "玩家后台刷取职业修改成功";
                    break;
                case 11:
                    players.Player_Job = 11;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("[매유진캐릭터]로 클래스를 성공적으로 변경했습니다. 뒤로 물러나 다시 시작하세요!", 9, "GM");
                    tishi.Text = "玩家后台刷取职业修改成功";
                    break;
                case 12:
                    players.Player_Job = 12;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("[노호캐릭터]로 클래스를 성공적으로 변경했습니다. 뒤로 물러나 다시 시작하세요!", 9, "GM");
                    tishi.Text = "玩家后台刷取职业修改成功";
                    break;
                case 13:
                    players.Player_Job = 13;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("[미고캐릭터]로 클래스를 성공적으로 변경했습니다. 뒤로 물러나 다시 시작하세요!", 9, "GM");
                    tishi.Text = "玩家后台刷取职业修改成功";
                    break;
            }
        else
            tishi.Text = "Người chơi không Online";

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.SaveCharacterData();
        players.StoredProcedureForSavingCharacterData();
    }

    private void button7_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox3.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
            switch (num2)
            {
                case 2:
                    players.Player_Sex = num2;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.UpdateMoneyAndWeight();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("Bạn đã chuyển giới tính thành: " + num2 + ", vui lòng relog", 50);
                    tishi.Text = "Chuyển giới tính thành công";
                    break;
                case 1:
                    players.Player_Sex = num2;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.UpdateMoneyAndWeight();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("Bạn đã chuyển giới tính thành: " + num2 + ", vui lòng relog", 50);
                    tishi.Text = "Chuyển giới tính thành công";
                    break;
            }
        else
            tishi.Text = "Người chơi không Online";

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.UpdateMoneyAndWeight();
        players.SaveCharacterData();
        players.StoredProcedureForSavingCharacterData();
    }

    private void button8_Click(object sender, EventArgs e)
    {
        var num3 = int.Parse(textBox4.Text);
        var num4 = 1;
        var num5 = 2;
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        var flag = true;
        while (true)
        {
            if (players != null)
            {
                while (true)
                {
                    IL_019d:
                    var flag2 = true;
                    while (true)
                    {
                        switch (num3 == num4 ? 6 : 0)
                        {
                            case 4:
                                break;
                            default:
                                num3 = int.Parse(textBox4.Text);
                                num4 = 1;
                                num5 = 2;
                                players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
                                break;
                            case 0:
                                if (num3 != num5) goto end_IL_002d;
                                goto case 5;
                            case 5:
                                players.Player_Zx = num3;
                                players.TinhToan_NhanVatCoBan_DuLieu3();
                                players.CapNhat_HP_MP_SP();
                                players.UpdateMartialArtsAndStatus();
                                players.UpdateKinhNghiemVaTraiNghiem();
                                players.UpdateMoneyAndWeight();
                                players.SaveCharacterData();
                                players.StoredProcedureForSavingCharacterData();
                                players.HeThongNhacNho("Chuyển thế lực thành công, vui lòng relog", 9, "GM");
                                tishi.Text = "Chuyển thế lực thành công";
                                goto end_IL_002d;
                            case 6:
                                players.Player_Zx = num3;
                                players.TinhToan_NhanVatCoBan_DuLieu3();
                                players.CapNhat_HP_MP_SP();
                                players.UpdateMartialArtsAndStatus();
                                players.UpdateKinhNghiemVaTraiNghiem();
                                players.UpdateMoneyAndWeight();
                                players.SaveCharacterData();
                                players.StoredProcedureForSavingCharacterData();
                                players.HeThongNhacNho("Chuyển thế lực thành công, vui lòng relog", 9, "GM");
                                tishi.Text = "Chuyển thế lực thành công";
                                goto end_IL_002d;
                            case 7:
                                continue;
                            case 3:
                                goto IL_019d;
                            case 1:
                            case 2:
                            case 8:
                                goto end_IL_002d;
                        }

                        break;
                    }

                    break;
                }

                continue;
            }

            tishi.Text = "Người chơi không Online";
            break;
            continue;
            end_IL_002d:
            break;
        }

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.UpdateMoneyAndWeight();
        players.SaveCharacterData();
        players.StoredProcedureForSavingCharacterData();
    }

    private void button9_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox5.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
            switch (num2)
            {
                case 1:
                    players.Player_Job_level = num2;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.UpdateMoneyAndWeight();
                    players.SaveCharacterData();
                    players.UpdateKhiCong();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("Thăng chức[" + num2 + "Lần], vui lòng relog!", 9, "GM");
                    tishi.Text = "Thay đổi thăng chức OK";
                    break;
                case 2:
                    players.Player_Job_level = num2;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.UpdateMoneyAndWeight();
                    players.UpdateKhiCong();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("Thăng chức[" + num2 + "Lần], vui lòng relog!", 9, "GM");
                    tishi.Text = "Thay đổi thăng chức OK";
                    break;
                case 3:
                    players.Player_Job_level = num2;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.UpdateMoneyAndWeight();
                    players.SaveCharacterData();
                    players.UpdateKhiCong();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("Thăng chức[" + num2 + "Lần], vui lòng relog!", 9, "GM");
                    tishi.Text = "Thay đổi thăng chức OK";
                    break;
                case 4:
                    players.Player_Job_level = num2;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.UpdateMoneyAndWeight();
                    players.UpdateKhiCong();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("Thăng chức[" + num2 + "Lần], vui lòng relog!", 9, "GM");
                    tishi.Text = "Thay đổi thăng chức OK";
                    break;
                case 5:
                    players.Player_Job_level = num2;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.UpdateMoneyAndWeight();
                    players.UpdateKhiCong();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("Thăng chức[" + num2 + "Lần], vui lòng relog!", 9, "GM");
                    tishi.Text = "Thay đổi thăng chức OK";
                    break;
                case 6:
                    players.Player_Job_level = num2;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.UpdateMoneyAndWeight();
                    players.UpdateKhiCong();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("Thăng chức[" + num2 + "Lần], vui lòng relog!", 9, "GM");
                    tishi.Text = "Thay đổi thăng chức OK";
                    break;
                case 7:
                    players.Player_Job_level = num2;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.UpdateMoneyAndWeight();
                    players.UpdateKhiCong();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("Thăng chức[" + num2 + "Lần], vui lòng relog!", 9, "GM");
                    tishi.Text = "Thay đổi thăng chức OK";
                    break;
                case 8:
                    players.Player_Job_level = num2;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.UpdateMoneyAndWeight();
                    players.UpdateKhiCong();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("Thăng chức[" + num2 + "Lần], vui lòng relog!", 9, "GM");
                    tishi.Text = "Thay đổi thăng chức OK";
                    break;
                case 9:
                    players.Player_Job_level = num2;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.UpdateMoneyAndWeight();
                    players.UpdateKhiCong();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("Thăng chức[" + num2 + "Lần], vui lòng relog!", 9, "GM");
                    tishi.Text = "Thay đổi thăng chức OK";
                    break;
                case 10:
                    players.Player_Job_level = num2;
                    players.TinhToan_NhanVatCoBan_DuLieu3();
                    players.CapNhat_HP_MP_SP();
                    players.UpdateMartialArtsAndStatus();
                    players.UpdateKinhNghiemVaTraiNghiem();
                    players.UpdateMoneyAndWeight();
                    players.UpdateKhiCong();
                    players.SaveCharacterData();
                    players.StoredProcedureForSavingCharacterData();
                    players.HeThongNhacNho("Thăng chức[" + num2 + "Lần], vui lòng relog!", 9, "GM");
                    tishi.Text = "Thay đổi thăng chức OK";
                    break;
            }
        else
            tishi.Text = "Người chơi không Online";

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.UpdateMoneyAndWeight();
        players.SaveCharacterData();
        players.UpdateKhiCong();
        players.StoredProcedureForSavingCharacterData();
    }

    private void button10_Click_1(object sender, EventArgs e)
    {
        var player_Level = int.Parse(textBox6.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.Player_Level = player_Level;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CharacterExperience = 0L;
            players.TipsAfterTheUpgrade(1);
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.StoredProcedureForSavingCharacterData();
            players.HeThongNhacNho("Bạn đã thăng cấp: [" + player_Level + " Lv]!", 9, "GM");
            tishi.Text = "Hoàn tất";
        }
        else
        {
            tishi.Text = "Người chơi không online hoặc tên không tồn tại";
        }

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CharacterExperience = 0L;
        players.TipsAfterTheUpgrade(1);
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.UpdateMoneyAndWeight();
        players.SaveCharacterData();
        players.StoredProcedureForSavingCharacterData();
        players.UpdateBroadcastCharacterData();
        players.UpdateKhiCong();
    }

    private void button21_Click(object sender, EventArgs e)
    {
        var num2 = long.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.Player_Money -= num2;
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("Bị khấu trừ" + num2 + " Gold", 50);
            tishi.Text = "Hoàn tất";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }
    }

    private void button22_Click(object sender, EventArgs e)
    {
        var num2 = long.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.CharacterExperience -= num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            tishi.Text = "Giảm kinh nghiệm thành công";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.UpdateMoneyAndWeight();
        players.SaveCharacterData();
    }

    private void button23_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.Player_ExpErience -= num2;
            players.UpdateKinhNghiemVaTraiNghiem();
            players.SaveCharacterData();
            players.HeThongNhacNho("Bị khấu trừ" + num2 + "Exp", 50);
            tishi.Text = "giảm kinh nghiêm thành công";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.SaveCharacterData();
    }

    private void button24_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.Player_WuXun -= num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.SaveCharacterData();
            players.HeThongNhacNho("Bị khấu trừ" + num2 + "武勋", 50);
            tishi.Text = "玩家后台刷取减少武勋成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.SaveCharacterData();
    }

    private void button25_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.CheckTheNumberOfIngotsInBaibaoge();
            players.FLD_RXPIONT -= num2;
            players.Save_NguyenBaoData();
            players.HeThongNhacNho("Bị khấu trừ" + num2 + "Point", 50);
            tishi.Text = "Trừ Cash thành công";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.Save_NguyenBaoData();
    }

    private void button26_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.FLD_NhanVat_ThemVao_CongKich -= num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("被系统减少攻击" + num2, 50);
            tishi.Text = "玩家后台刷取减少攻击成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.UpdateMoneyAndWeight();
        players.SaveCharacterData();
    }

    private void button27_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.FLD_NhanVat_ThemVao_PhongNgu -= num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("被系统减少防御" + num2, 50);
            tishi.Text = "玩家后台刷取减少防御成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.UpdateMoneyAndWeight();
        players.SaveCharacterData();
    }

    private void button28_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.FLD_NhanVat_ThemVao_LucPhongNguVoCong -= num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("被系统减少武功防御" + num2, 50);
            tishi.Text = "玩家后台刷取减少武功防御成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.UpdateMoneyAndWeight();
        players.SaveCharacterData();
    }

    private void button29_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.CharactersToAddMax_HP -= num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("被系统减少生命" + num2, 50);
            tishi.Text = "玩家后台刷取减少生命成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }
    }

    private void button30_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.CharactersToAddMax_MP -= num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("被系统减少内功" + num2, 50);
            tishi.Text = "玩家后台刷取减少内功成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }
    }

    private void button31_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.FLD_NhanVat_ThemVao_TrungDich -= num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("被系统减少命中" + num2, 50);
            tishi.Text = "玩家后台刷取减少命中成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }
    }

    private void button32_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.FLD_NhanVat_ThemVao_NeTranh -= num2;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.HeThongNhacNho("被系统减少回避" + num2, 50);
            tishi.Text = "玩家后台刷取减少回避成功";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }
    }

    private void button33_Click(object sender, EventArgs e)
    {
        var int_ = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.CheckTheIngotPointData(int_, 1);
            players.Save_NguyenBaoData();
            players.HeThongNhacNho("Nhận được" + int_ + "Cash", 50);
            tishi.Text = "Tăng Cash thành công";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.Save_NguyenBaoData();
    }

    private void button34_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.FLD_RXPIONTX -= num2;
            players.Save_NguyenBaoData();
            players.HeThongNhacNho("Bị khấu trừ" + num2 + "Cash", 50);
            tishi.Text = "Trừ Cash thành công";
        }
        else
        {
            tishi.Text = "Nhân vật không online";
        }

        players.Save_NguyenBaoData();
    }

    private void button35_Click(object sender, EventArgs e)
    {
        var 人物经验 = long.Parse(textBox9.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.CharacterExperience = 人物经验;
            players.TinhToan_NhanVatCoBan_DuLieu3();
            players.CapNhat_HP_MP_SP();
            players.UpdateMartialArtsAndStatus();
            players.UpdateKinhNghiemVaTraiNghiem();
            players.UpdateMoneyAndWeight();
            players.SaveCharacterData();
            players.StoredProcedureForSavingCharacterData();
            players.HeThongNhacNho("Exp thay đổi: " + 人物经验 + ", vui lòng relog", 50);
            tishi.Text = "Thanh kinh nghiệm đã được sửa đổi";
        }
        else
        {
            tishi.Text = "Người chơi không Online";
        }

        players.TinhToan_NhanVatCoBan_DuLieu3();
        players.CapNhat_HP_MP_SP();
        players.UpdateMartialArtsAndStatus();
        players.UpdateKinhNghiemVaTraiNghiem();
        players.UpdateMoneyAndWeight();
        players.SaveCharacterData();
        players.StoredProcedureForSavingCharacterData();
        players.UpdateBroadcastCharacterData();
        players.UpdateKhiCong();
    }

    private void button36_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.FLD_Couple_Love += num2;
            players.SaveCharacterData();
            players.HeThongNhacNho("恭喜您获得增加：" + num2 + "爱情度", 50);
            tishi.Text = "玩家后台添加爱情度成功";
        }
        else
        {
            tishi.Text = "刷取属性时玩家必须在线,请检查该玩家是否在线状态";
        }

        players.SaveCharacterData();
    }

    private void button37_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox2.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            players.FLD_Couple_Love -= num2;
            players.SaveCharacterData();
            players.HeThongNhacNho("您被系统减少：" + num2 + "爱情度", 50);
            tishi.Text = "玩家后台减少爱情度成功";
        }
        else
        {
            tishi.Text = "刷取属性时玩家必须在线,请检查该玩家是否在线状态";
        }

        players.SaveCharacterData();
    }

    private void button38_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox11.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            if (World.allConnectedChars.TryGetValue(players.CharacterFullServerID, out var _))
            {
                var now = DateTime.Now;
                now = DateTime.Now.AddDays(num2);
                players.FLD_VIP = 1;
                players.FLD_VIPTIM = now;
                players.HeThongNhacNho("恭喜您获得" + num2 + "天的VIP！", 9, "GM");
                players.HeThongNhacNho("你的VIP结束时间是:" + players.FLD_VIPTIM.ToString("yyyy年MM月dd日 hh时mm分"), 9, "GM");
                players.HeThongNhacNho("续时成功,你的VIP结束时间是:" + players.FLD_VIPTIM.ToString("yyyy年MM月dd日 hh时mm分"), 9, "GM");
                players.SaveCharacterData();
                players.SaveMemberData();
            }

            tishi.Text = "玩家后台刷会员成功";
        }
        else
        {
            tishi.Text = "刷取属性时玩家必须在线,请检查该玩家是否在线状态";
        }

        players.SaveMemberData();
        players.SaveCharacterData();
    }

    private void button39_Click(object sender, EventArgs e)
    {
        var num2 = int.Parse(textBox12.Text);
        var players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
        if (players != null)
        {
            if (World.allConnectedChars.TryGetValue(players.CharacterFullServerID, out var _))
            {
                var now = DateTime.Now;
                now = DateTime.Now.AddDays(num2);
                players.FLD_QCVIP = 1;
                players.FLD_QCVIPTIM = now;
                players.HeThongNhacNho("您被系统设为：" + num2 + "天八彩会员!", 50);
                players.HeThongNhacNho("你的八彩VIP结束时间是:" + players.FLD_QCVIPTIM.ToString("yyyy年MM月dd日 hh时mm分"), 9, "GM");
                players.HeThongNhacNho("续时成功,你的八彩VIP结束时间是:" + players.FLD_QCVIPTIM.ToString("yyyy年MM月dd日 hh时mm分"), 9,
                    "GM");
                players.SaveCharacterData();
                players.Bao_ton_tam_mau_hoi_vien_so_lieu();
            }

            tishi.Text = "玩家后台刷会员成功";
        }
        else
        {
            tishi.Text = "刷取属性时玩家必须在线,请检查该玩家是否在线状态";
        }

        players.Bao_ton_tam_mau_hoi_vien_so_lieu();
        players.SaveCharacterData();
    }

    private void button40_Click(object sender, EventArgs e)
    {
    }

    private void button41_Click(object sender, EventArgs e)
    {
    }

    private void button43_Click(object sender, EventArgs e)
    {
    }

    private void button42_Click(object sender, EventArgs e)
    {
    }

    private void GMGJ_Load(object sender, EventArgs e)
    {
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        groupBox1 = new GroupBox();
        label22 = new Label();
        label21 = new Label();
        groupBox2 = new GroupBox();
        button37 = new Button();
        button36 = new Button();
        button34 = new Button();
        button33 = new Button();
        button32 = new Button();
        button31 = new Button();
        button30 = new Button();
        button29 = new Button();
        button28 = new Button();
        button27 = new Button();
        button26 = new Button();
        button25 = new Button();
        button24 = new Button();
        button23 = new Button();
        button22 = new Button();
        button21 = new Button();
        button18 = new Button();
        button17 = new Button();
        button16 = new Button();
        button15 = new Button();
        button14 = new Button();
        button13 = new Button();
        button12 = new Button();
        button6 = new Button();
        button5 = new Button();
        button4 = new Button();
        button3 = new Button();
        label9 = new Label();
        textBox2 = new TextBox();
        button2 = new Button();
        button41 = new Button();
        textBox13 = new TextBox();
        label19 = new Label();
        button40 = new Button();
        button39 = new Button();
        button38 = new Button();
        textBox14 = new TextBox();
        label20 = new Label();
        textBox12 = new TextBox();
        label18 = new Label();
        textBox11 = new TextBox();
        label17 = new Label();
        textBox10 = new TextBox();
        label16 = new Label();
        comboBox1 = new ComboBox();
        textBox9 = new TextBox();
        button35 = new Button();
        button20 = new Button();
        button19 = new Button();
        textBox8 = new TextBox();
        label6 = new Label();
        textBox3 = new TextBox();
        label14 = new Label();
        button11 = new Button();
        button10 = new Button();
        button9 = new Button();
        button8 = new Button();
        button7 = new Button();
        textBox7 = new TextBox();
        textBox6 = new TextBox();
        textBox5 = new TextBox();
        textBox4 = new TextBox();
        label13 = new Label();
        label12 = new Label();
        label11 = new Label();
        label10 = new Label();
        label8 = new Label();
        label7 = new Label();
        label5 = new Label();
        comboBox19 = new ComboBox();
        textBox68 = new TextBox();
        textBox67 = new TextBox();
        textBox66 = new TextBox();
        textBox65 = new TextBox();
        textBox64 = new TextBox();
        textBox63 = new TextBox();
        comboBox18 = new ComboBox();
        comboBox17 = new ComboBox();
        comboBox16 = new ComboBox();
        comboBox15 = new ComboBox();
        comboBox14 = new ComboBox();
        comboBox13 = new ComboBox();
        label46 = new Label();
        label45 = new Label();
        label44 = new Label();
        label43 = new Label();
        label42 = new Label();
        label41 = new Label();
        button1 = new Button();
        textBox1 = new TextBox();
        label4 = new Label();
        label3 = new Label();
        label2 = new Label();
        label1 = new Label();
        comboBox12 = new ComboBox();
        comboBox11 = new ComboBox();
        listBox3 = new ListBox();
        statusStrip1 = new StatusStrip();
        toolStripStatusLabel1 = new ToolStripStatusLabel();
        tishi = new ToolStripStatusLabel();
        groupBox1.SuspendLayout();
        groupBox2.SuspendLayout();
        statusStrip1.SuspendLayout();
        SuspendLayout();
        groupBox1.BackColor = Color.Black;
        groupBox1.Controls.Add(label22);
        groupBox1.Controls.Add(label21);
        groupBox1.Controls.Add(groupBox2);
        groupBox1.Controls.Add(button41);
        groupBox1.Controls.Add(textBox13);
        groupBox1.Controls.Add(label19);
        groupBox1.Controls.Add(button40);
        groupBox1.Controls.Add(button39);
        groupBox1.Controls.Add(button38);
        groupBox1.Controls.Add(textBox14);
        groupBox1.Controls.Add(label20);
        groupBox1.Controls.Add(textBox12);
        groupBox1.Controls.Add(label18);
        groupBox1.Controls.Add(textBox11);
        groupBox1.Controls.Add(label17);
        groupBox1.Controls.Add(textBox10);
        groupBox1.Controls.Add(label16);
        groupBox1.Controls.Add(comboBox1);
        groupBox1.Controls.Add(textBox9);
        groupBox1.Controls.Add(button35);
        groupBox1.Controls.Add(button20);
        groupBox1.Controls.Add(button19);
        groupBox1.Controls.Add(textBox8);
        groupBox1.Controls.Add(label6);
        groupBox1.Controls.Add(textBox3);
        groupBox1.Controls.Add(label14);
        groupBox1.Controls.Add(button11);
        groupBox1.Controls.Add(button10);
        groupBox1.Controls.Add(button9);
        groupBox1.Controls.Add(button8);
        groupBox1.Controls.Add(button7);
        groupBox1.Controls.Add(textBox7);
        groupBox1.Controls.Add(textBox6);
        groupBox1.Controls.Add(textBox5);
        groupBox1.Controls.Add(textBox4);
        groupBox1.Controls.Add(label13);
        groupBox1.Controls.Add(label12);
        groupBox1.Controls.Add(label11);
        groupBox1.Controls.Add(label10);
        groupBox1.Controls.Add(label8);
        groupBox1.Controls.Add(label7);
        groupBox1.Controls.Add(label5);
        groupBox1.Controls.Add(comboBox19);
        groupBox1.Controls.Add(textBox68);
        groupBox1.Controls.Add(textBox67);
        groupBox1.Controls.Add(textBox66);
        groupBox1.Controls.Add(textBox65);
        groupBox1.Controls.Add(textBox64);
        groupBox1.Controls.Add(textBox63);
        groupBox1.Controls.Add(comboBox18);
        groupBox1.Controls.Add(comboBox17);
        groupBox1.Controls.Add(comboBox16);
        groupBox1.Controls.Add(comboBox15);
        groupBox1.Controls.Add(comboBox14);
        groupBox1.Controls.Add(comboBox13);
        groupBox1.Controls.Add(label46);
        groupBox1.Controls.Add(label45);
        groupBox1.Controls.Add(label44);
        groupBox1.Controls.Add(label43);
        groupBox1.Controls.Add(label42);
        groupBox1.Controls.Add(label41);
        groupBox1.Controls.Add(button1);
        groupBox1.Controls.Add(textBox1);
        groupBox1.Controls.Add(label4);
        groupBox1.Controls.Add(label3);
        groupBox1.Controls.Add(label2);
        groupBox1.Controls.Add(label1);
        groupBox1.Controls.Add(comboBox12);
        groupBox1.Controls.Add(comboBox11);
        groupBox1.Controls.Add(listBox3);
        groupBox1.FlatStyle = FlatStyle.Flat;
        groupBox1.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Regular, GraphicsUnit.Point, 134);
        groupBox1.ForeColor = Color.White;
        groupBox1.Location = new Point(18, 10);
        groupBox1.Name = "groupBox1";
        groupBox1.Size = new Size(1120, 865);
        groupBox1.TabIndex = 63;
        groupBox1.TabStop = false;
        groupBox1.Text = "Vật phẩm sửa chữa / Tăng thêm:";
        label22.AutoSize = true;
        label22.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 0);
        label22.ForeColor = Color.Yellow;
        label22.Location = new Point(38, 447);
        label22.Name = "label22";
        label22.Size = new Size(45, 19);
        label22.TabIndex = 174;
        label22.Text = "Exp:";
        label21.AutoSize = true;
        label21.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label21.ForeColor = Color.Red;
        label21.Location = new Point(16, 276);
        label21.Name = "label21";
        label21.Size = new Size(135, 17);
        label21.TabIndex = 173;
        label21.Text = "ZX: 1 Chính / 2 Tà";
        groupBox2.BackColor = Color.Black;
        groupBox2.Controls.Add(button37);
        groupBox2.Controls.Add(button36);
        groupBox2.Controls.Add(button34);
        groupBox2.Controls.Add(button33);
        groupBox2.Controls.Add(button32);
        groupBox2.Controls.Add(button31);
        groupBox2.Controls.Add(button30);
        groupBox2.Controls.Add(button29);
        groupBox2.Controls.Add(button28);
        groupBox2.Controls.Add(button27);
        groupBox2.Controls.Add(button26);
        groupBox2.Controls.Add(button25);
        groupBox2.Controls.Add(button24);
        groupBox2.Controls.Add(button23);
        groupBox2.Controls.Add(button22);
        groupBox2.Controls.Add(button21);
        groupBox2.Controls.Add(button18);
        groupBox2.Controls.Add(button17);
        groupBox2.Controls.Add(button16);
        groupBox2.Controls.Add(button15);
        groupBox2.Controls.Add(button14);
        groupBox2.Controls.Add(button13);
        groupBox2.Controls.Add(button12);
        groupBox2.Controls.Add(button6);
        groupBox2.Controls.Add(button5);
        groupBox2.Controls.Add(button4);
        groupBox2.Controls.Add(button3);
        groupBox2.Controls.Add(label9);
        groupBox2.Controls.Add(textBox2);
        groupBox2.Controls.Add(button2);
        groupBox2.FlatStyle = FlatStyle.Flat;
        groupBox2.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        groupBox2.ForeColor = Color.White;
        groupBox2.Location = new Point(417, 331);
        groupBox2.Name = "groupBox2";
        groupBox2.Size = new Size(393, 256);
        groupBox2.TabIndex = 65;
        groupBox2.TabStop = false;
        groupBox2.Text = "Thiết lập cấu hình";
        groupBox2.Enter += groupBox2_Enter;
        button37.BackColor = SystemColors.Window;
        button37.FlatStyle = FlatStyle.Flat;
        button37.Font = new Font("Tahoma", 7f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button37.ForeColor = Color.FromArgb(0, 64, 0);
        button37.Location = new Point(299, 101);
        button37.Name = "button37";
        button37.Size = new Size(89, 32);
        button37.TabIndex = 157;
        button37.Text = "-Điểm Love";
        button37.UseVisualStyleBackColor = false;
        button37.Click += button37_Click;
        button36.BackColor = SystemColors.Window;
        button36.FlatStyle = FlatStyle.Flat;
        button36.Font = new Font("Tahoma", 7f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button36.ForeColor = Color.FromArgb(0, 64, 0);
        button36.Location = new Point(204, 101);
        button36.Name = "button36";
        button36.Size = new Size(89, 32);
        button36.TabIndex = 156;
        button36.Text = "+Điểm Love";
        button36.UseVisualStyleBackColor = false;
        button36.Click += button36_Click;
        button34.BackColor = SystemColors.Window;
        button34.FlatStyle = FlatStyle.Flat;
        button34.Location = new Point(299, 63);
        button34.Name = "button34";
        button34.Size = new Size(89, 32);
        button34.TabIndex = 155;
        button34.Text = "-Time";
        button34.UseVisualStyleBackColor = false;
        button34.Click += button34_Click;
        button33.BackColor = SystemColors.Window;
        button33.FlatStyle = FlatStyle.Flat;
        button33.Location = new Point(204, 63);
        button33.Name = "button33";
        button33.Size = new Size(89, 32);
        button33.TabIndex = 154;
        button33.Text = "+Time ";
        button33.UseVisualStyleBackColor = false;
        button33.Click += button33_Click;
        button32.BackColor = SystemColors.Window;
        button32.FlatStyle = FlatStyle.Flat;
        button32.ForeColor = Color.Teal;
        button32.Location = new Point(299, 269);
        button32.Name = "button32";
        button32.Size = new Size(89, 32);
        button32.TabIndex = 153;
        button32.Text = "减少回避";
        button32.UseVisualStyleBackColor = false;
        button32.Click += button32_Click;
        button31.BackColor = SystemColors.Window;
        button31.FlatStyle = FlatStyle.Flat;
        button31.ForeColor = Color.FromArgb(64, 64, 0);
        button31.Location = new Point(109, 269);
        button31.Name = "button31";
        button31.Size = new Size(89, 32);
        button31.TabIndex = 152;
        button31.Text = "减少命中";
        button31.UseVisualStyleBackColor = false;
        button31.Click += button31_Click;
        button30.BackColor = SystemColors.Window;
        button30.FlatStyle = FlatStyle.Flat;
        button30.ForeColor = Color.MediumSlateBlue;
        button30.Location = new Point(299, 213);
        button30.Name = "button30";
        button30.Size = new Size(89, 32);
        button30.TabIndex = 151;
        button30.Text = "-Mana";
        button30.UseVisualStyleBackColor = false;
        button30.Click += button30_Click;
        button29.BackColor = SystemColors.Window;
        button29.FlatStyle = FlatStyle.Flat;
        button29.ForeColor = Color.Red;
        button29.Location = new Point(109, 213);
        button29.Name = "button29";
        button29.Size = new Size(89, 32);
        button29.TabIndex = 150;
        button29.Text = "-HP";
        button29.UseVisualStyleBackColor = false;
        button29.Click += button29_Click;
        button28.BackColor = SystemColors.Window;
        button28.FlatStyle = FlatStyle.Flat;
        button28.ForeColor = Color.FromArgb(0, 192, 0);
        button28.Location = new Point(299, 308);
        button28.Name = "button28";
        button28.Size = new Size(89, 32);
        button28.TabIndex = 149;
        button28.Text = "减少武防";
        button28.UseVisualStyleBackColor = false;
        button28.Click += button28_Click;
        button27.BackColor = SystemColors.Window;
        button27.FlatStyle = FlatStyle.Flat;
        button27.ForeColor = Color.FromArgb(64, 0, 64);
        button27.Location = new Point(299, 139);
        button27.Name = "button27";
        button27.Size = new Size(89, 32);
        button27.TabIndex = 148;
        button27.Text = "-Def";
        button27.UseVisualStyleBackColor = false;
        button27.Click += button27_Click;
        button26.BackColor = SystemColors.Window;
        button26.FlatStyle = FlatStyle.Flat;
        button26.ForeColor = Color.FromArgb(0, 0, 192);
        button26.Location = new Point(109, 137);
        button26.Name = "button26";
        button26.Size = new Size(89, 32);
        button26.TabIndex = 147;
        button26.Text = "-Attack";
        button26.UseVisualStyleBackColor = false;
        button26.Click += button26_Click;
        button25.BackColor = SystemColors.Window;
        button25.FlatStyle = FlatStyle.Flat;
        button25.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button25.ForeColor = Color.Fuchsia;
        button25.Location = new Point(109, 63);
        button25.Name = "button25";
        button25.Size = new Size(89, 32);
        button25.TabIndex = 146;
        button25.Text = "-Point";
        button25.UseVisualStyleBackColor = false;
        button25.Click += button25_Click;
        button24.BackColor = SystemColors.Window;
        button24.FlatStyle = FlatStyle.Flat;
        button24.ForeColor = Color.FromArgb(192, 64, 0);
        button24.Location = new Point(299, 174);
        button24.Name = "button24";
        button24.Size = new Size(89, 32);
        button24.TabIndex = 145;
        button24.Text = "-Wuxun";
        button24.UseVisualStyleBackColor = false;
        button24.Click += button24_Click;
        button23.BackColor = SystemColors.Window;
        button23.FlatStyle = FlatStyle.Flat;
        button23.ForeColor = Color.FromArgb(128, 64, 0);
        button23.Location = new Point(109, 174);
        button23.Name = "button23";
        button23.Size = new Size(89, 32);
        button23.TabIndex = 144;
        button23.Text = "- Kỹ năng";
        button23.UseVisualStyleBackColor = false;
        button23.Click += button23_Click;
        button22.BackColor = SystemColors.Window;
        button22.FlatStyle = FlatStyle.Flat;
        button22.ForeColor = Color.FromArgb(0, 0, 64);
        button22.Location = new Point(109, 308);
        button22.Name = "button22";
        button22.Size = new Size(89, 32);
        button22.TabIndex = 143;
        button22.Text = "减少经验";
        button22.UseVisualStyleBackColor = false;
        button22.Click += button22_Click;
        button21.BackColor = SystemColors.Window;
        button21.FlatStyle = FlatStyle.Flat;
        button21.ForeColor = Color.FromArgb(128, 64, 0);
        button21.Location = new Point(109, 101);
        button21.Name = "button21";
        button21.Size = new Size(89, 32);
        button21.TabIndex = 142;
        button21.Text = "-Gold";
        button21.UseVisualStyleBackColor = false;
        button21.Click += button21_Click;
        button18.BackColor = SystemColors.Window;
        button18.FlatStyle = FlatStyle.Flat;
        button18.ForeColor = Color.Teal;
        button18.Location = new Point(204, 269);
        button18.Name = "button18";
        button18.Size = new Size(89, 32);
        button18.TabIndex = 141;
        button18.Text = "增加回避";
        button18.UseVisualStyleBackColor = false;
        button18.Click += button18_Click;
        button17.BackColor = SystemColors.Window;
        button17.FlatStyle = FlatStyle.Flat;
        button17.ForeColor = Color.FromArgb(64, 64, 0);
        button17.Location = new Point(14, 269);
        button17.Name = "button17";
        button17.Size = new Size(89, 32);
        button17.TabIndex = 140;
        button17.Text = "增加命中";
        button17.UseVisualStyleBackColor = false;
        button17.Click += button17_Click;
        button16.BackColor = SystemColors.Window;
        button16.FlatStyle = FlatStyle.Flat;
        button16.ForeColor = Color.MediumSlateBlue;
        button16.Location = new Point(204, 212);
        button16.Name = "button16";
        button16.Size = new Size(89, 32);
        button16.TabIndex = 139;
        button16.Text = "+Mana";
        button16.UseVisualStyleBackColor = false;
        button16.Click += button16_Click;
        button15.BackColor = SystemColors.Window;
        button15.FlatStyle = FlatStyle.Flat;
        button15.ForeColor = Color.Red;
        button15.Location = new Point(14, 213);
        button15.Name = "button15";
        button15.Size = new Size(89, 32);
        button15.TabIndex = 138;
        button15.Text = "+HP";
        button15.UseVisualStyleBackColor = false;
        button15.Click += button15_Click;
        button14.BackColor = SystemColors.Window;
        button14.FlatStyle = FlatStyle.Flat;
        button14.ForeColor = Color.FromArgb(0, 192, 0);
        button14.Location = new Point(204, 308);
        button14.Name = "button14";
        button14.Size = new Size(89, 32);
        button14.TabIndex = 137;
        button14.Text = "增加武防";
        button14.UseVisualStyleBackColor = false;
        button14.Click += button14_Click;
        button13.BackColor = SystemColors.Window;
        button13.FlatStyle = FlatStyle.Flat;
        button13.ForeColor = Color.FromArgb(64, 0, 64);
        button13.Location = new Point(204, 139);
        button13.Name = "button13";
        button13.Size = new Size(89, 32);
        button13.TabIndex = 136;
        button13.Text = "+Def";
        button13.UseVisualStyleBackColor = false;
        button13.Click += button13_Click;
        button12.BackColor = SystemColors.Window;
        button12.FlatStyle = FlatStyle.Flat;
        button12.ForeColor = Color.FromArgb(0, 0, 192);
        button12.Location = new Point(14, 137);
        button12.Name = "button12";
        button12.Size = new Size(89, 32);
        button12.TabIndex = 135;
        button12.Text = "+Attack";
        button12.UseVisualStyleBackColor = false;
        button12.Click += button12_Click;
        button6.BackColor = SystemColors.Window;
        button6.FlatStyle = FlatStyle.Flat;
        button6.ForeColor = Color.FromArgb(192, 64, 0);
        button6.Location = new Point(204, 174);
        button6.Name = "button6";
        button6.Size = new Size(89, 32);
        button6.TabIndex = 134;
        button6.Text = "+Wuxun";
        button6.UseVisualStyleBackColor = false;
        button6.Click += button6_Click;
        button5.BackColor = SystemColors.Window;
        button5.FlatStyle = FlatStyle.Flat;
        button5.ForeColor = Color.FromArgb(128, 64, 0);
        button5.Location = new Point(14, 175);
        button5.Name = "button5";
        button5.Size = new Size(89, 32);
        button5.TabIndex = 133;
        button5.Text = "+Kỹ năng";
        button5.UseVisualStyleBackColor = false;
        button5.Click += button5_Click;
        button4.BackColor = SystemColors.Window;
        button4.FlatStyle = FlatStyle.Flat;
        button4.ForeColor = Color.FromArgb(0, 0, 64);
        button4.Location = new Point(14, 308);
        button4.Name = "button4";
        button4.Size = new Size(89, 32);
        button4.TabIndex = 132;
        button4.Text = "增加经验";
        button4.UseVisualStyleBackColor = false;
        button4.Click += button4_Click;
        button3.BackColor = SystemColors.Window;
        button3.FlatStyle = FlatStyle.Flat;
        button3.ForeColor = Color.FromArgb(128, 64, 0);
        button3.Location = new Point(14, 101);
        button3.Name = "button3";
        button3.Size = new Size(89, 32);
        button3.TabIndex = 131;
        button3.Text = "+Gold";
        button3.UseVisualStyleBackColor = false;
        button3.Click += button3_Click_1;
        label9.AutoSize = true;
        label9.Font = new Font("Tahoma", 12f, FontStyle.Regular, GraphicsUnit.Point, 134);
        label9.ForeColor = Color.Yellow;
        label9.Location = new Point(20, 34);
        label9.Name = "label9";
        label9.Size = new Size(32, 19);
        label9.TabIndex = 130;
        label9.Text = "SL:";
        textBox2.BackColor = SystemColors.Info;
        textBox2.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        textBox2.Location = new Point(66, 29);
        textBox2.MaxLength = 68888;
        textBox2.Name = "textBox2";
        textBox2.Size = new Size(279, 27);
        textBox2.TabIndex = 127;
        button2.BackColor = SystemColors.Window;
        button2.FlatStyle = FlatStyle.Flat;
        button2.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button2.ForeColor = Color.Fuchsia;
        button2.Location = new Point(14, 63);
        button2.Name = "button2";
        button2.Size = new Size(89, 32);
        button2.TabIndex = 126;
        button2.Text = "+ Point";
        button2.UseVisualStyleBackColor = false;
        button2.Click += button2_Click;
        button41.BackColor = SystemColors.Window;
        button41.FlatStyle = FlatStyle.Flat;
        button41.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button41.ForeColor = Color.FromArgb(192, 0, 192);
        button41.Location = new Point(267, 716);
        button41.Name = "button41";
        button41.Size = new Size(126, 32);
        button41.TabIndex = 172;
        button41.Text = "增加五彩";
        button41.UseVisualStyleBackColor = false;
        textBox13.BackColor = Color.White;
        textBox13.Location = new Point(125, 719);
        textBox13.Name = "textBox13";
        textBox13.Size = new Size(123, 26);
        textBox13.TabIndex = 171;
        label19.AutoSize = true;
        label19.Font = new Font("Tahoma", 14.25f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label19.ForeColor = Color.FromArgb(192, 0, 192);
        label19.Location = new Point(19, 721);
        label19.Name = "label19";
        label19.Size = new Size(97, 23);
        label19.TabIndex = 170;
        label19.Text = "五彩提示:";
        button40.BackColor = SystemColors.Window;
        button40.FlatStyle = FlatStyle.Flat;
        button40.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button40.ForeColor = Color.Blue;
        button40.Location = new Point(267, 796);
        button40.Name = "button40";
        button40.Size = new Size(126, 35);
        button40.TabIndex = 163;
        button40.Text = "修改PK等级";
        button40.UseVisualStyleBackColor = false;
        button40.Click += button40_Click;
        button39.BackColor = SystemColors.Window;
        button39.FlatStyle = FlatStyle.Flat;
        button39.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button39.ForeColor = Color.Lime;
        button39.Location = new Point(267, 757);
        button39.Name = "button39";
        button39.Size = new Size(126, 32);
        button39.TabIndex = 162;
        button39.Text = "增加八彩";
        button39.UseVisualStyleBackColor = false;
        button38.BackColor = SystemColors.Window;
        button38.FlatStyle = FlatStyle.Flat;
        button38.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button38.ForeColor = SystemColors.ButtonShadow;
        button38.Location = new Point(253, 524);
        button38.Name = "button38";
        button38.Size = new Size(123, 26);
        button38.TabIndex = 161;
        button38.Text = "OK";
        button38.UseVisualStyleBackColor = false;
        button38.Click += button38_Click;
        textBox14.BackColor = Color.White;
        textBox14.Location = new Point(125, 800);
        textBox14.Name = "textBox14";
        textBox14.Size = new Size(123, 26);
        textBox14.TabIndex = 158;
        label20.AutoSize = true;
        label20.Font = new Font("Tahoma", 14.25f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label20.ForeColor = Color.FromArgb(0, 0, 192);
        label20.Location = new Point(37, 802);
        label20.Name = "label20";
        label20.Size = new Size(82, 23);
        label20.TabIndex = 157;
        label20.Text = "PK等级:";
        textBox12.BackColor = Color.White;
        textBox12.Location = new Point(125, 758);
        textBox12.Name = "textBox12";
        textBox12.Size = new Size(123, 26);
        textBox12.TabIndex = 156;
        label18.AutoSize = true;
        label18.Font = new Font("Tahoma", 14.25f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label18.ForeColor = Color.Lime;
        label18.Location = new Point(19, 760);
        label18.Name = "label18";
        label18.Size = new Size(97, 23);
        label18.TabIndex = 155;
        label18.Text = "八彩提示:";
        label18.Click += label18_Click;
        textBox11.BackColor = Color.White;
        textBox11.Location = new Point(125, 524);
        textBox11.Name = "textBox11";
        textBox11.Size = new Size(123, 26);
        textBox11.TabIndex = 154;
        label17.AutoSize = true;
        label17.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label17.ForeColor = Color.Yellow;
        label17.Location = new Point(39, 527);
        label17.Name = "label17";
        label17.Size = new Size(44, 19);
        label17.TabIndex = 153;
        label17.Text = "VIP:";
        textBox10.BackColor = SystemColors.Window;
        textBox10.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        textBox10.ForeColor = Color.Orange;
        textBox10.Location = new Point(983, 296);
        textBox10.MaxLength = 7;
        textBox10.Name = "textBox10";
        textBox10.Size = new Size(75, 27);
        textBox10.TabIndex = 152;
        textBox10.Text = "0";
        label16.AutoSize = true;
        label16.Font = new Font("Tahoma", 10f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label16.ForeColor = Color.FromArgb(255, 128, 0);
        label16.Location = new Point(687, 304);
        label16.Name = "label16";
        label16.Size = new Size(79, 17);
        label16.TabIndex = 151;
        label16.Text = "Thức tỉnh:";
        comboBox1.BackColor = SystemColors.Window;
        comboBox1.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox1.Font = new Font("Tahoma", 12f, FontStyle.Regular, GraphicsUnit.Point, 134);
        comboBox1.ForeColor = Color.Green;
        comboBox1.FormattingEnabled = true;
        comboBox1.Items.AddRange(new object[2] { "Không", "Thức tỉnh" });
        comboBox1.Location = new Point(777, 298);
        comboBox1.Name = "comboBox1";
        comboBox1.Size = new Size(200, 27);
        comboBox1.TabIndex = 150;
        comboBox1.SelectedIndexChanged += comboBox1_SelectedIndexChanged;
        textBox9.BackColor = Color.White;
        textBox9.Location = new Point(124, 444);
        textBox9.Name = "textBox9";
        textBox9.Size = new Size(123, 26);
        textBox9.TabIndex = 149;
        button35.BackColor = SystemColors.Window;
        button35.FlatStyle = FlatStyle.Flat;
        button35.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button35.ForeColor = SystemColors.ButtonShadow;
        button35.Location = new Point(253, 444);
        button35.Name = "button35";
        button35.Size = new Size(123, 26);
        button35.TabIndex = 147;
        button35.Text = "OK";
        button35.UseVisualStyleBackColor = false;
        button35.Click += button35_Click;
        button20.BackColor = SystemColors.Window;
        button20.FlatStyle = FlatStyle.Flat;
        button20.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button20.ForeColor = SystemColors.ButtonShadow;
        button20.Location = new Point(316, 404);
        button20.Name = "button20";
        button20.Size = new Size(60, 26);
        button20.TabIndex = 146;
        button20.Text = "Giảm";
        button20.UseVisualStyleBackColor = false;
        button20.Click += button20_Click;
        button19.BackColor = SystemColors.Window;
        button19.FlatStyle = FlatStyle.Flat;
        button19.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button19.ForeColor = SystemColors.ButtonShadow;
        button19.Location = new Point(252, 238);
        button19.Name = "button19";
        button19.Size = new Size(123, 26);
        button19.TabIndex = 145;
        button19.Text = "OK";
        button19.UseVisualStyleBackColor = false;
        button19.Click += button19_Click;
        textBox8.BackColor = Color.White;
        textBox8.Location = new Point(123, 238);
        textBox8.Name = "textBox8";
        textBox8.Size = new Size(123, 26);
        textBox8.TabIndex = 144;
        textBox8.MouseClick += textBox8_MouseClick;
        label6.AutoSize = true;
        label6.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label6.ForeColor = Color.Yellow;
        label6.Location = new Point(35, 241);
        label6.Name = "label6";
        label6.Size = new Size(57, 19);
        label6.TabIndex = 143;
        label6.Text = "Nghề:";
        textBox3.BackColor = Color.White;
        textBox3.Location = new Point(125, 483);
        textBox3.Name = "textBox3";
        textBox3.Size = new Size(123, 26);
        textBox3.TabIndex = 142;
        textBox3.MouseClick += textBox3_MouseClick;
        label14.AutoSize = true;
        label14.BackColor = Color.Black;
        label14.Font = new Font("Tahoma", 10.2f, FontStyle.Bold, GraphicsUnit.Point, 0);
        label14.ForeColor = Color.Red;
        label14.Location = new Point(16, 121);
        label14.Name = "label14";
        label14.Size = new Size(324, 17);
        label14.TabIndex = 141;
        label14.Text = "Nhắc nhở: Người chơi nhất định phải là online ";
        button11.BackColor = SystemColors.Window;
        button11.FlatStyle = FlatStyle.Flat;
        button11.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button11.ForeColor = SystemColors.ButtonShadow;
        button11.Location = new Point(254, 404);
        button11.Name = "button11";
        button11.Size = new Size(60, 26);
        button11.TabIndex = 140;
        button11.Text = "Tăng";
        button11.UseVisualStyleBackColor = false;
        button10.BackColor = SystemColors.Window;
        button10.FlatStyle = FlatStyle.Flat;
        button10.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button10.ForeColor = SystemColors.ButtonShadow;
        button10.Location = new Point(253, 370);
        button10.Name = "button10";
        button10.Size = new Size(123, 26);
        button10.TabIndex = 139;
        button10.Text = "OK";
        button10.UseVisualStyleBackColor = false;
        button10.Click += button10_Click_1;
        button9.BackColor = SystemColors.Window;
        button9.FlatStyle = FlatStyle.Flat;
        button9.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button9.ForeColor = SystemColors.ButtonShadow;
        button9.Location = new Point(253, 333);
        button9.Name = "button9";
        button9.Size = new Size(123, 26);
        button9.TabIndex = 138;
        button9.Text = "OK";
        button9.UseVisualStyleBackColor = false;
        button9.Click += button9_Click;
        button8.BackColor = SystemColors.Window;
        button8.FlatStyle = FlatStyle.Flat;
        button8.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button8.ForeColor = SystemColors.ButtonShadow;
        button8.Location = new Point(253, 303);
        button8.Name = "button8";
        button8.Size = new Size(123, 26);
        button8.TabIndex = 137;
        button8.Text = "OK";
        button8.UseVisualStyleBackColor = false;
        button8.Click += button8_Click;
        button7.BackColor = SystemColors.Window;
        button7.FlatStyle = FlatStyle.Flat;
        button7.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        button7.ForeColor = Color.FromArgb(255, 128, 128);
        button7.Location = new Point(254, 483);
        button7.Name = "button7";
        button7.Size = new Size(123, 26);
        button7.TabIndex = 136;
        button7.Text = "OK";
        button7.UseVisualStyleBackColor = false;
        button7.Click += button7_Click;
        textBox7.BackColor = Color.White;
        textBox7.Location = new Point(125, 404);
        textBox7.Name = "textBox7";
        textBox7.Size = new Size(123, 26);
        textBox7.TabIndex = 135;
        textBox7.MouseClick += textBox7_MouseClick;
        textBox6.BackColor = Color.White;
        textBox6.Location = new Point(125, 370);
        textBox6.Name = "textBox6";
        textBox6.Size = new Size(123, 26);
        textBox6.TabIndex = 134;
        textBox6.MouseClick += textBox6_MouseClick;
        textBox5.BackColor = Color.White;
        textBox5.Location = new Point(124, 333);
        textBox5.Name = "textBox5";
        textBox5.Size = new Size(123, 26);
        textBox5.TabIndex = 133;
        textBox5.MouseClick += textBox5_MouseClick;
        textBox4.BackColor = Color.White;
        textBox4.Location = new Point(124, 303);
        textBox4.Name = "textBox4";
        textBox4.Size = new Size(123, 26);
        textBox4.TabIndex = 132;
        textBox4.MouseClick += textBox4_MouseClick;
        label13.AutoSize = true;
        label13.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label13.ForeColor = Color.Yellow;
        label13.Location = new Point(10, 408);
        label13.Name = "label13";
        label13.Size = new Size(115, 19);
        label13.TabIndex = 130;
        label13.Text = "Chuyển Sinh:";
        label12.AutoSize = true;
        label12.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label12.ForeColor = Color.Yellow;
        label12.Location = new Point(38, 373);
        label12.Name = "label12";
        label12.Size = new Size(58, 19);
        label12.TabIndex = 129;
        label12.Text = "Level:";
        label12.Click += label12_Click;
        label11.AutoSize = true;
        label11.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label11.ForeColor = Color.Yellow;
        label11.Location = new Point(6, 337);
        label11.Name = "label11";
        label11.Size = new Size(111, 19);
        label11.TabIndex = 128;
        label11.Text = "Thăng Chức:";
        label10.AutoSize = true;
        label10.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label10.ForeColor = Color.Yellow;
        label10.Location = new Point(23, 306);
        label10.Name = "label10";
        label10.Size = new Size(73, 19);
        label10.TabIndex = 127;
        label10.Text = "Thế Lực";
        label8.AutoSize = true;
        label8.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label8.ForeColor = Color.Yellow;
        label8.Location = new Point(23, 483);
        label8.Name = "label8";
        label8.Size = new Size(84, 19);
        label8.TabIndex = 126;
        label8.Text = "Giới tính:";
        label7.AutoSize = true;
        label7.BackColor = Color.Black;
        label7.Font = new Font("Tahoma", 11.25f, FontStyle.Bold, GraphicsUnit.Point, 0);
        label7.ForeColor = Color.Red;
        label7.Location = new Point(696, 33);
        label7.Name = "label7";
        label7.Size = new Size(410, 18);
        label7.TabIndex = 125;
        label7.Text = "Lưu ý: Cường hóa tối cao 99999, thuộc tính tối cao 10,";
        label7.Click += label7_Click;
        label5.AutoSize = true;
        label5.Font = new Font("Tahoma", 10f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label5.ForeColor = Color.FromArgb(255, 128, 128);
        label5.Location = new Point(829, 336);
        label5.Name = "label5";
        label5.Size = new Size(52, 17);
        label5.TabIndex = 124;
        label5.Text = "Thêm:";
        label5.Click += label5_Click;
        comboBox19.BackColor = SystemColors.Window;
        comboBox19.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox19.Font = new Font("Tahoma", 12f, FontStyle.Regular, GraphicsUnit.Point, 134);
        comboBox19.ForeColor = Color.DarkRed;
        comboBox19.FormattingEnabled = true;
        comboBox19.Items.AddRange(new object[4] { "Không buộc", "Khóa lại", "Khá tốt", "Cao Cấp" });
        comboBox19.Location = new Point(923, 331);
        comboBox19.Name = "comboBox19";
        comboBox19.Size = new Size(123, 27);
        comboBox19.TabIndex = 123;
        comboBox19.SelectedIndexChanged += comboBox19_SelectedIndexChanged;
        textBox68.BackColor = SystemColors.Window;
        textBox68.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        textBox68.ForeColor = Color.DarkOliveGreen;
        textBox68.Location = new Point(983, 263);
        textBox68.MaxLength = 7;
        textBox68.Name = "textBox68";
        textBox68.Size = new Size(75, 27);
        textBox68.TabIndex = 122;
        textBox68.Text = "0";
        textBox67.BackColor = SystemColors.Window;
        textBox67.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        textBox67.ForeColor = Color.DarkOliveGreen;
        textBox67.Location = new Point(983, 231);
        textBox67.MaxLength = 7;
        textBox67.Name = "textBox67";
        textBox67.Size = new Size(75, 27);
        textBox67.TabIndex = 121;
        textBox67.Text = "0";
        textBox66.BackColor = SystemColors.Window;
        textBox66.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        textBox66.ForeColor = Color.DarkOliveGreen;
        textBox66.Location = new Point(983, 198);
        textBox66.MaxLength = 7;
        textBox66.Name = "textBox66";
        textBox66.Size = new Size(75, 27);
        textBox66.TabIndex = 120;
        textBox66.Text = "0";
        textBox65.BackColor = SystemColors.Window;
        textBox65.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        textBox65.ForeColor = Color.DarkOliveGreen;
        textBox65.Location = new Point(983, 163);
        textBox65.MaxLength = 7;
        textBox65.Name = "textBox65";
        textBox65.Size = new Size(75, 27);
        textBox65.TabIndex = 119;
        textBox65.Text = "0";
        textBox64.BackColor = SystemColors.Window;
        textBox64.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        textBox64.ForeColor = Color.FromArgb(0, 0, 192);
        textBox64.Location = new Point(983, 130);
        textBox64.MaxLength = 7;
        textBox64.Name = "textBox64";
        textBox64.Size = new Size(75, 27);
        textBox64.TabIndex = 118;
        textBox64.Text = "0";
        textBox63.BackColor = SystemColors.Window;
        textBox63.Font = new Font("Tahoma", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        textBox63.ForeColor = Color.FromArgb(64, 0, 0);
        textBox63.Location = new Point(983, 98);
        textBox63.MaxLength = 7;
        textBox63.Name = "textBox63";
        textBox63.Size = new Size(75, 27);
        textBox63.TabIndex = 117;
        textBox63.Text = "0";
        comboBox18.BackColor = SystemColors.Info;
        comboBox18.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox18.Font = new Font("Tahoma", 12f, FontStyle.Regular, GraphicsUnit.Point, 134);
        comboBox18.ForeColor = Color.FromArgb(64, 0, 64);
        comboBox18.FormattingEnabled = true;
        comboBox18.Items.AddRange(new object[19]
        {
            "Không", "Công lực tăng", "Lực phòng ngự tăng", "Sinh mệnh tăng", "Nội công tăng", "Chính xác tăng",
            "Né tránh tăng", "Công kích võ công %", "Khí công tăng", "Tỷ lệ cường hoá/ hợp thành %",
            "Lực đã kích tăng", "Lực phòng ngự  võ công tăng", "X.Suất nhận tiền %", "Kinh nghiệm khi chết giảm %",
            "Kinh nghiệm tăng %", "Not Know", "Red Blue Refining", "New recovery item capacity", "Số lượng sử dụng"
        });
        comboBox18.Location = new Point(777, 266);
        comboBox18.MaxDropDownItems = 18;
        comboBox18.Name = "comboBox18";
        comboBox18.Size = new Size(200, 27);
        comboBox18.TabIndex = 116;
        comboBox17.BackColor = SystemColors.Info;
        comboBox17.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox17.Font = new Font("Tahoma", 12f, FontStyle.Regular, GraphicsUnit.Point, 134);
        comboBox17.ForeColor = Color.FromArgb(64, 0, 64);
        comboBox17.FormattingEnabled = true;
        comboBox17.Items.AddRange(new object[19]
        {
            "Không", "Công lực tăng", "Lực phòng ngự tăng", "Sinh mệnh tăng", "Nội công tăng", "Chính xác tăng",
            "Né tránh tăng", "Công kích võ công %", "Khí công tăng", "Tỷ lệ cường hoá/ hợp thành %",
            "Lực đã kích tăng", "Lực phòng ngự  võ công tăng", "X.Suất nhận tiền %", "Kinh nghiệm khi chết giảm %",
            "Kinh nghiệm tăng %", "Not Know", "Red Blue Refining", "New recovery item capacity", "Số lượng sử dụng"
        });
        comboBox17.Location = new Point(777, 233);
        comboBox17.MaxDropDownItems = 18;
        comboBox17.Name = "comboBox17";
        comboBox17.Size = new Size(200, 27);
        comboBox17.TabIndex = 115;
        comboBox16.BackColor = SystemColors.Info;
        comboBox16.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox16.Font = new Font("Tahoma", 12f, FontStyle.Regular, GraphicsUnit.Point, 134);
        comboBox16.ForeColor = Color.FromArgb(64, 0, 64);
        comboBox16.FormattingEnabled = true;
        comboBox16.Items.AddRange(new object[19]
        {
            "Không", "Công lực tăng", "Lực phòng ngự tăng", "Sinh mệnh tăng", "Nội công tăng", "Chính xác tăng",
            "Né tránh tăng", "Công kích võ công %", "Khí công tăng", "Tỷ lệ cường hoá/ hợp thành %",
            "Lực đã kích tăng", "Lực phòng ngự  võ công tăng", "X.Suất nhận tiền %", "Kinh nghiệm khi chết giảm %",
            "Kinh nghiệm tăng %", "Not Know", "Red Blue Refining", "New recovery item capacity", "Số lượng sử dụng"
        });
        comboBox16.Location = new Point(777, 201);
        comboBox16.MaxDropDownItems = 18;
        comboBox16.Name = "comboBox16";
        comboBox16.Size = new Size(200, 27);
        comboBox16.TabIndex = 114;
        comboBox15.BackColor = SystemColors.Info;
        comboBox15.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox15.Font = new Font("Tahoma", 12f, FontStyle.Regular, GraphicsUnit.Point, 134);
        comboBox15.ForeColor = Color.FromArgb(64, 0, 64);
        comboBox15.FormattingEnabled = true;
        comboBox15.Items.AddRange(new object[19]
        {
            "Không", "Công lực tăng", "Lực phòng ngự tăng", "Sinh mệnh tăng", "Nội công tăng", "Chính xác tăng",
            "Né tránh tăng", "Công kích võ công %", "Khí công tăng", "Tỷ lệ cường hoá/ hợp thành %",
            "Lực đã kích tăng", "Lực phòng ngự  võ công tăng", "X.Suất nhận tiền %", "Kinh nghiệm khi chết giảm %",
            "Kinh nghiệm tăng %", "Not Know", "Red Blue Refining", "New recovery item capacity", "Số lượng sử dụng"
        });
        comboBox15.Location = new Point(777, 166);
        comboBox15.MaxDropDownItems = 18;
        comboBox15.Name = "comboBox15";
        comboBox15.Size = new Size(200, 27);
        comboBox15.TabIndex = 113;
        comboBox14.BackColor = SystemColors.Info;
        comboBox14.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox14.Font = new Font("Tahoma", 12f, FontStyle.Regular, GraphicsUnit.Point, 134);
        comboBox14.ForeColor = Color.FromArgb(64, 0, 64);
        comboBox14.FormattingEnabled = true;
        comboBox14.Items.AddRange(new object[7] { "Không", "Độc", "Ngoại công", "Nội công", "Phong", "Thuỷ", "Hoả" });
        comboBox14.Location = new Point(777, 132);
        comboBox14.MaxDropDownItems = 18;
        comboBox14.Name = "comboBox14";
        comboBox14.Size = new Size(200, 27);
        comboBox14.TabIndex = 112;
        comboBox13.BackColor = SystemColors.Info;
        comboBox13.DisplayMember = "1";
        comboBox13.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox13.Font = new Font("Tahoma", 12f, FontStyle.Regular, GraphicsUnit.Point, 134);
        comboBox13.ForeColor = Color.FromArgb(64, 0, 64);
        comboBox13.FormattingEnabled = true;
        comboBox13.Items.AddRange(new object[5]
            { "Không", "Tăng cường Vũ Khí", "Tăng cường áo giáp", "Tăng cường trang sức", "Số lượng" });
        comboBox13.Location = new Point(777, 100);
        comboBox13.Name = "comboBox13";
        comboBox13.Size = new Size(200, 27);
        comboBox13.TabIndex = 111;
        comboBox13.SelectedIndexChanged += comboBox13_SelectedIndexChanged;
        label46.AutoSize = true;
        label46.Font = new Font("Tahoma", 10f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label46.ForeColor = Color.FromArgb(255, 128, 0);
        label46.Location = new Point(696, 271);
        label46.Name = "label46";
        label46.Size = new Size(63, 17);
        label46.TabIndex = 110;
        label46.Text = "Dòng 4:";
        label45.AutoSize = true;
        label45.Font = new Font("Tahoma", 10f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label45.ForeColor = Color.FromArgb(255, 128, 0);
        label45.Location = new Point(696, 238);
        label45.Name = "label45";
        label45.Size = new Size(63, 17);
        label45.TabIndex = 109;
        label45.Text = "Dòng 3:";
        label44.AutoSize = true;
        label44.Font = new Font("Tahoma", 10f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label44.ForeColor = Color.FromArgb(255, 128, 0);
        label44.Location = new Point(696, 206);
        label44.Name = "label44";
        label44.Size = new Size(63, 17);
        label44.TabIndex = 108;
        label44.Text = "Dòng 2:";
        label43.AutoSize = true;
        label43.Font = new Font("Tahoma", 10f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label43.ForeColor = Color.FromArgb(255, 128, 0);
        label43.Location = new Point(696, 171);
        label43.Name = "label43";
        label43.Size = new Size(63, 17);
        label43.TabIndex = 107;
        label43.Text = "Dòng 1:";
        label42.AutoSize = true;
        label42.Font = new Font("Tahoma", 10f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label42.ForeColor = Color.FromArgb(255, 128, 0);
        label42.Location = new Point(696, 137);
        label42.Name = "label42";
        label42.Size = new Size(87, 17);
        label42.TabIndex = 106;
        label42.Text = "Thuộc tính:";
        label41.AutoSize = true;
        label41.Font = new Font("Tahoma", 10f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label41.ForeColor = Color.FromArgb(255, 128, 0);
        label41.Location = new Point(689, 104);
        label41.Name = "label41";
        label41.Size = new Size(90, 17);
        label41.TabIndex = 105;
        label41.Text = "Cường hoá:";
        button1.BackColor = Color.FromArgb(255, 128, 0);
        button1.FlatStyle = FlatStyle.Flat;
        button1.Font = new Font("Tahoma", 13.8f, FontStyle.Bold, GraphicsUnit.Point, 0);
        button1.ForeColor = Color.Honeydew;
        button1.Location = new Point(830, 361);
        button1.Name = "button1";
        button1.Size = new Size(180, 41);
        button1.TabIndex = 71;
        button1.Text = "Thêm vật phẩm";
        button1.UseVisualStyleBackColor = false;
        button1.Click += button1_Click;
        textBox1.BackColor = Color.White;
        textBox1.Font = new Font("Tahoma", 12f, FontStyle.Regular, GraphicsUnit.Point, 0);
        textBox1.Location = new Point(123, 160);
        textBox1.Name = "textBox1";
        textBox1.Size = new Size(123, 27);
        textBox1.TabIndex = 70;
        textBox1.MouseClick += textBox1_MouseClick;
        label4.AutoSize = true;
        label4.Font = new Font("Tahoma", 13.8f, FontStyle.Bold, GraphicsUnit.Point, 0);
        label4.ForeColor = Color.Yellow;
        label4.Location = new Point(65, 161);
        label4.Name = "label4";
        label4.Size = new Size(52, 23);
        label4.TabIndex = 69;
        label4.Text = "Tên:";
        label3.AutoSize = true;
        label3.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label3.ForeColor = Color.Yellow;
        label3.Location = new Point(283, 65);
        label3.Name = "label3";
        label3.Size = new Size(149, 17);
        label3.TabIndex = 68;
        label3.Text = "tra tìm vật phẩm ID:";
        label2.AutoSize = true;
        label2.BackColor = Color.Black;
        label2.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label2.ForeColor = Color.White;
        label2.Location = new Point(11, 208);
        label2.Name = "label2";
        label2.Size = new Size(140, 17);
        label2.TabIndex = 67;
        label2.Text = "Thông tin nhân vật";
        label2.Click += label2_Click;
        label1.AutoSize = true;
        label1.Font = new Font("Tahoma", 10.5f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label1.ForeColor = Color.Yellow;
        label1.Location = new Point(291, 32);
        label1.Name = "label1";
        label1.Size = new Size(146, 17);
        label1.TabIndex = 66;
        label1.Text = "Vật phẩm phân loại:";
        comboBox12.BackColor = Color.White;
        comboBox12.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox12.Font = new Font("Tahoma", 9.75f, FontStyle.Regular, GraphicsUnit.Point, 0);
        comboBox12.ForeColor = Color.FromArgb(64, 0, 0);
        comboBox12.FormattingEnabled = true;
        comboBox12.Location = new Point(449, 59);
        comboBox12.MaxDropDownItems = 37;
        comboBox12.Name = "comboBox12";
        comboBox12.Size = new Size(234, 24);
        comboBox12.TabIndex = 65;
        comboBox11.BackColor = Color.White;
        comboBox11.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox11.Font = new Font("Tahoma", 9.75f, FontStyle.Regular, GraphicsUnit.Point, 0);
        comboBox11.ForeColor = Color.FromArgb(0, 0, 64);
        comboBox11.FormattingEnabled = true;
        comboBox11.Items.AddRange(new object[17]
        {
            "Vũ Khí", "Y Phục", "Hộ Thủ", "Ủng / Giầy", "Nội Giáp", "Khuyên Tai", "Dây Chuyền / Vòng Cổ", "Nhẫn",
            "Áo choàng", "Áo choàng bang",
            "Thú Nuôi", "Sách /Mũi tên", "Tảng Đá / Ngọc", "Bùa may mắn", "Hộp / Bảo Rương", "Gói Script", "Khác"
        });
        comboBox11.Location = new Point(449, 27);
        comboBox11.MaxDropDownItems = 20;
        comboBox11.Name = "comboBox11";
        comboBox11.Size = new Size(234, 24);
        comboBox11.TabIndex = 64;
        comboBox11.SelectedIndexChanged += comboBox11_SelectedIndexChanged;
        listBox3.BackColor = Color.White;
        listBox3.Font = new Font("Tahoma", 9.75f, FontStyle.Regular, GraphicsUnit.Point, 163);
        listBox3.FormattingEnabled = true;
        listBox3.ItemHeight = 16;
        listBox3.Location = new Point(449, 90);
        listBox3.Name = "listBox3";
        listBox3.Size = new Size(234, 244);
        listBox3.TabIndex = 63;
        listBox3.SelectedIndexChanged += listBox3_SelectedIndexChanged;
        statusStrip1.BackColor = Color.Black;
        statusStrip1.ImageScalingSize = new Size(20, 20);
        statusStrip1.Items.AddRange(new ToolStripItem[2] { toolStripStatusLabel1, tishi });
        statusStrip1.Location = new Point(0, 727);
        statusStrip1.Name = "statusStrip1";
        statusStrip1.Size = new Size(1370, 22);
        statusStrip1.TabIndex = 64;
        statusStrip1.Text = "statusStrip1";
        toolStripStatusLabel1.BackColor = Color.Black;
        toolStripStatusLabel1.ForeColor = SystemColors.ControlDarkDark;
        toolStripStatusLabel1.Name = "toolStripStatusLabel1";
        toolStripStatusLabel1.Size = new Size(60, 17);
        toolStripStatusLabel1.Text = "Hệ thống:";
        tishi.ForeColor = Color.Red;
        tishi.Name = "tishi";
        tishi.Size = new Size(0, 17);
        AutoScaleDimensions = new SizeF(6f, 13f);
        AutoScaleMode = AutoScaleMode.Font;
        BackColor = Color.Black;
        ClientSize = new Size(1370, 749);
        Controls.Add(statusStrip1);
        Controls.Add(groupBox1);
        Font = new Font("Tahoma", 8.25f, FontStyle.Regular, GraphicsUnit.Point, 0);
        Name = "GMGJ";
        ShowIcon = false;
        SizeGripStyle = SizeGripStyle.Show;
        Text = "GM TOOL V1";
        Load += GMGJ_Load;
        groupBox1.ResumeLayout(false);
        groupBox1.PerformLayout();
        groupBox2.ResumeLayout(false);
        groupBox2.PerformLayout();
        statusStrip1.ResumeLayout(false);
        statusStrip1.PerformLayout();
        ResumeLayout(false);
        PerformLayout();
    }

    private void label18_Click(object sender, EventArgs e)
    {
    }

    private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
    {
    }

    private void comboBox13_SelectedIndexChanged(object sender, EventArgs e)
    {
    }

    private void textBox15_TextChanged(object sender, EventArgs e)
    {
    }

    private void label22_Click(object sender, EventArgs e)
    {
    }

    private void comboBox19_SelectedIndexChanged(object sender, EventArgs e)
    {
    }

    private void label7_Click(object sender, EventArgs e)
    {
    }

    private void label5_Click(object sender, EventArgs e)
    {
    }

    private void groupBox2_Enter(object sender, EventArgs e)
    {
    }

    private void label2_Click(object sender, EventArgs e)
    {
    }

    private void button20_Click(object sender, EventArgs e)
    {
    }
}