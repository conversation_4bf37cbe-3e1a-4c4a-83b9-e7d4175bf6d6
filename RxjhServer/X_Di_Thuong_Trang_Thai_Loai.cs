using System;
using System.Timers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class X_Di_Thuong_Trang_Thai_Loai : IDisposable
{
    public NpcClass Npc;

    public int NpcPlayId;
    public System.Timers.Timer npcyd;

    public Players Play;

    public DateTime time;

    public System.Timers.Timer yczt;

    public double ycztsl;

    public X_Di_Thuong_Trang_Thai_Loai(Players Play_, int ThoiGian, int DiThuong_ID, double DiThuong_SoLuong)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường trạng thái loại -NEW 222");
        FLD_PID = DiThuong_ID;
        FLD_NUM = DiThuong_SoLuong;
        time = DateTime.Now;
        time = time.AddMilliseconds(ThoiGian);
        Play = Play_;
        npcyd = new System.Timers.Timer(ThoiGian);
        npcyd.Elapsed += TimeEndEvent1;
        npcyd.Enabled = true;
        npcyd.AutoReset = false;
        StatusEffect(FLD_PID, 1, (int)DiThuong_SoLuong, ThoiGian / 1000);
    }

    public X_Di_Thuong_Trang_Thai_Loai(NpcClass Play_, int _NpcPlayId, int ThoiGian, int DiThuong_ID,
        double DiThuong_SoLuong)
    {
        NpcPlayId = _NpcPlayId;
        FLD_PID = DiThuong_ID;
        time = DateTime.Now;
        time = time.AddMilliseconds(ThoiGian);
        Npc = Play_;
        npcyd = new System.Timers.Timer(ThoiGian);
        npcyd.Elapsed += TimeEndEvent1;
        npcyd.Enabled = true;
        npcyd.AutoReset = false;
        FLD_NUM = DiThuong_SoLuong;
        StatusEffect(FLD_PID, 1, (int)DiThuong_SoLuong, ThoiGian / 1000);
    }

    public int FLD_PID { get; set; }

    public double FLD_NUM { get; set; }

    public void Dispose()
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường trạng thái loại -Dispose 444");
        if (npcyd != null)
        {
            npcyd.Enabled = false;
            npcyd.Close();
            npcyd.Dispose();
            npcyd = null;
        }

        if (yczt != null)
        {
            yczt.Enabled = false;
            yczt.Close();
            yczt.Dispose();
            yczt = null;
        }

        Play = null;
        Npc = null;
    }

    public void TrangThai_BatThuong_LoaiChayMau(double ycztsll)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "TrangThai_BatThuongClass-TrangThai_BatThuong_LoaiChayMau");
        ycztsl = ycztsll;
        yczt = new System.Timers.Timer(1000.0);
        yczt.Elapsed += yczt_Elapsed;
        yczt.Enabled = true;
        yczt.AutoReset = true;
    }

    public void ThoiGianKetThucVanDe_ThucDayLuuThongMauHuyet()
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường trạng thái loại - Thời gian kết thúc sự kiện 444");
        try
        {
            if (Play != null)
            {
                Play.TrangThai_BatThuong.Remove(19);
                StatusEffect(19, 0, 0, 0);
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1,
                "Dị thường trạng thái loại Thời gian kết thúc sự kiệnPhạm sai lầm: 222 [" + FLD_PID + "]" + ex);
        }
        finally
        {
            Dispose();
        }
    }

    public void ThoiGianKetThuc_NgocPhongTuong()
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường trạng thái loại - Thời gian kết thúc sự kiện 555");
        try
        {
            if (Play != null)
            {
                Play.TrangThai_BatThuong.Remove(20);
                StatusEffect(20, 0, 0, 0);
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1,
                "Dị thường trạng thái loại Thời gian kết thúc sự kiệnPhạm sai lầm: 333 [" + FLD_PID + "]" + ex);
        }
        finally
        {
            Dispose();
        }
    }

    public void yczt_Elapsed(object sender, ElapsedEventArgs e)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "yczt_Elapsed");
        if (Play != null)
        {
            if (Play.PlayerTuVong || Play.Exiting || Play.GiaoDich.GiaoDichBenTrong || Play.OpenWarehouse ||
                Play.InTheShop) return;
            Play.NhanVat_HP -= (int)ycztsl;
            if (Play.NhanVat_HP <= 0)
            {
                Play.Death(1);
                if (yczt != null)
                {
                    yczt.Enabled = false;
                    yczt.Close();
                    yczt.Dispose();
                    yczt = null;
                }
            }

            Play.CapNhat_HP_MP_SP();
        }
        else
        {
            if (Npc == null) return;
            Npc.Rxjh_HP -= (int)ycztsl;
            if (Npc.Rxjh_HP <= 0)
            {
                Npc.GuiDiTuVongSoLieu(NpcPlayId);
                if (yczt != null)
                {
                    yczt.Enabled = false;
                    yczt.Close();
                    yczt.Dispose();
                    yczt = null;
                }
            }
        }
    }

    public void TimeEndEvent1(object sender, ElapsedEventArgs e)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "TimeEndEvent1");
        ThoiGianKetThucSuKien();
    }

    public void ThoiGianKetThucSuKien()
    {
        var num = 0;
        try
        {
            if (npcyd != null)
            {
                num = 1;
                npcyd.Enabled = false;
                num = 2;
                npcyd.Close();
                num = 3;
                npcyd.Dispose();
                num = 4;
                npcyd = null;
            }

            num = 5;
            if (Npc != null)
            {
                num = 6;
                var fLD_PID = FLD_PID;
                var num2 = fLD_PID;
                if (num2 != 9)
                {
                }

                if (Npc.TrangThai_BatThuong != null)
                {
                    num = 7;
                    Npc.TrangThai_BatThuong.Remove(FLD_PID);
                    num = 8;
                }

                num = 9;
                StatusEffect(FLD_PID, 0, 0, 0);
                num = 10;
                Dispose();
            }
            else if (Play != null)
            {
                num = 11;
                switch (FLD_PID)
                {
                    case 242:
                        num = 20;
                        Play.FLD_NhanVat_ThemVao_CongKich -= 15;
                        Play.FLD_NhanVat_ThemVao_PhongNgu -= 15;
                        Play.CharactersToAddMax_HP -= 300;
                        Play.CharactersToAddMax_MP -= 300;
                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.2;
                        Play.FLD_KetHonLeVat_ThemVaoThuocTinhThach = 0;
                        Play.UpdateMartialArtsAndStatus();
                        Play.CapNhat_HP_MP_SP();
                        num = 21;
                        break;
                    case 17:
                        Play.NhanVatKhoa_Chat = false;
                        break;
                    case 1:
                        num = 12;
                        Play.FLD_TRUDAME_CAMSU = 0.0;
                        Play.UpdateMartialArtsAndStatus();
                        num = 13;
                        break;
                    case 2:
                        num = 14;
                        Play.FLD_TRUDEF_CAMSU = 0.0;
                        Play.UpdateMartialArtsAndStatus();
                        num = 15;
                        break;
                    case 3:
                        num = 16;
                        Play.Poisoning = false;
                        num = 17;
                        break;
                    case 4:
                        Play.NhanVatKhoa_Chat = false;
                        break;
                    case 8:
                        Play.NhanVatKhoa_Chat = false;
                        break;
                    case 19:
                        Play.NhanVatKhoa_Chat = false;
                        break;
                    case 9:
                        num = 18;
                        Play.FLD_TRUDEF_NINJA = 0.0;
                        Play.UpdateMartialArtsAndStatus();
                        num = 19;
                        break;
                    case 13:
                        Play.FLD_WorldBoss_GiamBotCongKich = 0.0;
                        Play.UpdateMartialArtsAndStatus();
                        break;
                }

                num = 22;
                if (Play.TrangThai_BatThuong != null)
                {
                    num = 23;
                    Play.TrangThai_BatThuong.Remove(FLD_PID);
                }

                num = 24;
                StatusEffect(FLD_PID, 0, 0, 0);
                num = 25;
                Dispose();
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(100, num + "|TrangThai_BatThuongClass ThoiGianKetThucSuKien error：[" + FLD_PID + "]" + ex);
        }
        finally
        {
            Dispose();
        }
    }

    public void StatusEffect(int DiThuong_ID, int SwitchOnOff, int DiThuong_SoLuong, int ThoiGian)
    {
        var array = Converter.HexStringToByte(
            "AA5546003527401538008C0300002C0100000900000001000000000000006016A2496016A2492600000014000000000000008C030000E80300000900000001000000000000000000000055AA");
        System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 18, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 58, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(SwitchOnOff), 0, array, 22, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(SwitchOnOff), 0, array, 62, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(ThoiGian), 0, array, 38, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_SoLuong), 0, array, 42, 4);
        if (Play != null)
        {
            System.Buffer.BlockCopy(BitConverter.GetBytes(Play.CharacterFullServerID), 0, array, 14, 2);
            System.Buffer.BlockCopy(BitConverter.GetBytes(Play.CharacterFullServerID), 0, array, 4, 2);
            Play.Client?.Send_Map_Data(array, array.Length);
            Play.SendCurrentRangeBroadcastData(array, array.Length);
        }
        else if (Npc != null)
        {
            System.Buffer.BlockCopy(BitConverter.GetBytes(Npc.FLD_INDEX), 0, array, 14, 2);
            System.Buffer.BlockCopy(BitConverter.GetBytes(Npc.FLD_INDEX), 0, array, 4, 2);
            Npc.QuangBaSoLieu(array, array.Length);
        }
    }
}