using System.Collections.Generic;

namespace RxjhServer;

public class LRUCacheHelper<K, V>
{
    private readonly Dictionary<K, V> _dict;

    private readonly int _max;
    private readonly LinkedList<K> _queue = new();

    private readonly object _syncRoot = new();

    public LRUCacheHelper(int capacity, int int_0)
    {
        _dict = new Dictionary<K, V>(capacity);
        _max = int_0;
    }

    public void Add(K gparam_0, V value)
    {
        lock (_syncRoot)
        {
            checkAndTruncate();
            _queue.AddFirst(gparam_0);
            _dict[gparam_0] = value;
        }
    }

    private void checkAndTruncate()
    {
        lock (_syncRoot)
        {
            var num = 0;
            var num2 = 0;
            var count = _dict.Count;
            if (count >= _max)
            {
                num = count / 10;
                for (num2 = 0; num2 < num; num2++)
                {
                    _dict.Remove(_queue.Last.Value);
                    _queue.RemoveLast();
                }
            }
        }
    }

    public void Delete(K gparam_0)
    {
        lock (_syncRoot)
        {
            _dict.Remove(gparam_0);
            _queue.Remove(gparam_0);
        }
    }

    public V Get(K gparam_0)
    {
        lock (_syncRoot)
        {
            _dict.TryGetValue(gparam_0, out var value);
            _queue.Remove(gparam_0);
            _queue.AddFirst(gparam_0);
            return value;
        }
    }
}