using System;
using System.ComponentModel;
using System.Drawing;
using System.Net;
using System.Windows.Forms;
using RxjhServer.DbClss;
using RxjhServer.Network;

namespace RxjhServer;

public class BinIP : Form
{
    private Button button1;

    private CheckBox checkBox1;

    private CheckBox checkBox2;

    private CheckBox checkBox3;

    private CheckBox checkBox4;

    private CheckBox checkBox5;

    private ColumnHeader columnHeader1;

    private ColumnHeader columnHeader2;

    private ColumnHeader columnHeader3;
    private IContainer components;

    private ContextMenuStrip contextMenuStrip1;

    private ContextMenuStrip contextMenuStrip2;

    private GroupBox groupBox1;

    private GroupBox groupBox2;

    private GroupBox groupBox3;

    private GroupBox groupBox4;

    private GroupBox groupBox5;

    private Label label1;

    private Label label2;

    private Label label3;

    private Label label4;

    private Label label5;

    private Label label6;

    private ListView listView1;

    private ListView listView2;

    private NumericUpDown numericUpDown1;

    private NumericUpDown numericUpDown2;

    private NumericUpDown numericUpDown3;

    private ToolStripMenuItem toolStripMenuItem1;

    private ToolStripMenuItem 删除ToolStripMenuItem;

    public BinIP()
    {
        InitializeComponent();
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        components = new Container();
        contextMenuStrip1 = new ContextMenuStrip(components);
        删除ToolStripMenuItem = new ToolStripMenuItem();
        button1 = new Button();
        groupBox1 = new GroupBox();
        listView2 = new ListView();
        columnHeader3 = new ColumnHeader();
        columnHeader1 = new ColumnHeader();
        contextMenuStrip2 = new ContextMenuStrip(components);
        toolStripMenuItem1 = new ToolStripMenuItem();
        groupBox2 = new GroupBox();
        listView1 = new ListView();
        columnHeader2 = new ColumnHeader();
        groupBox3 = new GroupBox();
        label5 = new Label();
        numericUpDown3 = new NumericUpDown();
        label6 = new Label();
        groupBox5 = new GroupBox();
        label4 = new Label();
        label3 = new Label();
        numericUpDown2 = new NumericUpDown();
        checkBox5 = new CheckBox();
        groupBox4 = new GroupBox();
        checkBox4 = new CheckBox();
        checkBox3 = new CheckBox();
        checkBox2 = new CheckBox();
        checkBox1 = new CheckBox();
        label2 = new Label();
        numericUpDown1 = new NumericUpDown();
        label1 = new Label();
        contextMenuStrip1.SuspendLayout();
        groupBox1.SuspendLayout();
        contextMenuStrip2.SuspendLayout();
        groupBox2.SuspendLayout();
        groupBox3.SuspendLayout();
        ((ISupportInitialize)numericUpDown3).BeginInit();
        groupBox5.SuspendLayout();
        ((ISupportInitialize)numericUpDown2).BeginInit();
        groupBox4.SuspendLayout();
        ((ISupportInitialize)numericUpDown1).BeginInit();
        SuspendLayout();
        contextMenuStrip1.Items.AddRange(new ToolStripItem[1] { 删除ToolStripMenuItem });
        contextMenuStrip1.Name = "contextMenuStrip1";
        contextMenuStrip1.Size = new Size(100, 26);
        删除ToolStripMenuItem.Name = "删除ToolStripMenuItem";
        删除ToolStripMenuItem.Size = new Size(99, 22);
        删除ToolStripMenuItem.Text = "删除";
        删除ToolStripMenuItem.Click += 删除ToolStripMenuItem_Click;
        button1.Location = new Point(79, 269);
        button1.Name = "button1";
        button1.Size = new Size(75, 25);
        button1.TabIndex = 4;
        button1.Text = "Save";
        button1.UseVisualStyleBackColor = true;
        button1.Click += button1_Click;
        groupBox1.Controls.Add(listView2);
        groupBox1.Location = new Point(8, 10);
        groupBox1.Name = "groupBox1";
        groupBox1.Size = new Size(151, 303);
        groupBox1.TabIndex = 6;
        groupBox1.TabStop = false;
        groupBox1.Text = "List Conection";
        listView2.Columns.AddRange(new ColumnHeader[2] { columnHeader3, columnHeader1 });
        listView2.ContextMenuStrip = contextMenuStrip2;
        listView2.Dock = DockStyle.Fill;
        listView2.FullRowSelect = true;
        listView2.GridLines = true;
        listView2.HideSelection = false;
        listView2.Location = new Point(3, 16);
        listView2.Name = "listView2";
        listView2.Size = new Size(145, 284);
        listView2.TabIndex = 4;
        listView2.UseCompatibleStateImageBehavior = false;
        listView2.View = View.Details;
        columnHeader3.Text = "ID";
        columnHeader3.Width = 30;
        columnHeader1.Text = "IP";
        columnHeader1.Width = 150;
        contextMenuStrip2.Items.AddRange(new ToolStripItem[1] { toolStripMenuItem1 });
        contextMenuStrip2.Name = "contextMenuStrip1";
        contextMenuStrip2.Size = new Size(124, 26);
        toolStripMenuItem1.Name = "toolStripMenuItem1";
        toolStripMenuItem1.Size = new Size(123, 22);
        toolStripMenuItem1.Text = "断开连接";
        toolStripMenuItem1.Click += toolStripMenuItem1_Click;
        groupBox2.Controls.Add(listView1);
        groupBox2.Location = new Point(165, 10);
        groupBox2.Name = "groupBox2";
        groupBox2.Size = new Size(156, 303);
        groupBox2.TabIndex = 7;
        groupBox2.TabStop = false;
        groupBox2.Text = "Block";
        listView1.Columns.AddRange(new ColumnHeader[1] { columnHeader2 });
        listView1.ContextMenuStrip = contextMenuStrip1;
        listView1.Dock = DockStyle.Fill;
        listView1.FullRowSelect = true;
        listView1.GridLines = true;
        listView1.HideSelection = false;
        listView1.Location = new Point(3, 16);
        listView1.Name = "listView1";
        listView1.Size = new Size(150, 284);
        listView1.TabIndex = 5;
        listView1.UseCompatibleStateImageBehavior = false;
        listView1.View = View.Details;
        columnHeader2.Text = "IP";
        columnHeader2.Width = 150;
        groupBox3.Controls.Add(label5);
        groupBox3.Controls.Add(numericUpDown3);
        groupBox3.Controls.Add(label6);
        groupBox3.Controls.Add(groupBox5);
        groupBox3.Controls.Add(groupBox4);
        groupBox3.Controls.Add(checkBox1);
        groupBox3.Controls.Add(button1);
        groupBox3.Controls.Add(label2);
        groupBox3.Controls.Add(numericUpDown1);
        groupBox3.Controls.Add(label1);
        groupBox3.Location = new Point(327, 10);
        groupBox3.Name = "groupBox3";
        groupBox3.Size = new Size(162, 303);
        groupBox3.TabIndex = 8;
        groupBox3.TabStop = false;
        groupBox3.Text = "GuardSetting";
        label5.AutoSize = true;
        label5.Location = new Point(137, 70);
        label5.Name = "label5";
        label5.Size = new Size(26, 13);
        label5.TabIndex = 18;
        label5.Text = "Sec";
        numericUpDown3.Location = new Point(65, 66);
        numericUpDown3.Maximum = new decimal(new int[4] { 60000, 0, 0, 0 });
        numericUpDown3.Name = "numericUpDown3";
        numericUpDown3.Size = new Size(60, 20);
        numericUpDown3.TabIndex = 17;
        numericUpDown3.Value = new decimal(new int[4] { 10000, 0, 0, 0 });
        label6.AutoSize = true;
        label6.Location = new Point(6, 70);
        label6.Name = "label6";
        label6.Size = new Size(30, 13);
        label6.TabIndex = 16;
        label6.Text = "Time";
        groupBox5.Controls.Add(label4);
        groupBox5.Controls.Add(label3);
        groupBox5.Controls.Add(numericUpDown2);
        groupBox5.Controls.Add(checkBox5);
        groupBox5.Location = new Point(6, 189);
        groupBox5.Name = "groupBox5";
        groupBox5.Size = new Size(150, 74);
        groupBox5.TabIndex = 15;
        groupBox5.TabStop = false;
        groupBox5.Text = "Block Setting";
        label4.AutoSize = true;
        label4.Location = new Point(108, 44);
        label4.Name = "label4";
        label4.RightToLeft = RightToLeft.No;
        label4.Size = new Size(26, 13);
        label4.TabIndex = 17;
        label4.Text = "Sec";
        label3.AutoSize = true;
        label3.Location = new Point(12, 44);
        label3.Name = "label3";
        label3.Size = new Size(60, 13);
        label3.TabIndex = 16;
        label3.Text = "Time Block";
        numericUpDown2.Location = new Point(67, 40);
        numericUpDown2.Name = "numericUpDown2";
        numericUpDown2.Size = new Size(38, 20);
        numericUpDown2.TabIndex = 15;
        numericUpDown2.Value = new decimal(new int[4] { 10, 0, 0, 0 });
        checkBox5.AutoSize = true;
        checkBox5.Location = new Point(12, 18);
        checkBox5.Name = "checkBox5";
        checkBox5.Size = new Size(94, 17);
        checkBox5.TabIndex = 14;
        checkBox5.Text = "Auto BlockList";
        checkBox5.UseVisualStyleBackColor = true;
        groupBox4.Controls.Add(checkBox4);
        groupBox4.Controls.Add(checkBox3);
        groupBox4.Controls.Add(checkBox2);
        groupBox4.Location = new Point(6, 92);
        groupBox4.Name = "groupBox4";
        groupBox4.Size = new Size(150, 86);
        groupBox4.TabIndex = 7;
        groupBox4.TabStop = false;
        groupBox4.Text = "Attack Setting";
        checkBox4.AutoSize = true;
        checkBox4.Location = new Point(12, 59);
        checkBox4.Name = "checkBox4";
        checkBox4.Size = new Size(107, 17);
        checkBox4.TabIndex = 14;
        checkBox4.Text = "Block Contection";
        checkBox4.UseVisualStyleBackColor = true;
        checkBox3.AutoSize = true;
        checkBox3.BackColor = SystemColors.ControlDark;
        checkBox3.Location = new Point(13, 36);
        checkBox3.Name = "checkBox3";
        checkBox3.Size = new Size(81, 17);
        checkBox3.TabIndex = 13;
        checkBox3.Text = "Check Filler";
        checkBox3.UseVisualStyleBackColor = false;
        checkBox2.AutoSize = true;
        checkBox2.Location = new Point(14, 15);
        checkBox2.Name = "checkBox2";
        checkBox2.Size = new Size(109, 17);
        checkBox2.TabIndex = 12;
        checkBox2.Text = "Closed Conection";
        checkBox2.UseVisualStyleBackColor = true;
        checkBox1.AutoSize = true;
        checkBox1.Location = new Point(11, 18);
        checkBox1.Name = "checkBox1";
        checkBox1.Size = new Size(97, 17);
        checkBox1.TabIndex = 6;
        checkBox1.Text = "Active FireWall";
        checkBox1.UseVisualStyleBackColor = true;
        label2.AutoSize = true;
        label2.Location = new Point(109, 44);
        label2.Name = "label2";
        label2.Size = new Size(34, 13);
        label2.TabIndex = 2;
        label2.Text = "kb/IP";
        numericUpDown1.Location = new Point(65, 40);
        numericUpDown1.Minimum = new decimal(new int[4] { 2, 0, 0, 0 });
        numericUpDown1.Name = "numericUpDown1";
        numericUpDown1.Size = new Size(38, 20);
        numericUpDown1.TabIndex = 1;
        numericUpDown1.Value = new decimal(new int[4] { 5, 0, 0, 0 });
        label1.AutoSize = true;
        label1.Location = new Point(6, 44);
        label1.Name = "label1";
        label1.Size = new Size(51, 13);
        label1.TabIndex = 0;
        label1.Text = "Maximum";
        AutoScaleDimensions = new SizeF(6f, 13f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(493, 317);
        Controls.Add(groupBox3);
        Controls.Add(groupBox2);
        Controls.Add(groupBox1);
        MaximizeBox = false;
        Name = "BinIP";
        StartPosition = FormStartPosition.CenterScreen;
        Text = "CC Setting";
        Load += BinIP_Load;
        contextMenuStrip1.ResumeLayout(false);
        groupBox1.ResumeLayout(false);
        contextMenuStrip2.ResumeLayout(false);
        groupBox2.ResumeLayout(false);
        groupBox3.ResumeLayout(false);
        groupBox3.PerformLayout();
        ((ISupportInitialize)numericUpDown3).EndInit();
        groupBox5.ResumeLayout(false);
        groupBox5.PerformLayout();
        ((ISupportInitialize)numericUpDown2).EndInit();
        groupBox4.ResumeLayout(false);
        groupBox4.PerformLayout();
        ((ISupportInitialize)numericUpDown1).EndInit();
        ResumeLayout(false);
    }

    private void button1_Click(object sender, EventArgs e)
    {
        World.BlockIP = checkBox1.Checked;
        World.MaximumNumberOfConnectionsToTheGameLoginPort = (int)numericUpDown1.Value;
        World.AutomaticConnectionTime = (int)numericUpDown2.Value;
        World.MaximumConnectionTimeOfTheGameLoginPort = (int)numericUpDown3.Value;
        World.AutomaticallyOpenTheConnection = checkBox5.Checked;
        World.Disconnect = checkBox2.Checked;
        World.AddToFilterList = checkBox3.Checked;
        World.CloseTheConnection = checkBox4.Checked;
        Config.IniWriteValue("GameServer", "封IP", checkBox1.Checked.ToString());
        Config.IniWriteValue("GameServer", "自动开启连接", checkBox5.Checked.ToString());
        Config.IniWriteValue("GameServer", "断开连接", checkBox2.Checked.ToString());
        Config.IniWriteValue("GameServer", "加入过滤列表", checkBox3.Checked.ToString());
        Config.IniWriteValue("GameServer", "关闭连接", checkBox4.Checked.ToString());
        Config.IniWriteValue("GameServer", "游戏登陆端口最大连接数", numericUpDown1.Value.ToString());
        Config.IniWriteValue("GameServer", "自动连接时间", numericUpDown2.Value.ToString());
        Config.IniWriteValue("GameServer", "游戏登陆端口最大连接时间数", numericUpDown3.Value.ToString());
        Close();
    }

    private void BinIP_Load(object sender, EventArgs e)
    {
        bind();
        bind2();
        bind3();
    }

    public void bind3()
    {
        checkBox1.Checked = World.BlockIP;
        numericUpDown1.Value = World.MaximumNumberOfConnectionsToTheGameLoginPort;
        numericUpDown2.Value = World.AutomaticConnectionTime;
        numericUpDown3.Value = World.MaximumConnectionTimeOfTheGameLoginPort;
        checkBox5.Checked = World.AutomaticallyOpenTheConnection;
        checkBox2.Checked = World.Disconnect;
        checkBox3.Checked = World.AddToFilterList;
        checkBox4.Checked = World.CloseTheConnection;
    }

    public void bind()
    {
        listView1.Items.Clear();
        foreach (var bip in World.BipList)
            listView1.Items.Insert(listView1.Items.Count, new ListViewItem(new string[1] { bip.ToString() }));
    }

    public void bind2()
    {
        listView2.Items.Clear();
        var enumerator = World.list.Values.GetEnumerator();
        try
        {
            while (enumerator.MoveNext())
            {
                var current = enumerator.Current;
                listView2.Items.Insert(listView2.Items.Count, new ListViewItem(new string[2]
                {
                    current.WorldId.ToString(),
                    current.ToString()
                }));
            }
        }
        finally
        {
            var num = 1;
            while (true)
            {
                switch (num)
                {
                    case 2:
                        break;
                    case 0:
                        enumerator.Dispose();
                        num = 2;
                        continue;
                    default:
                        if (enumerator != null)
                        {
                            num = 0;
                            continue;
                        }

                        break;
                }

                break;
            }
        }
    }

    private void 删除ToolStripMenuItem_Click(object sender, EventArgs e)
    {
        var num = 0;
        if (listView1.SelectedItems.Count > 0)
            for (num = 0; num < listView1.SelectedItems.Count; num++)
            {
                var text = listView1.SelectedItems[num].SubItems[0].Text;
                World.BipList.Remove(IPAddress.Parse(text));
                bind();
            }
    }

    private void toolStripMenuItem1_Click(object sender, EventArgs e)
    {
        var num = 0;
        string text = null;
        NetState value = null;
        if (listView2.SelectedItems.Count <= 0) return;
        for (num = 0; num < listView2.SelectedItems.Count; num++)
        {
            text = listView2.SelectedItems[num].SubItems[0].Text;
            if (World.list.TryGetValue(int.Parse(text), out value)) value.Dispose();
            bind2();
        }
    }
}