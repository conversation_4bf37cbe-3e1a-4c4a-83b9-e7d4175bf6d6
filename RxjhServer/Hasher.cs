using System;
using System.Diagnostics;
using System.Management;
using System.Net;
using System.Net.Sockets;
using System.Runtime.InteropServices;

namespace RxjhServer;

public class Hasher
{
    [DllImport("kernel32")]
    public static extern void GetSystemInfo(ref CPU_INFO cpu_INFO_0);

    public static string GetCpuID()
    {
        try
        {
            var num2 = 0;
            var instances = new ManagementClass("Win32_Processor").GetInstances();
            string result = null;
            var enumerator = instances.GetEnumerator();
            try
            {
                num2 = 0;
                if (enumerator.MoveNext())
                {
                    num2 = 3;
                    result = ((ManagementObject)enumerator.Current).Properties["ProcessorId"].Value.ToString();
                    num2 = 1;
                }

                num2 = 2;
            }
            finally
            {
                num2 = 2;
                while (true)
                {
                    switch (num2)
                    {
                        case 1:
                            break;
                        case 0:
                            ((IDisposable)enumerator).Dispose();
                            num2 = 1;
                            continue;
                        default:
                            if (enumerator != null)
                            {
                                num2 = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            return "CpUIDerror" + ex.Message;
        }
    }

    public static string GetIP()
    {
        var flag = true;
        while (true)
        {
            var addressList = Dns.GetHostEntry(Dns.GetHostName()).AddressList;
            var num = 0;
            while (true)
            {
                IL_0093:
                var flag2 = true;
                while (true)
                {
                    switch (num >= addressList.Length ? 1 : 4)
                    {
                        case 4:
                            if (addressList[num].AddressFamily != AddressFamily.InterNetwork)
                            {
                                num++;
                                goto IL_0093;
                            }

                            goto case 3;
                        case 1:
                            return addressList[0].ToString();
                        case 3:
                            return addressList[num].ToString();
                        case 0:
                            continue;
                        case 2:
                        case 5:
                            goto IL_0093;
                    }

                    break;
                }

                break;
            }
        }
    }

    public static string GetMac()
    {
        var result = string.Empty;
        var networkAdapterConfigurations = new ManagementClass("Win32_NetworkAdapterConfiguration");
        try
        {
            var instances = networkAdapterConfigurations.GetInstances();
            foreach (ManagementObject managementObject in instances)
            {
                if (managementObject == null || managementObject["IPEnabled"] == null ||
                    managementObject["IPEnabled"].ToString() != "True" ||
                    managementObject["MacAddress"] == null) continue;
                result = managementObject["MacAddress"].ToString();
                break;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("Error retrieving MAC address: " + ex.Message);
            return string.Empty;
        }
        finally
        {
            networkAdapterConfigurations?.Dispose();
        }

        return result;
    }

    [DllImport("kernel32.dll")]
    private static extern int GetVolumeInformation(string string_0, string string_1, int int_0, ref int int_1,
        int int_2, int int_3, string string_2, int int_4);

    public static string GetDriveID(string string_0)
    {
        try
        {
            return AtapiDevicea.getHad();
        }
        catch (Exception)
        {
            return "0";
        }
    }

    public static string smethod_0(string string_0)
    {
        var empty = string.Empty;
        try
        {
            var process = new Process();
            process.StartInfo.FileName = "cmd.exe";
            process.StartInfo.UseShellExecute = false;
            process.StartInfo.RedirectStandardInput = true;
            process.StartInfo.RedirectStandardOutput = true;
            process.StartInfo.CreateNoWindow = true;
            process.Start();
            process.StandardInput.WriteLine(string_0);
            process.StandardInput.WriteLine("exit");
            empty = process.StandardOutput.ReadToEnd();
            process.Close();
            return empty;
        }
        catch (Exception ex)
        {
            return "执行DOS命令错误" + ex.Message;
        }
    }

    public struct CPU_INFO
    {
        public uint dwOemId;

        public uint dwPageSize;

        public uint lpMinimumApplicationAddress;

        public uint lpMaximumApplicationAddress;

        public uint dwActiveProcessorMask;

        public uint dwNumberOfProcessors;

        public uint dwProcessorType;

        public uint dwAllocationGranularity;

        public uint dwProcessorLevel;

        public uint dwProcessorRevision;
    }
}