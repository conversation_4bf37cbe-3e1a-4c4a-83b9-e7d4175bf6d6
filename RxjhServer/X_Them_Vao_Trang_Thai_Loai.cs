using System;
using System.Timers;

namespace RxjhServer;

public class X_Them_Vao_Trang_Thai_Loai : IDisposable
{
    public System.Timers.Timer npcyd;

    public Players Play;

    public DateTime time;

    public X_Them_Vao_Trang_Thai_Loai(Players Play_, int ThoiGian, int VatPham_ID, int FLD_RESIDE1)
    {
        FLD_PID = VatPham_ID;
        this.FLD_RESIDE1 = FLD_RESIDE1;
        time = DateTime.Now;
        time = time.AddMilliseconds(ThoiGian);
        Play = Play_;
        npcyd = new System.Timers.Timer(ThoiGian);
        npcyd.Elapsed += ThoiGianKetThucSuKien2;
        npcyd.Enabled = true;
        npcyd.AutoReset = false;
    }

    public X_Them_Vao_Trang_Thai_Loai(Players Play_, double ThoiGian, double VatPham_ID, double FLD_RESIDE1)
    {
        FLD_PID = (int)VatPham_ID;
        this.FLD_RESIDE1 = (int)FLD_RESIDE1;
        time = DateTime.Now;
        time = time.AddMilliseconds(ThoiGian);
        Play = Play_;
        npcyd = new System.Timers.Timer(ThoiGian);
        npcyd.Elapsed += ThoiGianKetThucSuKien3;
        npcyd.Enabled = true;
        npcyd.AutoReset = false;
    }

    public X_Them_Vao_Trang_Thai_Loai(Players Play_, int ThoiGian, int VatPham_ID, int FLD_RESIDE1, double vale)
    {
        FLD_PID = VatPham_ID;
        this.FLD_RESIDE1 = FLD_RESIDE1;
        this.vale = vale;
        time = DateTime.Now;
        time = time.AddMilliseconds(ThoiGian);
        Play = Play_;
        npcyd = new System.Timers.Timer(ThoiGian);
        npcyd.Elapsed += ThoiGianKetThucSuKien2;
        npcyd.Enabled = true;
        npcyd.AutoReset = false;
    }

    private double vale { get; }

    public int FLD_PID { get; set; }

    public int FLD_RESIDE1 { get; set; }

    public int FLD_sj => getsj();

    public void Dispose()
    {
        if (npcyd != null)
        {
            npcyd.Enabled = false;
            npcyd.Close();
            npcyd.Dispose();
            npcyd = null;
        }

        if (Play != null) Play = null;
    }

    ~X_Them_Vao_Trang_Thai_Loai()
    {
        if (npcyd != null)
        {
            npcyd.Enabled = false;
            npcyd.Close();
            npcyd.Dispose();
            npcyd = null;
        }
    }

    public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
    {
        ThoiGianKetThucSuKien();
    }

    public void ThoiGianKetThucSuKien3(object sender, ElapsedEventArgs e)
    {
        TimeEndEvent1();
    }

    public void TimeEndEvent1()
    {
        var num2 = 0;
        if (npcyd != null)
        {
            npcyd.Enabled = false;
            npcyd.Close();
            npcyd.Dispose();
            npcyd = null;
        }

        num2 = 25;
        if (Play != null)
        {
            if (!Play.Exiting && Play.Client.Running)
                try
                {
                    if (Play.GetAddState(FLD_PID)) Play.AppendStatusList.Remove(FLD_PID);
                    num2 = 10;
                    Dispose();
                    return;
                }
                catch (Exception ex)
                {
                    var array = new string[6]
                    {
                        "追加武功状态类   时间结束事件   出错：",
                        num2.ToString(),
                        "[",
                        FLD_PID.ToString(),
                        "]",
                        null
                    };
                    array[5] = ex?.ToString();
                    Form1.WriteLine(1, string.Concat(array));
                    return;
                }
                finally
                {
                    Dispose();
                }

            Play.AppendStatusList?.Clear();
            Dispose();
        }
        else
        {
            Dispose();
        }
    }

    public void ThoiGianKetThucSuKien(int mergepill = 0)
    {
        if (npcyd != null)
        {
            npcyd.Enabled = false;
            npcyd.Close();
            npcyd.Dispose();
            npcyd = null;
        }

        var num = 25;
        if (Play == null)
        {
            Dispose();
        }
        else if (!Play.Exiting && Play.Client.Running)
        {
            switch (mergepill)
            {
                case 1:
                    Play.AppendStatusList?.RemoveSafe(FLD_PID);
                    Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
                    Dispose();
                    break;
                case 0:
                    try
                    {
                        if (World.List_Pill.TryGetValue(FLD_PID, out var del))
                        {
                            Play.Del_Power_Character(del.Bonus_ATK, del.Bonus_AtkPercent, del.Bonus_DF,
                                del.Bonus_DfPercent, del.Bonus_HP, del.Bonus_HpPercent, del.Bonus_MP,
                                del.Bonus_MpPercent, del.Bonus_Accuracy, del.Bonus_AccuPercent, del.Bonus_Evasion,
                                del.Bonus_EvaPercent, del.Bonus_AtkSkillPercent, del.Bonus_DfSkill,
                                del.Bonus_DfSkillPercent, del.Bonus_ExpPercent, del.Bonus_GoldPercent,
                                del.Bonus_DropPercent, del.Bonus_Abilities, del.Upgrade_Weapon, del.Upgrade_Armor,
                                del.Bonus_Lucky, del.Bonus_DiemHoangKim);
                            if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                            Play.UpdateMartialArtsAndStatus();
                            Play.CalculateCharacterEquipmentData();
                            Play.UpdateBroadcastCharacterData();
                            Play.UpdateCharacterData(Play);
                            Play.CapNhat_HP_MP_SP();
                        }
                        else if (World.PartyBonus_IconPID != 0 && FLD_PID == World.PartyBonus_IconPID)
                        {
                            Play.PartyBonusExp = 0.0;
                            Play.UpdateMartialArtsAndStatus();
                            Play.CapNhat_HP_MP_SP();
                        }
                        else
                        {
                            switch (FLD_PID)
                            {
                                case 1600101:
                                case 1600102:
                                case 1600103:
                                case 1600104:
                                    Play.BienHinh_ID = 0;
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case **********:
                                case **********:
                                case **********:
                                    return;
                                case 700904:
                                    Play.Player_VoDich = false;
                                    break;
                                case 1008000194:
                                    Play.CharactersToAddMax_HP -= 1000;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.15);
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.15);
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.4;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
                                    Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.1;
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 900000047:
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 100;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 100;
                                    Play.CharactersToAddMax_HP -= 500;
                                    Play.FLD_NhanVat_ThemVao_KhiCong--;
                                    break;
                                case 900000046:
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 60;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 60;
                                    Play.CharactersToAddMax_HP -= 300;
                                    break;
                                case 1000000861:
                                    Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= 0.05;
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 242:
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 15;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 15;
                                    Play.CharactersToAddMax_HP -= 300;
                                    Play.CharactersToAddMax_MP -= 300;
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.2;
                                    Play.FLD_KetHonLeVat_ThemVaoThuocTinhThach = 0;
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 201201:
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.05);
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 301201:
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.1);
                                    Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= 0.05;
                                    if (Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat < 0.0)
                                        Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat = 0.0;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 401202:
                                    Play.FLD_NhanVat_ThemVao_NeTranh += 20;
                                    Play.FLD_NhanVat_ThemVao_TrungDich -= 40;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 401203:
                                    Play.FLD_NhanVat_ThemVao_TrungDich += 20;
                                    Play.FLD_NhanVat_ThemVao_NeTranh -= 40;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 401301:
                                    Play.FLD_LucCongKichVoCongGiaTang -= 10.0;
                                    if (Play.FLD_LucCongKichVoCongGiaTang < 0.0)
                                        Play.FLD_LucCongKichVoCongGiaTang = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 401302:
                                    Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.1;
                                    if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 401303:
                                    switch (Play.Player_Job)
                                    {
                                        case 1:
                                            Play.ChanVu_TuyetKich -= 10.0;
                                            if (Play.ChanVu_TuyetKich < 0.0) Play.ChanVu_TuyetKich = 0.0;
                                            break;
                                        case 2:
                                            Play.KIEM_NoHai_CuongLan -= 10.0;
                                            if (Play.KIEM_NoHai_CuongLan < 0.0) Play.KIEM_NoHai_CuongLan = 0.0;
                                            break;
                                        case 3:
                                            Play.THUONG_NoYChiHong -= 10.0;
                                            if (Play.THUONG_NoYChiHong < 0.0) Play.THUONG_NoYChiHong = 0.0;
                                            break;
                                        case 4:
                                            Play.CUNG_DocBaGiangHo_140 -= 10.0;
                                            if (Play.CUNG_DocBaGiangHo_140 < 0.0) Play.CUNG_DocBaGiangHo_140 = 0.0;
                                            break;
                                        case 5:
                                            Play.ChanVu_TuyetKich -= 10.0;
                                            if (Play.ChanVu_TuyetKich < 0.0) Play.ChanVu_TuyetKich = 0.0;
                                            break;
                                        case 6:
                                            Play.NINJA_TamThanNgungTu -= 10.0;
                                            if (Play.NINJA_TamThanNgungTu < 0.0) Play.NINJA_TamThanNgungTu = 0.0;
                                            break;
                                        case 7:
                                            Play.CAMSU_ThangThien_2_KhiCong_TamDamAnhNguyet -= 10.0;
                                            if (Play.CAMSU_ThangThien_2_KhiCong_TamDamAnhNguyet < 0.0)
                                                Play.CAMSU_ThangThien_2_KhiCong_TamDamAnhNguyet = 0.0;
                                            break;
                                        case 8:
                                            Play.ChanVu_TuyetKich -= 10.0;
                                            if (Play.ChanVu_TuyetKich < 0.0) Play.ChanVu_TuyetKich = 0.0;
                                            break;
                                        case 9:
                                            Play.DamHoaLien_NoHai_CuongLan -= 10.0;
                                            if (Play.DamHoaLien_NoHai_CuongLan < 0.0)
                                                Play.DamHoaLien_NoHai_CuongLan = 0.0;
                                            break;
                                        case 12:
                                            Play.ChanVu_TuyetKich -= 10.0;
                                            if (Play.ChanVu_TuyetKich < 0.0) Play.ChanVu_TuyetKich = 0.0;
                                            break;
                                    }

                                    break;
                                case 401401:
                                    Play.FLD_ThemVaoTiLePhanTram_TrungDich -= 0.4;
                                    Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= 0.2;
                                    if (Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat < 0.0)
                                        Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat = 0.0;
                                    if (Play.FLD_ThemVaoTiLePhanTram_TrungDich < 0.0)
                                        Play.FLD_ThemVaoTiLePhanTram_TrungDich = 0.0;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 501301:
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(vale);
                                    Play.AppendStatusList.RemoveSafe(501301);
                                    Play.AppendStatusList.RemoveSafe(501501);
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 501302:
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(vale);
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 501303:
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(vale);
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 501401:
                                    Play.FLD_ThemVaoTiLePhanTram_TrungDich -= vale;
                                    if (Play.FLD_ThemVaoTiLePhanTram_TrungDich < 0.0)
                                        Play.FLD_ThemVaoTiLePhanTram_TrungDich = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 501402:
                                    Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh -= vale;
                                    if (Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh < 0.0)
                                        Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 501403:
                                    Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= vale;
                                    if (Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat < 0.0)
                                        Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat = 0.0;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 501501:
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(vale);
                                    Play.AppendStatusList.RemoveSafe(501501);
                                    Play.AppendStatusList.RemoveSafe(501301);
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 501502:
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(vale);
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 501601:
                                    Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= vale;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 501602:
                                    Play.FLD_ThemVaoTiLePhanTram_TrungDich -= vale;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 501603:
                                    Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh -= vale;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 601101:
                                    Play.WalkingState(BitConverter.GetBytes(FLD_PID), 1);
                                    Play.WalkingStatusId = 1;
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    Play.UpdateMovementSpeed();
                                    Dispose();
                                    return;
                                case 601102:
                                    Play.WalkingState(BitConverter.GetBytes(FLD_PID), 1);
                                    Play.WalkingStatusId = 1;
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    Play.UpdateMovementSpeed();
                                    Dispose();
                                    return;
                                case 601103:
                                    Play.WalkingState(BitConverter.GetBytes(FLD_PID), 1);
                                    Play.WalkingStatusId = 1;
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    Play.UpdateMovementSpeed();
                                    Dispose();
                                    return;
                                case 601201:
                                    Play.FLD_PhuThe_HoTro_ThemVao_DoPhongNguThuocTinh = 0;
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 601202:
                                    Play.FLD_PhuThe_HoTro_ThemVao_VuKhiThuocTinh = 0;
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 700313:
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    Play.HNCP_ThemVao_TiLePhanTram_PN = 0.0;
                                    Play.HNCP_ThemVao_TiLePhanTram_TC = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 700014:
                                    Play.NoKhi = false;
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    if (Play.Player_Job == 10)
                                    {
                                        Play.QuyenSu_HoiTamNhatKich_UyLuc = 0.3;
                                    }
                                    else if (Play.Player_Job == 11)
                                    {
                                        Play.CharacterIsBasicallyTheLargest_Barrier =
                                            (int)(Play.CharacterIsBasicallyTheLargest_Barrier / 1.2 + 0.5);
                                        Play.MaiLieuChan_ChuongLucVanDung -= vale;
                                        Play.CapNhat_HP_MP_SP();
                                    }

                                    Play.CPVP_ThemVao_TiLePhanTram_TC = 0.0;
                                    Play.CPVP_ThemVao_TiLePhanTram_PN = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 700201:
                                    Play.CharacterBeast.FLD_ThemVaoTiLePhanTram_NeTranh -= 0.1;
                                    Play.CharacterBeast.FLD_ThemVaoTiLePhanTram_TrungDich -= 0.1;
                                    Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    Play.UpdateSpiritBeastMartialArtsAndStatus();
                                    break;
                                case 700202:
                                    Play.CharacterBeast.dllFLD_ThemVaoTiLePhanTram_Attack(0.1);
                                    Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    Play.UpdateSpiritBeastMartialArtsAndStatus();
                                    break;
                                case 700203:
                                    Play.CharacterBeast.dllFLD_ThemVaoTiLePhanTram_PhongNgu(0.1);
                                    Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    Play.UpdateSpiritBeastMartialArtsAndStatus();
                                    break;
                                case 700301:
                                    Play.CharacterBeast.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= 0.1;
                                    Play.CharacterBeast.FLD_ThemVaoTiLePhanTram_MPCaoNhat -= 0.1;
                                    Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    Play.UpdateSpiritBeastHP_MP_SP();
                                    break;
                                case 700302:
                                    Play.CharacterBeast.FLD_SpiritBeast_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
                                    Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    break;
                                case 700303:
                                    Play.CharacterBeast.FLD_SpiritBeast_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.1;
                                    Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    break;
                                case 700310:
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.1);
                                    Play.FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram -= 30.0;
                                    if (Play.FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    Dispose();
                                    return;
                                case 700090:
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram -= 0.2;
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.2;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    Dispose();
                                    return;
                                case 700350:
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.FLD_DEF_HOANVU_LOANHOA = 0.0;
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    Dispose();
                                    return;
                                case 700401:
                                    Play.WalkingStateCharacterSpiritBeast(BitConverter.GetBytes(FLD_PID), 1);
                                    Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 300000);
                                    break;
                                case 700402:
                                    Play.WalkingStateCharacterSpiritBeast(BitConverter.GetBytes(FLD_PID), 1);
                                    Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 300000);
                                    break;
                                case 700403:
                                    Play.WalkingStateCharacterSpiritBeast(BitConverter.GetBytes(FLD_PID), 1);
                                    Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 300000);
                                    break;
                                case 801201:
                                    Play.FLD_CongKichTocDo = 100;
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.Update_CongKichTocDo();
                                    break;
                                case 801302:
                                    Play.addFLD_ThemVaoTiLePhanTram_PhongNgu(0.2);
                                    Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh -= 1.0;
                                    if (Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh < 0.0)
                                        Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.Update_CongKichTocDo();
                                    break;
                                case 900401:
                                    Play.CamSu_TrangThai = 0;
                                    Play.CPVP_ThemVao_TiLePhanTram_PN = 0.0;
                                    Play.CPVP_ThemVao_TiLePhanTram_TC = 0.0;
                                    Play.AppendStatusList.Remove(900401);
                                    Play.UpdateCharacterData(Play);
                                    Play.StatusEffect(BitConverter.GetBytes(900401), 0, 0);
                                    Play.UpdateMartialArtsAndStatus();
                                    Dispose();
                                    return;
                                case 900402:
                                    Play.CamSu_TrangThai = 0;
                                    Play.CPVP_ThemVao_TiLePhanTram_PN = 0.0;
                                    Play.CPVP_ThemVao_TiLePhanTram_TC = 0.0;
                                    Play.AppendStatusList.Remove(900402);
                                    Play.UpdateCharacterData(Play);
                                    Play.StatusEffect(BitConverter.GetBytes(900402), 0, 0);
                                    Play.UpdateMartialArtsAndStatus();
                                    Dispose();
                                    return;
                                case 900403:
                                    Play.CamSu_TrangThai = 0;
                                    Play.CPVP_ThemVao_TiLePhanTram_PN = 0.0;
                                    Play.CPVP_ThemVao_TiLePhanTram_TC = 0.0;
                                    Play.AppendStatusList.Remove(900403);
                                    Play.UpdateCharacterData(Play);
                                    Play.StatusEffect(BitConverter.GetBytes(900403), 0, 0);
                                    Play.UpdateMartialArtsAndStatus();
                                    Dispose();
                                    return;
                                case 1001101:
                                    Play.WalkingState(BitConverter.GetBytes(FLD_PID), 1);
                                    Play.WalkingStatusId = 1;
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    Play.UpdateMovementSpeed();
                                    Dispose();
                                    return;
                                case 1001102:
                                    Play.WalkingState(BitConverter.GetBytes(FLD_PID), 1);
                                    Play.WalkingStatusId = 1;
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    Play.UpdateMovementSpeed();
                                    Dispose();
                                    return;
                                case 1001201:
                                    if (Play.GetAddState(1001101))
                                    {
                                        Play.WalkingStatusId = 6;
                                        Play.WalkingState(BitConverter.GetBytes(1001101), 6);
                                        Play.TocDoDiChuyen_Max = 78f;
                                    }
                                    else if (Play.GetAddState(1001102))
                                    {
                                        Play.WalkingStatusId = 7;
                                        Play.WalkingState(BitConverter.GetBytes(1001102), 7);
                                        Play.TocDoDiChuyen_Max = 90f;
                                    }
                                    else
                                    {
                                        Play.WalkingStatusId = 1;
                                        Play.WalkingState(BitConverter.GetBytes(1001101), 1);
                                        Play.TocDoDiChuyen_Max = 50f;
                                        Play.UpdateMovementSpeed();
                                    }

                                    Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    break;
                                case 1001202:
                                    if (Play.GetAddState(1001101))
                                    {
                                        Play.WalkingStatusId = 6;
                                        Play.WalkingState(BitConverter.GetBytes(1001101), 6);
                                        Play.TocDoDiChuyen_Max = 78f;
                                    }
                                    else if (Play.GetAddState(1001102))
                                    {
                                        Play.WalkingStatusId = 7;
                                        Play.WalkingState(BitConverter.GetBytes(1001102), 7);
                                        Play.TocDoDiChuyen_Max = 90f;
                                    }
                                    else
                                    {
                                        Play.WalkingStatusId = 1;
                                        Play.WalkingState(BitConverter.GetBytes(1001101), 1);
                                        Play.TocDoDiChuyen_Max = 50f;
                                    }

                                    Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    break;
                                case 1001301:
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1001302:
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1001303:
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.15;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 2001301:
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.03);
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 9000085:
                                case 9000120:
                                case 9000121:
                                case 9000165:
                                case 9000166:
                                case 9000317:
                                case 9001207:
                                case 9001211:
                                case 9001215:
                                case 9009076:
                                case 9009150:
                                case 9009217:
                                case 9009309:
                                case 9009839:
                                case 9009941:
                                case 9009943:
                                case 9009965:
                                case 9009966:
                                case 9009970:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.2;
                                    break;
                                case 9000088:
                                case 9000104:
                                case 9000105:
                                case 9000144:
                                case 9000145:
                                case 9000168:
                                case 9001214:
                                case 9009077:
                                case 9009092:
                                case 9009151:
                                case 9009788:
                                case 9009789:
                                case 9009790:
                                case 9009820:
                                case 9009964:
                                    Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem -= 0.5;
                                    break;
                                case 1008002585:
                                    Play.Mobile(420f, 1550f, 15f, 101);
                                    break;
                                case 900000072:
                                case 900000073:
                                case 900000074:
                                case *********:
                                case *********:
                                case *********:
                                case *********:
                                    if (Play.QueryThienQuanDiaDoMap(Play.NhanVatToaDo_BanDo))
                                        Play.Mobile(529f, 1528f, 15f, 101);
                                    break;
                                case *********:
                                    Play.HeThongNhacNho("Kết thúc Safe PK, hành tẩu giang hồ hãy cẩn thận!", 10);
                                    Play.SafeMode = 0;
                                    break;
                                case 1000000030:
                                    Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem -= 1.0;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem = 0.0;
                                    break;
                                case 1000000167:
                                case 1000000168:
                                case 1000000170:
                                case 1000000171:
                                case 1000000173:
                                case 1000000174:
                                case 1000000176:
                                case 1000000177:
                                    if (Play.CharacterBeast == null) return;
                                    if (FLD_PID == 1000000167 || FLD_PID == 1000000170 || FLD_PID == 1000000173 ||
                                        FLD_PID == 1000000176)
                                    {
                                        Play.CharacterBeast.FLD_VatPham_ThemVao_HP -= 100;
                                        Play.CharacterBeast.FLD_VatPham_ThemVao_MP -= 90;
                                        if (Play.CharacterBeast.FLD_HP > Play.CharacterBeast.灵兽基本最大_HP)
                                            Play.CharacterBeast.FLD_HP = Play.CharacterBeast.灵兽基本最大_HP;
                                        if (Play.CharacterBeast.FLD_MP > Play.CharacterBeast.灵兽基本最大_MP)
                                            Play.CharacterBeast.FLD_MP = Play.CharacterBeast.灵兽基本最大_MP;
                                    }
                                    else
                                    {
                                        Play.CharacterBeast.FLD_VatPham_ThemVao_HP -= 200;
                                        Play.CharacterBeast.FLD_VatPham_ThemVao_MP -= 100;
                                        if (Play.CharacterBeast.FLD_HP > Play.CharacterBeast.灵兽基本最大_HP)
                                            Play.CharacterBeast.FLD_HP = Play.CharacterBeast.灵兽基本最大_HP;
                                        if (Play.CharacterBeast.FLD_MP > Play.CharacterBeast.灵兽基本最大_MP)
                                            Play.CharacterBeast.FLD_MP = Play.CharacterBeast.灵兽基本最大_MP;
                                    }

                                    Play.StateEffectCharacterBeast(BitConverter.GetBytes(FLD_PID), 0, 0);
                                    Play.UpdateSpiritBeastHP_MP_SP();
                                    Play.AppendStatusList?.Remove(FLD_PID);
                                    Dispose();
                                    return;
                                case 1000000408:
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.1);
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1000000409:
                                    Play.CharactersToAddMax_HP -= 500;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 1000000410:
                                    Play.CharactersToAddMax_MP -= 500;
                                    if (Play.NhanVat_MP > Play.CharacterMax_MP) Play.NhanVat_MP = Play.CharacterMax_MP;
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 1000000411:
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.1);
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1000000412:
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.05);
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1000000413:
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1000000414:
                                    Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh -= 0.05;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1000000775:
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 20;
                                    if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0) Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1000000776:
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
                                    Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.1;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1000000830:
                                    Play.CharactersToAddMax_HP -= 100;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 1000000831:
                                    Play.CharactersToAddMax_HP -= 50;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 1000000832:
                                    Play.CharactersToAddMax_HP -= 100;
                                    Play.CharactersToAddMax_HP -= 100;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    if (Play.NhanVat_MP > Play.CharacterMax_MP) Play.NhanVat_MP = Play.CharacterMax_MP;
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 1000000835:
                                    Play.FLD_ThemVaoTiLePhanTram_MPCaoNhat -= 0.05;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 1000000836:
                                    Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= 0.05;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 1000000891:
                                case 1000000892:
                                case 1000000893:
                                case 1008001382:
                                case 1008001383:
                                case 1008001384:
                                case 1008001385:
                                    switch (Play.FLD_loveDegreeLevel)
                                    {
                                        case 1:
                                            Play.CharactersToAddMax_HP -= 150;
                                            Play.FLD_NhanVat_ThemVao_CongKich -= 15;
                                            Play.FLD_NhanVat_ThemVao_PhongNgu -= 15;
                                            Play.FLD_NhanVat_ThemVao_KhiCong--;
                                            Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.05;
                                            Play.UpdateKhiCong();
                                            break;
                                        case 2:
                                            Play.CharactersToAddMax_HP -= 150;
                                            Play.FLD_NhanVat_ThemVao_CongKich -= 15;
                                            Play.FLD_NhanVat_ThemVao_PhongNgu -= 15;
                                            Play.FLD_NhanVat_ThemVao_KhiCong--;
                                            Play.UpdateKhiCong();
                                            break;
                                        case 3:
                                            Play.CharactersToAddMax_HP -= 150;
                                            Play.FLD_NhanVat_ThemVao_CongKich -= 15;
                                            Play.FLD_NhanVat_ThemVao_PhongNgu -= 15;
                                            break;
                                        case 4:
                                            Play.CharactersToAddMax_HP -= 150;
                                            Play.FLD_NhanVat_ThemVao_CongKich -= 10;
                                            Play.FLD_NhanVat_ThemVao_PhongNgu -= 10;
                                            break;
                                        case 5:
                                            Play.CharactersToAddMax_HP -= 150;
                                            Play.FLD_NhanVat_ThemVao_CongKich -= 10;
                                            Play.FLD_NhanVat_ThemVao_PhongNgu -= 5;
                                            break;
                                        case 6:
                                            Play.CharactersToAddMax_HP -= 150;
                                            Play.FLD_NhanVat_ThemVao_CongKich -= 5;
                                            Play.FLD_NhanVat_ThemVao_PhongNgu -= 5;
                                            break;
                                        case 7:
                                            Play.CharactersToAddMax_HP -= 150;
                                            Play.FLD_NhanVat_ThemVao_CongKich -= 5;
                                            break;
                                        case 8:
                                            Play.CharactersToAddMax_HP -= 150;
                                            break;
                                        case 9:
                                            Play.CharactersToAddMax_HP -= 100;
                                            break;
                                        case 10:
                                            Play.CharactersToAddMax_HP -= 50;
                                            break;
                                    }

                                    Play.UpdateMartialArtsAndStatus();
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 1007000005:
                                    Play.CharactersToAddMax_HP -= 300;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 1007000006:
                                    Play.CharactersToAddMax_HP -= 500;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 1007000007:
                                    Play.CharactersToAddMax_HP -= 700;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 1008000016:
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.1);
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1008000017:
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.1);
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1008000018:
                                    Play.delFLD_TrangBi_ThemVao_VuKhi_CuongHoa(2);
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    num = 1;
                                    Play.CalculateCharacterEquipmentData();
                                    num = 2;
                                    Play.UpdateMartialArtsAndStatus();
                                    num = 3;
                                    break;
                                case 1008000019:
                                    num = 4;
                                    Play.delFLD_TrangBi_ThemVao_DoPhongNgu_CuongHoa(1);
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    num = 5;
                                    Play.CalculateCharacterEquipmentData();
                                    num = 6;
                                    Play.UpdateMartialArtsAndStatus();
                                    num = 7;
                                    break;
                                case 1008000053:
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1008000055:
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.1);
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.1);
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    break;
                                case 1008000054:
                                    Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.1;
                                    if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    break;
                                case 1008000220:
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    break;
                                case 1008000219:
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    break;
                                case 999000585:
                                    Play.FLD_NhanVat_ThemVao_KhiCong--;
                                    if (Play.FLD_NhanVat_ThemVao_KhiCong < 0) Play.FLD_NhanVat_ThemVao_KhiCong = 0;
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    break;
                                case 1000000949:
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 50;
                                    if (Play.FLD_NhanVat_ThemVao_CongKich < 0) Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    break;
                                case 1000000950:
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 50;
                                    if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0) Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    break;
                                case 1008000095:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.2;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    break;
                                case 1008000096:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.3;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    break;
                                case 1008000097:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.4;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    break;
                                case 1008000156:
                                    Play.AppendStatusList.Remove(1008000156);
                                    Play.CharactersToAddMax_HP -= 300;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 1008000183:
                                    Play.AppendStatusList.Remove(1008000183);
                                    Play.CharactersToAddMax_HP -= 500;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.1);
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.2;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    Dispose();
                                    return;
                                case 1008000187:
                                    Play.AppendStatusList.Remove(1008000187);
                                    Play.CharactersToAddMax_HP -= 300;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.FLD_NhanVat_ThemVao_KhiCong--;
                                    if (Play.FLD_NhanVat_ThemVao_KhiCong < 0) Play.FLD_NhanVat_ThemVao_KhiCong = 0;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.UpdateKhiCong();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Dispose();
                                    return;
                                case 1000000920:
                                    Play.AppendStatusList.Remove(1000000920);
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 1.0;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 1008001304:
                                    Play.AppendStatusList.Remove(1008001304);
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.15);
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.15);
                                    Play.CharactersToAddMax_HP -= 300;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.CharactersToAddMax_MP -= 300;
                                    if (Play.CharactersToAddMax_MP < 0) Play.CharactersToAddMax_MP = 0;
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.1;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 1008000188:
                                    Play.AppendStatusList.Remove(1008000188);
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.15);
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.15);
                                    Play.CharactersToAddMax_HP -= 300;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.CharactersToAddMax_MP -= 300;
                                    if (Play.CharactersToAddMax_MP < 0) Play.CharactersToAddMax_MP = 0;
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.1;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 1000000404:
                                    Play.AppendStatusList.Remove(1000000404);
                                    Play.FLD_NhanVat_ThemVao_KhiCong -= 2;
                                    Play.CharactersToAddMax_HP -= 200;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 1000000403:
                                    Play.AppendStatusList.Remove(1000000403);
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.15);
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.15);
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 1000000402:
                                    Play.AppendStatusList.Remove(1000000402);
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.15;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.FLD_NhanVat_ThemVao_LucPhongNguVoCong -= 60.0;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case *********:
                                    Play.AppendStatusList.Remove(*********);
                                    Play.FLD_Event_Exp -= 1.0;
                                    if (Play.FLD_Event_Exp < 0.0) Play.FLD_Event_Exp = 0.0;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Play.StatusEffect(BitConverter.GetBytes(*********), 0, 0);
                                    Play.HeThongNhacNho(
                                        "Tăng " + Play.FLD_Event_Exp * 100.0 + "% kinh nghiệm đã kết thúc", 10,
                                        "Sự kiện");
                                    Dispose();
                                    return;
                                case 1008000213:
                                    Play.AppendStatusList.Remove(1008000213);
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.2);
                                    Play.FLD_NhanVat_ThemVao_NeTranh -= 20;
                                    Play.FLD_NhanVat_ThemVao_TrungDich -= 20;
                                    Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.1;
                                    if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
                                    Play.CharactersToAddMax_HP -= 500;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.2;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 1008000212:
                                    Play.AppendStatusList.Remove(1008000212);
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.2);
                                    Play.FLD_NhanVat_ThemVao_NeTranh -= 20;
                                    Play.FLD_NhanVat_ThemVao_TrungDich -= 20;
                                    Play.FLD_NhanVat_ThemVao_KhiCong -= 2;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.15;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateKhiCong();
                                    Dispose();
                                    return;
                                case 700500:
                                    Play.AppendStatusList.Remove(700500);
                                    Play.DEl_DEF_TLC(0.0);
                                    Play.DEl_ATT_TLC(0.0);
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 700501:
                                    Play.AppendStatusList.Remove(700501);
                                    Play.DEl_DEF_TLC(0.0);
                                    Play.DEl_ATT_TLC(0.0);
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 1008001190:
                                    Play.AppendStatusList.Remove(1008001190);
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 1008000197:
                                    Play.AppendStatusList.Remove(1008000197);
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.1);
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.15);
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.1;
                                    if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
                                    Play.CharactersToAddMax_HP -= 700;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 1008000195:
                                    Play.AppendStatusList.Remove(1008000195);
                                    Play.CharactersToAddMax_HP -= 300;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.1);
                                    if (Play.FLD_NhanVat_ThemVao_KhiCong < 0) Play.FLD_NhanVat_ThemVao_KhiCong = 0;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.UpdateKhiCong();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 1008000144:
                                    Play.AppendStatusList.Remove(1008000144);
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.05);
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    Dispose();
                                    return;
                                case 1008000143:
                                    Play.AppendStatusList.Remove(1008000143);
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.03);
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    Dispose();
                                    return;
                                case 1008000169:
                                    Play.AppendStatusList.Remove(1008000169);
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.11);
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.13);
                                    Play.CharactersToAddMax_HP -= 500;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.13;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.11;
                                    if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    Dispose();
                                    return;
                                case 1000000010:
                                    Play.AppendStatusList.Remove(1000000010);
                                    Play.CharactersToAddMax_HP -= 300;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 100;
                                    if (Play.FLD_NhanVat_ThemVao_CongKich < 0) Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 50;
                                    if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0) Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.07;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    Dispose();
                                    return;
                                case 1000000626:
                                    Play.AppendStatusList.Remove(1000000626);
                                    Play.CharactersToAddMax_HP -= 300;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 25;
                                    if (Play.FLD_NhanVat_ThemVao_CongKich < 0) Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 30;
                                    if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0) Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    Dispose();
                                    return;
                                case 1000000627:
                                    Play.AppendStatusList.Remove(1000000626);
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.15);
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.15);
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    Dispose();
                                    return;
                                case 1008000232:
                                    Play.AppendStatusList.Remove(1008000232);
                                    Play.CharactersToAddMax_HP -= 100;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.2;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram -= 0.4;
                                    if (Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram = 0.0;
                                    Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram -= 0.2;
                                    if (Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram = 0.0;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 1008000239:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 1.0;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    break;
                                case 1008000240:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.05;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 30;
                                    if (Play.FLD_NhanVat_ThemVao_CongKich < 0) Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.YeuHoaThanhThao = false;
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008002410:
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 150;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 150;
                                    Play.CharactersToAddMax_HP -= 750;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.CharactersToAddMax_MP -= 750;
                                    if (Play.CharactersToAddMax_MP < 0) Play.CharactersToAddMax_MP = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.FLD_NhanVat_ThemVao_NeTranh -= (int)(Play.FLD_NeTranh * 0.05);
                                    if (Play.FLD_NhanVat_ThemVao_NeTranh < 0) Play.FLD_NhanVat_ThemVao_NeTranh = 0;
                                    Play.YeuHoaThanhThao = false;
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    break;
                                case 1008002385:
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 100;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 100;
                                    Play.CharactersToAddMax_HP -= 800;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.CharactersToAddMax_MP -= 800;
                                    if (Play.CharactersToAddMax_MP < 0) Play.CharactersToAddMax_MP = 0;
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.4;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.YeuHoaThanhThao = false;
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    break;
                                case 1008002386:
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 100;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 100;
                                    Play.CharactersToAddMax_HP -= 800;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.CharactersToAddMax_MP -= 800;
                                    if (Play.CharactersToAddMax_MP < 0) Play.CharactersToAddMax_MP = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.FLD_NhanVat_ThemVao_NeTranh -= (int)(Play.FLD_NeTranh * 0.05);
                                    if (Play.FLD_NhanVat_ThemVao_NeTranh < 0) Play.FLD_NhanVat_ThemVao_NeTranh = 0;
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.4;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.YeuHoaThanhThao = false;
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008000241:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.05;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 30;
                                    if (Play.FLD_NhanVat_ThemVao_CongKich < 0) Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.YeuHoaThanhThao = false;
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008000242:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.05;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 30;
                                    if (Play.FLD_NhanVat_ThemVao_CongKich < 0) Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.YeuHoaThanhThao = false;
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008000243:
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.2);
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.2);
                                    Play.CharactersToAddMax_HP -= 200;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.15;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
                                    break;
                                case 1008000245:
                                    Play.AppendStatusList.Remove(1008000245);
                                    Play.CharactersToAddMax_HP -= 300;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.FLD_NhanVat_ThemVao_NeTranh -= (int)(Play.FLD_NeTranh * 0.05);
                                    if (Play.FLD_NhanVat_ThemVao_NeTranh < 0) Play.FLD_NhanVat_ThemVao_NeTranh = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.FLD_NhanVat_ThemVao_KhiCong--;
                                    if (Play.FLD_NhanVat_ThemVao_KhiCong < 0) Play.FLD_NhanVat_ThemVao_KhiCong = 0;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.UpdateKhiCong();
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Dispose();
                                    return;
                                case 1008000248:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 1.0;
                                    Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram -= 1.0;
                                    Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram -= 1.0;
                                    Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem -= 1.0;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    break;
                                case 1008000250:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.05;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 30;
                                    if (Play.FLD_NhanVat_ThemVao_CongKich < 0) Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.YeuHoaThanhThao = false;
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008000251:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.05;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 30;
                                    if (Play.FLD_NhanVat_ThemVao_CongKich < 0) Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.YeuHoaThanhThao = false;
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008000252:
                                {
                                    Play.AppendStatusList.Remove(1008000252);
                                    var play = Play;
                                    play.DuocPham_ThemVao_DoiQuai_CongKich -= 150;
                                    if (Play.DuocPham_ThemVao_DoiQuai_CongKich < 0)
                                        Play.DuocPham_ThemVao_DoiQuai_CongKich = 0;
                                    play = Play;
                                    play.DuocPham_ThemVao_DoiQuai_PhongNgu -= 100;
                                    if (Play.DuocPham_ThemVao_DoiQuai_PhongNgu < 0)
                                        Play.DuocPham_ThemVao_DoiQuai_PhongNgu = 0;
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.2;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    Dispose();
                                    return;
                                }
                                case 1008000304:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.05;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 30;
                                    if (Play.FLD_NhanVat_ThemVao_CongKich < 0) Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 30;
                                    if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0) Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.YeuHoaThanhThao = false;
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008000305:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.05;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 30;
                                    if (Play.FLD_NhanVat_ThemVao_CongKich < 0) Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 30;
                                    if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0) Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.03;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.YeuHoaThanhThao = false;
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008000306:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.1;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 40;
                                    if (Play.FLD_NhanVat_ThemVao_CongKich < 0) Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 40;
                                    if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0) Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.YeuHoaThanhThao = false;
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008000307:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.1;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 40;
                                    if (Play.FLD_NhanVat_ThemVao_CongKich < 0) Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 40;
                                    if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0) Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    Play.YeuHoaThanhThao = false;
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008000321:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.4;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    break;
                                case 1008000322:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.4;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    break;
                                case 1008000323:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 1.0;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    break;
                                case 1008000324:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 1.0;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    break;
                                case 1008000325:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.1;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 40;
                                    if (Play.FLD_NhanVat_ThemVao_CongKich < 0) Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 40;
                                    if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0) Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
                                    Play.CharactersToAddMax_HP -= 300;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.YeuHoaThanhThao = false;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008000326:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.1;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 40;
                                    if (Play.FLD_NhanVat_ThemVao_CongKich < 0) Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 40;
                                    if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0) Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
                                    Play.CharactersToAddMax_HP -= 300;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.05;
                                    if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                        Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                    if (Play.NhanVat_HP > Play.CharacterMax_HP) Play.NhanVat_HP = Play.CharacterMax_HP;
                                    Play.YeuHoaThanhThao = false;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008000362:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.5;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    break;
                                case 1008000363:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 1.5;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    break;
                                case 1008000388:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 3.0;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    break;
                                case 1008001097:
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 100;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 50;
                                    Play.CharactersToAddMax_HP -= 1000;
                                    Play.FLD_NhanVat_ThemVao_KhiCong -= 2;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008001575:
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 200;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 200;
                                    Play.CharactersToAddMax_HP -= 1000;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.4;
                                    Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.1;
                                    Play.delFLD_ThemVaoTiLePhanTram_Attack(0.2);
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.2);
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    break;
                                case 1008001098:
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 100;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 50;
                                    Play.CharactersToAddMax_HP -= 300;
                                    Play.FLD_NhanVat_ThemVao_KhiCong -= 2;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008000389:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 1.5;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                    break;
                                case 1008001021:
                                case 1008001022:
                                case 1008001023:
                                case 1008001024:
                                case 1008001025:
                                case 1008001026:
                                case 1008001027:
                                case 1008001028:
                                case 1008001029:
                                case 1008001030:
                                case 1008001305:
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.CalculateCharacterEquipmentData();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateMoneyAndWeight();
                                    break;
                                case 1008001111:
                                    Play.CharactersToAddMax_HP -= 500;
                                    Play.CharactersToAddMax_MP -= 500;
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.4;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                    {
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                        break;
                                    }

                                    Play.FLD_NhanVat_ThemVao_CongKich -= 50;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 100;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
                                    Play.YeuHoaThanhThao = false;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008001112:
                                    Play.CharactersToAddMax_HP -= 800;
                                    Play.FLD_NhanVat_ThemVao_NeTranh -= 10;
                                    Play.FLD_NhanVat_ThemVao_LucPhongNguVoCong -= 100.0;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 100;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 50;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.1;
                                    Play.YeuHoaThanhThao = false;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008001169:
                                    Play.FLD_TRUDEF_KCTT4 = 0.0;
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.UpdateMartialArtsAndStatus();
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 1008001170:
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 700603:
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 100;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 100;
                                    Play.CharactersToAddMax_HP -= 1000;
                                    Play.CharactersToAddMax_MP -= 1000;
                                    Play.CapNhat_HP_MP_SP();
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1008001174:
                                    Play.CharactersToAddMax_HP -= 1000;
                                    if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                    Play.CapNhat_HP_MP_SP();
                                    break;
                                case 1008001175:
                                    Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.1);
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1008001176:
                                    Play.FLD_ThemVaoAiHongPhienDa_GioiHanHPCaoNhat = 0.0;
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Play.CapNhat_HP_MP_SP();
                                    Dispose();
                                    return;
                                case 1008001814:
                                    Play.FLD_ThemVaoTiLePhanTram_CongKich -= 0.28;
                                    Play.FLD_NhanVat_ThemVao_CongKich -= 80;
                                    Play.FLD_ThemVaoTiLePhanTram_PhongNgu -= 0.3;
                                    Play.FLD_NhanVat_ThemVao_PhongNgu -= 80;
                                    Play.CharactersToAddMax_HP -= 3000;
                                    Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.35;
                                    Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.23;
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 2.1;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                    {
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                        break;
                                    }

                                    Play.FLD_NhanVat_ThemVao_HapHonTiLe_TiLePhanTram -= 10.0;
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                                case 1008002012:
                                    Play.AppendStatusList.Remove(FLD_PID);
                                    Play.UpdateCharacterData(Play);
                                    Play.UpdateBroadcastCharacterData();
                                    Dispose();
                                    return;
                                case 1008001187:
                                    Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 2.0;
                                    if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                    {
                                        Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                        break;
                                    }

                                    Play.UpdateBroadcastCharacterData();
                                    Play.UpdateCharacterData(Play);
                                    break;
                                case 1008002169:
                                    Play.UpdateMartialArtsAndStatus();
                                    break;
                            }
                        }

                        num = 8;
                        num = 9;
                        if (Play.GetAddState(FLD_PID)) Play.AppendStatusList.Remove(FLD_PID);
                        num = 10;
                        Play.StatusEffect(BitConverter.GetBytes(FLD_PID), 0, 0);
                        num = 11;
                        Dispose();
                        break;
                    }
                    catch (Exception ex)
                    {
                        Form1.WriteLine(1, "Thêm vào trạng thái loại : " + num + "[" + FLD_PID + "]: " + ex);
                        break;
                    }
                    finally
                    {
                        Dispose();
                    }
            }
        }
        else
        {
            Play.AppendStatusList?.Clear();
            Dispose();
        }
    }

    public int getsj()
    {
        return (int)time.Subtract(DateTime.Now).TotalMilliseconds;
    }

    public void setsj(int num)
    {
        time = time.AddSeconds(num);
    }
}