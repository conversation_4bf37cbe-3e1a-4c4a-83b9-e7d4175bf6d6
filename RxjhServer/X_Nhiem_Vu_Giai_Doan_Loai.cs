using System.Collections.Generic;

namespace RxjhServer;

public class X_Nhiem_Vu_Giai_Doan_Loai
{
    public Dictionary<int, X_Giai_Doan_Can_Vat_Pham_Loai> GiaiDoanCanVatPham_ = new();

    public int Npc未知1;

    public string 任务条件不符合提示2 = string.Empty;

    public string 任务条件不符合提示3 = string.Empty;

    public string 任务条件不符合提示4 = string.Empty;

    public string 任务条件不符合提示5 = string.Empty;

    public string 任务条件符合提示2 = string.Empty;

    public string 任务条件符合提示3 = string.Empty;

    public string 任务条件符合提示4 = string.Empty;

    public string 任务条件符合提示5 = string.Empty;

    public Dictionary<int, X_Giai_Doan_Ban_Thuong_Vat_Pham_Loai> 阶段奖励物品_ = new();

    public string NPCNAME { get; set; } = string.Empty;

    public string 任务阶段内容 { get; set; } = string.Empty;

    public int GiaiDoanID { get; set; }

    public int GiaiDoan_TrangThai { get; set; } = -1;

    public int MucDo_K<PERSON>han { get; set; }

    public int NpcID { get; set; }

    public int Npc地图ID { get; set; }

    public int Npc坐标X { get; set; }

    public int Npc坐标Y { get; set; }

    public string 任务条件符合提示1 { get; set; } = string.Empty;

    public string 任务条件不符合提示1 { get; set; } = string.Empty;

    public X_Nhiem_Vu_Giai_Doan_Loai GetRWJD(int int_7, int int_8)
    {
        try
        {
            using (var enumerator = World.NhiemVulist.Values.GetEnumerator())
            {
                X_Nhiem_Vu_Loai 任务类 = null;
                while (enumerator.MoveNext())
                {
                    任务类 = enumerator.Current;
                    if (任务类.RwID == int_7) return 任务类.NhiemVu_GiaiDoan[int_8];
                }
            }

            return null;
        }
        catch
        {
            return null;
        }
    }
}