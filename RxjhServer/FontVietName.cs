using System;

namespace RxjhServer;

internal class FontVietName
{
    private static string[] startKeys;

    private static string[] endKeys;

    private readonly string _endChars =
        "đ|á|à|ả|ã|ạ|ă|ắ|ằ|ẳ|ẵ|ặ|â|ấ|ầ|ẩ|ẫ|ậ|é|è|ẻ|ẽ|ẹ|ê|ế|ề|ể|ễ|ệ|í|ì|ỉ|ĩ|ị|ó|ò|ỏ|õ|ọ|ô|ố|ồ|ổ|ỗ|ộ|ơ|ớ|ờ|ở|ỡ|ợ|ý|ỳ|ỷ|ỹ|ỵ|ú|ù|ủ|ũ|ụ|ư|ứ|ừ|ử|ữ|ự";

    private readonly string _startCha =
        "ð|aì|aÌ|aÒ|aÞ|aò|ã|ãì|ãÌ|ãÒ|ãÞ|ãò|â|âì|âÌ|âÒ|âÞ|âò|eì|eÌ|eÒ|eÞ|eò|ê|êì|êÌ|êÒ|êÞ|êò|iì|iÌ|iÒ|iÞ|iò|oì|oÌ|oÒ|oÞ|oò|ô|ôì|ôÌ|ôÒ|ôÞ|ôò|õ|õì|õÌ|õÒ|õÞ|õò|yì|yÌ|yÒ|yÞ|yò|uì|uÌ|uÒ|uÞ|uò|ý|ýì|ýÌ|ýÒ|ýÞ|ýò";

    public FontVietName()
    {
        startKeys = _startCha.Split('|');
        endKeys = _endChars.Split('|');
    }

    public static string Convert(string input)
    {
        var Length = input.Length;
        var inputText = new string[Length];
        for (var j = 0; j < Length; j++) inputText[j] = input[j].ToString();
        for (var i = 0; i <= Length - 1; i++)
            try
            {
                var index = Array.IndexOf(startKeys, inputText[i]);
                inputText[i] = inputText[i].Replace(startKeys[index], endKeys[index]);
            }
            catch
            {
            }

        return string.Join("", inputText);
    }
}