using System;
using System.Collections;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;

namespace RxjhServer;

public class Class2
{
    private readonly string string_0 =
        "<RSAKeyValue><Modulus>y+XGM4lAnEKsEb23uoadipw11QLSSLrcqLR4jZqAPAMGVsVlMi+SUJOTMFO+QsN2mDzCSaAdOxH7Cn9+MqH3wO+FwFuILmIdlXG9ZNARH8hwheRJrCD9X5cENXYHKRIXSVu+INbyicjQXGiJ/ycf9e35OBHOZJgy7FBqjC6m/u8=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>";

    public string EncryptByPublicKey(string string_1)
    {
        var rSACryptoServiceProvider = new RSACryptoServiceProvider(1024);
        rSACryptoServiceProvider.FromXmlString(string_0);
        var rSAParameters = rSACryptoServiceProvider.ExportParameters(false);
        var modulus = rSAParameters.Modulus;
        var exponent = rSAParameters.Exponent;
        var bigInteger_ = new BigInteger(modulus);
        var bigInteger_2 = new BigInteger(exponent);
        var bytes = Encoding.UTF8.GetBytes(string_1);
        var num = 86;
        var num2 = bytes.Length;
        var num3 = num2 / 86;
        var stringBuilder = new StringBuilder();
        for (var i = 0; i <= num3; i++)
        {
            var num4 = num2 - num * i > num ? num : num2 - num * i;
            var array = new byte[num4];
            System.Buffer.BlockCopy(bytes, num * i, array, 0, array.Length);
            var array2 = method_0(array, bigInteger_2, bigInteger_);
            Array.Reverse(array2);
            stringBuilder.Append(Convert.ToBase64String(array2));
        }

        return stringBuilder.ToString();
    }

    public string DecryptByPublicKey(string string_1)
    {
        var rSACryptoServiceProvider = new RSACryptoServiceProvider(1024);
        rSACryptoServiceProvider.FromXmlString(string_0);
        var rSAParameters = rSACryptoServiceProvider.ExportParameters(false);
        var modulus = rSAParameters.Modulus;
        var exponent = rSAParameters.Exponent;
        var bigInteger_ = new BigInteger(modulus);
        var bigInteger_2 = new BigInteger(exponent);
        var num2 = 172;
        var num3 = string_1.Length / 172;
        var arrayList = new ArrayList();
        for (var i = 0; i < num3; i++)
        {
            var array = Convert.FromBase64String(string_1.Substring(num2 * i, num2));
            Array.Reverse(array);
            arrayList.AddRange(method_1(array, bigInteger_2, bigInteger_));
        }

        return Encoding.UTF8.GetString(arrayList.ToArray(Type.GetType("System.Byte")) as byte[]);
    }

    private byte[] method_0(byte[] byte_0, BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        var num2 = 0;
        List<byte> list = null;
        byte[] array = null;
        var num3 = 0;
        string text = null;
        var num4 = byte_0.Length;
        var num5 = 0;
        var num6 = 0;
        var num7 = num4 % 120 != 0 ? num4 / 120 + 1 : num4 / 120;
        num5 = num7;
        list = new List<byte>();
        for (num2 = 0; num2 < num5; num2++)
        {
            var num8 = num4 < 120 ? num4 : 120;
            num6 = num8;
            var array2 = new byte[num6];
            Array.Copy(byte_0, num2 * 120, array2, 0, num6);
            Encoding.UTF8.GetString(array2);
            text = new BigInteger(array2).modPow(bigInteger_0, bigInteger_1).ToHexString();
            if (text.Length < 256)
                while (text.Length != 256)
                    text = "0" + text;
            array = new byte[128];
            for (num3 = 0; num3 < array.Length; num3++) array[num3] = Convert.ToByte(text.Substring(num3 * 2, 2), 16);
            list.AddRange(array);
            num4 -= num6;
        }

        return list.ToArray();
    }

    private byte[] method_1(byte[] byte_0, BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        var num = 0;
        List<byte> list = null;
        var num2 = byte_0.Length;
        var num3 = 0;
        var num4 = 0;
        var num5 = num2 % 128 != 0 ? num2 / 128 + 1 : num2 / 128;
        num3 = num5;
        list = new List<byte>();
        for (num = 0; num < num3; num++)
        {
            var num6 = num2 < 128 ? num2 : 128;
            num4 = num6;
            var array = new byte[num4];
            Array.Copy(byte_0, num * 128, array, 0, num4);
            var bytes = new BigInteger(array).modPow(bigInteger_0, bigInteger_1).getBytes();
            Encoding.UTF8.GetString(bytes);
            list.AddRange(bytes);
            num2 -= num4;
        }

        return list.ToArray();
    }
}