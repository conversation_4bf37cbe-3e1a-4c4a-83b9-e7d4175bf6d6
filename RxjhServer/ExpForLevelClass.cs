namespace RxjhServer;

public class ExpForLevelClass
{
    public int ID { get; set; }

    public int FLD_LEVEL { get; set; }

    public long FLD_EXP { get; set; }

    public long FLD_EXP_PER_MINUTE { get; set; }

    public static long Get_ExpPerMinute(int level)
    {
        foreach (var Value in World.List_Level.Values)
            if (Value.FLD_LEVEL == level)
                return Value.FLD_EXP_PER_MINUTE;
        return 0L;
    }
}