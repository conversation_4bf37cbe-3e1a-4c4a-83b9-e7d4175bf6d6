using System;
using System.Collections.Generic;

namespace RxjhServer;

public class DropClass
{
    public int _是否开启公告;
    public List<DropShuXClass> DropShuX = new();

    public int FLD_MAGICNew0;

    public int FLD_MAGICNew1;

    public int FLD_MAGICNew2;

    public int FLD_MAGICNew3;

    public int FLD_MAGICNew4;

    public int FLD_PIDNew;

    public int FLD_LEVEL1 { get; set; }

    public int FLD_LEVEL2 { get; set; }

    public int FLD_PID { get; set; }

    public int FLD_PP { get; set; }

    public int FLD_MAGIC0 { get; set; }

    public int FLD_MAGIC1 { get; set; }

    public int FLD_MAGIC2 { get; set; }

    public int FLD_MAGIC3 { get; set; }

    public int FLD_MAGIC4 { get; set; }

    public string FLD_NAME { get; set; }

    public int FLD_NJ { get; set; }

    public int FLD_SoCapPhuHon { get; set; }

    public int FLD_TrungCapPhuHon { get; set; }

    public int CoMoThongBao
    {
        get => _是否开启公告;
        set => _是否开启公告 = value;
    }

    public int FLD_TienHoa { get; set; }

    public int FLD_KhoaLai { get; set; }

    public static List<DropClass> GetBossDrop(int leve)
    {
        try
        {
            var list = new List<DropClass>();
            var list2 = new List<DropClass>();
            var random = new Random(DateTime.Now.Millisecond);
            RNG.Next(1, 8000);
            foreach (var item in World.BossDrop)
            {
                var num = RNG.Next(1, 8000);
                if (item.FLD_PP != 0 && item.FLD_LEVEL1 <= leve && item.FLD_LEVEL2 >= leve && item.FLD_PP >= num)
                    list.Add(item);
            }

            if (list.Count == 0) return null;
            var num2 = 15;
            var num3 = 0;
            using (var enumerator2 = list.GetEnumerator())
            {
                while (enumerator2.MoveNext() && num3 < num2)
                {
                    var num4 = random.Next(0, list.Count);
                    list2.Add(list[num4]);
                    num3++;
                }
            }

            if (list2.Count == 0) return null;
            return list2;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "GetBossDrop error[" + leve + "]" + ex.Message);
            return null;
        }
    }

    public static List<DropClass> GetGSDrop(int int_14, int int_15, int int_16)
    {
        try
        {
            var list = new List<DropClass>();
            var list2 = new List<DropClass>();
            var random = new Random(DateTime.Now.Millisecond);
            var num = RNG.Next(1, 8000);
            foreach (var drop_G in World.Drop_GS)
                if (drop_G.FLD_PP != 0 && drop_G.FLD_LEVEL1 <= int_14 && drop_G.FLD_LEVEL2 >= int_14 &&
                    drop_G.FLD_PP >= num)
                    list.Add(drop_G);
            if (list.Count == 0) return null;
            var num2 = random.Next(1, 2);
            var num3 = 0;
            foreach (var item in list)
            {
                if (num3 >= num2) break;
                list2.Add(list[RNG.Next(0, list.Count - 1)]);
                num3++;
            }

            return list2.Count == 0 ? null : list2;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "GetGSDrop error[" + int_14 + "]" + ex.Message);
            return null;
        }
    }

    public static DropClass GetDrop(int int_14)
    {
        try
        {
            var list = new List<DropClass>();
            var num = RNG.Next(1, 8000);
            foreach (var item in World.Drop)
                if (item.FLD_PP != 0 && item.FLD_LEVEL1 <= int_14 && item.FLD_LEVEL2 >= int_14 && item.FLD_PP >= num)
                    list.Add(item);
            return list.Count == 0 ? null : list[RNG.Next(0, list.Count - 1)];
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "GetDrop error[" + int_14 + "]" + ex.Message);
            return null;
        }
    }

    public static DropClass GetVipDrop(int int_14)
    {
        try
        {
            DropClass dropClass = null;
            DropClass result = null;
            var list = new List<DropClass>();
            new Random(DateTime.Now.Millisecond);
            var num3 = RNG.Next(1, 8000);
            var enumerator = World.Drop.GetEnumerator();
            while (true)
            {
                try
                {
                    while (enumerator.MoveNext())
                    {
                        dropClass = enumerator.Current;
                        if (dropClass.FLD_PP != 0 && dropClass.FLD_LEVEL1 <= int_14 && dropClass.FLD_LEVEL2 >= int_14 &&
                            dropClass.FLD_PP >= num3) list.Add(dropClass);
                    }
                }
                finally
                {
                    enumerator.Dispose();
                }

                while (true)
                {
                    object obj;
                    switch (list.Count != 0 ? 4 : 0)
                    {
                        default:
                            list = new List<DropClass>();
                            new Random(DateTime.Now.Millisecond);
                            num3 = RNG.Next(1, 8000);
                            enumerator = World.Drop.GetEnumerator();
                            break;
                        case 0:
                            obj = null;
                            goto IL_015f;
                        case 4:
                        case 5:
                            obj = list[RNG.Next(0, list.Count - 1)];
                            goto IL_015f;
                        case 2:
                            return result;
                        case 3:
                            continue;
                        case 1:
                            break;
                            IL_015f:
                            result = (DropClass)obj;
                            goto case 2;
                    }

                    break;
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "VIP Rơi ra sai leve[" + int_14 + "]" + ex.Message);
            return null;
        }
    }
}