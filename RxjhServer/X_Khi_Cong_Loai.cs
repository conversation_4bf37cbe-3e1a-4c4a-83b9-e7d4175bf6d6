namespace RxjhServer;

public class <PERSON>_<PERSON><PERSON>_Cong_Loai
{
    public int KhiCongID;

    public X_<PERSON>hi_Cong_Loai()
    {
    }

    public X_Khi_Cong_Loai(byte[] byte_0)
    {
        KhiCong_byte = byte_0;
    }

    public byte[] KhiCong_byte { get; set; }

    public int KhiCong_SoLuong
    {
        get => Buffer.ToInt16(KhiCong_byte, 0);
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, KhiCong_byte, 0, 2);
    }
}