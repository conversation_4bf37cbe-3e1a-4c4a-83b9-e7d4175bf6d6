using System;
using System.IO;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace RxjhServer
{
    public class LuaScriptValidator
    {
        private static readonly List<string> CommonIssues = new List<string>();
        
        public static void ValidateAllScripts(string scriptDirectory)
        {
            if (!Directory.Exists(scriptDirectory))
            {
                Form1.WriteLine(1, $"LuaScriptValidator: Directory not found: {scriptDirectory}");
                return;
            }
            
            Form1.WriteLine(2, "Starting Lua script validation...");
            var luaFiles = Directory.GetFiles(scriptDirectory, "*.lua");
            
            int totalFiles = luaFiles.Length;
            int validFiles = 0;
            int fixedFiles = 0;
            int errorFiles = 0;
            
            foreach (var file in luaFiles)
            {
                try
                {
                    var result = ValidateAndFixScript(file);
                    switch (result)
                    {
                        case ValidationResult.Valid:
                            validFiles++;
                            break;
                        case ValidationResult.Fixed:
                            fixedFiles++;
                            break;
                        case ValidationResult.Error:
                            errorFiles++;
                            break;
                    }
                }
                catch (Exception ex)
                {
                    Form1.WriteLine(1, $"LuaScriptValidator: Error processing {Path.GetFileName(file)}: {ex.Message}");
                    errorFiles++;
                }
            }
            
            Form1.WriteLine(2, $"Lua script validation completed:");
            Form1.WriteLine(2, $"  Total files: {totalFiles}");
            Form1.WriteLine(2, $"  Valid files: {validFiles}");
            Form1.WriteLine(2, $"  Fixed files: {fixedFiles}");
            Form1.WriteLine(2, $"  Error files: {errorFiles}");
            
            if (CommonIssues.Count > 0)
            {
                Form1.WriteLine(2, "Common issues found:");
                foreach (var issue in CommonIssues)
                {
                    Form1.WriteLine(2, $"  - {issue}");
                }
            }
        }
        
        public static ValidationResult ValidateAndFixScript(string filePath)
        {
            try
            {
                var content = File.ReadAllText(filePath);
                var originalContent = content;
                bool hasChanges = false;
                
                // Check for basic syntax issues
                var issues = new List<string>();
                
                // Check for unmatched if/end statements
                if (CheckIfEndBalance(content, out string ifEndIssue))
                {
                    issues.Add(ifEndIssue);
                }
                
                // Check for unmatched function/end statements
                if (CheckFunctionEndBalance(content, out string funcEndIssue))
                {
                    issues.Add(funcEndIssue);
                }
                
                // Fix common syntax issues
                content = FixCommonSyntaxIssues(content, ref hasChanges);
                
                // Validate function parameters
                content = ValidateFunctionParameters(content, ref hasChanges);
                
                // Add null checks for Player object
                content = AddPlayerNullChecks(content, ref hasChanges);
                
                if (hasChanges)
                {
                    // Create backup
                    File.Copy(filePath, filePath + ".backup", true);
                    File.WriteAllText(filePath, content);
                    Form1.WriteLine(2, $"Fixed script: {Path.GetFileName(filePath)}");
                    return ValidationResult.Fixed;
                }
                
                if (issues.Count > 0)
                {
                    Form1.WriteLine(1, $"Issues in {Path.GetFileName(filePath)}:");
                    foreach (var issue in issues)
                    {
                        Form1.WriteLine(1, $"  - {issue}");
                        if (!CommonIssues.Contains(issue))
                        {
                            CommonIssues.Add(issue);
                        }
                    }
                    return ValidationResult.Error;
                }
                
                return ValidationResult.Valid;
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"ValidateAndFixScript error for {Path.GetFileName(filePath)}: {ex.Message}");
                return ValidationResult.Error;
            }
        }
        
        private static bool CheckIfEndBalance(string content, out string issue)
        {
            issue = "";
            var lines = content.Split('\n');
            int ifCount = 0;
            int endCount = 0;
            
            foreach (var line in lines)
            {
                var trimmed = line.Trim();
                if (trimmed.StartsWith("if ") || trimmed.Contains(" if "))
                {
                    ifCount++;
                }
                if (trimmed == "end" || trimmed.StartsWith("end "))
                {
                    endCount++;
                }
            }
            
            if (ifCount != endCount)
            {
                issue = $"Unbalanced if/end statements: {ifCount} if, {endCount} end";
                return true;
            }
            
            return false;
        }
        
        private static bool CheckFunctionEndBalance(string content, out string issue)
        {
            issue = "";
            var functionPattern = @"function\s+\w+\s*\(";
            var functionMatches = Regex.Matches(content, functionPattern);
            var endMatches = Regex.Matches(content, @"\bend\b");
            
            if (functionMatches.Count > endMatches.Count)
            {
                issue = $"Missing 'end' for function declarations: {functionMatches.Count} functions, {endMatches.Count} ends";
                return true;
            }
            
            return false;
        }
        
        private static string FixCommonSyntaxIssues(string content, ref bool hasChanges)
        {
            var original = content;
            
            // Fix missing 'then' after if statements
            content = Regex.Replace(content, @"if\s+([^then\n]+)\s*\n", "if $1 then\n");
            
            // Fix missing 'do' after for/while statements
            content = Regex.Replace(content, @"for\s+([^do\n]+)\s*\n", "for $1 do\n");
            content = Regex.Replace(content, @"while\s+([^do\n]+)\s*\n", "while $1 do\n");
            
            if (content != original)
            {
                hasChanges = true;
            }
            
            return content;
        }
        
        private static string ValidateFunctionParameters(string content, ref bool hasChanges)
        {
            // Add validation for function parameters
            var lines = content.Split('\n');
            var result = new List<string>();
            
            foreach (var line in lines)
            {
                if (line.Trim().StartsWith("local Player = GetPlayer("))
                {
                    result.Add(line);
                    result.Add("  if Player == nil then");
                    result.Add("    SendSysMsg(UserWorldId, \"Lỗi: Không tìm thấy thông tin người chơi\", 9, \"Lỗi\")");
                    result.Add("    return");
                    result.Add("  end");
                    hasChanges = true;
                }
                else
                {
                    result.Add(line);
                }
            }
            
            return string.Join("\n", result);
        }
        
        private static string AddPlayerNullChecks(string content, ref bool hasChanges)
        {
            // Add null checks before accessing Player properties
            if (content.Contains("Player.") && !content.Contains("if Player == nil"))
            {
                var lines = content.Split('\n');
                var result = new List<string>();
                bool addedCheck = false;
                
                foreach (var line in lines)
                {
                    if (line.Contains("Player.") && !addedCheck && !line.Contains("GetPlayer"))
                    {
                        result.Add("  -- Safety check for Player object");
                        result.Add("  if Player == nil then");
                        result.Add("    SendSysMsg(UserWorldId, \"Lỗi hệ thống: Player object null\", 9, \"Lỗi\")");
                        result.Add("    return");
                        result.Add("  end");
                        result.Add("");
                        addedCheck = true;
                        hasChanges = true;
                    }
                    result.Add(line);
                }
                
                return string.Join("\n", result);
            }
            
            return content;
        }
    }
    
    public enum ValidationResult
    {
        Valid,
        Fixed,
        Error
    }
}
