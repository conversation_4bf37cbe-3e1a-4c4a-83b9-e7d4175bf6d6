using System;
using System.Collections;
using System.Collections.Generic;

namespace RxjhServer;

public interface IThreadSafeDictionary<TKey, TValue> : IDictionary<TKey, TValue>,
    ICollection<KeyValuePair<TKey, TValue>>, IEnumerable<KeyValuePair<TKey, TValue>>, IEnumerable, IDisposable
{
    void MergeSafe(TKey gparam_0, TValue newValue);

    void RemoveSafe(TKey gparam_0);

    new void Dispose();
}