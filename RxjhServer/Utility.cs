using System.IO;
using System.Text;

namespace RxjhServer;

public static class Utility
{
    private static Encoding encoding_0;

    public static Encoding UTF8
    {
        get
        {
            var num = 2;
            while (true)
            {
                switch (num)
                {
                    case 0:
                        encoding_0 = new UTF8Encoding(false, false);
                        num = 1;
                        continue;
                    default:
                        if (encoding_0 == null)
                        {
                            num = 0;
                            continue;
                        }

                        break;
                    case 1:
                        break;
                }

                break;
            }

            return encoding_0;
        }
    }

    public static void FormatBuffer(TextWriter textWriter_0, Stream stream_0, int int_0)
    {
        var num13 = 0;
        var num14 = 0;
        StringBuilder stringBuilder = null;
        var num15 = 0;
        StringBuilder stringBuilder2 = null;
        var num16 = 0;
        StringBuilder stringBuilder3 = null;
        StringBuilder stringBuilder4 = null;
        textWriter_0.WriteLine("        0  1  2  3  4  5  6  7   8  9  A  B  C  D  E  F");
        textWriter_0.WriteLine("       -- -- -- -- -- -- -- --  -- -- -- -- -- -- -- --");
        var num17 = 0;
        var num10 = int_0 >> 4;
        var num11 = int_0 & 0xF;
        var num12 = 0;
        while (true)
        {
            var flag = true;
            while (true)
            {
                IL_0435:
                if (num12 >= num10)
                {
                    var flag2 = true;
                    while (true)
                    {
                        var flag3 = true;
                        while (true)
                        {
                            if (num11 == 0) return;
                            stringBuilder = new StringBuilder(49);
                            stringBuilder3 = new StringBuilder(num11);
                            num14 = 0;
                            var flag4 = true;
                            while (true)
                            {
                                switch (num14 >= 16 ? 34 : 33)
                                {
                                    case 14:
                                        break;
                                    case 25:
                                        return;
                                    case 33:
                                        if (num14 < num11) goto case 18;
                                        stringBuilder.Append("   ");
                                        goto case 4;
                                    case 18:
                                        num15 = stream_0.ReadByte();
                                        stringBuilder.Append(num15.ToString("X2"));
                                        goto case 31;
                                    case 31:
                                        if (num14 != 7) goto case 7;
                                        stringBuilder.Append("  ");
                                        goto case 9;
                                    case 7:
                                        stringBuilder.Append(' ');
                                        goto case 9;
                                    case 9:
                                    case 12:
                                    case 35:
                                        if (num15 >= 32) goto case 15;
                                        goto IL_02cb;
                                    case 15:
                                    case 32:
                                        if (num15 < 128) goto case 23;
                                        goto IL_02cb;
                                    case 23:
                                        stringBuilder3.Append((char)num15);
                                        goto case 4;
                                    case 4:
                                    case 8:
                                    case 11:
                                        num14++;
                                        continue;
                                    default:
                                        textWriter_0.WriteLine(
                                            "        0  1  2  3  4  5  6  7   8  9  A  B  C  D  E  F");
                                        textWriter_0.WriteLine(
                                            "       -- -- -- -- -- -- -- --  -- -- -- -- -- -- -- --");
                                        num17 = 0;
                                        num10 = int_0 >> 4;
                                        num11 = int_0 & 0xF;
                                        num12 = 0;
                                        goto IL_0435;
                                    case 34:
                                        textWriter_0.Write(num17.ToString("X4"));
                                        textWriter_0.Write("   ");
                                        textWriter_0.Write(stringBuilder.ToString());
                                        textWriter_0.Write("  ");
                                        textWriter_0.WriteLine(stringBuilder3.ToString());
                                        return;
                                    case 0:
                                    case 22:
                                    case 28:
                                        continue;
                                    case 24:
                                        goto end_IL_0071;
                                    case 30:
                                        goto end_IL_02f6;
                                    case 21:
                                    case 27:
                                        goto IL_0334;
                                    case 2:
                                    case 3:
                                    case 29:
                                        goto IL_0352;
                                    case 16:
                                        goto end_IL_0435;
                                    case 5:
                                        goto IL_03d6;
                                    case 6:
                                        goto IL_0403;
                                    case 20:
                                    case 36:
                                        goto IL_0410;
                                    case 10:
                                    case 13:
                                    case 26:
                                        goto IL_041d;
                                    case 1:
                                    case 17:
                                    case 19:
                                        goto IL_0435;
                                        IL_02cb:
                                        stringBuilder3.Append('.');
                                        goto case 4;
                                }

                                break;
                            }

                            continue;
                            end_IL_0071:
                            break;
                        }

                        continue;
                        end_IL_02f6:
                        break;
                    }

                    goto IL_0323;
                }

                stringBuilder4 = new StringBuilder(49);
                stringBuilder2 = new StringBuilder(16);
                num13 = 0;
                goto IL_0352;
                IL_041d:
                if (num16 >= 32) goto IL_0334;
                goto IL_03f6;
                IL_0323:
                stringBuilder2.Append((char)num16);
                goto IL_0410;
                IL_0334:
                if (num16 < 128) goto IL_0323;
                goto IL_03f6;
                IL_0352:
                if (num13 < 16)
                {
                    num16 = stream_0.ReadByte();
                    stringBuilder4.Append(num16.ToString("X2"));
                    goto IL_03d6;
                }

                break;
                IL_03f6:
                stringBuilder2.Append('.');
                goto IL_0410;
                IL_03d6:
                if (num13 != 7) goto IL_0403;
                stringBuilder4.Append("  ");
                goto IL_041d;
                IL_0403:
                stringBuilder4.Append(' ');
                goto IL_041d;
                IL_0410:
                num13++;
                goto IL_0352;
                continue;
                end_IL_0435:
                break;
            }

            textWriter_0.Write(num17.ToString("X4"));
            textWriter_0.Write("   ");
            textWriter_0.Write(stringBuilder4.ToString());
            textWriter_0.Write("  ");
            textWriter_0.WriteLine(stringBuilder2.ToString());
            num12++;
            num17 += 16;
        }
    }
}