using System;
using System.Threading;
using System.Timers;
using RxjhServer.DbClss;

namespace RxjhServer;

public class EventClass : IDisposable
{
    private DateTime Check_TLCInvitation;
    private string jlsqlzj;

    private DateTime kssj;

    private DateTime kssjgj;

    private int kssjint;

    private int TheLucChiensj;

    private readonly System.Timers.Timer ThoiGian1;

    private System.Timers.Timer ThoiGian2;

    private System.Timers.Timer ThoiGian3;

    private System.Timers.Timer ThoiGian4;

    private System.Timers.Timer ThoiGian5;

    public EventClass()
    {
        jlsqlzj = string.Empty;
        try
        {
            World.EventTop.Clear();
            DBA.ExeSqlCommand("DELETE FROM EventTop");
            kssj = DateTime.Now.AddMinutes(World.ThoiGianChuanBi_ChoTheLucChien);
            World.TheLucChien_Progress = 1;
            World.TheLucChien_ChinhPhai_DiemSo = 0;
            World.TheLucChien_TaPhai_DiemSo = 0;
            World.TheLucChien_ThoiGianChienDau = DateTime.Now;
            Check_TLCInvitation = DateTime.Now.AddMinutes(-1.0);
            ThoiGian1 = new System.Timers.Timer(3000.0);
            ThoiGian1.Elapsed += TimeEndEvent1;
            ThoiGian1.Enabled = true;
            ThoiGian1.AutoReset = true;
            TimeEndEvent1(null, null);
            World.conn.Transmit("势力战进程|" + World.TheLucChien_Progress);
            World.conn.Transmit("TheLucChienStarting|" + World.ServerID);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Thế lực chiến EventClassPhạm sai lầm: " + ex);
        }
    }

    public void Dispose()
    {
        try
        {
            foreach (var value in World.allConnectedChars.Values)
                if (value.NhanVatToaDo_BanDo == 801)
                {
                    value.TheLucChien_PhePhai = string.Empty;
                    value.TheLucChien_SatNhan_SoLuong = 0;
                    value.TheLucChien_TuVong_SoLuong = 0;
                    value.SwitchPkMode(0);
                    if (World.TheLucChien_LienServer != 0)
                        value.Mobile(-5f, -145f, 15f, 1201);
                    else
                        value.Mobile(500f, 1550f, 15f, 101);
                }

            World.TheLucChien_Progress = 0;
            World.TheLucChien_MidTime = 0;
            World.TheLucChien_ChinhPhai_DiemSo = 0;
            World.TheLucChien_TaPhai_DiemSo = 0;
            World.TheLucChien_ChinhPhai_SoNguoi = 0;
            World.TheLucChien_TaPhai_SoNguoi = 0;
            World.TheLucChien_ThoiGianChienDau = DateTime.MinValue;
            if (World.TheLucChien_Giam_NguoiChoi.Count > 0) World.TheLucChien_Giam_NguoiChoi.Clear();
            if (ThoiGian1 != null)
            {
                ThoiGian1.Enabled = false;
                ThoiGian1.Close();
                ThoiGian1.Dispose();
            }

            if (ThoiGian2 != null)
            {
                ThoiGian2.Enabled = false;
                ThoiGian2.Close();
                ThoiGian2.Dispose();
            }

            if (ThoiGian3 != null)
            {
                ThoiGian3.Enabled = false;
                ThoiGian3.Close();
                ThoiGian3.Dispose();
            }

            if (ThoiGian4 != null)
            {
                ThoiGian4.Enabled = false;
                ThoiGian4.Close();
                ThoiGian4.Dispose();
            }

            if (ThoiGian5 != null)
            {
                ThoiGian5.Enabled = false;
                ThoiGian5.Close();
                ThoiGian5.Dispose();
            }

            if (World.EventClass != null) World.EventClass = null;
        }
        catch
        {
        }
        finally
        {
            World.conn.Transmit("势力战进程|" + World.TheLucChien_Progress);
            World.conn.Transmit("势力战人数|" + World.TheLucChien_ChinhPhai_SoNguoi + "|" +
                                World.TheLucChien_TaPhai_SoNguoi);
            World.conn.Transmit("TheLucChienEnd|" + World.ServerID);
            World.TheLucChien_Progress = 0;
            World.TheLucChien_MidTime = 0;
            World.TheLucChien_ChinhPhai_DiemSo = 0;
            World.TheLucChien_TaPhai_DiemSo = 0;
            World.TheLucChien_ChinhPhai_SoNguoi = 0;
            World.TheLucChien_TaPhai_SoNguoi = 0;
            World.TheLucChien_ThoiGianChienDau = DateTime.MinValue;
            if (World.TheLucChien_Giam_NguoiChoi.Count > 0) World.TheLucChien_Giam_NguoiChoi.Clear();
            if (ThoiGian1 != null)
            {
                ThoiGian1.Enabled = false;
                ThoiGian1.Close();
                ThoiGian1.Dispose();
            }

            if (ThoiGian2 != null)
            {
                ThoiGian2.Enabled = false;
                ThoiGian2.Close();
                ThoiGian2.Dispose();
            }

            if (ThoiGian3 != null)
            {
                ThoiGian3.Enabled = false;
                ThoiGian3.Close();
                ThoiGian3.Dispose();
            }

            if (ThoiGian4 != null)
            {
                ThoiGian4.Enabled = false;
                ThoiGian4.Close();
                ThoiGian4.Dispose();
            }

            if (ThoiGian5 != null)
            {
                ThoiGian5.Enabled = false;
                ThoiGian5.Close();
                ThoiGian5.Dispose();
            }

            if (World.EventClass != null) World.EventClass = null;
            World.delNpc(801, 15137);
        }
    }

    public void TimeEndEvent1(object sender, ElapsedEventArgs e)
    {
        try
        {
            var num = (int)kssj.Subtract(DateTime.Now).TotalSeconds;
            if (num <= 0)
            {
                World.TheLucChien_Progress = 2;
                World.TheLucChien_ChinhPhai_DiemSo = 0;
                World.TheLucChien_TaPhai_DiemSo = 0;
                World.conn.Transmit("势力战进程|" + World.TheLucChien_Progress);
                num = 0;
            }

            kssjint = num;
            if (World.TheLucChien_LienServer != 0 && DateTime.Now.Subtract(Check_TLCInvitation).TotalSeconds > 30.0)
            {
                Check_TLCInvitation = DateTime.Now;
                World.conn.Transmit("TLCInvitation|" + World.ServerID);
                Form1.WriteLine(3, "TLCInvitation");
            }

            foreach (var value in World.allConnectedChars.Values)
                if (value.NhanVatToaDo_BanDo == 801)
                {
                    value.Gui_di_the_luc_chien_nhanh_bat_dau_tin_tuc(kssjint);
                    value.GuiDi_TheLucChien_DemNguoc(kssjint);
                }
                else if (value.Player_Job_level >= 2 && value.NhanVatToaDo_BanDo != 801)
                {
                    value.GuiDi_TheLucChien_LoiMoi_New2();
                }

            if (kssjint > 0) return;
            var num2 = 0;
            var flag = true;
            foreach (var value2 in World.allConnectedChars.Values)
            {
                switch (num2)
                {
                }

                if (value2.NhanVatToaDo_BanDo == 801)
                {
                    value2.SwitchPkMode(1);
                    flag = false;
                }

                num2++;
                if (num2 >= 4) num2 = 0;
            }

            if (flag)
            {
                Dispose();
                return;
            }

            ThoiGian1.Enabled = false;
            ThoiGian1.Close();
            ThoiGian1.Dispose();
            World.TheLucChien_Progress = 3;
            World.conn.Transmit("势力战进程|" + World.TheLucChien_Progress);
            kssjgj = DateTime.Now.AddMinutes(World.TheLucChien_TongThoiGian);
            ThoiGian2 = new System.Timers.Timer(3000.0);
            ThoiGian2.Elapsed += ThoiGianKetThucSuKien2;
            ThoiGian2.Enabled = true;
            ThoiGian2.AutoReset = true;
            World.TheLucChien_ThoiGianChienDau = DateTime.Now;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Thế Lực Chiến Thời gian kết thúc sự kiện 1：" + ex);
        }
    }

    public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
    {
        try
        {
            var totalSeconds = World.TheLucChien_MidTime = (int)kssjgj.Subtract(DateTime.Now).TotalSeconds;
            var num = World.TheLucChien_MidTime = (int)kssjgj.Subtract(DateTime.Now).TotalSeconds;
            foreach (var value in World.allConnectedChars.Values)
                if (value.NhanVatToaDo_BanDo == 801)
                {
                    value.GuiDi_TheLucChien_TinTuc2();
                    value.GuiDi_TheLucChien_TinTuc();
                    if (value.CharacterPKMode == 0) value.SwitchPkMode(1);
                    if (World.AFKTLC != 1 || ((totalSeconds < 1050 || totalSeconds > 1100) &&
                                              (totalSeconds < 850 || totalSeconds > 900) &&
                                              (totalSeconds < 600 || totalSeconds > 650))) continue;
                    if (World.EventTop.TryGetValue(value.UserName, out var classc))
                    {
                        if (classc.GietNguoiSoLuong <= 1 && classc.TuVongSoLuong <= 5)
                        {
                            value.HeThongNhacNho("Quá thời gian quy định, bạn đang AFK", 22);
                            if (World.Newversion >= 14 && World.Newversion <= 15)
                                value.Mobile(-5f, -145f, 15f, 1201);
                            else
                                value.Mobile(-5f, -145f, 15f, 1201);
                        }
                    }
                    else
                    {
                        value.HeThongNhacNho("Quá thời gian quy định, bạn đang AFK", 22);
                        if (World.Newversion >= 14 && World.Newversion <= 15)
                            value.Mobile(-5f, -145f, 15f, 1201);
                        else
                            value.Mobile(-5f, -145f, 15f, 1201);
                    }
                }
                else if (DateTime.Now.Subtract(World.TheLucChien_ThoiGianChienDau).TotalSeconds <= 600.0 &&
                         value.Player_Job_level >= 2 && value.NhanVatToaDo_BanDo != 801)
                {
                    value.GuiDi_TheLucChien_LoiMoi_New2();
                }

            if (num <= 0)
            {
                ThoiGian2.Enabled = false;
                ThoiGian2.Close();
                ThoiGian2.Dispose();
                World.TheLucChien_Progress = 4;
                World.conn.Transmit("势力战进程|" + World.TheLucChien_Progress);
                ThoiGian3 = new System.Timers.Timer(10000.0);
                ThoiGian3.Elapsed += ThoiGianKetThucSuKien3;
                ThoiGian3.Enabled = true;
                ThoiGian3.AutoReset = false;
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Thế Lực Chiến ThoiGianKetThucSuKien2 error：" + ex);
        }
    }

    public void ThoiGianKetThucSuKien3(object sender, ElapsedEventArgs e)
    {
        try
        {
            foreach (var value in World.EventTop.Values)
                try
                {
                    var TenNhanVat = value.TenNhanVat;
                    var dBToDataTable =
                        DBA.GetDBToDataTable($"SELECT TenNhanVat from [EventTop] where TenNhanVat ='{TenNhanVat}'");
                    if (dBToDataTable != null && dBToDataTable.Rows.Count > 0)
                    {
                        DBA.ExeSqlCommand(string.Format(
                            "UPDATE EventTop SET BangPhai='{1}',DangCap={2},GietNguoiSoLuong=GietNguoiSoLuong+{3},TuVongSoLuong=TuVongSoLuong+{4},TongDiem={5} WHERE TenNhanVat='{0}'",
                            TenNhanVat, value.BangPhai, value.DangCap, value.GietNguoiSoLuong, value.TuVongSoLuong,
                            value.DiemSo));
                    }
                    else
                    {
                        var cmd =
                            $"INSERT INTO EventTop (TenNhanVat,BangPhai,TheLuc,DangCap,GietNguoiSoLuong,TuVongSoLuong, TongDiem)values('{TenNhanVat}','{value.BangPhai}','{value.TheLuc}',{value.DangCap},{value.GietNguoiSoLuong},{value.TuVongSoLuong},{value.DiemSo})";
                        DBA.ExeSqlCommand(cmd);
                    }

                    dBToDataTable.Dispose();
                    var players = World.KiemTra_Ten_NguoiChoi(TenNhanVat);
                    var int_ = 1;
                    if (players != null)
                    {
                        int_ = players.Player_Zx;
                        if (value.DiemSo > 0)
                        {
                            var slot = players.GetParcelVacancy(players);
                            if (slot != -1)
                            {
                                players.IncreaseItemWithAttributes(*********, slot, value.DiemSo, 0, 0, 0, 0, 0, 0, 0,
                                    0, 0, 0);
                                players.HeThongNhacNho($" {value.DiemSo} huy chương Thế Lực Chiến", 10, "Nhận được");
                            }
                            else
                            {
                                players.HeThongNhacNho("Túi đồ không còn chỗ trống!!", 7);
                            }
                        }
                    }

                    RxjhClass.Set_NguoiVinhDu_SoLieu(1, TenNhanVat, value.NgheNghiep, value.DangCap, int_,
                        value.BangPhai, string.Empty, value.GietNguoiSoLuong);
                }
                catch (Exception ex)
                {
                    Form1.WriteLine(1, "Thế Lực Chiến Set_NguoiVinhDu_SoLieu() error：" + ex.Message);
                }

            if (World.TheLucChien_ChinhPhai_DiemSo > World.TheLucChien_TaPhai_DiemSo)
            {
                TheLucChiensj = 1;
                jlsqlzj = "CHINH";
                World.GuiThongBao("Thế Lực Chiến kết thúc, Chính Phái dành chiến thắng");
            }
            else if (World.TheLucChien_ChinhPhai_DiemSo == World.TheLucChien_TaPhai_DiemSo)
            {
                TheLucChiensj = 3;
                jlsqlzj = string.Empty;
                World.GuiThongBao("Thế Lực Chiến kết thúc 2 bên hòa nhau");
            }
            else
            {
                TheLucChiensj = 2;
                jlsqlzj = "TA";
                World.GuiThongBao("Thế Lực Chiến kết thúc, Tà Phái dành chiến thắng");
            }

            World.TLCRewards();
            try
            {
                foreach (var value2 in World.EventTop.Values)
                    try
                    {
                        var NguoiChoi = World.FindPlayerbyName(value2.TenNhanVat);
                        if (NguoiChoi == null) continue;
                        NguoiChoi.GuiDi_TheLucChien_KetThuc_TinTuc(TheLucChiensj);
                        if (jlsqlzj != string.Empty)
                        {
                            if (NguoiChoi.TheLucChien_PhePhai == jlsqlzj)
                            {
                                var Rewards = ForceWarRewardsClass.GetRewards_By_WinLoseDraw(1);
                                ForceWarRewardsClass.Send_ForceWar_Rewards(Rewards, NguoiChoi);
                            }
                            else
                            {
                                var Rewards2 = ForceWarRewardsClass.GetRewards_By_WinLoseDraw(2);
                                ForceWarRewardsClass.Send_ForceWar_Rewards(Rewards2, NguoiChoi);
                            }
                        }
                        else
                        {
                            var Rewards3 = ForceWarRewardsClass.GetRewards_By_WinLoseDraw(3);
                            ForceWarRewardsClass.Send_ForceWar_Rewards(Rewards3, NguoiChoi);
                        }
                    }
                    catch
                    {
                        Form1.WriteLine(1, "Thế Lực Chiến Ban Thưởng Phạm Sai Lầm. Không tìm thấy đối tượng!!!");
                    }
            }
            catch (Exception ex2)
            {
                Form1.WriteLine(1, "Thế lực chiến Ban thưởng ngẫu nhiên hộp erro" + ex2.Message);
            }

            World.TheLucChien_Progress = 5;
            World.EventTop.Clear();
            World.conn.Transmit("势力战进程|" + World.TheLucChien_Progress);
            kssjgj = DateTime.Now.AddSeconds(600.0);
            ThoiGian3.Enabled = false;
            ThoiGian3.Close();
            ThoiGian3.Dispose();
            ThoiGian4 = new System.Timers.Timer(3000.0);
            ThoiGian4.Elapsed += ThoiGianKetThucSuKien4;
            ThoiGian4.Enabled = true;
            ThoiGian4.AutoReset = true;
            ThoiGian5 = new System.Timers.Timer(15000.0);
            ThoiGian5.Elapsed += ThoiGianKetThucSuKien5;
            ThoiGian5.Enabled = true;
            ThoiGian5.AutoReset = true;
        }
        catch (Exception ex3)
        {
            Form1.WriteLine(1, "Thế lực chiến ThoiGianKetThucSuKien3 error：" + ex3.Message);
        }
    }

    public void ThoiGianKetThucSuKien4(object sender, ElapsedEventArgs e)
    {
        try
        {
            var num = World.TheLucChien_MidTime = (int)kssjgj.Subtract(DateTime.Now).TotalSeconds;
            foreach (var value in World.allConnectedChars.Values)
            {
                if (value.NhanVatToaDo_BanDo != 801) continue;
                value.GuiDi_TheLucChien_TinTuc();
                if (value.Player_Zx != TheLucChiensj && TheLucChiensj != 3)
                {
                    value.TheLucChien_PhePhai = string.Empty;
                    value.TheLucChien_SatNhan_SoLuong = 0;
                    value.TheLucChien_TuVong_SoLuong = 0;
                    value.SwitchPkMode(0);
                    if (World.TheLucChien_LienServer != 0)
                        value.Mobile(-5f, -145f, 15f, 1201);
                    else
                        value.Mobile(500f, 1550f, 15f, 101);
                }
            }

            if (num > 0) return;
            ThoiGian4.Enabled = false;
            ThoiGian4.Close();
            ThoiGian4.Dispose();
            World.TheLucChien_Progress = 6;
            World.conn.Transmit("势力战进程|" + World.TheLucChien_Progress);
            foreach (var value2 in World.allConnectedChars.Values)
                if (value2.NhanVatToaDo_BanDo == 801)
                {
                    value2.TheLucChien_PhePhai = string.Empty;
                    value2.TheLucChien_SatNhan_SoLuong = 0;
                    value2.TheLucChien_TuVong_SoLuong = 0;
                    value2.SwitchPkMode(0);
                    if (World.TheLucChien_LienServer != 0)
                        value2.Mobile(-5f, -145f, 15f, 1201);
                    else
                        value2.Mobile(450f, 1550f, 15f, 101);
                }

            Dispose();
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Thế lực chiến ThoiGianKetThucSuKien4 error：" + ex);
        }
    }

    public void ThoiGianKetThucSuKien5(object sender, ElapsedEventArgs e)
    {
        try
        {
            ThoiGian5.Enabled = false;
            ThoiGian5.Close();
            ThoiGian5.Dispose();
            Thread.Sleep(3000);
            World.AddNpc(15137, 0f, 0f, 801);
            Thread.Sleep(1000);
            World.AddNpc(15137, 10f, 0f, 801);
            Thread.Sleep(1000);
            World.AddNpc(15137, -10f, 0f, 801);
            Thread.Sleep(1000);
            World.AddNpc(15137, 15f, -5f, 801);
            Thread.Sleep(1000);
            World.AddNpc(15137, -15f, 5f, 801);
            foreach (var value4 in World.allConnectedChars.Values)
                if (value4.NhanVatToaDo_BanDo == 801)
                {
                    value4.HeThongNhacNho("Boss Thế Lực Chiến đã xuất hiện", 8);
                    value4.SwitchPkMode(0);
                }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Thế Lực Chiến ThoiGianKetThucSuKien5 error：" + ex);
        }
    }
}