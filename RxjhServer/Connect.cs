using System;
using System.IO;
using System.IO.Compression;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Timers;
using DNGuard;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace RxjhServer;

[SecureMethod]
public class Connect
{
    private readonly byte[] dataReceive;

    private Socket socket_0;
    private readonly System.Timers.Timer timer_0;

    public Connect()
    {
        dataReceive = new byte[102400];
        timer_0 = new System.Timers.Timer(5000.0);
        timer_0.Elapsed += timer_0_Elapsed;
        timer_0.AutoReset = true;
        timer_0.Enabled = true;
    }

    private void timer_0_Elapsed(object sender, ElapsedEventArgs e)
    {
        var num = 2;
        while (true)
        {
            switch (num)
            {
                case 1:
                    return;
                case 0:
                    Sestup();
                    num = 1;
                    continue;
            }

            if (!socket_0.Connected)
            {
                num = 0;
                continue;
            }

            return;
        }
    }

    public void Sestup()
    {
        try
        {
            var remoteEP = new IPEndPoint(IPAddress.Parse(World.AccountVerificationServerIP),
                World.AccountVerificationServerPort);
            socket_0 = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
            socket_0.BeginConnect(remoteEP, method_0, socket_0);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(2,
                "Kết nối số tài khoản nghiệm chứng Server phạm sai lầm " + World.AccountVerificationServerPort +
                " IP " + World.AccountVerificationServerIP + " " + ex.Message);
        }
    }

    public void Dispose()
    {
        if (timer_0 != null)
        {
            timer_0.Enabled = false;
            timer_0.Close();
            timer_0.Dispose();
        }

        try
        {
            socket_0.Shutdown(SocketShutdown.Both);
        }
        catch
        {
        }

        socket_0?.Close();
        socket_0 = null;
    }

    public void ReviewUserLogin()
    {
        try
        {
            var stringBuilder = new StringBuilder();
            foreach (var value9 in World.list.Values)
            {
                var value = "NULL";
                var value2 = 0;
                if (value9.TreoMay) value2 = 1;
                var value3 = 0;
                var value4 = string.Empty;
                var value5 = string.Empty;
                var value6 = 0;
                var value7 = string.Empty;
                var value8 = string.Empty;
                var players = World.KiemTra_PlayerWorld_ID(value9.WorldId);
                if (players != null)
                {
                    value = players.UserName;
                    value3 = players.OriginalServerSerialNumber;
                    value4 = players.OriginalServerIP;
                    value5 = players.OriginalServerPort.ToString();
                    value6 = players.OriginalServerID;
                    value7 = players.SilverCoinSquareServerIP;
                    value8 = players.SilverCoinSquareServerPort.ToString();
                }

                stringBuilder.Append(value9.Player.Userid);
                stringBuilder.Append("-");
                stringBuilder.Append(value9);
                stringBuilder.Append("-");
                stringBuilder.Append(value9.BindAccount);
                stringBuilder.Append("-");
                stringBuilder.Append(value2);
                stringBuilder.Append("-");
                stringBuilder.Append(value);
                stringBuilder.Append("-");
                stringBuilder.Append(value3);
                stringBuilder.Append("-");
                stringBuilder.Append(value4);
                stringBuilder.Append("-");
                stringBuilder.Append(value5);
                stringBuilder.Append("-");
                stringBuilder.Append(value6);
                stringBuilder.Append("-");
                stringBuilder.Append(value7);
                stringBuilder.Append("-");
                stringBuilder.Append(value8);
                stringBuilder.Append("-");
                stringBuilder.Append(value9.WorldId);
                stringBuilder.Append(",");
            }

            if (stringBuilder.Length > 0) stringBuilder.Remove(stringBuilder.Length - 1, 1);
            World.conn.Transmit("复查用户登陆|" + stringBuilder);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Review NguoiChoiDangNhap      error" + ex.Message);
        }
    }

    private void method_0(IAsyncResult iasyncResult_0)
    {
        try
        {
            ((Socket)iasyncResult_0.AsyncState).EndConnect(iasyncResult_0);
            try
            {
                Transmit("服务器连接登陆|" + World.ServerID + "|" + World.MaximumOnline + "|" + CRC32.GetEXECRC32());
                Form1.WriteLine(2,
                    "Số tài khoản Server kết nối thành công Cảng " + World.AccountVerificationServerPort + " IP " +
                    World.AccountVerificationServerIP);
                socket_0.BeginReceive(dataReceive, 0, dataReceive.Length, SocketFlags.None, OnReceiveData, this);
                Thread.Sleep(500);
                Transmit("更新服务器端口|" + World.ServerID + "|" + World.GameServerPort2);
                ReviewUserLogin();
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "Nghiệm chứng Server ConnectCallback Phạm sai lầm: " + ex.Message);
            }
        }
        catch (Exception ex2)
        {
            Form1.WriteLine(1, "Số tài khoản Server kết nối phạm sai lầm: " + ex2.Message);
        }
    }

    public virtual void OnReceiveData(IAsyncResult iasyncResult_0)
    {
        try
        {
            var num2 = socket_0.EndReceive(iasyncResult_0);
            if (num2 > 0 && socket_0.Connected)
            {
                ProcessDataReceived(dataReceive, num2);
                socket_0.BeginReceive(dataReceive, 0, dataReceive.Length, SocketFlags.None, OnReceiveData, this);
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Số tài khoản Server Tiếp thu phạm sai lầm: " + ex.Message);
        }
    }

    public static byte[] Compress(byte[] byte_0)
    {
        try
        {
            var memoryStream = new MemoryStream();
            var gZipStream = new GZipStream(memoryStream, CompressionMode.Compress, true);
            gZipStream.Write(byte_0, 0, byte_0.Length);
            gZipStream.Close();
            var array = new byte[memoryStream.Length];
            memoryStream.Position = 0L;
            memoryStream.Read(array, 0, array.Length);
            memoryStream.Close();
            return array;
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
    }

    public static string CompressString(string string_0)
    {
        return Convert.ToBase64String(Compress(Encoding.GetEncoding("UTF-8").GetBytes(string_0)));
    }

    public void Transmit(string string_0)
    {
        try
        {
            if (socket_0 != null && socket_0.Connected)
            {
                var bytes = Encoding.Default.GetBytes(string_0);
                Send(bytes, bytes.Length);
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Nghiệm chứng Server gửi đi phạm sai lầm: " + string_0 + ex.Message);
        }
    }

    public virtual void Send(byte[] byte_0, int int_0)
    {
        try
        {
            var array = new byte[int_0 + 6];
            array[0] = 204;
            array[1] = 153;
            System.Buffer.BlockCopy(Buffer.GetBytes(int_0), 0, array, 2, 4);
            System.Buffer.BlockCopy(byte_0, 0, array, 6, int_0);
            socket_0.BeginSend(array, 0, int_0 + 6, SocketFlags.None, OnSended2, this);
        }
        catch (SocketException ex)
        {
            Form1.WriteLine(1, "Số tài khoản Server Gửi đi phạm sai lầm 111: " + ex.Message);
        }
        catch (Exception ex2)
        {
            Form1.WriteLine(1, "Số tài khoản ServerGửi đi phạm sai lầm: " + ex2.Message);
        }
    }

    public void OnSended2(IAsyncResult iasyncResult_0)
    {
        try
        {
            socket_0.EndSend(iasyncResult_0);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Số tài khoản Server Gửi đi phạm sai lầm 222: " + ex.Message);
        }
    }

    public void ProcessDataReceived(byte[] byte_0, int int_0)
    {
        try
        {
            var num2 = 0;
            byte[] array = null;
            var num3 = 0;
            if (204 == byte_0[0] && 153 == byte_0[1])
            {
                array = new byte[4];
                System.Buffer.BlockCopy(byte_0, 2, array, 0, 4);
                num2 = Buffer.ToInt32(array, 0);
                if (int_0 < num2 + 6) return;
                while (true)
                {
                    var array2 = new byte[num2];
                    System.Buffer.BlockCopy(byte_0, num3 + 6, array2, 0, num2);
                    num3 += num2 + 6;
                    DataReceived(array2, num2);
                    if (num3 < int_0 && byte_0[num3] == 204 && byte_0[num3 + 1] == 153)
                    {
                        System.Buffer.BlockCopy(byte_0, num3 + 2, array, 0, 4);
                        num2 = Buffer.ToInt16(array, 0);
                        continue;
                    }

                    break;
                }
            }
            else
            {
                Form1.WriteLine(1, "LoginServer Sai bao: " + byte_0[0] + " " + byte_0[1]);
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Phạm sai lầm 111: " + ex.Message);
            Console.WriteLine(ex.Message);
            Console.WriteLine(ex.Source);
            Console.WriteLine(ex.StackTrace);
        }
    }

    public void DataReceived(byte[] byte_0, int int_0)
    {
        var @string = Encoding.Default.GetString(byte_0);
        try
        {
            var array = @string.Split('|');
            var text = array[0];
            if (text == null) return;
            if (text == "开启宝盒") MoRa_RuongVatPham(array[1], array[2], array[3]);
            if (text == "势力战掉线" && World.TheLucChien_Giam_NguoiChoi != null &&
                !World.TheLucChien_Giam_NguoiChoi.ContainsKey(array[1]))
                World.TheLucChien_Giam_NguoiChoi.Add(array[1], array[2]);
            if (text == "狮吼功") RoarPower(array[1], array[2], array[3]);
            if (text == "狮子吼")
            {
                NetState value2;
                if (Buffer.IsEquals(array[1], "OK"))
                    World.SendFullServerLionRoarMessageBroadcastData(int.Parse(array[2]), array[3], int.Parse(array[4]),
                        array[5], int.Parse(array[6]), int.Parse(array[7]), int.Parse(array[8]));
                else if (World.list.TryGetValue(int.Parse(array[2]), out value2))
                    value2.Player.HeThongNhacNho("Sư tử hống hàng chờ đang chạy.....");
            }

            if (text == "获取服务器列表")
            {
                var players4 = World.KiemTraNguoiChoi(array[1]);
                if (players4 != null)
                    for (var j = 2; j < array.Length - 1; j++)
                        players4.UpdateServerList(array[j]);
            }

            if (text == "PlayerGetServerList")
            {
                var players5 = World.KiemTraNguoiChoi(array[1]);
                if (players5 != null)
                    for (var k = 2; k < array.Length - 1; k++)
                        players5.UpdateServerList(array[k]);
            }

            if (text == "移除势力战掉线" && World.TheLucChien_Giam_NguoiChoi != null &&
                World.TheLucChien_Giam_NguoiChoi.ContainsKey(array[1]))
                World.TheLucChien_Giam_NguoiChoi.Remove(array[1]);
            if (text == "传音消息")
                World.GuiDi_TruyenAm_TinTuc(int.Parse(array[1]), array[2], array[3], array[4], int.Parse(array[5]),
                    array[6]);
            if (text == "PK提示") PKNhacNho(array[1], array[2]);
            if (text == "情侣提示") CoupleTips(array[1], array[2]);
            if (text == "更新配置") UpdateConfiguration(array[1], array[2]);
            if (text == "UpdateConfiguration") UpdateConfiguration(array[1], array[2]);
            if (text == "势力战进程") World.TheLucChien_Progress = int.Parse(array[1]);
            if (text == "势力战人数")
            {
                World.TheLucChien_ChinhPhai_SoNguoi = int.Parse(array[1]);
                World.TheLucChien_TaPhai_SoNguoi = int.Parse(array[2]);
            }

            if (text == "帮派消息")
            {
                var array2 = smethod_0(array[2]);
                World.SendGangMessage(array[1], array2, array2.Length);
            }

            if (text == "PVP")
                for (var i = 1; i < array.Length; i++)
                {
                    var players3 = World.KiemTra_Ten_NguoiChoi(array[i]);
                    if (players3 != null)
                    {
                        players3.FLD_VIP = 1;
                        players3.UpdateCharacterData(players3);
                    }
                }

            if (text == "刷怪掉宝") DanhQuai_RoiHop(array[1], array[2], array[3]);
            if (text == "用户踢出" && array[1].Length != 0) UserKicksOut(int.Parse(array[1]));
            if (text == "UserKicksOut" && array[1].Length != 0) UserKicksOut(int.Parse(array[1]));
            if (text == "帐号服务器断开连接" && socket_0 != null) socket_0.Close();
            if (text == "用户换线登陆")
            {
                Form1.WriteLine(1, "收到LS：" + @string);
                UserSwitchChannelLogin(int.Parse(array[4]), array[1], array[2], array[3], array[7], array[8], array[9],
                    array[10], array[11], array[12], array[13], array[6], array[14]);
            }

            if (text == "UserSwitchChannelLogin")
            {
                Form1.WriteLine(1, "Received from Login Server：" + @string);
                UserSwitchChannelLogin(int.Parse(array[4]), array[1], array[2], array[3], array[7], array[8], array[9],
                    array[10], array[11], array[12], array[13], array[6], array[14]);
            }

            if (text == "用户登陆")
                NguoiChoiDangNhap(int.Parse(array[2]), array[1], array[3], array[4], array[5], array[6], array[7],
                    array[8], array[9]);
            if (text == "OpClient")
                try
                {
                    var players2 = World.KiemTra_PlayerWorld_ID(int.Parse(array[1]));
                    NetState value;
                    if (players2 != null)
                    {
                        if (players2.Client != null) players2.OpClient(int.Parse(array[2]));
                    }
                    else if (World.list.TryGetValue(int.Parse(array[1]), out value))
                    {
                        if (value.Player != null)
                        {
                            value.Player.OpClient(int.Parse(array[2]));
                        }
                        else
                        {
                            var array3 = Converter.HexStringToByte("AA5512000100BB00040001000000000000000000000055AA");
                            System.Buffer.BlockCopy(Buffer.GetBytes(int.Parse(array[2])), 0, array3, 10, 2);
                            System.Buffer.BlockCopy(Buffer.GetBytes(int.Parse(array[1])), 0, array3, 4, 2);
                            value.Send_Map_Data(array3, array3.Length);
                        }
                    }
                }
                catch
                {
                }

            if (text == "发送公告") GuiThongBao(int.Parse(array[1]), array[2]);
            if (text == "用户踢出ID" && array[1].Length != 0) UserKicksOutID(array[1]);
            if (text == "TLCInvitation" && array[1].Length != 0)
                foreach (var players in World.allConnectedChars.Values)
                    if (players.Player_Job_level >= 2 && players.NhanVatToaDo_BanDo != 801)
                        players.GuiDi_TheLucChien_LoiMoi_New2();
            if (text == "TheLucChienStarting" && array[1].Length != 0)
            {
                Form1.WriteLine(4, "The Luc Chien Lien Server Starting!");
                if (World.TheLucChien_LienServer != 0)
                {
                    Form1.WriteLine(4, "The Luc Chien Lien Server Starting 2!");
                    World.TheLucChien_LienServer_Starting = 1;
                    World.TheLucChien_MoRa_Gio = DateTime.Now.Hour;
                    World.TheLucChien_MoRa_Phut = DateTime.Now.Minute;
                    World.TheLucChien_MoRa_Giay = DateTime.Now.Second;
                }
            }

            if (text == "TheLucChienEnd" && array[1].Length != 0 && World.TheLucChien_LienServer != 0)
            {
                World.TheLucChien_LienServer_Starting = 0;
                World.TheLucChien_MoRa_Gio =
                    int.Parse(Config.IniReadValue("GameServer", "TheLucChien_MoRa_Gio").Trim());
                World.TheLucChien_MoRa_Phut =
                    int.Parse(Config.IniReadValue("GameServer", "TheLucChien_MoRa_Phut").Trim());
                World.TheLucChien_MoRa_Giay =
                    int.Parse(Config.IniReadValue("GameServer", "TheLucChien_MoRa_Giay").Trim());
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Nghiệm chứng Server tiếp thu phạm sai lầm:：" + @string + ex.Message);
        }
    }

    private static byte[] smethod_0(string string_0)
    {
        byte[] array = null;
        var num2 = 0;
        string_0 = string_0.Replace("      ", string.Empty);
        if (string_0.Length % 2 != 0) string_0 += "      ";
        array = new byte[string_0.Length / 2];
        for (num2 = 0; num2 < array.Length; num2++) array[num2] = Convert.ToByte(string_0.Substring(num2 * 2, 2), 16);
        return array;
    }

    public void UpdateConfiguration(string string_0, string string_1)
    {
        var players = World.KiemTraNguoiChoi(string_0);
        if (players != null)
        {
            if (players.ShortcutBar.Contains(1008000044)) players.CharactersToAddMax_HP += 300;
            if (players.ShortcutBar.Contains(1008000045)) players.CharactersToAddMax_MP += 200;
            if (!players.WhetherToUpdateTheConfiguration)
            {
                var array = Converter.HexStringToByte(string_1);
                System.Buffer.BlockCopy(Buffer.GetBytes(players.CharacterFullServerID), 0, array, 4, 2);
                players.ChangeTheLineToUpdateTheConfiguration(array, array.Length);
            }

            players.ClientSettings = string_1;
            players.CapNhat_HP_MP_SP();
        }
    }

    public void NguoiChoiDangNhap(int int_0, string string_0, string string_1, string string_2, string string_3,
        string string_4, string string_5, string string_6, string string_7)
    {
        Form1.WriteLine(1, "Nguoi Choi Dang Nhap");
        try
        {
            NetState value = null;
            if (World.list.TryGetValue(int_0, out value))
                value.Player.KetNoi_DangNhap2(string_0, string_1, string_2, string_3, string_4, string_5, string_6,
                    string_7);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Nghiệm chứng Server người sử dụng đăng lục phạm sai lầm: " + ex.Message);
        }
    }

    public void UserSwitchChannelLogin(int int_0, string string_0, string string_1, string string_2, string string_3,
        string string_4, string string_5, string string_6, string string_7, string string_8, string string_9,
        string string_10, string string_11)
    {
        try
        {
            NetState value = null;
            if (World.list.TryGetValue(int_0, out value))
                value.Player.SwitchChannelAccountLogin(string_0, int.Parse(string_1), int.Parse(string_2), int_0,
                    string_3, string_4, string_5, string_6, string_7, string_8, string_9, string_10, string_11);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Nghiệm chứng người sử dụng đô\u0309i tuyến đăng lục: " + ex.Message);
        }
    }

    public void RoarPower(string string_0, string string_1, string string_2)
    {
        try
        {
            var enumerator = World.allConnectedChars.Values.GetEnumerator();
            try
            {
                while (enumerator.MoveNext())
                    enumerator.Current?.HeThongNhacNho(string_1, 21, string_0 + ": Kênh: " + string_2 + "Tuyến");
            }
            finally
            {
                var num2 = 2;
                while (true)
                {
                    switch (num2)
                    {
                        case 1:
                            break;
                        case 0:
                            enumerator.Dispose();
                            num2 = 1;
                            continue;
                        default:
                            if (enumerator != null)
                            {
                                num2 = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Gửi đi sư hống công phạm sai lầm: " + ex.Message);
        }
    }

    public void DanhQuai_RoiHop(string string_0, string string_1, string string_2)
    {
        try
        {
            World.ToanCucNhacNho("DanhQuai_RoiHop:line:" + string_2 + "line", 22, string_1);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Gửi đi cày quái rơi bảo phạm sai lầm 111: " + ex.Message);
        }
    }

    public void CoupleTips(string string_0, string string_1)
    {
        try
        {
            World.ToanCucNhacNho("Couple NhacNho:", 20, string_1);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "TransmitDanhQuai_RoiHoperror:: " + ex.Message);
        }
    }

    public void PKNhacNho(string string_0, string string_1)
    {
        try
        {
            World.ToanCucNhacNho("PKNhacNho:", 24, string_1);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Gửi đi cày quái rơi bảo phạm sai lầm 333: " + ex.Message);
        }
    }

    public void MoRa_RuongVatPham(string string_0, string string_1, string string_2)
    {
        try
        {
            World.ToanCucNhacNho("MoRa_RuongVatPham:line:" + string_2 + "line", 22, string_1);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Gửi đi mở ra bảo hạp phạm sai lầm: " + ex.Message);
        }
    }

    public void GuiThongBao(int int_0, string string_0)
    {
        try
        {
            var enumerator = World.allConnectedChars.Values.GetEnumerator();
            try
            {
                Players players = null;
                while (enumerator.MoveNext())
                {
                    players = enumerator.Current;
                    if (players != null)
                        switch (int_0)
                        {
                            case 0:
                                players.SystemNotification(string_0);
                                break;
                            case 1:
                                players.SystemRollingAnnouncement(string_0);
                                break;
                            case 2:
                                players.HeThongNhacNho(string_0, 10, ":");
                                break;
                        }
                }
            }
            finally
            {
                var num2 = 0;
                while (true)
                {
                    switch (num2)
                    {
                        case 1:
                            break;
                        default:
                            if (enumerator != null)
                            {
                                num2 = 2;
                                continue;
                            }

                            break;
                        case 2:
                            enumerator.Dispose();
                            num2 = 1;
                            continue;
                    }

                    break;
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Gửi đi thông cáo phạm sai lầm 111: " + ex.Message);
        }
    }

    public void UserKicksOut(int int_0)
    {
        try
        {
            NetState value = null;
            var players = World.KiemTra_PlayerWorld_ID(int_0);
            var text = string.Empty;
            var text2 = string.Empty;
            var text3 = string.Empty;
            if (players != null)
            {
                text = players.Userid;
                text2 = players.UserName;
                text3 = players.Client.ToString();
                if (players.Client.TreoMay)
                {
                    players.Client.DisposedOffline();
                    World.OffLine_SoLuong--;
                    if (World.OffLine_SoLuong < 0) World.OffLine_SoLuong = 0;
                }
                else
                {
                    players.kickidlog("用户踢出()");
                    players.OpClient(1);
                    players.Logout();
                    players.Client.Dispose();
                }
            }

            if (World.list.TryGetValue(int_0, out value))
            {
                value.delWorldIdd(int_0);
                value.Dispose();
            }

            Form1.WriteLine(3, "Người sử dụng bị đá ra [" + text + "]-[" + text2 + "]" + text3);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Nghiệm chứng Server người sử dụng đá ra phạm sai lầm: " + int_0 + " " + ex.Message);
        }
    }

    public void UserKicksOutID(string string_0)
    {
        try
        {
            var players = World.KiemTraNguoiChoi(string_0);
            if (players == null) return;
            if (players.Client.TreoMay || players.Auto_Offline)
            {
                players.Client.DisposedOffline();
                World.OffLine_SoLuong--;
                if (World.OffLine_SoLuong < 0) World.OffLine_SoLuong = 0;
            }
            //else if  (players.Client.ToString() == "127.0.0.1 "){
            //    players.Client.DisposedOffline();
            //    World.OffLine_SoLuong--;
            //    if (World.OffLine_SoLuong < 0) World.OffLine_SoLuong = 0;
            //     players.OpClient(1);
            //    players.kickidlog("Player repeats Login      -      UserKicksOutID()");
            //    players.Logout();
            //    players.Client.DisposedOffline();
            //    players.Client.Dispose();
            //}
            else
            {
               
                players.OpClient(1);
                players.kickidlog("Player repeats Login      -      UserKicksOutID()");
                players.Logout();
                players.Client.DisposedOffline();
                players.Client.Dispose();
            }

            World.conn.Transmit("ServerID|" + World.ServerID + "|" + string_0);
            Form1.WriteLine(3,
                "Player repeats Login      -      UserKicksOutID [" + players.Userid + "]-[" + players.UserName + "]" +
                players.Client);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Authentication server UserKicksOuterror: " + string_0 + " " + ex.Message);
        }
    }
}