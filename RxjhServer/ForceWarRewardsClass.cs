namespace RxjhServer;

public class ForceWarRewardsClass
{
    public int ID { get; set; }

    public string Reward_Name { get; set; }

    public int Top { get; set; }

    public int Force { get; set; }

    public int Bonus_VoHuan { get; set; }

    public int Bonus_Gold { get; set; }

    public int Bonus_VoHoang { get; set; }

    public int Bonus_Cash { get; set; }

    public int Win_Lose_Draw { get; set; }

    public string Bonus_Item { get; set; }

    public static ForceWarRewardsClass GetRewards_By_WinLoseDraw(int WLD)
    {
        foreach (var reward in World.List_ForceWarRewards.Values)
            if (WLD == reward.Win_Lose_Draw)
                return reward;
        return null;
    }

    public static ForceWarRewardsClass GetRewards_By_Top(int rank, int force)
    {
        foreach (var reward in World.List_ForceWarRewards.Values)
            if (reward.Win_Lose_Draw == 0 && reward.Top == rank && reward.Force == force)
                return reward;
        return null;
    }

    public static void Send_ForceWar_Rewards(ForceWarRewardsClass reward, Players value2)
    {
        Form1.WriteLine(3,
            "ADD Quà TLC : " + value2.UserName + "|" + reward.Bonus_VoHoang + "|" + reward.Bonus_VoHuan + "|" +
            reward.Bonus_Gold + "|" + reward.Bonus_Cash + "|" + reward.Bonus_Item);
        if (reward.Bonus_VoHoang != 0)
        {
            value2.Player_VoHoang += reward.Bonus_VoHoang;
            value2.HeThongNhacNho("Chúc mừng bạn nhận được [" + reward.Bonus_VoHoang + "] Võ hoàng!!!", 22);
        }

        if (reward.Bonus_Gold != 0)
        {
            value2.Player_Money += reward.Bonus_Gold;
            value2.HeThongNhacNho("Chúc mừng bạn nhận được [" + reward.Bonus_Gold + "] lượng!!!", 22);
        }

        if (reward.Bonus_VoHuan != 0)
        {
            value2.Player_WuXun += reward.Bonus_VoHuan;
            value2.HeThongNhacNho("Bạn nhận được " + reward.Bonus_VoHuan + " võ huân", 22, "TLC");
        }

        if (reward.Bonus_Cash != 0)
        {
            value2.KiemSoatNguyenBao_SoLuong(reward.Bonus_Cash, 1);
            value2.HeThongNhacNho("Chúc mừng bạn nhận được [" + reward.Bonus_Cash + "] Cash!!!", 22);
            value2.Save_NguyenBaoData();
        }

        if (reward.Bonus_Item != "")
            try
            {
                var strItem = reward.Bonus_Item.Split(';');
                for (var i = 0; i < strItem.Length; i++)
                {
                    var slot = value2.GetParcelVacancy(value2);
                    if (slot != -1)
                    {
                        var item = strItem[i].Split(',');
                        value2.IncreaseItemWithAttributes(int.Parse(item[0]), slot, int.Parse(item[1]),
                            int.Parse(item[2]), int.Parse(item[3]), int.Parse(item[4]), int.Parse(item[5]),
                            int.Parse(item[6]), int.Parse(item[7]), int.Parse(item[8]), int.Parse(item[9]),
                            int.Parse(item[10]), int.Parse(item[11]));
                        value2.HeThongNhacNho1258(
                            "Baòn nhâòn ðýõòc vâòt phâÒm " + ItmeClass.DatDuocVatPhamTen_XungHao(int.Parse(item[0])),
                            22, "TLC");
                    }
                    else
                    {
                        value2.HeThongNhacNho("Không còn đủ chỗ trống", 10, "Túi đồ");
                    }
                }
            }
            catch
            {
            }

        value2.UpdateMartialArtsAndStatus();
        value2.UpdateMoneyAndWeight();
        value2.CapNhat_HP_MP_SP();
        value2.Update_Item_In_Bag();
    }
}