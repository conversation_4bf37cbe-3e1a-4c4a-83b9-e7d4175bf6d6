using System;
using System.Linq;
using System.Timers;
using RxjhServer.DbClss;

namespace RxjhServer;

public class WorldBossClass : IDisposable
{
    private int announce;

    private DateTime time_announce;

    private DateTime time_end;

    private int time_end_int;

    private DateTime time_start;

    private int time_start_int;

    private DateTime time_wait;

    private int time_wait_int;

    private System.Timers.Timer TimeEndWorldBoss;

    private System.Timers.Timer TimeStartWorldBoss;
    private System.Timers.Timer TimeWaitWorldBoss;

    private int TraoQuaWorldBoss;

    public WorldBossClass()
    {
        try
        {
            time_announce = DateTime.Now;
            announce = World.WorldBoss_ThoiGianCho;
            World.WorldBoss_LastHitName = "";
            World.WorldBoss_BossStatus = 0;
            World.List_WorldBossTop.Clear();
            DBA.ExeSqlCommand("DELETE WorldBossTop");
            TraoQuaWorldBoss = 0;
            foreach (var value in World.allConnectedChars.Values)
                value.HeThongNhacNho("<PERSON> Thế Giới sẽ xuất hiện lúc 21:30 tại bản đồ <PERSON>ợ (!move tttb)", 8);
            announce--;
            time_wait = DateTime.Now.AddMinutes(World.WorldBoss_ThoiGianCho);
            World.WorldBoss_Process = 1;
            TimeWaitWorldBoss = new System.Timers.Timer(1000.0);
            TimeWaitWorldBoss.Elapsed += WorldBossEvent_TimeWait;
            TimeWaitWorldBoss.Enabled = true;
            TimeWaitWorldBoss.AutoReset = true;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "WorldBoss EventClass error：" + ex);
        }
    }

    public void Dispose()
    {
        try
        {
            Form1.WriteLine(2, "Boss Thế Giới Kết Thúc!!!!");
            World.WorldBoss_Process = 0;
            World.WorldBoss_LastHitName = "";
            World.WorldBoss_BossStatus = 0;
            TraoQuaWorldBoss = 0;
            if (TimeWaitWorldBoss != null)
            {
                TimeWaitWorldBoss.Enabled = false;
                TimeWaitWorldBoss.Close();
                TimeWaitWorldBoss.Dispose();
                TimeWaitWorldBoss = null;
            }

            if (TimeStartWorldBoss != null)
            {
                TimeStartWorldBoss.Enabled = false;
                TimeStartWorldBoss.Close();
                TimeStartWorldBoss.Dispose();
                TimeStartWorldBoss = null;
            }

            if (TimeEndWorldBoss != null)
            {
                TimeEndWorldBoss.Enabled = false;
                TimeEndWorldBoss.Close();
                TimeEndWorldBoss.Dispose();
                TimeEndWorldBoss = null;
            }

            if (World.WorldBossEvent != null) World.WorldBossEvent = null;
            World.delNpc(World.WorldBoss_MapID, World.WorldBoss_BossID);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Thế lực chiến Dispose() error：" + ex);
        }
    }

    public void WorldBossEvent_TimeWait(object sender, ElapsedEventArgs e)
    {
        try
        {
            time_wait_int = (int)time_wait.Subtract(DateTime.Now).TotalMilliseconds;
            if (DateTime.Now.Subtract(time_announce).TotalSeconds > 60.0 && announce > 0)
            {
                foreach (var value2 in World.allConnectedChars.Values)
                    value2.HeThongNhacNho("Boss Thế Giới sẽ xuất hiện lúc 21:30 tại bản đồ Chợ (!move tttb)", 8);
                announce--;
                time_announce = DateTime.Now;
            }

            if (time_wait_int > 0) return;
            TimeWaitWorldBoss.Enabled = false;
            TimeWaitWorldBoss.Close();
            TimeWaitWorldBoss.Dispose();
            time_start = DateTime.Now.AddMinutes(World.WorldBoss_ThoiGianKeoDai);
            World.WorldBoss_Process = 2;
            TimeStartWorldBoss = new System.Timers.Timer(1000.0);
            TimeStartWorldBoss.Elapsed += WorldBossEvent_TimeStart;
            TimeStartWorldBoss.Enabled = true;
            TimeStartWorldBoss.AutoReset = true;
            foreach (var value in World.allConnectedChars.Values)
                if (value.NhanVatToaDo_BanDo == World.WorldBoss_MapID)
                {
                    value.HeThongNhacNho("Boss Thế Giới đã xuất hiện, hãy tập trung tiêu diệt", 8);
                    value.GuiDi_TheLucChien_DemNguoc(World.WorldBoss_ThoiGianKeoDai * 60);
                }

            World.AddNpc(World.WorldBoss_BossID, 283f, -465f, World.WorldBoss_MapID);
            World.WorldBoss_BossStatus = 1;
            time_announce = DateTime.Now;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "WorldBossEvent_TimeWait error：" + ex);
        }
    }

    public void WorldBossEvent_TimeStart(object sender, ElapsedEventArgs e)
    {
        try
        {
            time_start_int = (int)time_start.Subtract(DateTime.Now).TotalMilliseconds;
            if (DateTime.Now.Subtract(time_announce).TotalSeconds >= 30.0)
            {
                time_announce = DateTime.Now;
                var RankWorldBoss = World.List_WorldBossTop.Values.OrderByDescending(p => p.TongSatThuong);
                foreach (var value3 in World.allConnectedChars.Values)
                {
                    var hang = 0;
                    var satthuong = 0L;
                    if (value3.NhanVatToaDo_BanDo == World.WorldBoss_MapID)
                    {
                        var i = 0;
                        foreach (var rank in RankWorldBoss)
                        {
                            i++;
                            if (i <= 5)
                                value3.HeThongNhacNho(i + " - DMG: " + rank.TongSatThuong + " - " + rank.UserName, 10,
                                    "TOP");
                            if (rank.UserName == value3.UserName)
                            {
                                hang = i;
                                satthuong = rank.TongSatThuong;
                            }
                        }
                    }

                    value3.HeThongNhacNho(hang + " - DMG: " + satthuong, 23, "Xếp hạng");
                }

                World.CapNhatDuLieu_WorldBossTop_VaoDatabase();
            }

            if (time_start_int <= 0)
            {
                foreach (var value in World.allConnectedChars.Values)
                    if (value.NhanVatToaDo_BanDo == World.WorldBoss_MapID)
                    {
                        value.HeThongNhacNho(
                            "Boss Thế Giới vẫn chưa bị hạ gục, kết thúc sau " + World.WorldBoss_ThoiGianKetThuc +
                            " phút", 8);
                        value.GuiDi_TheLucChien_DemNguoc(World.WorldBoss_ThoiGianKetThuc * 60);
                    }

                TimeStartWorldBoss.Enabled = false;
                TimeStartWorldBoss.Close();
                TimeStartWorldBoss.Dispose();
                time_end = DateTime.Now.AddMinutes(World.WorldBoss_ThoiGianKetThuc);
                World.WorldBoss_Process = 3;
                TimeEndWorldBoss = new System.Timers.Timer(1000.0);
                TimeEndWorldBoss.Elapsed += WorldBossEvent_TimeEnd;
                TimeEndWorldBoss.Enabled = true;
                TimeEndWorldBoss.AutoReset = true;
                World.CapNhatDuLieu_WorldBossTop_VaoDatabase();
            }
            else
            {
                if (!(World.WorldBoss_LastHitName != "") || World.WorldBoss_BossStatus != -1) return;
                foreach (var value2 in World.allConnectedChars.Values)
                    if (value2.NhanVatToaDo_BanDo == World.WorldBoss_MapID)
                    {
                        value2.HeThongNhacNho("Boss Thế Giới đã bị " + World.WorldBoss_LastHitName + " hạ gục", 8);
                        value2.GuiDi_TheLucChien_DemNguoc(World.WorldBoss_ThoiGianKetThuc * 60);
                    }

                TimeStartWorldBoss.Enabled = false;
                TimeStartWorldBoss.Close();
                TimeStartWorldBoss.Dispose();
                time_end = DateTime.Now.AddMinutes(World.WorldBoss_ThoiGianKetThuc);
                World.WorldBoss_Process = 3;
                TimeEndWorldBoss = new System.Timers.Timer(1000.0);
                TimeEndWorldBoss.Elapsed += WorldBossEvent_TimeEnd;
                TimeEndWorldBoss.Enabled = true;
                TimeEndWorldBoss.AutoReset = true;
                World.CapNhatDuLieu_WorldBossTop_VaoDatabase();
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "WorldBossEvent_TimeWait error：" + ex);
        }
    }

    public void WorldBossEvent_TimeEnd(object sender, ElapsedEventArgs e)
    {
        time_end_int = (int)time_end.Subtract(DateTime.Now).TotalMilliseconds;
        if (TraoQuaWorldBoss == 0)
        {
            TraoQuaWorldBoss = 1;
            DBA.ExeSqlCommand(string.Format("UPDATE WorldBossTop SET LastHit={1}  WHERE TenNhanVat='{0}'",
                World.WorldBoss_LastHitName, 1));
            if (World.WorldBoss_LastHitName != "")
            {
                var player = World.KiemTra_Ten_NguoiChoi(World.WorldBoss_LastHitName);
                if (player != null)
                {
                    var slot = player.GetParcelVacancy(player);
                    if (slot != -1)
                    {
                        player.IncreaseItemWithAttributes(World.WorldBoss_PhanThuong_LastHitPID, slot, 1, 0, 0, 0, 0, 0,
                            0, 0, 0, 0, 0);
                        player.HeThongNhacNho("Phần quà kết liễu Boss Thế Giới", 10, "Nhận được");
                    }
                    else
                    {
                        player.HeThongNhacNho("Không còn chỗ trống, không thể nhận phần thưởng", 10, "Túi đồ");
                    }
                }
            }

            World.WorldBoss_TraoThuong();
        }

        if (time_end_int <= 0)
        {
            TimeEndWorldBoss.Enabled = false;
            TimeEndWorldBoss.Close();
            TimeEndWorldBoss.Dispose();
            Dispose();
        }
    }
}