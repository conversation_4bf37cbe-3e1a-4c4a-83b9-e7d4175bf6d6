using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using System.Windows.Forms;
using ns0;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;
using RxjhServer.Network;
using RxjhTool;
using YulgangServer.RxjhServer.YGZodiac;
using Listener = RxjhServer.Network.Listener;

namespace RxjhServer;

public class Form1 : Form
{
    private static List<TxtClass> list_0;

    private static int kjdx;

    private static World world;

    private static Thread timerThread;

    private System.Timers.Timer AutoConnectTimer;

    private IContainer components;

    private readonly DateTime dateTime_0 = DateTime.Now;

    private FlickerFreePanel GraphPanel;

    private ImageList imageList1;

    private int int_0 = 2;

    private System.Timers.Timer LionRoarTimer = new(21000.0);

    private Listener listener_0;

    private MainMenu mainMenu1;

    private MenuItem menuItem1;

    private MenuItem menuItem10;

    private MenuItem menuItem100;

    private MenuItem menuItem11;

    private MenuItem menuItem12;

    private MenuItem menuItem13;

    private MenuItem menuItem14;

    private MenuItem menuItem15;

    private MenuItem menuItem16;

    private MenuItem menuItem17;

    private MenuItem menuItem18;

    private MenuItem menuItem19;

    private MenuItem menuItem2;

    private MenuItem menuItem20;

    private MenuItem menuItem21;

    private MenuItem menuItem22;

    private MenuItem menuItem23;

    private MenuItem menuItem24;

    private MenuItem menuItem25;

    private MenuItem menuItem26;

    private MenuItem menuItem27;

    private MenuItem menuItem28;

    private MenuItem menuItem29;

    private MenuItem menuItem3;

    private MenuItem menuItem30;

    private MenuItem menuItem31;

    private MenuItem menuItem32;

    private MenuItem menuItem33;

    private MenuItem menuItem34;

    private MenuItem menuItem35;

    private MenuItem menuItem36;

    private MenuItem menuItem37;

    private MenuItem menuItem38;

    private MenuItem menuItem39;

    private MenuItem menuItem4;

    private MenuItem menuItem40;

    private MenuItem menuItem41;

    private MenuItem menuItem42;

    private MenuItem menuItem43;

    private MenuItem menuItem44;

    private MenuItem menuItem45;

    private MenuItem menuItem46;

    private MenuItem menuItem47;

    private MenuItem menuItem48;

    private MenuItem menuItem49;

    private MenuItem menuItem5;

    private MenuItem menuItem50;

    private MenuItem menuItem51;

    private MenuItem menuItem52;

    private MenuItem menuItem53;

    private MenuItem menuItem54;

    private MenuItem menuItem55;

    private MenuItem menuItem56;

    private MenuItem menuItem57;

    private MenuItem menuItem58;

    private MenuItem menuItem59;

    private MenuItem menuItem6;

    private MenuItem menuItem60;

    private MenuItem menuItem61;

    private MenuItem menuItem62;

    private MenuItem menuItem63;

    private MenuItem menuItem64;

    private MenuItem menuItem65;

    private MenuItem menuItem66;

    private MenuItem menuItem67;

    private MenuItem menuItem68;

    private MenuItem menuItem69;

    private MenuItem menuItem7;

    private MenuItem menuItem70;

    private MenuItem menuItem71;

    private MenuItem menuItem72;

    private MenuItem menuItem73;

    private MenuItem menuItem74;

    private MenuItem menuItem75;

    private MenuItem menuItem76;

    private MenuItem menuItem77;

    private MenuItem menuItem78;

    private MenuItem menuItem79;

    private MenuItem menuItem8;

    private MenuItem menuItem80;

    private MenuItem menuItem81;

    private MenuItem menuItem82;

    private MenuItem menuItem83;

    private MenuItem menuItem84;

    private MenuItem menuItem85;

    private MenuItem menuItem86;

    private MenuItem menuItem87;

    private MenuItem menuItem88;

    private MenuItem menuItem89;

    private MenuItem menuItem9;

    private MenuItem menuItem90;

    private MenuItem menuItem91;

    private MenuItem menuItem92;

    private MenuItem menuItem93;

    private MenuItem menuItem94;

    private MenuItem menuItem95;

    private MenuItem menuItem96;

    private MenuItem menuItem97;

    private MenuItem menuItem98;

    private MenuItem menuItem99;

    private bool runn;

    private StatusBar statusBar1;

    private StatusBarPanel statusBarPanel1;

    private StatusBarPanel statusBarPanel2;

    private StatusBarPanel statusBarPanel3;

    private StatusBarPanel statusBarPanel4;

    private Thread thThreadRead;

    private System.Windows.Forms.Timer timer1;

    private ToolStrip toolStrip1;

    private ToolStripButton toolStripButton1;

    private ToolStripButton toolStripButton2;

    private ToolStripButton toolStripButton3;

    private ToolStripButton toolStripButton4;

    private ToolStripComboBox toolStripComboBox1;

    private ToolStripComboBox toolStripComboBox2;

    private ToolStripSeparator toolStripSeparator1;

    private ToolStripSeparator toolStripSeparator2;

    private ToolStripTextBox toolStripTextBox1;

    private ToolStripTextBox toolStripTextBox2;
    private MenuItem menuItem101;
    private MenuItem menuItem102;
    private MenuItem menuItem103;
    private int TuDongThongBaoID;

    static Form1()
    {
        old_acctor_mc();
    }

    public Form1()
    {
        Show1.WriteLine(4, "Load Success !!!");
        InitializeComponent();
    }

    protected override void Dispose(bool disposing)
    {
        base.Dispose(disposing);
    }

       private void InitializeComponent()
    {
        components = new Container();
        statusBar1 = new StatusBar();
        statusBarPanel1 = new StatusBarPanel();
        statusBarPanel2 = new StatusBarPanel();
        statusBarPanel3 = new StatusBarPanel();
        statusBarPanel4 = new StatusBarPanel();
        imageList1 = new ImageList(components);
        mainMenu1 = new MainMenu(components);
        menuItem1 = new MenuItem();
        menuItem32 = new MenuItem();
        menuItem3 = new MenuItem();
        menuItem2 = new MenuItem();
        menuItem29 = new MenuItem();
        menuItem17 = new MenuItem();
        menuItem31 = new MenuItem();
        menuItem4 = new MenuItem();
        menuItem5 = new MenuItem();
        menuItem27 = new MenuItem();
        menuItem37 = new MenuItem();
        menuItem20 = new MenuItem();
        menuItem38 = new MenuItem();
        menuItem19 = new MenuItem();
        menuItem15 = new MenuItem();
        menuItem18 = new MenuItem();
        menuItem21 = new MenuItem();
        menuItem26 = new MenuItem();
        menuItem62 = new MenuItem();
        menuItem63 = new MenuItem();
        menuItem33 = new MenuItem();
        menuItem34 = new MenuItem();
        menuItem35 = new MenuItem();
        menuItem36 = new MenuItem();
        menuItem54 = new MenuItem();
        menuItem51 = new MenuItem();
        menuItem66 = new MenuItem();
        menuItem59 = new MenuItem();
        menuItem61 = new MenuItem();
        menuItem79 = new MenuItem();
        menuItem80 = new MenuItem();
        menuItem94 = new MenuItem();
        menuItem6 = new MenuItem();
        menuItem7 = new MenuItem();
        menuItem30 = new MenuItem();
        menuItem28 = new MenuItem();
        menuItem8 = new MenuItem();
        menuItem22 = new MenuItem();
        menuItem23 = new MenuItem();
        menuItem9 = new MenuItem();
        menuItem25 = new MenuItem();
        menuItem10 = new MenuItem();
        menuItem11 = new MenuItem();
        menuItem12 = new MenuItem();
        menuItem13 = new MenuItem();
        menuItem50 = new MenuItem();
        menuItem14 = new MenuItem();
        menuItem16 = new MenuItem();
        menuItem39 = new MenuItem();
        menuItem42 = new MenuItem();
        menuItem43 = new MenuItem();
        menuItem44 = new MenuItem();
        menuItem45 = new MenuItem();
        menuItem24 = new MenuItem();
        menuItem52 = new MenuItem();
        menuItem46 = new MenuItem();
        menuItem53 = new MenuItem();
        menuItem77 = new MenuItem();
        menuItem65 = new MenuItem();
        menuItem82 = new MenuItem();
        menuItem83 = new MenuItem();
        menuItem84 = new MenuItem();
        menuItem85 = new MenuItem();
        menuItem86 = new MenuItem();
        menuItem87 = new MenuItem();
        menuItem89 = new MenuItem();
        menuItem90 = new MenuItem();
        menuItem91 = new MenuItem();
        menuItem92 = new MenuItem();
        menuItem93 = new MenuItem();
        menuItem95 = new MenuItem();
        menuItem96 = new MenuItem();
        menuItem97 = new MenuItem();
        menuItem98 = new MenuItem();
        menuItem99 = new MenuItem();
        menuItem100 = new MenuItem();
        menuItem40 = new MenuItem();
        menuItem41 = new MenuItem();
        menuItem49 = new MenuItem();
        menuItem64 = new MenuItem();
        menuItem60 = new MenuItem();
        menuItem48 = new MenuItem();
        menuItem55 = new MenuItem();
        menuItem71 = new MenuItem();
        menuItem72 = new MenuItem();
        menuItem73 = new MenuItem();
        menuItem88 = new MenuItem();
        menuItem76 = new MenuItem();
        menuItem56 = new MenuItem();
        menuItem57 = new MenuItem();
        menuItem58 = new MenuItem();
        menuItem74 = new MenuItem();
        menuItem75 = new MenuItem();
        menuItem67 = new MenuItem();
        menuItem68 = new MenuItem();
        menuItem69 = new MenuItem();
        menuItem70 = new MenuItem();
        menuItem47 = new MenuItem();
        menuItem78 = new MenuItem();
        menuItem81 = new MenuItem();
        menuItem101 = new MenuItem();
        menuItem102 = new MenuItem();
        menuItem103 = new MenuItem();
        timer1 = new System.Windows.Forms.Timer(components);
        toolStrip1 = new ToolStrip();
        toolStripTextBox1 = new ToolStripTextBox();
        toolStripComboBox2 = new ToolStripComboBox();
        toolStripButton1 = new ToolStripButton();
        toolStripTextBox2 = new ToolStripTextBox();
        toolStripComboBox1 = new ToolStripComboBox();
        toolStripButton2 = new ToolStripButton();
        toolStripSeparator1 = new ToolStripSeparator();
        toolStripButton3 = new ToolStripButton();
        toolStripSeparator2 = new ToolStripSeparator();
        toolStripButton4 = new ToolStripButton();
        GraphPanel = new FlickerFreePanel();
        ((ISupportInitialize)statusBarPanel1).BeginInit();
        ((ISupportInitialize)statusBarPanel2).BeginInit();
        ((ISupportInitialize)statusBarPanel3).BeginInit();
        ((ISupportInitialize)statusBarPanel4).BeginInit();
        toolStrip1.SuspendLayout();
        SuspendLayout();
        statusBar1.Location = new Point(0, 307);
        statusBar1.Name = "statusBar1";
        statusBar1.Panels.AddRange(new StatusBarPanel[4]
            { statusBarPanel1, statusBarPanel2, statusBarPanel3, statusBarPanel4 });
        statusBar1.RightToLeft = RightToLeft.Yes;
        statusBar1.ShowPanels = true;
        statusBar1.Size = new Size(759, 27);
        statusBar1.TabIndex = 6;
        statusBar1.Text = "statusBar1";
        statusBarPanel1.Alignment = HorizontalAlignment.Right;
        statusBarPanel1.Name = "statusBarPanel1";
        statusBarPanel1.Text = "Connect 1000 Online 1000";
        statusBarPanel1.Width = 200;
        statusBarPanel2.Alignment = HorizontalAlignment.Right;
        statusBarPanel2.Name = "statusBarPanel2";
        statusBarPanel2.Width = 160;
        statusBarPanel3.Alignment = HorizontalAlignment.Right;
        statusBarPanel3.Name = "statusBarPanel3";
        statusBarPanel3.Width = 180;
        statusBarPanel4.Name = "statusBarPanel4";
        statusBarPanel4.Text = "statusBarPanel4";
        statusBarPanel4.Width = 120;
        imageList1.ColorDepth = ColorDepth.Depth8Bit;
        imageList1.ImageSize = new Size(16, 16);
        imageList1.TransparentColor = Color.Transparent;
        mainMenu1.MenuItems.AddRange(new MenuItem[10]
        {
            menuItem1, menuItem4, menuItem6, menuItem87, menuItem40, menuItem56, menuItem67, menuItem78, menuItem81,menuItem101
        });
        menuItem1.Index = 0;
        menuItem1.MenuItems.AddRange(new MenuItem[6]
            { menuItem32, menuItem3, menuItem2, menuItem29, menuItem17, menuItem31 });
        menuItem1.Text = "Service";
        menuItem32.Index = 0;
        menuItem32.Text = "Start login service";
        menuItem32.Click += menuItem32_Click;
        menuItem3.Index = 1;
        menuItem3.Text = "Stop login service";
        menuItem3.Click += menuItem3_Click;
        menuItem2.Index = 2;
        menuItem2.Text = "Stop the main service";
        menuItem2.Click += menuItem2_Click;
        menuItem29.Index = 3;
        menuItem29.Text = "Thread stop";
        menuItem29.Click += menuItem29_Click;
        menuItem17.Index = 4;
        menuItem17.Text = "Archive character";
        menuItem17.Click += menuItem17_Click;
        menuItem31.Index = 5;
        menuItem31.Text = "Archive all";
        menuItem31.Click += menuItem31_Click_1;
        menuItem4.Index = 1;
        menuItem4.MenuItems.AddRange(new MenuItem[23]
        {
            menuItem5, menuItem27, menuItem37, menuItem20, menuItem38, menuItem19, menuItem15, menuItem18, menuItem21,
            menuItem26,
            menuItem62, menuItem33, menuItem34, menuItem35, menuItem36, menuItem54, menuItem51, menuItem66, menuItem59,
            menuItem61,
            menuItem79, menuItem80, menuItem94
        });
        menuItem4.Text = "Điều Khiển";
        menuItem5.Index = 0;
        menuItem5.Text = "Người chơi";
        menuItem5.Click += menuItem5_Click;
        menuItem27.Index = 1;
        menuItem27.Text = "Lập nhóm";
        menuItem27.Click += menuItem27_Click;
        menuItem37.Index = 2;
        menuItem37.Text = "Các item dưới đất";
        menuItem37.Click += menuItem37_Click;
        menuItem20.Checked = true;
        menuItem20.Index = 3;
        menuItem20.Text = "Hiển thị bản ghi";
        menuItem20.Click += menuItem20_Click;
        menuItem38.Index = 4;
        menuItem38.Text = "Hiển thị Drop";
        menuItem38.Click += menuItem38_Click;
        menuItem19.Index = 5;
        menuItem19.Text = "Log";
        menuItem19.Click += menuItem19_Click;
        menuItem15.Index = 6;
        menuItem15.Text = "Ghi các packet";
        menuItem15.Click += menuItem15_Click;
        menuItem18.Index = 7;
        menuItem18.Text = "Máy chủ xác thực";
        menuItem18.Click += menuItem18_Click;
        menuItem21.Checked = true;
        menuItem21.Index = 8;
        menuItem21.Text = "Kiểm tra bản sao";
        menuItem21.Click += menuItem21_Click;
        menuItem26.Index = 9;
        menuItem26.Text = "Kiểm tra SQL";
        menuItem26.Click += menuItem26_Click;
        menuItem62.Index = 10;
        menuItem62.MenuItems.AddRange(new MenuItem[1] { menuItem63 });
        menuItem62.Text = "Xoá dữ liệu vùng mở bằng 1 click chuột";
        menuItem63.Index = 0;
        menuItem63.Text = "OK để dọn dẹp";
        menuItem63.Click += menuItem63_Click;
        menuItem33.Index = 11;
        menuItem33.Text = "Dọn dẹp dữ liệu ký tự";
        menuItem33.Click += menuItem33_Click;
        menuItem34.Index = 12;
        menuItem34.Text = "Dọn dẹp kho cá nhân";
        menuItem34.Click += menuItem34_Click;
        menuItem35.Index = 13;
        menuItem35.Text = "Dọn dẹp kho chung";
        menuItem35.Click += menuItem35_Click;
        menuItem36.Index = 14;
        menuItem36.Text = "Dọn dẹp dữ liệu băng đảng";
        menuItem36.Click += menuItem36_Click;
        menuItem54.Index = 15;
        menuItem54.Text = "Mở bản đồ đào tạo";
        menuItem54.Click += menuItem54_Click;
        menuItem51.Index = 16;
        menuItem51.Text = "Bắt đầu Công Thành Chiến";
        menuItem51.Click += menuItem51_Click;
        menuItem66.Index = 17;
        menuItem66.Text = "Mở bang chiến";
        menuItem66.Click += menuItem66_Click;
        menuItem59.Index = 18;
        menuItem59.Text = "Bắt đầu Thế Lực Chiến";
        menuItem59.Click += menuItem59_Click;
        menuItem61.Index = 19;
        menuItem61.Text = "Bắt đầu ChatEvent";
        menuItem61.Click += menuItem61_Click;
        menuItem79.Index = 20;
        menuItem79.Text = "Bắt đầu X2 Exp";
        menuItem79.Click += menuItem79_Click;
        menuItem80.Index = 21;
        menuItem80.Text = "Bắt Safe PK";
        menuItem80.Click += menuItem80_Click;
        menuItem94.Index = 22;
        menuItem94.Text = "Bật Tắt Thế Lực Chiến Random";
        menuItem94.Click += menuItem94_Click;
        menuItem6.Index = 2;
        menuItem6.MenuItems.AddRange(new MenuItem[31]
        {
            menuItem7, menuItem30, menuItem28, menuItem8, menuItem22, menuItem23, menuItem9, menuItem25, menuItem10,
            menuItem11,
            menuItem12, menuItem13, menuItem50, menuItem14, menuItem16, menuItem39, menuItem42, menuItem43, menuItem44,
            menuItem45,
            menuItem24, menuItem52, menuItem46, menuItem53, menuItem77, menuItem65, menuItem82, menuItem83, menuItem84,
            menuItem85,
            menuItem86
        });
        menuItem6.Text = "Reload";
        menuItem7.Index = 0;
        menuItem7.Text = "Tải lại tất cả config";
        menuItem7.Click += menuItem7_Click;
        menuItem30.Index = 1;
        menuItem30.Text = "Tải lại dữ liệu Skill";
        menuItem30.Click += menuItem30_Click;
        menuItem28.Index = 2;
        menuItem28.Text = "Tải lại từ cấm";
        menuItem28.Click += menuItem28_Click;
        menuItem8.Index = 3;
        menuItem8.Text = "Tải lại dữ liệu NPC (chỉ bấm ở k2)";
        menuItem8.Click += menuItem8_Click;
        menuItem22.Index = 4;
        menuItem22.Text = "Tải dữ liệu Monster (chỉ bấm ở K2)";
        menuItem22.Click += menuItem22_Click;
        menuItem23.Index = 5;
        menuItem23.Text = "Tải lại boss drop";
        menuItem23.Click += menuItem23_Click;
        menuItem9.Index = 6;
        menuItem9.Text = "Tải lại Item drop";
        menuItem9.Click += menuItem9_Click;
        menuItem25.Index = 7;
        menuItem25.Text = "Tải lại quái đặc biệt drop";
        menuItem25.Click += menuItem25_Click;
        menuItem10.Index = 8;
        menuItem10.Text = "Tải lại dữ liệu Hộp";
        menuItem10.Click += menuItem10_Click;
        menuItem11.Index = 9;
        menuItem11.Text = "Tải lại dữ liệu vật phẩm";
        menuItem11.Click += menuItem11_Click;
        menuItem12.Index = 10;
        menuItem12.Text = "Cập nhật NPC Shop";
        menuItem12.Click += menuItem12_Click;
        menuItem13.Index = 11;
        menuItem13.Text = "Cập nhật điểm dịch chuyển";
        menuItem13.Click += menuItem13_Click;
        menuItem50.Index = 12;
        menuItem50.Text = "Cập nhật bản đồ";
        menuItem50.Click += menuItem50_Click;
        menuItem14.Index = 13;
        menuItem14.Text = "Cập nhật thông báo có sẵn";
        menuItem14.Click += menuItem14_Click;
        menuItem16.Index = 14;
        menuItem16.Text = "Cập nhật file cấu hình .ini";
        menuItem16.Click += menuItem16_Click;
        menuItem39.Index = 15;
        menuItem39.Text = "Cập nhật Script";
        menuItem39.Click += menuItem39_Click;
        menuItem42.Index = 16;
        menuItem42.Text = "Cập nhật chế tạo vật phẩm";
        menuItem42.Click += menuItem42_Click;
        menuItem43.Index = 17;
        menuItem43.Text = "Cập nhật Webshop";
        menuItem43.Click += menuItem43_Click;
        menuItem44.Index = 18;
        menuItem44.Text = "Cập nhật các Gói Vật Phẩm";
        menuItem44.Click += menuItem44_Click;
        menuItem45.Index = 19;
        menuItem45.Text = "Cập nhật level nhân thưởng";
        menuItem45.Click += menuItem45_Click;
        menuItem24.Index = 20;
        menuItem24.Text = "Cập nhật Vật phẩm đổi quà";
        menuItem24.Click += menuItem24_Click;
        menuItem52.Index = 21;
        menuItem52.Text = "Cập nhật dữ liệu nhiệm vụ";
        menuItem52.Click += menuItem52_Click;
        menuItem46.Index = 22;
        menuItem46.Text = "Cập nhật Khí Công";
        menuItem46.Click += menuItem46_Click;
        menuItem53.Index = 23;
        menuItem53.Text = "Cập nhật các loại Ngọc";
        menuItem53.Click += menuItem53_Click;
        menuItem77.Index = 24;
        menuItem77.Text = "Cập nhật Bảng Tái Sinh";
        menuItem77.Click += menuItem77_Click;
        menuItem65.Index = 25;
        menuItem65.Text = "Cập nhật vật phẩm tái chế";
        menuItem65.Click += menuItem65_Click;
        menuItem82.Index = 26;
        menuItem82.Text = "Cập nhật Phần thưởng Giftcode ";
        menuItem82.Click += menuItem82_Click;
        menuItem83.Index = 27;
        menuItem83.Text = "Cập nhật Open Chest";
        menuItem83.Click += menuItem83_Click;
        menuItem84.Index = 28;
        menuItem84.Text = "Cập nhật ItemOption";
        menuItem84.Click += menuItem84_Click;
        menuItem85.Index = 29;
        menuItem85.Text = "Cập nhật Trade To Npc Rewards";
        menuItem85.Click += menuItem85_Click;
        menuItem86.Index = 30;
        menuItem86.Text = "Cập nhật phần thưởng hoạt động";
        menuItem86.Click += menuItem86_Click;
        menuItem87.Index = 3;
        menuItem87.MenuItems.AddRange(new MenuItem[11]
        {
            menuItem89, menuItem90, menuItem91, menuItem92, menuItem93, menuItem95, menuItem96, menuItem97, menuItem98,
            menuItem99,
            menuItem100
        });
        menuItem87.Text = "Reload New";
        menuItem89.Index = 0;
        menuItem89.Text = "List Magic Qigong";
        menuItem89.Click += menuItem89_Click;
        menuItem90.Index = 1;
        menuItem90.Text = "Load Set Trang bị Bonus";
        menuItem90.Click += menuItem90_Click;
        menuItem91.Index = 2;
        menuItem91.Text = "Load Tẩy luyện bảo châu";
        menuItem91.Click += menuItem91_Click;
        menuItem92.Index = 3;
        menuItem92.Text = "Load List Pill";
        menuItem92.Click += menuItem92_Click;
        menuItem93.Index = 4;
        menuItem93.Text = "Load List Pill Exchange";
        menuItem93.Click += menuItem93_Click;
        menuItem95.Index = 5;
        menuItem95.Text = "Load Upgrade Item";
        menuItem95.Click += menuItem95_Click;
        menuItem96.Index = 6;
        menuItem96.Text = "Load Phần Thưởng TLC";
        menuItem96.Click += menuItem96_Click;
        menuItem97.Index = 7;
        menuItem97.Text = "Load BossMap Setting";
        menuItem97.Click += menuItem97_Click;
        menuItem98.Index = 8;
        menuItem98.Text = "Load Random Map Position";
        menuItem98.Click += menuItem98_Click;
        menuItem99.Index = 9;
        menuItem99.Text = "Load Summon Boss";
        menuItem99.Click += menuItem99_Click;
        menuItem100.Index = 10;
        menuItem100.Text = "Load Phó Bản Hàng Ma";
        menuItem100.Click += menuItem100_Click;
        menuItem40.Index = 4;
        menuItem40.MenuItems.AddRange(new MenuItem[11]
        {
            menuItem41, menuItem49, menuItem64, menuItem60, menuItem48, menuItem55, menuItem71, menuItem72, menuItem73,
            menuItem88,
            menuItem76
        });
        menuItem40.Text = "tool";
        menuItem41.Index = 0;
        menuItem41.Text = "YBQ editor";
        menuItem41.Click += menuItem41_Click;
        menuItem49.Index = 1;
        menuItem49.Text = "YBI editor";
        menuItem49.Click += menuItem49_Click;
        menuItem64.Index = 2;
        menuItem64.Text = "Online GM tool";
        menuItem64.Click += menuItem64_Click;
        menuItem60.Index = 3;
        menuItem60.Text = "Tool đổi tên";
        menuItem60.Click += menuItem60_Click;
        menuItem48.Index = 4;
        menuItem48.Text = "Tool sửa thông báo trong game";
        menuItem48.Click += menuItem48_Click_1;
        menuItem55.Index = 5;
        menuItem55.Text = "Tool sửa Shop NPC";
        menuItem55.Click += menuItem55_Click;
        menuItem71.Index = 6;
        menuItem71.Text = "Tool sửa Item";
        menuItem71.Click += menuItem71_Click;
        menuItem72.Index = 7;
        menuItem72.Text = "Tool sửa Webshop";
        menuItem72.Click += menuItem72_Click;
        menuItem73.Index = 8;
        menuItem73.Text = "Tool Mở Hộp";
        menuItem73.Click += menuItem73_Click;
        menuItem88.Index = 9;
        menuItem88.Text = "Tool ADDITEM";
        menuItem88.Click += menuItem88_Click;
        menuItem76.Index = 10;
        menuItem76.Text = "Tạo giftcode";
        menuItem76.Click += menuItem76_Click;
        menuItem56.Index = 5;
        menuItem56.MenuItems.AddRange(new MenuItem[4] { menuItem57, menuItem58, menuItem74, menuItem75 });
        menuItem56.Text = "Set up";
        menuItem57.Index = 0;
        menuItem57.Text = "Network settings";
        menuItem57.Click += menuItem57_Click;
        menuItem58.Index = 1;
        menuItem58.Text = "Anti-CC setting";
        menuItem58.Click += menuItem58_Click;
        menuItem74.Index = 2;
        menuItem74.Text = "All members cross the line";
        menuItem74.Click += menuItem74_Click;
        menuItem75.Index = 3;
        menuItem75.Text = "Real-time mobile parameters";
        menuItem75.Click += menuItem75_Click;
        menuItem67.Index = 6;
        menuItem67.MenuItems.AddRange(new MenuItem[4] { menuItem68, menuItem69, menuItem70, menuItem47 });
        menuItem67.Text = "Database tools";
        menuItem68.Index = 0;
        menuItem68.Text = "Qigong control";
        menuItem68.Click += menuItem68_Click;
        menuItem69.Index = 1;
        menuItem69.Text = "Stone control";
        menuItem69.Click += menuItem69_Click;
        menuItem70.Index = 2;
        menuItem70.Text = "NPC control";
        menuItem70.Click += menuItem70_Click;
        menuItem47.Index = 3;
        menuItem47.Text = "Monster spawning control";
        menuItem47.Click += menuItem47_Click;
        menuItem78.Index = 7;
        menuItem78.Text = "Chat in Game";
        menuItem78.Click += menuItem78_Click;
        menuItem81.Index = 8;
        menuItem81.Text = "Zodiac Tool";
        menuItem81.Click += menuItem81_Click;

        this.menuItem101.Index = 9;
        this.menuItem101.MenuItems.AddRange(new System.Windows.Forms.MenuItem[] {
        this.menuItem102,
        this.menuItem103});
        this.menuItem101.Text = "Auto";
        // 
        // menuItem102
        // 
        this.menuItem102.Index = 0;
        this.menuItem102.Text = "LoadOffAttack";
           this.menuItem102.Click += menuItem102_Click;
        // 
        // menuItem103
        // 
        this.menuItem103.Index = 1;
        this.menuItem103.Text = "AutoParty";
         this.menuItem103.Click += menuItem103_Click;

        timer1.Enabled = true;
        timer1.Interval = 1000;
        timer1.Tick += timer1_Tick;
        toolStrip1.ImageScalingSize = new Size(20, 20);
        toolStrip1.Items.AddRange(new ToolStripItem[10]
        {
            toolStripTextBox1, toolStripComboBox2, toolStripButton1, toolStripTextBox2, toolStripComboBox1,
            toolStripButton2, toolStripSeparator1, toolStripButton3, toolStripSeparator2, toolStripButton4
        });
        toolStrip1.Location = new Point(0, 0);
        toolStrip1.Name = "toolStrip1";
        toolStrip1.Size = new Size(759, 25);
        toolStrip1.TabIndex = 15;
        toolStrip1.Text = "toolStrip1";
        toolStripTextBox1.Font = new Font("Microsoft Sans Serif", 9f);
        toolStripTextBox1.Name = "toolStripTextBox1";
        toolStripTextBox1.Size = new Size(166, 25);
        toolStripComboBox2.DropDownWidth = 75;
        toolStripComboBox2.IntegralHeight = false;
        toolStripComboBox2.Items.AddRange(new object[2] { "normal", "encryption" });
        toolStripComboBox2.Name = "toolStripComboBox2";
        toolStripComboBox2.Size = new Size(75, 25);
        toolStripComboBox2.Text = "normal";
        toolStripButton1.DisplayStyle = ToolStripItemDisplayStyle.Text;
        toolStripButton1.ImageTransparentColor = Color.Magenta;
        toolStripButton1.Name = "toolStripButton1";
        toolStripButton1.Size = new Size(36, 22);
        toolStripButton1.Text = "send";
        toolStripButton1.Click += toolStripButton1_Click;
        toolStripTextBox2.Font = new Font("Microsoft Sans Serif", 9f);
        toolStripTextBox2.Name = "toolStripTextBox2";
        toolStripTextBox2.Size = new Size(84, 25);
        toolStripTextBox2.Text = "Empty";
        toolStripComboBox1.Items.AddRange(new object[2] { "10", "9" });
        toolStripComboBox1.Name = "toolStripComboBox1";
        toolStripComboBox1.Size = new Size(75, 25);
        toolStripComboBox1.Text = "10";
        toolStripButton2.DisplayStyle = ToolStripItemDisplayStyle.Text;
        toolStripButton2.ImageTransparentColor = Color.Magenta;
        toolStripButton2.Name = "toolStripButton2";
        toolStripButton2.Size = new Size(36, 22);
        toolStripButton2.Text = "send";
        toolStripButton2.Click += toolStripButton2_Click;
        toolStripSeparator1.Name = "toolStripSeparator1";
        toolStripSeparator1.Size = new Size(6, 25);
        toolStripButton3.DisplayStyle = ToolStripItemDisplayStyle.Text;
        toolStripButton3.ImageTransparentColor = Color.Magenta;
        toolStripButton3.Name = "toolStripButton3";
        toolStripButton3.Size = new Size(78, 22);
        toolStripButton3.Text = "Review users";
        toolStripButton3.Click += toolStripButton3_Click;
        toolStripSeparator2.Name = "toolStripSeparator2";
        toolStripSeparator2.Size = new Size(6, 25);
        toolStripButton4.DisplayStyle = ToolStripItemDisplayStyle.Text;
        toolStripButton4.ImageTransparentColor = Color.Magenta;
        toolStripButton4.Name = "toolStripButton4";
        toolStripButton4.Size = new Size(46, 22);
        toolStripButton4.Text = "Search";
        toolStripButton4.Click += toolStripButton4_Click;
        
        GraphPanel.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
        GraphPanel.BackColor = Color.DarkGray;
        GraphPanel.BorderStyle = BorderStyle.Fixed3D;
        GraphPanel.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Regular, GraphicsUnit.Point, 0);
        GraphPanel.Location = new Point(0, 26);
        GraphPanel.Name = "GraphPanel";
        GraphPanel.Size = new Size(759, 283);
        GraphPanel.TabIndex = 0;
        GraphPanel.Paint += GraphPanel_Paint;
        AutoScaleBaseSize = new Size(5, 13);
        ClientSize = new Size(759, 334);
        Controls.Add(toolStrip1);
        Controls.Add(statusBar1);
        Controls.Add(GraphPanel);
        Menu = mainMenu1;
        Name = "Form1";
        StartPosition = FormStartPosition.CenterScreen;
        Text = "Yulgang Server";
        FormClosing += Form1_FormClosing;
        Load += Form1_Load;
        Layout += Form1_Layout;
        ((ISupportInitialize)statusBarPanel1).EndInit();
        ((ISupportInitialize)statusBarPanel2).EndInit();
        ((ISupportInitialize)statusBarPanel3).EndInit();
        ((ISupportInitialize)statusBarPanel4).EndInit();
        toolStrip1.ResumeLayout(false);
        toolStrip1.PerformLayout();
        ResumeLayout(false);
        PerformLayout();
    }


    [STAThread]
    private static void Main()
    {
        try
        {
            Application.Run(new Form1());
        }
        catch (Exception ex)
        {
            MessageBox.Show("Main  error" + ex);
        }
    }

    private void Form1_Load(object sender, EventArgs e)
    {
        try
        {
            timerThread = new Thread(new Timer.TimerThread().TimerMain);
            timerThread.Name = "Timer  Thread";
            timerThread.Start();
            world = new World();
            world.SetConfig();
            world.SetConfig3();
            world.SetConfig4();
            world.SetConfig2();
            world.SetConfig5();
            try
            {
                RxjhClass.Delete_All_UserLogin(World.GameServerPort);
            }
            catch (Exception ex2)
            {
                MessageBox.Show("Delete All UserLogin Error : " + ex2);
            }

            if (World.KiemTraSoLieu_DatabaseConfig())
            {
                world.SetMonSter();
                world.SetNpc();
                world.SetDrop();
                world.Set_GSDrop();
                world.SetBossDrop();
                world.SetOpen();
                // world.Set_Set();
                world.setBbgCategory();
                world.SetBbgItem();
                world.SetLever();
                world.SetWxLever();
                world.SetKONGFU();
                world.SetQuestDrop();
                world.SetItme();
                world.SetShop();
                world.SetMover();
                world.SetThongBao();
                world.SetDangCapBanThuong();
                world.SetReset();
                world.SetVatPhamTraoDoi();
                world.SetMobile();
                world.SetCheDuocVatPham();
                world.SetKhuAnToan();
                world.SetKill();
                world.SetJianc();
                world.SetThangThienKhiCong();
                world.SetCheTaoVatPham();
                world.SetNhiemVuSoLieuMoi();
                world.SetQG();
                world.SetDaThuocTinh();
                world.VinhDuBangPhaiXepHang();
                World.UpdateAllRankingData();
                world.VinhDuBangPhaiXepHang();
                World.Set_GiftCodeRewards();
                World.SetOpenChestClass();
                World.SetItemOptionClass();
                World.SetTradeToNPC_Rewards();
                World.Set_EventRewards();
                World.Set_MagicQigong();
                World.SetTrangBiBonus();
                World.SetRandomOptionOrb();
                World.SetPillClass();
                World.Set_ListPillExchange();
                World.SetUpgradeItem();
                World.Set_ForceWarRewards();
                World.SetBossMapSettingsClass();
                World.SetRandomPosition();
                World.SetSummonBoss();
                World.SetPBhangMaSetting();
                world.SetScript();
                World.conn = new Connect();
                World.conn.Sestup();
                listener_0 = new Listener((ushort)World.GameServerPort);
                new AuthServer(World.百宝阁服务器IP, World.BaibaogeServerPort);
                Text = Text + "_" + World.ServerName + "_" + Assembly.GetExecutingAssembly().GetName().Version;
                thThreadRead = new Thread(FlushAll);
                thThreadRead.Name = "FlushAll";
                thThreadRead.Priority = ThreadPriority.Lowest;
                thThreadRead.Start();
                World.GameServerPort1 = World.GameServerPort;
                new WGServer("127.0.0.1", World.ForwarderGatewayServicePort).Start();
                World.ycServerConnect = new AtServerConnect();
                World.ycServerConnect.Sestup();
                Timer.DelayCall(TimeSpan.FromMilliseconds(180000.0), TimeSpan.FromMilliseconds(180000.0),
                    TuDong_ThongBaoEvent);
                Timer.DelayCall(TimeSpan.FromMilliseconds(360000.0), TimeSpan.FromMilliseconds(360000.0),
                    Doi_moi_vinh_du);
                LionRoarTimer = new System.Timers.Timer(21000.0);
                LionRoarTimer.Elapsed += 狮子吼事件;
                LionRoarTimer.AutoReset = true;
                LionRoarTimer.Enabled = true;
                AutoConnectTimer = new System.Timers.Timer(World.AutomaticConnectionTime * 1000);
                AutoConnectTimer.Elapsed += 自动连接事件;
                AutoConnectTimer.AutoReset = true;
                AutoConnectTimer.Enabled = true;
                Thread1();
            }
        }
        catch (Exception ex)
        {
            WriteLine(1, "Form1_Load  error" + ex.Message);
        }
    }

    public void Doi_moi_vinh_du()
    {
        var num2 = 0;
        while (true)
        {
            switch (num2)
            {
                default:
                    if (World.jlMsg == 1)
                    {
                        num2 = 2;
                        continue;
                    }

                    break;
                case 2:
                    WriteLine(0, "Refresh honor event");
                    num2 = 1;
                    continue;
                case 1:
                    break;
            }

            break;
        }

        World.UpdateAllRankingData();
        world.VinhDuBangPhaiXepHang();
        WriteLine(2, "Refresh the honor ranking data completed");
    }

    private void 自动连接事件(object sender, ElapsedEventArgs e)
    {
        AutoConnectTimer.Interval = World.AutomaticConnectionTime * 1000;
        if (!World.MainSocket)
        {
            menuItem2.Text = "停止主服务";
            if (listener_0 != null)
            {
                listener_0.Stop();
                listener_0 = null;
            }

            var num2 = new Random().Next(10, 200);
            listener_0 = new Listener((ushort)(World.GameServerPort + num2));
            World.PortReplacementNotice();
        }
    }

    private void menuItem32_Click(object sender, EventArgs e)
    {
        var num = 0;
        while (true)
        {
            switch (num)
            {
                default:
                    if (World.conn != null)
                    {
                        num = 2;
                        continue;
                    }

                    break;
                case 2:
                    World.conn.Dispose();
                    World.conn = null;
                    num = 1;
                    continue;
                case 1:
                    break;
            }

            break;
        }

        World.conn = new Connect();
        World.conn.Sestup();
    }

    private void menuItem2_Click(object sender, EventArgs e)
    {
        if (listener_0 != null)
        {
            menuItem2.Text = "开始主服务";
            listener_0.Stop();
            listener_0 = null;
            return;
        }

        menuItem2.Text = "停止主服务";
        var num2 = new Random().Next(10, 200);
        World.GameServerPort1 = World.GameServerPort + num2;
        listener_0 = new Listener((ushort)World.GameServerPort1);
        World.停止服务器 = false;
        World.PortReplacementNotice();
    }

    private void menuItem3_Click(object sender, EventArgs e)
    {
        var num2 = 0;
        World.conn.Dispose();
        var list = new List<Players>();
        var enumerator = World.allConnectedChars.Values.GetEnumerator();
        try
        {
            num2 = 2;
            while (true)
            {
                num2 = 1;
                if (!enumerator.MoveNext()) break;
                var current = enumerator.Current;
                list.Add(current);
                num2 = 3;
            }

            num2 = 4;
            num2 = 0;
        }
        finally
        {
            num2 = 0;
            while (true)
            {
                switch (num2)
                {
                    case 1:
                        break;
                    default:
                        if (enumerator != null)
                        {
                            num2 = 2;
                            continue;
                        }

                        break;
                    case 2:
                        enumerator.Dispose();
                        num2 = 1;
                        continue;
                }

                break;
            }
        }

        using (var enumerator2 = list.GetEnumerator())
        {
            num2 = 2;
            Players players = null;
            while (true)
            {
                num2 = 1;
                if (!enumerator2.MoveNext()) break;
                players = enumerator2.Current;
                num2 = 3;
                try
                {
                    num2 = 1;
                    if (players.Client != null)
                    {
                        num2 = 0;
                        players.Client.Dispose();
                        num2 = 3;
                    }

                    num2 = 2;
                }
                catch (Exception ex)
                {
                    WriteLine(1, "Save character data  error" + ex.Message);
                }
            }

            num2 = 4;
            num2 = 0;
        }

        list.Clear();
        World.停止服务器 = true;
    }

    private void 狮子吼事件(object sender, ElapsedEventArgs e)
    {
        World.ProcessLionRoarQueue();
    }

    private static void Thread1()
    {
        try
        {
            if (World.ScriptClass.pLuaVM != null)
            {
                var function = World.ScriptClass.pLuaVM.GetFunction("OpenItmeTrigGer");
                if (function != null)
                {
                    var args = new object[4] { 0, 100, 0, 1 };
                    function.Call(args);
                }
            }
        }
        catch (Exception value)
        {
            Console.WriteLine(value);
        }
    }

    private void Form1_FormClosing(object sender, FormClosingEventArgs e)
    {
        try
        {
            while (true)
            {
                switch (World.SqlPool.Count > 0 ? 2 : 4)
                {
                    case 15:
                        return;
                    case 2:
                    case 3:
                        if (MessageBox.Show("The data queue is not yet complete  " + World.SqlPool.Count,
                                "The data queue is not yet complete", MessageBoxButtons.OKCancel,
                                MessageBoxIcon.Asterisk) == DialogResult.OK) goto case 0;
                        e.Cancel = true;
                        return;
                    case 0:
                        runn = true;
                        timerThread.Abort();
                        thThreadRead.Abort();
                        goto case 18;
                    case 4:
                        if (World.allConnectedChars.Count > 0) goto case 9;
                        runn = true;
                        timerThread.Abort();
                        thThreadRead.Abort();
                        ClassDllImport.FreeLib();
                        goto case 1;
                    case 1:
                        if (listener_0 == null) return;
                        break;
                    case 9:
                    case 16:
                        if (MessageBox.Show("There are people online  " + World.allConnectedChars.Count,
                                "There are characters online", MessageBoxButtons.OKCancel, MessageBoxIcon.Asterisk) ==
                            DialogResult.OK) goto case 7;
                        e.Cancel = true;
                        return;
                    case 7:
                        runn = true;
                        timerThread.Abort();
                        thThreadRead.Abort();
                        goto case 6;
                    case 6:
                        if (listener_0 == null) return;
                        goto case 14;
                    case 14:
                        listener_0.Dispose();
                        listener_0 = null;
                        return;
                    case 18:
                        if (listener_0 == null) return;
                        goto case 13;
                    case 13:
                        listener_0.Dispose();
                        listener_0 = null;
                        return;
                    case 19:
                        break;
                    case 5:
                    case 8:
                    case 10:
                    case 12:
                    case 17:
                        return;
                    default:
                        continue;
                }

                break;
            }

            listener_0.Dispose();
            listener_0 = null;
        }
        catch
        {
        }
    }

    private void FlushAll()
    {
        try
        {
            while (true)
            {
                var flag = true;
                while (true)
                {
                    switch (runn ? 10 : 3)
                    {
                        case 8:
                            return;
                        case 3:
                            if (World.AutomaticallyOpenTheConnection) goto case 9;
                            goto case 5;
                        case 9:
                        case 11:
                            if (World.list.Count - World.allConnectedChars.Count > World.SoLuongKetNoi_ToiDaChoPhep)
                                goto case 1;
                            goto case 5;
                        case 1:
                        case 7:
                            if (listener_0 != null) goto case 6;
                            goto case 0;
                        case 6:
                            listener_0.Stop();
                            listener_0 = null;
                            goto case 0;
                        case 0:
                        {
                            var random = new Random();
                            World.GameServerPort1 = World.GameServerPort + random.Next(10, 200);
                            listener_0 = new Listener((ushort)World.GameServerPort);
                            World.PortReplacementNotice();
                            goto case 5;
                        }
                        case 5:
                            Timer.Slice();
                            Thread.Sleep(1);
                            World.ProcessSqlQueue();
                            break;
                        case 10:
                            return;
                        case 12:
                            continue;
                    }

                    break;
                }
            }
        }
        catch (Exception ex)
        {
            WriteLine(1, "FlushAll  错误" + ex.Message);
            if (!runn)
            {
                thThreadRead = new Thread(FlushAll);
                thThreadRead.Name = "FlushAll";
                thThreadRead.Start();
            }
        }
    }

    public void TuDong_ThongBaoEvent()
    {
        ReviewUserLogin();
        if (World.ThongBao.Count > 0)
        {
            var ThongBaoClass = World.ThongBao[TuDongThongBaoID];
            ThongBaoGuiDi(ThongBaoClass.msg, ThongBaoClass.type);
            TuDongThongBaoID++;
            if (TuDongThongBaoID >= World.ThongBao.Count) TuDongThongBaoID = 0;
        }

        if (World.AutomaticArchive == 1) menuItem17.PerformClick();
        try
        {
            if (World.allConnectedChars.Count > World.list.Count)
            {
                var queue = Queue.Synchronized(new Queue());
                foreach (var value2 in World.allConnectedChars.Values) queue.Enqueue(value2);
                while (queue.Count > 0)
                {
                    var players = (Players)queue.Dequeue();
                    if (!World.list.TryGetValue(players.CharacterFullServerID, out var _))
                        World.allConnectedChars.Remove(players.CharacterFullServerID);
                }

                if (World.allConnectedChars.Count > World.list.Count)
                {
                    World.conn.Dispose();
                    menuItem3.PerformClick();
                    World.AutomaticArchive = 0;
                }
            }

            if (World.PhaiChangMoRa_CuuTuyenBanDo == 1.0)
                foreach (var value3 in World.allConnectedChars.Values)
                    if (value3.NhanVatToaDo_BanDo >= 23001 && value3.NhanVatToaDo_BanDo <= 24000)
                    {
                        var array = Converter.HexStringToByte(
                            "AA5516002C01121708******************************558D55AA");
                        System.Buffer.BlockCopy(BitConverter.GetBytes(World.VuongLong_GoldCoin), 0, array, 10, 8);
                        value3.Client?.Send_Map_Data(array, array.Length);
                    }

            if (World.ItemRecord == 1)
                DBA.ExeSqlCommand("DELETE  FROM  ItemRecord  WHERE            DateDiff(dd,ThoiGian,getdate())>" +
                                  World.RecordKeepingDays);
            if (World.LoginRecord == 1)
                DBA.ExeSqlCommand("DELETE  FROM  LoginRecord  WHERE            DateDiff(dd,ThoiGian,getdate())>" +
                                  World.RecordKeepingDays);
            World.MaximumOnline = int.Parse(Config.IniReadValue("GameServer", "MaximumOnline").Trim());
            World.conn.Transmit("Update server configuration|" + World.ServerID + "|" + World.MaximumOnline);
            World.week = (int)DateTime.Now.DayOfWeek;
        }
        catch
        {
        }
    }

    private void ThongBaoGuiDi(string txt, int type)
    {
        try
        {
            foreach (var value in World.allConnectedChars.Values)
                if (!value.Client.TreoMay && value != null)
                {
                    if (type == 0)
                        value.SystemNotification2(txt);
                    else
                        value.SystemRollingAnnouncement2(txt);
                }
        }
        catch (Exception ex)
        {
            WriteLine(1, "SetLogs  错误" + ex.Message);
        }
    }

    private void timer1_Tick(object sender, EventArgs e)
    {
        string text = null;
        string text2 = null;
        GraphPanel.Invalidate();
        statusBarPanel1.Text = "Connect:" + World.list.Count + "  Online:" + World.allConnectedChars.Count +
                               "  Offline store:" + World.OffLine_SoLuong;
        var statusBarPanel = statusBarPanel2;
        var str = World.ItmeTeM.Count.ToString();
        var str2 = MapClass.GetNpcConn().ToString();
        statusBarPanel.Text = "Map items:" + str + "  monster:" + str2;
        var TiepNhan_TocDo = World.TiepNhan_TocDo;
        var GuiDi_TocDo = World.GuiDi_TocDo;
        var PhatDong_GuiDi_TocDo = World.PhatDong_GuiDi_TocDo;
        var text3 = !(TiepNhan_TocDo >= 1024.0)
            ? Math.Round(TiepNhan_TocDo, 0) + "B"
            : Math.Round(World.TiepNhan_TocDo / 1024.0, 0) + "K";
        var text4 = text3;
        text = text4;
        var text5 = !(GuiDi_TocDo >= 1024.0)
            ? Math.Round(GuiDi_TocDo, 0) + "B"
            : Math.Round(World.GuiDi_TocDo / 1024.0, 0) + "K";
        var text6 = text5;
        text2 = text6;
        if (PhatDong_GuiDi_TocDo >= 1024.0)
        {
        }

        statusBarPanel3.Text = "Receive:" + text + "/s  send:" + text2 + "/s  packet:" + Converter.Hexstring.Count;
        World.TiepNhan_TocDo = 0.0;
        World.GuiDi_TocDo = 0.0;
        World.PhatDong_GuiDi_TocDo = 0.0;
        var timeSpan = DateTime.Now.Subtract(dateTime_0);
        statusBarPanel4.Text = timeSpan.Days + "Day" + timeSpan.Hours + "H" + timeSpan.Minutes + "M" +
                               timeSpan.Seconds + "S";
    }

    private void menuItem29_Click(object sender, EventArgs e)
    {
        if (!runn)
        {
            runn = true;
            timerThread.Abort();
            thThreadRead.Abort();
            return;
        }

        thThreadRead = new Thread(FlushAll);
        thThreadRead.Name = "FlushAll";
        thThreadRead.Start();
        timerThread = new Thread(new Timer.TimerThread().TimerMain);
        timerThread.Name = "Timer  Thread";
        timerThread.Start();
    }

    private void menuItem5_Click(object sender, EventArgs e)
    {
        new UserList().ShowDialog();
    }

    private void menuItem27_Click(object sender, EventArgs e)
    {
        new FormUser组队().ShowDialog();
    }

    private void menuItem7_Click(object sender, EventArgs e)
    {
        world.SetDrop();
        world.SetOpen();
        // world.Set_Set(); 
        world.setBbgCategory();
        world.SetBbgItem();
        world.SetLever();
        world.SetKONGFU();
        world.SetQuestDrop();
        world.SetItme();
        world.SetShop();
        world.SetMover();
        world.SetThongBao();
        world.SetReset();
        world.SetDangCapBanThuong();
        world.SetVatPhamTraoDoi();
        world.SetDaThuocTinh();
        world.SetMobile();
    }

    private void menuItem8_Click(object sender, EventArgs e)
    {
        var queue = Queue.Synchronized(new Queue());
        foreach (var value in World.Map.Values)
        foreach (var value2 in value.npcTemplate.Values)
            queue.Enqueue(value2);
        while (queue.Count > 0) ((NpcClass)queue.Dequeue()).Dispose();
        world.SetNpc();
    }

    private void menuItem9_Click(object sender, EventArgs e)
    {
        world.SetDrop();
    }

    private void menuItem10_Click(object sender, EventArgs e)
    {
        world.SetOpen();
    }

    private void menuItem11_Click(object sender, EventArgs e)
    {
        world.SetItme();
    }

    private void menuItem12_Click(object sender, EventArgs e)
    {
        world.SetShop();
    }

    private void menuItem13_Click(object sender, EventArgs e)
    {
        world.SetMover();
    }

    private void menuItem14_Click(object sender, EventArgs e)
    {
        world.SetThongBao();
    }

    private void menuItem15_Click(object sender, EventArgs e)
    {
        if (menuItem15.Checked)
        {
            menuItem15.Checked = false;
            World.Log = 0;
        }
        else
        {
            menuItem15.Checked = true;
            World.Log = 1;
        }
    }

    private void menuItem26_Click(object sender, EventArgs e)
    {
        if (menuItem26.Checked)
        {
            menuItem26.Checked = false;
            World.jlMsg = 0;
        }
        else
        {
            menuItem26.Checked = true;
            World.jlMsg = 1;
        }
    }

    private void menuItem16_Click(object sender, EventArgs e)
    {
        world.SetConfig();
        world.SetConfig3();
        world.SetConfig4();
        world.SetConfig5();
        World.MaximumOnline = int.Parse(Config.IniReadValue("GameServer", "MaximumOnline").Trim());
        World.conn.Transmit("Update server configuration|" + World.ServerID + "|" + World.MaximumOnline);
        WriteLine(2, "Reloading the server configuration file is complete");
    }

    private void menuItem17_Click(object sender, EventArgs e)
    {
        var num2 = 0;
        var list = new List<Players>();
        var enumerator = World.allConnectedChars.Values.GetEnumerator();
        try
        {
            num2 = 4;
            while (true)
            {
                num2 = 1;
                if (!enumerator.MoveNext()) break;
                var current = enumerator.Current;
                list.Add(current);
                num2 = 2;
            }

            num2 = 0;
            num2 = 3;
        }
        finally
        {
            num2 = 2;
            while (true)
            {
                switch (num2)
                {
                    case 1:
                        break;
                    case 0:
                        enumerator.Dispose();
                        num2 = 1;
                        continue;
                    default:
                        if (enumerator != null)
                        {
                            num2 = 0;
                            continue;
                        }

                        break;
                }

                break;
            }
        }

        using (var enumerator2 = list.GetEnumerator())
        {
            num2 = 4;
            Players players = null;
            while (true)
            {
                num2 = 1;
                if (!enumerator2.MoveNext()) break;
                players = enumerator2.Current;
                num2 = 2;
                try
                {
                    players.SaveCharacterData();
                }
                catch (Exception ex)
                {
                    WriteLine(1, "Save character data  error" + ex.Message);
                }
            }

            num2 = 0;
            num2 = 3;
        }

        Show1.TB_Baotri();
        list.Clear();
        WriteLine(8, "Save character data  carry out");
    }

    private void menuItem18_Click(object sender, EventArgs e)
    {
        if (menuItem18.Checked)
        {
            menuItem18.Checked = false;
            World.VerifyServerLog = 0;
        }
        else
        {
            menuItem18.Checked = true;
            World.VerifyServerLog = 1;
        }
    }

    private void menuItem19_Click(object sender, EventArgs e)
    {
        if (menuItem19.Checked)
        {
            menuItem19.Checked = false;
            World.jllog = 0;
        }
        else
        {
            menuItem19.Checked = true;
            World.jllog = 1;
        }
    }

    private void menuItem20_Click(object sender, EventArgs e)
    {
        if (menuItem20.Checked)
        {
            menuItem20.Checked = false;
            World.AlWorldlog = false;
        }
        else
        {
            menuItem20.Checked = true;
            World.AlWorldlog = true;
        }
    }

    private void menuItem21_Click(object sender, EventArgs e)
    {
        try
        {
            if (menuItem21.Checked)
            {
                menuItem21.Checked = false;
                World.AllItmelog = 0;
            }
            else
            {
                menuItem21.Checked = true;
                World.AllItmelog = 1;
            }
        }
        catch (Exception ex)
        {
            WriteLine(1, "Check item error                        error" + ex.Message);
        }
    }

    private void menuItem22_Click(object sender, EventArgs e)
    {
        world.SetMonSter();
    }

    private void menuItem23_Click(object sender, EventArgs e)
    {
        world.SetBossDrop();
    }

    private void menuItem25_Click(object sender, EventArgs e)
    {
        world.Set_GSDrop();
    }

    private void menuItem28_Click(object sender, EventArgs e)
    {
        world.SetKill();
        world.SetJianc();
    }

    private void menuItem30_Click(object sender, EventArgs e)
    {
        world.SetKONGFU();
    }

    private void toolStripButton1_Click(object sender, EventArgs e)
    {
        try
        {
            var array = Converter.hexStringToByte2(toolStripTextBox1.Text);
            var enumerator = World.list.Values.GetEnumerator();
            try
            {
                NetState netState = null;
                while (enumerator.MoveNext())
                {
                    netState = enumerator.Current;
                    if (netState != null)
                    {
                        if (toolStripComboBox2.Text == "正常")
                        {
                            System.Buffer.BlockCopy(BitConverter.GetBytes(netState.WorldId), 0, array, 5, 2);
                            netState.Send单包(array, array.Length);
                            WriteLine(1, "Send MSG 1");
                        }
                        else
                        {
                            WriteLine(1, "Send MSG 2");
                            netState.SendMultiplePackage(array, array.Length);
                        }
                    }
                }
            }
            finally
            {
                var num2 = 0;
                while (true)
                {
                    switch (num2)
                    {
                        case 1:
                            break;
                        default:
                            if (enumerator != null)
                            {
                                num2 = 2;
                                continue;
                            }

                            break;
                        case 2:
                            enumerator.Dispose();
                            num2 = 1;
                            continue;
                    }

                    break;
                }
            }
        }
        catch
        {
        }

        int_0++;
    }

    public void SendSinglePackage(byte[] toSendBuff, int int_1)
    {
        var array = new byte[BitConverter.ToInt16(toSendBuff, 9) + 7];
        System.Buffer.BlockCopy(toSendBuff, 5, array, 0, array.Length);
        SendPackageSending(array, array.Length);
    }

    private void SendPackageSending(byte[] toSendBuff, int length)
    {
        var array = new byte[length + 15];
        array[0] = 170;
        array[1] = 85;
        System.Buffer.BlockCopy(BitConverter.GetBytes(length + 9), 0, array, 2, 2);
        System.Buffer.BlockCopy(toSendBuff, 0, array, 5, length);
        array[array.Length - 2] = 85;
        array[array.Length - 1] = 170;
    }

    private void toolStripButton2_Click(object sender, EventArgs e)
    {
        try
        {
            var enumerator = World.allConnectedChars.Values.GetEnumerator();
            try
            {
                while (enumerator.MoveNext())
                    enumerator.Current?.HeThongNhacNho(toolStripTextBox2.Text,
                        int.Parse(toolStripComboBox1.SelectedItem.ToString()), ":");
            }
            finally
            {
                var num2 = 0;
                while (true)
                {
                    switch (num2)
                    {
                        case 1:
                            break;
                        default:
                            if (enumerator != null)
                            {
                                num2 = 2;
                                continue;
                            }

                            break;
                        case 2:
                            enumerator.Dispose();
                            num2 = 1;
                            continue;
                    }

                    break;
                }
            }
        }
        catch
        {
        }
    }

    private void toolStripButton3_Click(object sender, EventArgs e)
    {
        ReviewUserLogin();
    }

    private void toolStripButton4_Click(object sender, EventArgs e)
    {
        try
        {
            var num2 = 0;
            IEnumerator<Players> enumerator = null;
            var enumerator2 = default(List<object>.Enumerator);
            var enumerator3 = default(Dictionary<int, NpcClass>.ValueCollection.Enumerator);
            var enumerator4 = default(Dictionary<int, X_To_Doi_Class>.ValueCollection.Enumerator);
            IEnumerator<Players> enumerator5 = null;
            var enumerator6 = World.Map.Values.GetEnumerator();
            num2 = 1;
            try
            {
                num2 = 1;
                while (true)
                {
                    num2 = 2;
                    if (!enumerator6.MoveNext()) break;
                    var current = enumerator6.Current;
                    enumerator3 = current.npcTemplate.Values.GetEnumerator();
                    num2 = 4;
                    try
                    {
                        num2 = 1;
                        while (true)
                        {
                            num2 = 4;
                            if (!enumerator3.MoveNext()) break;
                            var current2 = enumerator3.Current;
                            current2.getbl();
                            num2 = 0;
                        }

                        num2 = 2;
                        num2 = 3;
                    }
                    finally
                    {
                        enumerator3.Dispose();
                    }
                }

                num2 = 0;
                num2 = 3;
            }
            finally
            {
                enumerator6.Dispose();
            }

            enumerator4 = World.WToDoi.Values.GetEnumerator();
            num2 = 0;
            try
            {
                num2 = 2;
                while (true)
                {
                    num2 = 0;
                    if (!enumerator4.MoveNext()) break;
                    var current3 = enumerator4.Current;
                    WriteLine(2, "Team up:" + current3.TeamID + "  character：" + current3.ToDoi_NguoiChoi.Count);
                    enumerator5 = current3.ToDoi_NguoiChoi.Values.GetEnumerator();
                    num2 = 1;
                    try
                    {
                        num2 = 2;
                        while (true)
                        {
                            num2 = 0;
                            if (!enumerator5.MoveNext()) break;
                            var current4 = enumerator5.Current;
                            WriteLine(2, "Team members:" + current4.Userid + "  character：" + current4.UserName);
                            num2 = 1;
                        }

                        num2 = 4;
                        num2 = 3;
                    }
                    finally
                    {
                        num2 = 0;
                        while (true)
                        {
                            switch (num2)
                            {
                                case 2:
                                    break;
                                case 1:
                                    enumerator5.Dispose();
                                    num2 = 2;
                                    continue;
                                default:
                                    if (enumerator5 != null)
                                    {
                                        num2 = 1;
                                        continue;
                                    }

                                    break;
                            }

                            break;
                        }
                    }
                }

                num2 = 4;
                num2 = 3;
            }
            finally
            {
                enumerator4.Dispose();
            }

            enumerator = World.allConnectedChars.Values.GetEnumerator();
            num2 = 4;
            try
            {
                num2 = 1;
                while (true)
                {
                    num2 = 2;
                    if (!enumerator.MoveNext()) break;
                    num2 = 4;
                }

                num2 = 0;
                num2 = 3;
            }
            finally
            {
                num2 = 1;
                while (true)
                {
                    switch (num2)
                    {
                        case 0:
                            break;
                        default:
                            if (enumerator != null)
                            {
                                num2 = 2;
                                continue;
                            }

                            break;
                        case 2:
                            enumerator.Dispose();
                            num2 = 0;
                            continue;
                    }

                    break;
                }
            }

            enumerator2 = World.locklist3.GetEnumerator();
            num2 = 2;
            try
            {
                num2 = 1;
                while (true)
                {
                    num2 = 2;
                    if (!enumerator2.MoveNext()) break;
                    var current5 = enumerator2.Current;
                    WriteLine(2, current5.ToString());
                    num2 = 4;
                }

                num2 = 0;
                num2 = 3;
            }
            finally
            {
                enumerator2.Dispose();
            }

            num2 = 3;
        }
        catch (Exception ex)
        {
            WriteLine(1, ex.ToString());
        }

        GC.Collect();
    }

    public void ReviewUserLogin()
    {
        try
        {
            var num2 = 0;
            Players players = null;
            NetState netState = null;
            var num3 = 0;
            string text = null;
            var num4 = 0;
            string text2 = null;
            string text3 = null;
            var num5 = 0;
            string text4 = null;
            string text5 = null;
            var num6 = 0;
            var num7 = 0;
            var stringBuilder = new StringBuilder();
            var enumerator = World.list.Values.GetEnumerator();
            num2 = 8;
            try
            {
                num2 = 7;
                while (true)
                {
                    num2 = 0;
                    if (!enumerator.MoveNext()) break;
                    netState = enumerator.Current;
                    text = "NULL";
                    num3 = 0;
                    num2 = 3;
                    if (netState.TreoMay)
                    {
                        num2 = 6;
                        num3 = 1;
                        num2 = 4;
                    }

                    num4 = 0;
                    text2 = string.Empty;
                    text3 = string.Empty;
                    num5 = 0;
                    text4 = string.Empty;
                    text5 = string.Empty;
                    num6 = 0;
                    num7 = 0;
                    players = World.KiemTra_PlayerWorld_ID(netState.WorldId);
                    num2 = 1;
                    if (players != null)
                    {
                        num2 = 11;
                        text = players.UserName;
                        num4 = players.OriginalServerSerialNumber;
                        text2 = players.OriginalServerIP;
                        text3 = players.OriginalServerPort.ToString();
                        num5 = players.OriginalServerID;
                        text4 = players.SilverCoinSquareServerIP;
                        text5 = players.SilverCoinSquareServerPort.ToString();
                        num7 = players.Player_Job;
                        num2 = 12;
                        if (players.PublicDrugs.TryGetValue(**********, out var _))
                        {
                            num2 = 10;
                            num6 = 1;
                            num2 = 5;
                        }
                    }

                    stringBuilder.Append(netState.Player.Userid);
                    stringBuilder.Append("-");
                    stringBuilder.Append(netState);
                    stringBuilder.Append("-");
                    stringBuilder.Append(netState.BindAccount);
                    stringBuilder.Append("-");
                    stringBuilder.Append(num3);
                    stringBuilder.Append("-");
                    stringBuilder.Append(text);
                    stringBuilder.Append("-");
                    stringBuilder.Append(num4);
                    stringBuilder.Append("-");
                    stringBuilder.Append(text2);
                    stringBuilder.Append("-");
                    stringBuilder.Append(text3);
                    stringBuilder.Append("-");
                    stringBuilder.Append(num5);
                    stringBuilder.Append("-");
                    stringBuilder.Append(text4);
                    stringBuilder.Append("-");
                    stringBuilder.Append(text5);
                    stringBuilder.Append("-");
                    stringBuilder.Append(netState.WorldId);
                    stringBuilder.Append("-");
                    stringBuilder.Append(num6);
                    stringBuilder.Append("-");
                    stringBuilder.Append(num7);
                    stringBuilder.Append(",");
                    num2 = 2;
                }

                num2 = 9;
                num2 = 8;
            }
            finally
            {
                num2 = 1;
                while (true)
                {
                    switch (num2)
                    {
                        case 2:
                            break;
                        case 0:
                            enumerator.Dispose();
                            num2 = 2;
                            continue;
                        default:
                            if (enumerator != null)
                            {
                                num2 = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            num2 = 0;
            if (stringBuilder.Length > 0)
            {
                num2 = 2;
                stringBuilder.Remove(stringBuilder.Length - 1, 1);
                num2 = 6;
            }

            num2 = 1;
            World.conn.Transmit("复查用户登陆|" + stringBuilder);
            num2 = 4;
            if (World.AutGC != 0)
            {
                num2 = 5;
                GC.Collect();
                num2 = 7;
            }

            num2 = 3;
        }
        catch (Exception ex)
        {
            WriteLine(1, "Review user login    error" + ex.Message);
        }
    }

    private void GraphPanel_Paint(object sender, PaintEventArgs e)
    {
        try
        {
            var graphics = e.Graphics;
            graphics.SmoothingMode = SmoothingMode.AntiAlias;
            graphics.PixelOffsetMode = PixelOffsetMode.None;
            var s = "Connect:" + World.list.Count + "/" + World.MaximumOnline + "  Online:" +
                    World.allConnectedChars.Count + "  MainSocket:" + World.MainSocket + "  SocketState:" +
                    World.SocketState + "  Team up:" + World.WToDoi.Count + "  Offline queue:" +
                    World.m_Disposed.Count + "  Database queue:" + World.SqlPool.Count + "  Lions lined up:" +
                    World.SuTuHongList.Count;
            graphics.DrawString(s, DefaultFont, Brushes.Black, new Point(20, 5));
            var num2 = 0;
            using var enumerator = list_0.GetEnumerator();
            var num3 = 0;
            TxtClass txtClass = null;
            var num4 = 12;
            while (true)
            {
                switch (num4)
                {
                    case 9:
                        graphics.DrawString(txtClass.Txt, DefaultFont, Brushes.Black, new Point(5, num2 += 17));
                        num4 = 6;
                        continue;
                    case 11:
                        switch (num3)
                        {
                            default:
                                num4 = 9;
                                continue;
                            case 0:
                                graphics.DrawString(txtClass.Txt, DefaultFont, Brushes.Black, new Point(5, num2 += 17));
                                num4 = 10;
                                continue;
                            case 1:
                                graphics.DrawString(txtClass.Txt, DefaultFont, Brushes.Red, new Point(5, num2 += 17));
                                num4 = 13;
                                continue;
                            case 2:
                                graphics.DrawString(txtClass.Txt, DefaultFont, Brushes.Black, new Point(5, num2 += 17));
                                num4 = 0;
                                continue;
                            case 3:
                                graphics.DrawString(txtClass.Txt, DefaultFont, Brushes.Green, new Point(5, num2 += 17));
                                num4 = 5;
                                continue;
                            case 5:
                                graphics.DrawString(txtClass.Txt, DefaultFont, Brushes.DodgerBlue,
                                    new Point(5, num2 += 17));
                                num4 = 8;
                                continue;
                            case 6:
                                graphics.DrawString(txtClass.Txt, DefaultFont, Brushes.Blue, new Point(5, num2 += 17));
                                num4 = 7;
                                continue;
                            case 4:
                                break;
                        }

                        graphics.DrawString(txtClass.Txt, DefaultFont, Brushes.Teal, new Point(5, num2 += 17));
                        num4 = 3;
                        break;
                }

                num4 = 4;
                if (!enumerator.MoveNext()) break;
                txtClass = enumerator.Current;
                num3 = txtClass.type;
                num4 = 11;
            }

            num4 = 1;
            num4 = 2;
        }
        catch
        {
        }
    }

    public static void WriteLine(int type, string txtt)
    {
        var num2 = kjdx / 18;
        lock (list_0)
        {
            var num3 = 0;
            var num4 = 0;
            var num5 = 0;
            var num6 = 0;
            var num7 = 0;
            var num8 = 0;
            switch (type)
            {
                case 101:
                    logo.WGTxtLog(txtt);
                    list_0.Add(new TxtClass(type, DateTime.Now + "  " + txtt));
                    num5 = list_0.Count;
                    if (num2 <= 0) num2 = 20;
                    if (num5 > num2)
                        for (num8 = 0; num8 < num5 - num2; num8++)
                            list_0.RemoveAt(0);
                    return;
                case 100:
                    logo.FileTxtLog(txtt);
                    list_0.Add(new TxtClass(type, DateTime.Now + "  " + txtt));
                    num3 = list_0.Count;
                    if (num2 <= 0) num2 = 20;
                    if (num3 > num2)
                        for (num6 = 0; num6 < num3 - num2; num6++)
                            list_0.RemoveAt(0);
                    return;
                case 9:
                    logo.zhtfTxtLog(txtt);
                    return;
            }

            if (!World.AlWorldlog) return;
            if (World.jllog == 1)
                switch (type)
                {
                    case 1:
                        logo.FileTxtLog(txtt);
                        break;
                    case 2:
                        logo.FileCQTxtLog(txtt);
                        break;
                    case 3:
                        logo.FileLoninTxtLog(txtt);
                        break;
                    case 4:
                        logo.FileDropItmeTxtLog(txtt);
                        break;
                    case 5:
                        logo.FileItmeTxtLog(txtt);
                        break;
                    case 6:
                        logo.FileBugTxtLog(txtt);
                        break;
                    case 7:
                        logo.FilePakTxtLog(txtt);
                        break;
                    case 8:
                        logo.SeveTxtLog(txtt);
                        break;
                    case 9:
                        logo.BossLog(txtt);
                        break;
                }

            switch (type)
            {
                case 99:
                    logo.FileTxtLog(txtt);
                    break;
                case 88:
                    logo.pzTxtLog(txtt);
                    break;
                case 77:
                    logo.cfzTxtLog(txtt);
                    break;
            }

            list_0.Add(new TxtClass(type, DateTime.Now + "  " + txtt));
            num4 = list_0.Count;
            if (num2 <= 0) num2 = 20;
            if (num4 > num2)
                for (num7 = 0; num7 < num4 - num2; num7++)
                    list_0.RemoveAt(0);
        }
    }

    private void menuItem31_Click(object sender, EventArgs e)
    {
        new BinIP().ShowDialog();
    }

    private void menuItem33_Click(object sender, EventArgs e)
    {
        var thread = new Thread(Form2.FlushAll1);
        thread.Name = "Timer  Thread";
        thread.Start();
    }

    private void menuItem34_Click(object sender, EventArgs e)
    {
        var thread = new Thread(Form2.FlushAll2);
        thread.Name = "Timer  Thread";
        thread.Start();
    }

    private void menuItem35_Click(object sender, EventArgs e)
    {
        var thread = new Thread(Form2.FlushAll3);
        thread.Name = "Timer  Thread";
        thread.Start();
    }

    private void menuItem36_Click(object sender, EventArgs e)
    {
        var thread = new Thread(Form2.FlushAll4);
        thread.Name = "Timer  Thread";
        thread.Start();
    }

    private void Form1_Layout(object sender, LayoutEventArgs e)
    {
        if (GraphPanel.Height != 0) kjdx = GraphPanel.Height;
    }

    private void menuItem37_Click(object sender, EventArgs e)
    {
        new Form2().ShowDialog();
    }

    private void menuItem38_Click(object sender, EventArgs e)
    {
        if (menuItem38.Checked)
        {
            menuItem38.Checked = false;
            World.Droplog = false;
        }
        else
        {
            menuItem38.Checked = true;
            World.Droplog = true;
        }
    }

    private void menuItem39_Click(object sender, EventArgs e)
    {
        world.SetScript();
    }

    private void menuItem41_Click(object sender, EventArgs e)
    {
        new YbQForm().ShowDialog();
    }

    private void menuItem42_Click(object sender, EventArgs e)
    {
        world.SetCheTaoVatPham();
    }

    private void menuItem43_Click(object sender, EventArgs e)
    {
        world.setBbgCategory();
        world.SetBbgItem();
    }

    private void menuItem44_Click(object sender, EventArgs e)
    {
        // world.Set_Set();
    }

    private void menuItem45_Click(object sender, EventArgs e)
    {
        world.SetDangCapBanThuong();
    }

    private void menuItem24_Click(object sender, EventArgs e)
    {
        world.SetVatPhamTraoDoi();
    }

    private void menuItem49_Click(object sender, EventArgs e)
    {
        new YbiForm().ShowDialog();
    }

    private void menuItem50_Click(object sender, EventArgs e)
    {
        world.SetMobile();
    }

    private void menuItem52_Click(object sender, EventArgs e)
    {
        world.SetNhiemVuSoLieuMoi();
    }

    private void menuItem46_Click(object sender, EventArgs e)
    {
        world.SetQG();
        world.SetThangThienKhiCong();
    }

    private void menuItem48_Click(object sender, EventArgs e)
    {
        new NpcList().Show();
    }

    private void menuItem53_Click(object sender, EventArgs e)
    {
        world.SetDaThuocTinh();
    }

    private void menuItem54_Click(object sender, EventArgs e)
    {
        if (World.ChoTuLuyenMoRa_ID == 0)
        {
            World.ChoTuLuyenMoRa_ID = 1;
            World.ToanCucNhacNho("Hệ Thống", 6, "Cổng luyện tập đã mở, thời gian tu luyện 2 giờ");
            WriteLine(1, "The place of practice opens！");
            menuItem54.Text = "Kết thúc khu luyện tập";
        }
        else
        {
            World.ChoTuLuyenMoRa_ID = 0;
            World.ToanCucNhacNho("Hệ Thống", 6, "Khu luyện tập đã đóng");
            WriteLine(1, "The place of practice ends！");
            menuItem54.Text = "Mở khu luyện tập";
        }
    }

    private void menuItem57_Click(object sender, EventArgs e)
    {
        var num = 0;
        while (true)
        {
            switch (num)
            {
                case 2:
                    return;
                case 1:
                    new NetSet(listener_0.Server).Show();
                    num = 2;
                    continue;
            }

            if (listener_0 != null)
            {
                num = 1;
                continue;
            }

            return;
        }
    }

    private void menuItem58_Click(object sender, EventArgs e)
    {
        new BinIP().ShowDialog();
    }

    private void menuItem31_Click_1(object sender, EventArgs e)
    {
        try
        {
            var enumerator = World.allConnectedChars.Values.GetEnumerator();
            try
            {
                Players players = null;
                while (enumerator.MoveNext())
                {
                    players = enumerator.Current;
                    if (players.Client != null)
                    {
                        players.SaveCharacterData();
                        players.SaveThePersonalwarehouseStoredProcedure();
                        players.SaveTheComprehensivewarehouseStoredProcedure();
                    }
                }
            }
            finally
            {
                var num2 = 0;
                while (true)
                {
                    switch (num2)
                    {
                        case 2:
                            break;
                        case 1:
                            enumerator.Dispose();
                            num2 = 2;
                            continue;
                        default:
                            if (enumerator != null)
                            {
                                num2 = 1;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }
        }
        catch (Exception ex)
        {
            WriteLine(1, "保存人物的数据出错|" + ex.Message);
        }

        WriteLine(6, "Lưu tất cả dữ liệu của nhân vật là xong");
    }

    private void menuItem51_Click(object sender, EventArgs e)
    {
        if (World.CongThanhChien == null)
        {
            WriteLine(2, "Chiến tranh bao vây tự động bắt đầu thành công");
            World.CongThanhChien = new CongThanhClass();
        }
        else
        {
            World.CongThanhChien.Dispose();
            WriteLine(2, "Cuộc bao vây đã kết thúc");
        }
    }

    private void menuItem59_Click(object sender, EventArgs e)
    {
        if (World.EventClass == null)
        {
            World.EventClass = new EventClass();
            WriteLine(2, "TLC đã bắt đầu, hãy nhấp lại để kết thúc Cuộc chiến Tiên-Quỷ");
        }
        else
        {
            World.EventClass.Dispose();
            WriteLine(2, "TLC đã kết thúc, hãy nhấp lại để bắt đầu Fairy Demon War");
        }
    }

    private void menuItem61_Click(object sender, EventArgs e)
    {
        if (World.ykts == null)
        {
            World.ykts = new ChatEvent();
            WriteLine(2, "Mở ChatEvent");
        }
        else
        {
            World.ykts.Dispose();
            WriteLine(2, "Kết thúc ChatEvent");
        }
    }

    private void menuItem63_Click(object sender, EventArgs e)
    {
        var string_16 = "DELETE FROM TBL_XWWL_Char";
        var string_17 = "DELETE FROM EventTop";
        var string_18 = "DELETE FROM TBL_XWWL_Cw";
        var string_19 = "DELETE FROM TBL_XWWL_Guild";
        var string_20 = "DELETE FROM TBL_XWWL_GuildMember";
        var string_10 = "DELETE FROM TBL_XWWL_PublicWarehouse";
        var string_11 = "DELETE FROM TBL_XWWL_Warehouse";
        var string_12 = "DELETE FROM BachBaoCacRecord";
        var string_13 = "DELETE FROM LoginRecord";
        var string_14 = "DELETE FROM ItemRecord";
        var string_15 = "DELETE FROM ItemRecord";
        DBA.ExeSqlCommand(string_16, "GameServer");
        DBA.ExeSqlCommand(string_17, "GameServer");
        DBA.ExeSqlCommand(string_18, "GameServer");
        DBA.ExeSqlCommand(string_19, "GameServer");
        DBA.ExeSqlCommand(string_20, "GameServer");
        DBA.ExeSqlCommand(string_10, "GameServer");
        DBA.ExeSqlCommand(string_11, "GameServer");
        DBA.ExeSqlCommand(string_12, "GameServer");
        DBA.ExeSqlCommand(string_13, "GameServer");
        DBA.ExeSqlCommand(string_14, "GameServer");
        DBA.ExeSqlCommand(string_15, "GameServer");
        WriteLine(2, "Xoá toàn bộ data thành công~");
    }

    private void menuItem64_Click(object sender, EventArgs e)
    {
        var gMGJ = new GMGJ();
        gMGJ.StartPosition = FormStartPosition.CenterParent;
        gMGJ.ShowDialog();
    }

    private void menuItem60_Click(object sender, EventArgs e)
    {
        var form = new Form4();
        form.ShowDialog();
    }

    private void menuItem65_Click(object sender, EventArgs e)
    {
        world.SetItemRecyle();
    }

    private void menuItem66_Click(object sender, EventArgs e)
    {
        if (World.BangChien == null)
        {
            World.BangChien = new X_Bang_Phai_Chien_MonChien();
            WriteLine(2, "Kích hoạt bang chiến thành công");
        }
        else
        {
            World.BangChien.Dispose();
            WriteLine(2, "Kết thúc bang chiến");
        }
    }

    private void menuItem68_Click(object sender, EventArgs e)
    {
        new SkillContrl().Show();
    }

    private void menuItem69_Click(object sender, EventArgs e)
    {
        new StoneConfig().Show();
    }

    private void menuItem70_Click(object sender, EventArgs e)
    {
        new NpcList().Show();
    }

    private void menuItem47_Click(object sender, EventArgs e)
    {
        var form = new Form12();
        form.StartPosition = FormStartPosition.CenterParent;
        form.ShowDialog();
    }

    private void menuItem48_Click_1(object sender, EventArgs e)
    {
        var 游戏公告2 = new 游戏公告();
        游戏公告2.StartPosition = FormStartPosition.CenterParent;
        游戏公告2.ShowDialog();
    }

    private void menuItem55_Click(object sender, EventArgs e)
    {
        var sELL = new SELL();
        sELL.StartPosition = FormStartPosition.CenterParent;
        sELL.ShowDialog();
    }

    private void menuItem71_Click(object sender, EventArgs e)
    {
        var form = new Form7();
        form.StartPosition = FormStartPosition.CenterParent;
        form.ShowDialog();
    }

    private void menuItem72_Click(object sender, EventArgs e)
    {
        var form = new Form10();
        form.StartPosition = FormStartPosition.CenterParent;
        form.ShowDialog();
    }

    private void menuItem73_Click(object sender, EventArgs e)
    {
        var form = new Form3();
        form.StartPosition = FormStartPosition.CenterParent;
        form.ShowDialog();
    }

    private void menuItem74_Click(object sender, EventArgs e)
    {
        var 全体跨线2 = new CrossChannel();
        全体跨线2.StartPosition = FormStartPosition.CenterParent;
        全体跨线2.ShowDialog();
    }

    private void menuItem75_Click(object sender, EventArgs e)
    {
        var move = new Move();
        move.StartPosition = FormStartPosition.CenterParent;
        move.ShowDialog();
    }

    private static void old_acctor_mc()
    {
        list_0 = new List<TxtClass>();
        kjdx = 300;
    }

    private void menuItem76_Click(object sender, EventArgs e)
    {
    }

    private void menuItem88_Click(object sender, EventArgs e)
    {
    }

    private void menuItem77_Click(object sender, EventArgs e)
    {
        world.SetReset();
    }

    private void menuItem78_Click(object sender, EventArgs e)
    {
        new Show1().ShowDialog();
    }

    private void menuItem79_Click(object sender, EventArgs e)
    {
        if (World.SuKienExpX2 == 0)
        {
            World.SuKienExpX2 = 1;
            WriteLine(2, "Bắt đầu X2 EXp");
        }
        else
        {
            World.SuKienExpX2 = 0;
            WriteLine(2, "Kết thúc X2 EXp");
        }
    }

    private void menuItem80_Click(object sender, EventArgs e)
    {
        if (World.SafePK_Status == 0)
        {
            World.SafePK_Status = 1;
            WriteLine(2, "Bắt đầu Safe PK");
        }
        else
        {
            World.SafePK_Status = 0;
            WriteLine(2, "Kết thúc Safe PK");
        }
    }

    private void menuItem81_Click(object sender, EventArgs e)
    {
        var form = new frmCreateItem();
        form.Show();
    }

    private void menuItem82_Click(object sender, EventArgs e)
    {
        World.Set_GiftCodeRewards();
    }

    private void menuItem83_Click(object sender, EventArgs e)
    {
        World.SetOpenChestClass();
    }

    private void menuItem84_Click(object sender, EventArgs e)
    {
        World.SetItemOptionClass();
    }

    private void menuItem85_Click(object sender, EventArgs e)
    {
        World.SetTradeToNPC_Rewards();
    }

    private void menuItem86_Click(object sender, EventArgs e)
    {
        World.Set_EventRewards();
    }

    private void menuItem89_Click(object sender, EventArgs e)
    {
        World.Set_MagicQigong();
    }

    private void menuItem90_Click(object sender, EventArgs e)
    {
        World.SetTrangBiBonus();
    }

    private void menuItem91_Click(object sender, EventArgs e)
    {
        World.SetRandomOptionOrb();
    }

    private void menuItem92_Click(object sender, EventArgs e)
    {
        World.SetPillClass();
    }

    private void menuItem93_Click(object sender, EventArgs e)
    {
        World.Set_ListPillExchange();
    }

    private void menuItem94_Click(object sender, EventArgs e)
    {
        if (World.EventRandomClass != null)
        {
            World.EventRandomClass.Dispose();
            World.EventRandomClass = null;
            WriteLine(5, "Kết thúc Thể lực chiến random");
        }
        else if (World.EventClass != null)
        {
            WriteLine(5, "Thế lực chiến đang diễn ra, không thể mở");
        }
        else
        {
            World.EventRandomClass = new EventRandomClass();
            WriteLine(5, "Bắt đầu Thể lực chiến random");
        }
    }

    private void menuItem95_Click(object sender, EventArgs e)
    {
        World.SetUpgradeItem();
    }

    private void menuItem96_Click(object sender, EventArgs e)
    {
        World.Set_ForceWarRewards();
    }

    private void menuItem97_Click(object sender, EventArgs e)
    {
        World.SetBossMapSettingsClass();
    }

    private void menuItem98_Click(object sender, EventArgs e)
    {
        World.SetRandomPosition();
    }

    private void menuItem99_Click(object sender, EventArgs e)
    {
        World.SetSummonBoss();
    }

    private void menuItem100_Click(object sender, EventArgs e)
    {
        World.SetPBhangMaSetting();
    }

    private void menuItem102_Click(object sender, EventArgs e)
    {
        Task.Run(async () =>
            {
                await Task.Delay(1000);
                LoadCharacterOffAttack();
            });
    }

    private void menuItem103_Click(object sender, EventArgs e)
    {
        Task.Run(async () =>
            {
                await Task.Delay(1000);
                AutoPartyAllOffAttack();
            });
    }
    private void LoadCharacterOffAttack()
    {
        try
        {
            var ip_ = "127.0.0.1";

            var dbTable = DBA.GetDBToDataTable(@"
                SELECT DISTINCT TOP 100 
                    a.FLD_ID, 
                    a.FLD_LEVEL, 
                    a.FLD_INDEX,
                    b.*
                FROM 
                    TBL_XWWL_Char AS a
                INNER JOIN 
                    RealAccount.dbo.TBL_ACCOUNT AS b
                ON 
                    a.FLD_ID = b.FLD_ID
                WHERE 
                    b.FLD_ONLINE <> 1
                ORDER BY 
                    a.FLD_LEVEL DESC");
            if (dbTable == null)
            {
                return;
            }
            foreach (DataRow item in dbTable.Rows)
            {
                OffAtackACharacter(ip_, item);
            }
            dbTable.Dispose();
        }
        catch (Exception ex)
        {
            WriteLine(1, "Error Auto Offline menuItem41_Click " + ex.Message);
        }
    }

    private void OffAtackACharacter(string ip_, DataRow item)
    {
        // Limit the number of connected characters
        if (World.allConnectedChars.Count >= World.SoLuongKetNoi_ToiDaChoPhep)
        {
            return;
        }

        // Get the account ID
        var acc_ = item["FLD_ID"].ToString();

        // Check if the character is online using a Dictionary lookup
        if (World.allConnectedChars.Values.Any(play => play.UserName == acc_))
        {
            return;
        }

        // Get the index and generate other required values
        var index_ = (int)item["FLD_INDEX"];
        var intptr_0 = (IntPtr)(World.allConnectedChars.Count + 1000);
        var port = (ushort)RNG.Next(10000, 50000);

        // Create client information and NetState object
        var clientInfo = listener_0.method_3(intptr_0, ip_, port);
        var player = new NetState(clientInfo).Start();

        // Initialize the player and handle login
        player.LoginPlayerX(acc_, 250, ip_, "**********");
        player.GetAListOfPeople(new byte[0], 0);
        player.HandleCharacterLogin(index_);

        // Skip if the character is already in a specific map
        if (player.NhanVatToaDo_BanDo == 101)
        {
            return;
        }

        // Configure the player's state
        player.WalkingStatusId = 1;
        player.WalkingStatus_id1 = 0;
        player.WalkingState(BitConverter.GetBytes(2), 1);
        player.UpdateMovementSpeed();
        player.Auto_DanhQuai_GiamSat_ThoiGian = DateTime.Now.AddMilliseconds(-12000.0);
        player.Auto_DanhQuai_GiamSat_ThoiGian_Flag = 0;
        player.Auto_DanhQuai_HinhThuc = 1;
        player.Auto_Offline = true;
        player.Client.TreoMay = true;
        player.OffLine_TreoMay_ToaDo_X = (int)player.NhanVatToaDo_X;
        player.OffLine_TreoMay_ToaDo_Y = (int)player.NhanVatToaDo_Y;
        player.OffLine_TreoMay_BanDo = player.NhanVatToaDo_BanDo;
        player.GMMode = 1;

        // Find the highest-level skill
        int skillId = 0;
        try
        {
            var skill = player.FindHighestLevelVoCong();
            if (skill != null)
                skillId = skill.FLD_PID;
        }
        catch (Exception)
        {
            skillId = 0;
        }

        player.OffLine_TreoMay_VoCong_ID = skillId;

        // Handle specific job logic
        if (player.Player_Job == 4)
        {
            skillId = 400001;
            player.CurrentlyActiveSkill_ID = 400001;
            player.OffLine_TreoMay_VoCong_ID = 0;
        }
        else if (player.Player_Job == 6)
        {
            // Additional job logic if needed
        }

        // Save character data and finalize offline state
        player.StoredProcedureForSavingCharacterData();
        player.Client.TreoMay = true;
        World.OffLine_SoLuong++;
    }


    private void AutoPartyAllOffAttack()
    {
        const int maxPartySize = 8;

        foreach (var currentPlayer in World.allConnectedChars.Values)
        {
            // Check if the current player is in "Auto Mode" and not already in a team
            if (!currentPlayer.Client.TreoMay || currentPlayer.TeamID != 0)
                continue;
            WriteLine(2, "==Auto Party " + currentPlayer.UserName);
            // Get the current player's position and map
            var currentPosX = currentPlayer.NhanVatToaDo_X;
            var currentPosY = currentPlayer.NhanVatToaDo_Y;
            var currentMap = currentPlayer.NhanVatToaDo_BanDo;

            // Find nearby players on the same map
            var nearbyPlayers = World.allConnectedChars.Values
                .Where(player => player.NhanVatToaDo_BanDo == currentMap && player != currentPlayer)
                .Select(player => new
                {
                    Player = player,
                    Distance = Math.Sqrt(Math.Pow(player.NhanVatToaDo_X - currentPosX, 2) + Math.Pow(player.NhanVatToaDo_Y - currentPosY, 2))
                })
                .OrderBy(p => p.Distance)
                .Take(maxPartySize)
                .ToList();

            // Create a party if enough players are found
            if (nearbyPlayers.Count > 0)
            {
                // Assuming you have a method to create or add players to a party
                CreateOrAddToParty(currentPlayer, nearbyPlayers.Select(p => p.Player).ToList());
            }
        }
    }

    private void CreateOrAddToParty(Players currentPlayer, List<Players> partyMembers)
    {

        foreach (var member in partyMembers)
        {
            currentPlayer.Config.ToDoi = 1;
            member.Config.ToDoi = 1;
            currentPlayer.HandleSendTeam(member.CharacterFullServerID);
            WriteLine(2, "+Auto Party " + member.UserName);
            member.HandleParty(currentPlayer.CharacterFullServerID, 1);
        }

    }
}