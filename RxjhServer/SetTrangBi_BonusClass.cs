namespace RxjhServer;

public class SetTrangBi_BonusClass
{
    public int ID { get; set; }

    public string FLD_SetName { get; set; }

    public string FLD_SetGroup { get; set; }

    public int FLD_SetCount { get; set; }

    public int FLD_BonusATK { get; set; }

    public int FLD_BonusDEF { get; set; }

    public int FLD_BonusCLVC { get; set; }

    public int FLD_BonusULPT { get; set; }

    public int FLD_BonusHP { get; set; }

    public int FLD_BonusMP { get; set; }

    public int FLD_BonusCX { get; set; }

    public int FLD_BonusNT { get; set; }

    public int FLD_MonsterATK { get; set; }

    public int FLD_MonsterDEF { get; set; }

    public int FLD_PVE { get; set; }

    public int FLD_PVP { get; set; }

    public int FLD_DaKich { get; set; }

    public double FLD_LastDmgPercent { get; set; }

    public static SetTrangBi_BonusClass GetTBBonusClass(string CheckTrangBi)
    {
        foreach (var value in World.List_SetTrangBi.Values)
        {
            var count = 0;
            var list = CheckTrangBi.Split(',');
            for (var i = 0; i < list.Length; i++)
                if (list[i] == value.FLD_SetGroup)
                    count++;
            if (count >= value.FLD_SetCount) return value;
        }

        return null;
    }
}