using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace RxjhServer;

public class FormUser组队 : Form
{
    private ColumnHeader columnHeader1;

    private ColumnHeader columnHeader10;

    private ColumnHeader columnHeader11;

    private ColumnHeader columnHeader12;

    private ColumnHeader columnHeader13;

    private ColumnHeader columnHeader14;

    private ColumnHeader columnHeader15;

    private ColumnHeader columnHeader16;

    private ColumnHeader columnHeader17;

    private ColumnHeader columnHeader18;

    private ColumnHeader columnHeader19;

    private ColumnHeader columnHeader2;

    private ColumnHeader columnHeader3;

    private ColumnHeader columnHeader4;

    private ColumnHeader columnHeader5;

    private ColumnHeader columnHeader6;

    private ColumnHeader columnHeader7;

    private ColumnHeader columnHeader8;

    private ColumnHeader columnHeader9;
    private IContainer components;

    private ListView listView1;

    private ListView listView2;

    private SplitContainer splitContainer1;

    public FormUser组队()
    {
        InitializeComponent();
    }

    private void FormUser组队_Load(object sender, EventArgs e)
    {
        using var enumerator = World.WToDoi.Values.GetEnumerator();
        string[] array = null;
        X_To_Doi_Class 组队Class = null;
        while (enumerator.MoveNext())
        {
            组队Class = enumerator.Current;
            array = new string[2];
            try
            {
                array[0] = 组队Class.TeamID.ToString();
                array[1] = 组队Class.ToDoi_NguoiChoi.Count.ToString();
                listView1.Items.Insert(listView1.Items.Count, new ListViewItem(array));
            }
            catch
            {
            }
        }
    }

    private void listView1_Click(object sender, EventArgs e)
    {
        var num = 0;
        num = 1;
        IEnumerator<Players> enumerator = null;
        X_To_Doi_Class value = null;
        Players players = null;
        string[] array = null;
        if (listView1.SelectedItems.Count <= 0) return;
        num = 0;
        num = 2;
        if (!World.WToDoi.TryGetValue(int.Parse(listView1.SelectedItems[0].SubItems[0].Text), out value)) return;
        num = 4;
        listView2.Items.Clear();
        enumerator = value.ToDoi_NguoiChoi.Values.GetEnumerator();
        num = 3;
        try
        {
            num = 2;
            while (true)
            {
                num = 3;
                if (!enumerator.MoveNext()) break;
                players = enumerator.Current;
                array = new string[17];
                num = 0;
                try
                {
                    array[0] = players.CharacterFullServerID.ToString();
                    array[1] = players.Userid;
                    array[2] = players.UserName;
                    array[3] = players.Player_Level.ToString();
                    array[4] = players.NhanVat_HP.ToString();
                    num = 0;
                    if (players.Client != null)
                    {
                        num = 2;
                        array[5] = players.Client.ToString();
                        num = 1;
                    }

                    array[6] = players.NhanVatToaDo_BanDo.ToString();
                    array[7] = players.NhanVatToaDo_X.ToString();
                    array[8] = players.NhanVatToaDo_Y.ToString();
                    array[9] = players.FLD_NhanVatCoBan_CongKich.ToString();
                    array[10] = players.FLD_ThemVaoTiLePhanTram_CongKich.ToString();
                    array[11] = players.FLD_NhanVatCoBan_PhongNgu.ToString();
                    array[12] = players.FLD_ThemVaoTiLePhanTram_PhongNgu.ToString();
                    array[13] = players.FLD_TrangBi_ThemVao_KhiCong.ToString();
                    array[14] = players.Client.dwStop.ToString();
                    array[15] = players.CuongHoaVK.ToString();
                    array[16] = players.CuongHoaTB.ToString();
                    num = 3;
                }
                catch
                {
                    array[0] = players.CharacterFullServerID.ToString();
                    array[1] = players.Userid;
                    array[2] = players.UserName;
                    array[3] = players.Player_Level.ToString();
                }

                listView2.Items.Insert(listView2.Items.Count, new ListViewItem(array));
                num = 4;
            }

            num = 1;
            num = 5;
        }
        finally
        {
            num = 0;
            while (true)
            {
                switch (num)
                {
                    case 2:
                        break;
                    case 1:
                        enumerator.Dispose();
                        num = 2;
                        continue;
                    default:
                        if (enumerator != null)
                        {
                            num = 1;
                            continue;
                        }

                        break;
                }

                break;
            }
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        listView1 = new ListView();
        columnHeader5 = new ColumnHeader();
        columnHeader1 = new ColumnHeader();
        splitContainer1 = new SplitContainer();
        listView2 = new ListView();
        columnHeader2 = new ColumnHeader();
        columnHeader3 = new ColumnHeader();
        columnHeader4 = new ColumnHeader();
        columnHeader6 = new ColumnHeader();
        columnHeader9 = new ColumnHeader();
        columnHeader7 = new ColumnHeader();
        columnHeader8 = new ColumnHeader();
        columnHeader10 = new ColumnHeader();
        columnHeader11 = new ColumnHeader();
        columnHeader12 = new ColumnHeader();
        columnHeader14 = new ColumnHeader();
        columnHeader13 = new ColumnHeader();
        columnHeader15 = new ColumnHeader();
        columnHeader16 = new ColumnHeader();
        columnHeader17 = new ColumnHeader();
        columnHeader18 = new ColumnHeader();
        columnHeader19 = new ColumnHeader();
        splitContainer1.Panel1.SuspendLayout();
        splitContainer1.Panel2.SuspendLayout();
        splitContainer1.SuspendLayout();
        SuspendLayout();
        listView1.Columns.AddRange(new ColumnHeader[2] { columnHeader5, columnHeader1 });
        listView1.Dock = DockStyle.Fill;
        listView1.ForeColor = SystemColors.WindowText;
        listView1.FullRowSelect = true;
        listView1.GridLines = true;
        listView1.Location = new Point(0, 0);
        listView1.Name = "listView1";
        listView1.Size = new Size(550, 168);
        listView1.TabIndex = 9;
        listView1.UseCompatibleStateImageBehavior = false;
        listView1.View = View.Details;
        listView1.Click += listView1_Click;
        columnHeader5.Text = "名称";
        columnHeader5.Width = 71;
        columnHeader1.Text = "数据";
        columnHeader1.Width = 90;
        splitContainer1.Dock = DockStyle.Fill;
        splitContainer1.Location = new Point(0, 0);
        splitContainer1.Name = "splitContainer1";
        splitContainer1.Orientation = Orientation.Horizontal;
        splitContainer1.Panel1.Controls.Add(listView1);
        splitContainer1.Panel2.Controls.Add(listView2);
        splitContainer1.Size = new Size(550, 372);
        splitContainer1.SplitterDistance = 168;
        splitContainer1.TabIndex = 10;
        listView2.Columns.AddRange(new ColumnHeader[17]
        {
            columnHeader2, columnHeader3, columnHeader4, columnHeader6, columnHeader9, columnHeader7, columnHeader8,
            columnHeader10, columnHeader11, columnHeader12,
            columnHeader14, columnHeader13, columnHeader15, columnHeader16, columnHeader17, columnHeader18,
            columnHeader19
        });
        listView2.Dock = DockStyle.Fill;
        listView2.ForeColor = SystemColors.WindowText;
        listView2.FullRowSelect = true;
        listView2.GridLines = true;
        listView2.Location = new Point(0, 0);
        listView2.Name = "listView2";
        listView2.Size = new Size(550, 200);
        listView2.TabIndex = 2;
        listView2.UseCompatibleStateImageBehavior = false;
        listView2.View = View.Details;
        columnHeader2.Text = "序号";
        columnHeader2.Width = 36;
        columnHeader3.Text = "ID";
        columnHeader3.Width = 66;
        columnHeader4.Text = "名字";
        columnHeader4.Width = 98;
        columnHeader6.Text = "等级";
        columnHeader6.Width = 38;
        columnHeader9.Text = "HP";
        columnHeader9.Width = 47;
        columnHeader7.Text = "IP";
        columnHeader7.Width = 113;
        columnHeader8.Text = "地图";
        columnHeader8.Width = 42;
        columnHeader10.Text = "X";
        columnHeader10.Width = 61;
        columnHeader11.Text = "Y";
        columnHeader11.Width = 61;
        columnHeader12.Text = "攻";
        columnHeader12.Width = 36;
        columnHeader14.Text = "攻加";
        columnHeader14.Width = 41;
        columnHeader13.Text = "防";
        columnHeader13.Width = 37;
        columnHeader15.Text = "防加";
        columnHeader15.Width = 39;
        columnHeader16.Text = "气";
        columnHeader16.Width = 39;
        columnHeader17.Text = "ping";
        columnHeader17.Width = 39;
        columnHeader18.Text = "攻强";
        columnHeader18.Width = 39;
        columnHeader19.Text = "防强";
        columnHeader19.Width = 37;
        AutoScaleDimensions = new SizeF(6f, 12f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(550, 372);
        Controls.Add(splitContainer1);
        Name = "FormUser组队";
        StartPosition = FormStartPosition.CenterScreen;
        Text = "FormUser组队";
        Load += FormUser组队_Load;
        splitContainer1.Panel1.ResumeLayout(false);
        splitContainer1.Panel2.ResumeLayout(false);
        splitContainer1.ResumeLayout(false);
        ResumeLayout(false);
    }
}