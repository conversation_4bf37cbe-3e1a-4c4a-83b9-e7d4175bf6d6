using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace RxjhServer;

public class Class21
{
    private static readonly string string_0 = "abcd";

    public string Encrypt(string string_1)
    {
        try
        {
            var bytes = Encoding.Unicode.GetBytes(string_0);
            var bytes2 = Encoding.Unicode.GetBytes(string_1);
            var dESCryptoServiceProvider = new DESCryptoServiceProvider();
            var memoryStream = new MemoryStream();
            var cryptoStream = new CryptoStream(memoryStream, dESCryptoServiceProvider.CreateEncryptor(bytes, bytes),
                CryptoStreamMode.Write);
            cryptoStream.Write(bytes2, 0, bytes2.Length);
            cryptoStream.FlushFinalBlock();
            var inArray = memoryStream.ToArray();
            cryptoStream.Close();
            memoryStream.Close();
            return Convert.ToBase64String(inArray);
        }
        catch
        {
            return string_1;
        }
    }

    public string Decrypt(string string_1)
    {
        try
        {
            var bytes = Encoding.Unicode.GetBytes(string_0);
            var array = Convert.FromBase64String(string_1);
            var dESCryptoServiceProvider = new DESCryptoServiceProvider();
            var memoryStream = new MemoryStream();
            var cryptoStream = new CryptoStream(memoryStream, dESCryptoServiceProvider.CreateDecryptor(bytes, bytes),
                CryptoStreamMode.Write);
            cryptoStream.Write(array, 0, array.Length);
            cryptoStream.FlushFinalBlock();
            var bytes2 = memoryStream.ToArray();
            cryptoStream.Close();
            memoryStream.Close();
            return Encoding.Unicode.GetString(bytes2);
        }
        catch
        {
            return string_1;
        }
    }
}