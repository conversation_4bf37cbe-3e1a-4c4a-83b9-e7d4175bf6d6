using System;

namespace RxjhServer;

public class X_Vo_Cong_Loai : IDisposable
{
    public int FLD_TYPE;

    public X_Vo_Cong_Loai()
    {
    }

    public X_Vo_Cong_Loai(int FLD_PID_)
    {
        FLD_PID = FLD_PID_;
        初始化武功(FLD_PID);
    }

    public int VoCong_DangCap { get; set; }

    public int FLD_PID { get; set; }

    public string FLD_NAME { get; set; }

    public string FLD_MoiCapNguyHai { get; set; }

    public int FLD_ZX { get; set; }

    public int FLD_JOB { get; set; }

    public int FLD_JOBLEVEL { get; set; }

    public int FLD_LEVEL { get; set; }

    public int FLD_MP { get; set; }

    public int FLD_NEEDEXP { get; set; }

    public int FLD_AT { get; set; }

    public int FLD_EFFERT { get; set; }

    public int FLD_INDEX { get; set; }

    public int FLD_CongKichSoLuong { get; set; }

    public int FLD_VoCongLoaiHinh { get; set; }

    public int FLD_TIME { get; set; }

    public int FLD_DEATHTIME { get; set; }

    public int FLD_CDTIME { get; set; }

    public int FLD_VoCongToiCaoDangCap { get; set; }

    public int FLD_MoiCapThemMP { get; set; }

    public int FLD_MoiCapThemLichLuyen { get; set; }

    public int FLD_MoiCapThemNguyHai { get; set; }

    public int FLD_MoiCapVoCongDiemSo { get; set; }

    public int FLD_MoiCapThemTuLuyenDangCap { get; set; }

    public int Time_Animation { get; set; }

    public byte[] 武功ID_byte => Buffer.GetBytes(FLD_PID);

    public void Dispose()
    {
    }

    public void 初始化武功(int int_0)
    {
        var num = 0;
        X_Vo_Cong_Loai value = null;
        while (true)
            switch (num)
            {
                case 1:
                    return;
                default:
                    if (World.TBL_KONGFU.TryGetValue(int_0, out value))
                    {
                        num = 2;
                        break;
                    }

                    return;
                case 2:
                    FLD_NAME = value.FLD_NAME;
                    FLD_AT = value.FLD_AT;
                    FLD_EFFERT = value.FLD_EFFERT;
                    FLD_INDEX = value.FLD_INDEX;
                    FLD_JOB = value.FLD_JOB;
                    FLD_JOBLEVEL = value.FLD_JOBLEVEL;
                    FLD_LEVEL = value.FLD_LEVEL;
                    FLD_MP = value.FLD_MP;
                    FLD_NEEDEXP = value.FLD_NEEDEXP;
                    FLD_PID = value.FLD_PID;
                    FLD_TYPE = value.FLD_TYPE;
                    FLD_ZX = value.FLD_ZX;
                    FLD_CongKichSoLuong = value.FLD_CongKichSoLuong;
                    FLD_VoCongLoaiHinh = value.FLD_VoCongLoaiHinh;
                    FLD_MoiCapThemMP = value.FLD_MoiCapThemMP;
                    FLD_MoiCapThemLichLuyen = value.FLD_MoiCapThemLichLuyen;
                    FLD_MoiCapThemNguyHai = value.FLD_MoiCapThemNguyHai;
                    FLD_MoiCapVoCongDiemSo = value.FLD_MoiCapVoCongDiemSo;
                    FLD_DEATHTIME = value.FLD_DEATHTIME;
                    FLD_CDTIME = value.FLD_CDTIME;
                    FLD_VoCongToiCaoDangCap = value.FLD_VoCongToiCaoDangCap;
                    FLD_MoiCapThemTuLuyenDangCap = value.FLD_MoiCapThemTuLuyenDangCap;
                    FLD_MoiCapNguyHai = value.FLD_MoiCapNguyHai;
                    num = 1;
                    break;
            }
    }

    public int GetAt(int pid, int Level)
    {
        try
        {
            if (World.TBL_KONGFU.TryGetValue(pid, out var value))
                return int.Parse(value.FLD_MoiCapNguyHai.Split(';')[Level - 1]);
        }
        catch
        {
        }

        return 0;
    }

    public static X_Vo_Cong_Loai GetWg(int 人物正邪, int 人物职业, int FLD_武功类型, int FLD_INDEX)
    {
        using (var enumerator = World.TBL_KONGFU.Values.GetEnumerator())
        {
            X_Vo_Cong_Loai 武功类 = null;
            X_Vo_Cong_Loai result = null;
            var flag = true;
            while (true)
            {
                IL_01ad:
                var flag2 = true;
                while (enumerator.MoveNext())
                {
                    武功类 = enumerator.Current;
                    while (true)
                    {
                        switch (武功类.FLD_ZX == 0 ? 1 : 9)
                        {
                            case 7:
                                break;
                            case 13:
                                goto end_IL_0023;
                            case 9:
                                if (武功类.FLD_ZX != 人物正邪) goto IL_01ad;
                                goto case 10;
                            case 10:
                            case 14:
                                if (武功类.FLD_JOB != 人物职业) goto IL_01ad;
                                goto case 0;
                            case 1:
                            case 16:
                                if (武功类.FLD_JOB != 人物职业) goto IL_01ad;
                                goto case 3;
                            case 0:
                            case 18:
                                if (武功类.FLD_INDEX != FLD_INDEX) goto IL_01ad;
                                goto case 6;
                            case 6:
                            case 19:
                                if (武功类.FLD_VoCongLoaiHinh != FLD_武功类型) goto IL_01ad;
                                goto case 11;
                            case 3:
                            case 20:
                                if (武功类.FLD_INDEX != FLD_INDEX) goto IL_01ad;
                                goto case 5;
                            case 5:
                            case 17:
                                if (武功类.FLD_VoCongLoaiHinh != FLD_武功类型) goto IL_01ad;
                                goto case 15;
                            case 11:
                                result = 武功类;
                                goto case 21;
                            case 15:
                                result = 武功类;
                                goto case 12;
                            case 12:
                                return result;
                            case 21:
                                return result;
                            case 8:
                                goto end_IL_0023;
                            case 4:
                                continue;
                            default:
                                goto IL_01ad;
                        }

                        break;
                    }

                    continue;
                    end_IL_0023:
                    break;
                }

                break;
            }
        }

        return null;
    }

    public static X_Vo_Cong_Loai GetWg(int 武功ID)
    {
        using (var enumerator = World.TBL_KONGFU.Values.GetEnumerator())
        {
            X_Vo_Cong_Loai 武功类 = null;
            while (enumerator.MoveNext())
            {
                武功类 = enumerator.Current;
                if (武功类.FLD_PID == 武功ID) return 武功类;
            }
        }

        return null;
    }

    public static X_Vo_Cong_Loai Getsfewg(Players Playe, int wgid)
    {
        X_Vo_Cong_Loai value = null;
        if (World.TBL_KONGFU.TryGetValue(wgid, out value) && value != null &&
            Playe.VoCongMoi[value.FLD_VoCongLoaiHinh, value.FLD_INDEX] != null &&
            Playe.VoCongMoi[value.FLD_VoCongLoaiHinh, value.FLD_INDEX].FLD_PID == wgid)
            return Playe.VoCongMoi[value.FLD_VoCongLoaiHinh, value.FLD_INDEX];
        return null;
    }

    public static bool GetsfeWg(Players Playe, int wgid)
    {
        X_Vo_Cong_Loai value = null;
        if (World.TBL_KONGFU.TryGetValue(wgid, out value) && value != null)
            return Playe.VoCongMoi[value.FLD_VoCongLoaiHinh, value.FLD_INDEX].FLD_PID == wgid;
        return false;
    }

    public static X_Vo_Cong_Loai GetWg2(Players Playe, int FLD_武功类型, int FLD_INDEX)
    {
        using (var enumerator = World.TBL_KONGFU.Values.GetEnumerator())
        {
            X_Vo_Cong_Loai 武功类 = null;
            while (enumerator.MoveNext())
            {
                武功类 = enumerator.Current;
                if (武功类.FLD_JOB == Playe.Player_Job && 武功类.FLD_INDEX == FLD_INDEX &&
                    武功类.FLD_VoCongLoaiHinh == FLD_武功类型) return 武功类;
            }
        }

        return null;
    }

    public static bool KiemTra_DieuKienTuLuyen(Players Playe, int FLD_武功类型, int FLD_INDEX)
    {
        var wg = GetWg(Playe.Player_Zx, Playe.Player_Job, FLD_武功类型, FLD_INDEX);
        if (wg != null)
        {
            if ((wg.FLD_ZX != 0 && Playe.Player_Zx != wg.FLD_ZX) ||
                (wg.FLD_JOB != 0 && Playe.Player_Job != wg.FLD_JOB) ||
                (wg.FLD_JOBLEVEL != 0 && Playe.Player_Job_level < wg.FLD_JOBLEVEL) ||
                (wg.FLD_LEVEL != 0 && Playe.Player_Level < wg.FLD_LEVEL)) return false;
            if (Playe.VoCongMoi[FLD_武功类型, FLD_INDEX] != null)
            {
                Playe.HeThongNhacNho("Võ công đã học");
                return false;
            }
        }

        return true;
    }

    public static void LearnMartialArtsBook(Players Playe, int FLD_VoCongLoaiHinh, int FLD_INDEX)
    {
        var wg = GetWg(Playe.Player_Zx, Playe.Player_Job, FLD_VoCongLoaiHinh, FLD_INDEX);
        if (wg != null)
        {
            Playe.VoCongMoi[wg.FLD_VoCongLoaiHinh, wg.FLD_INDEX] = new X_Vo_Cong_Loai(wg.FLD_PID);
            if (wg.FLD_VoCongLoaiHinh == 3) Playe.VoCongMoi[wg.FLD_VoCongLoaiHinh, wg.FLD_INDEX].VoCong_DangCap = 1;
            if ((Playe.Player_Job == 8 || Playe.Player_Job == 9) &&
                (wg.FLD_VoCongLoaiHinh == 0 || wg.FLD_VoCongLoaiHinh == 3))
                Playe.VoCongMoi[wg.FLD_VoCongLoaiHinh, wg.FLD_INDEX].VoCong_DangCap = 1;
        }
    }

    public static int GetTimeAnimation(int pid)
    {
        if (World.TBL_KONGFU.TryGetValue(pid, out var value)) return value.Time_Animation;
        return 0;
    }
}