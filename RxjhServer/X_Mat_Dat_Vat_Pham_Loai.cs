using System;
using System.Timers;

namespace RxjhServer;

public class X_Mat_Dat_Vat_Pham_Loai : IDisposable
{
    public long id;
    private System.Timers.Timer npcyd;

    public ThreadSafeDictionary<int, Players> PlayList;

    public X_Mat_Dat_Vat_Pham_Loai()
    {
        PlayList = new ThreadSafeDictionary<int, Players>();
    }

    public X_Mat_Dat_Vat_Pham_Loai(byte[] VatPham_byte_, float x, float y, float z, int map, int VatPham_NoiRotRa)
    {
        PlayList = new ThreadSafeDictionary<int, Players>();
        time = DateTime.Now;
        VatPham = new X_Vat_Pham_Loai(VatPham_byte_);
        VatPham_byte = VatPham_byte_;
        id = BitConverter.ToInt64(VatPham.DatDuocGlobal_ID(), 0);
        Rxjh_X = x;
        Rxjh_Y = y;
        Rxjh_Z = z;
        Rxjh_Map = map;
        this.VatPham_NoiRotRa = VatPham_NoiRotRa;
        npcyd = new System.Timers.Timer(60000.0);
        npcyd.Elapsed += npcydtheout2;
        npcyd.Enabled = true;
        npcyd.AutoReset = false;
    }

    public X_Mat_Dat_Vat_Pham_Loai(byte[] VatPham_byte_, float x, float y, float z, int map, Players name,
        int VatPham_NoiRotRa)
    {
        PlayList = new ThreadSafeDictionary<int, Players>();
        VatPham_QuyenUuTien = name;
        time = DateTime.Now;
        VatPham = new X_Vat_Pham_Loai(VatPham_byte_);
        VatPham_byte = VatPham_byte_;
        id = BitConverter.ToInt64(VatPham.DatDuocGlobal_ID(), 0);
        Rxjh_X = x;
        Rxjh_Y = y;
        Rxjh_Z = z;
        Rxjh_Map = map;
        this.VatPham_NoiRotRa = VatPham_NoiRotRa;
        npcyd = new System.Timers.Timer(60000.0);
        npcyd.Elapsed += npcydtheout2;
        npcyd.Enabled = true;
        npcyd.AutoReset = false;
    }

    public DateTime time { get; set; }

    public X_Vat_Pham_Loai VatPham { get; set; }

    public byte[] VatPham_byte { get; set; }

    public float Rxjh_X { get; set; }

    public float Rxjh_Y { get; set; }

    public float Rxjh_Z { get; set; }

    public int Rxjh_Map { get; set; }

    public int FLD_PhiDiChuyen { get; set; }

    public Players VatPham_QuyenUuTien { get; set; }

    public int VatPham_NoiRotRa { get; set; }

    public void Dispose()
    {
        try
        {
            if (npcyd != null)
            {
                npcyd.Enabled = false;
                npcyd.Close();
                npcyd.Dispose();
                npcyd = null;
            }

            if (PlayList != null)
            {
                PlayList.Clear();
                PlayList.Dispose();
            }

            PlayList = null;
            VatPham = null;
            VatPham_QuyenUuTien = null;
        }
        catch (Exception)
        {
        }
    }

    ~X_Mat_Dat_Vat_Pham_Loai()
    {
    }

    public void npcydtheout()
    {
        try
        {
            if (npcyd != null)
            {
                npcyd.Enabled = false;
                npcyd.Close();
                npcyd.Dispose();
                npcyd = null;
            }

            World.ItmeTeM.Remove(id);
            获取范围玩家发送地面消失物品数据包();
        }
        catch (Exception ex)
        {
            var array = new string[6]
            {
                "物品消失 出错npcydtheout ：",
                Buffer.ToInt32(VatPham.DatDuocGlobal_ID(), 0).ToString(),
                " [",
                VatPham.DatDuocVatPhamTen_XungHao(),
                "]",
                null
            };
            array[5] = ex?.ToString();
            Form1.WriteLine(1, string.Concat(array));
        }
        finally
        {
            World.ItmeTeM.Remove(id);
            Dispose();
        }
    }

    public void npcydtheout2(object sender, ElapsedEventArgs e)
    {
        try
        {
            if (npcyd != null)
            {
                npcyd.Enabled = false;
                npcyd.Close();
                npcyd.Dispose();
                npcyd = null;
            }

            World.ItmeTeM.Remove(id);
            获取范围玩家发送地面消失物品数据包();
        }
        catch (Exception ex)
        {
            var array = new string[6]
            {
                "物品消失 出错npcydtheout2 ：",
                BitConverter.ToInt64(VatPham.DatDuocGlobal_ID(), 0).ToString(),
                " [",
                VatPham.DatDuocVatPhamTen_XungHao(),
                "]",
                null
            };
            array[5] = ex?.ToString();
            Form1.WriteLine(1, string.Concat(array));
        }
        finally
        {
            World.ItmeTeM.Remove(id);
            Dispose();
        }
    }

    public void GetARangeOfPlayersSendGroundIncreaseItemSoLieuPackage()
    {
        try
        {
            var enumerator = World.allConnectedChars.Values.GetEnumerator();
            try
            {
                Players players = null;
                while (enumerator.MoveNext())
                {
                    players = enumerator.Current;
                    if (Tra_tim_pham_vi_nguoi_choi(400, players)) players.ObtainGroundObjectsInTheReviewArea();
                }
            }
            finally
            {
                var num2 = 0;
                while (true)
                {
                    switch (num2)
                    {
                        case 1:
                            break;
                        default:
                            if (enumerator != null)
                            {
                                num2 = 2;
                                continue;
                            }

                            break;
                        case 2:
                            enumerator.Dispose();
                            num2 = 1;
                            continue;
                    }

                    break;
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1,
                "Thu hoạch phạm vi người chơi gửi đi mặt đất gia tăng vật phẩm số liệu bao Phạm sai lầm: " + ex);
        }
    }

    public void 获取范围玩家发送地面消失物品数据包()
    {
        try
        {
            var num2 = 0;
            num2 = 1;
            if (PlayList != null)
            {
                num2 = 4;
                try
                {
                    var enumerator = PlayList.Values.GetEnumerator();
                    try
                    {
                        num2 = 2;
                        while (true)
                        {
                            num2 = 3;
                            if (!enumerator.MoveNext()) break;
                            var current = enumerator.Current;
                            current.ObtainGroundObjectsInTheReviewArea();
                            num2 = 1;
                        }

                        num2 = 4;
                        num2 = 0;
                    }
                    finally
                    {
                        num2 = 1;
                        while (true)
                        {
                            switch (num2)
                            {
                                case 0:
                                    break;
                                default:
                                    if (enumerator != null)
                                    {
                                        num2 = 2;
                                        continue;
                                    }

                                    break;
                                case 2:
                                    enumerator.Dispose();
                                    num2 = 0;
                                    continue;
                            }

                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Form1.WriteLine(1,
                        "Thu hoạch phạm vi người chơi gửi đi mặt đất biến mất vật phẩm số liệu bao 1 Phạm sai lầm: " +
                        ex);
                }

                num2 = 5;
                if (PlayList != null)
                {
                    num2 = 0;
                    PlayList.Clear();
                    num2 = 3;
                }
            }

            num2 = 2;
        }
        catch (Exception ex2)
        {
            Form1.WriteLine(1,
                "Thu hoạch phạm vi người chơi gửi đi mặt đất biến mất vật phẩm số liệu bao 3 Phạm sai lầm: " + ex2);
        }
    }

    public bool Tra_tim_pham_vi_nguoi_choi(int far_, Players Playe)
    {
        if (Playe.NhanVatToaDo_BanDo != Rxjh_Map) return false;
        var num = Playe.NhanVatToaDo_X - Rxjh_X;
        var num2 = Playe.NhanVatToaDo_Y - Rxjh_Y;
        return (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far_;
    }

    public static X_Mat_Dat_Vat_Pham_Loai GetItme(long long_1)
    {
        if (World.ItmeTeM.TryGetValue(long_1, out var value)) return value;
        return null;
    }
}