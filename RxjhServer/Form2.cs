using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class Form2 : Form
{
    private ColumnHeader columnHeader10;

    private ColumnHeader columnHeader11;

    private ColumnHeader columnHeader12;

    private ColumnHeader columnHeader13;

    private ColumnHeader columnHeader14;

    private ColumnHeader columnHeader15;

    private ColumnHeader columnHeader16;

    private ColumnHeader columnHeader17;

    private ColumnHeader columnHeader2;

    private ColumnHeader columnHeader3;

    private ColumnHeader columnHeader4;

    private ColumnHeader columnHeader8;
    private IContainer components;

    private ListView listView2;

    public Form2()
    {
        InitializeComponent();
    }

    public static void FlushAll1()
    {
        DataTable dataTable = null;
        var num3 = 0;
        string[] array = null;
        var num4 = 0;
        Form1.WriteLine(100, "Xóa bỏ nhân vật số liệu bắt đầu 111");
        var dBToDataTable = DBA.GetDBToDataTable("select * from TBL_XWWL_Char");
        while (dBToDataTable != null)
        {
            var flag = true;
            while (true)
            {
                Form1.WriteLine(100, "Tổng cộng có nhân vật số liệu 111" + dBToDataTable.Rows.Count);
                num3 = 0;
                while (true)
                {
                    IL_0393:
                    var flag2 = true;
                    while (true)
                    {
                        switch (num3 < dBToDataTable.Rows.Count ? 1 : 13)
                        {
                            case 6:
                                break;
                            case 1:
                                if ((int)DBA.GetDBValue_3(
                                        string.Format("select count(*) from TBL_ACCOUNT where FLD_ID='{0}'",
                                            dBToDataTable.Rows[num3]["FLD_ID"]), "rxjhaccount") < 1) goto case 12;
                                goto case 16;
                            case 12:
                                num4++;
                                array = new string[5]
                                {
                                    "删除人物[",
                                    dBToDataTable.Rows[num3]["FLD_ID"].ToString(),
                                    "] [",
                                    null,
                                    null
                                };
                                goto case 11;
                            case 11:
                            {
                                var array2 = array;
                                array2[3] = dBToDataTable.Rows[num3]["FLD_NAME"]?.ToString();
                                array[4] = "]";
                                Form1.WriteLine(100, string.Concat(array));
                                DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_Char WHERE FLD_NAME  ='{0}'",
                                    dBToDataTable.Rows[num3]["FLD_NAME"]));
                                DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_warehouse WHERE FLD_NAME  ='{0}'",
                                    dBToDataTable.Rows[num3]["FLD_NAME"]));
                                dataTable = DBA.GetDBToDataTable(string.Format(
                                    "select * from TBL_XWWL_GuildMember WHERE FLD_NAME ='{0}'",
                                    dBToDataTable.Rows[num3]["FLD_NAME"]));
                                goto case 19;
                            }
                            case 19:
                                if (dataTable != null) goto case 0;
                                goto case 16;
                            case 0:
                            case 14:
                                if (dataTable.Rows.Count > 0) goto case 4;
                                goto case 5;
                            case 4:
                            case 8:
                                if (Buffer.IsEquals(dataTable.Rows[0]["leve"].ToString(), "6")) goto case 2;
                                DBA.ExeSqlCommand(string.Format(
                                    "DELETE FROM TBL_XWWL_GuildMember WHERE FLD_NAME  ='{0}'",
                                    dBToDataTable.Rows[num3]["FLD_NAME"]));
                                goto case 5;
                            case 2:
                            case 3:
                            {
                                var str = "删除帮派";
                                Form1.WriteLine(100, str + dataTable.Rows[0]["G_Name"]);
                                DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_Guild WHERE G_Name  ='{0}'",
                                    dataTable.Rows[0]["G_Name"]));
                                DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_GuildMember WHERE G_Name  ='{0}'",
                                    dataTable.Rows[0]["G_Name"]));
                                goto case 5;
                            }
                            case 5:
                            case 17:
                                dataTable.Dispose();
                                goto case 16;
                            case 16:
                                num3++;
                                goto IL_0393;
                            default:
                                num4 = 0;
                                Form1.WriteLine(100, "Xóa bỏ nhân vật số liệu bắt đầu 222");
                                dBToDataTable = DBA.GetDBToDataTable("select * from TBL_XWWL_Char");
                                goto end_IL_002e;
                            case 13:
                                dBToDataTable.Dispose();
                                goto end_IL_03a9;
                            case 15:
                                continue;
                            case 7:
                            case 9:
                                goto IL_0393;
                            case 10:
                                goto end_IL_002e;
                            case 18:
                                goto end_IL_03a9;
                        }

                        break;
                    }

                    break;
                }

                continue;
                end_IL_002e:
                break;
            }

            continue;
            end_IL_03a9:
            break;
        }

        Form1.WriteLine(100, "Xóa bỏ nhân vật số liệu hoàn thành " + num4);
    }

    public static void FlushAll2()
    {
        DataTable dataTable = null;
        var num3 = 0;
        string[] array = null;
        string[] array2 = null;
        var num4 = 0;
        Form1.WriteLine(100, "Xóa bỏ nhân vật nhà kho số liệu bắt đầu 111");
        var dBToDataTable = DBA.GetDBToDataTable("select * from TBL_XWWL_warehouse");
        while (dBToDataTable != null)
        {
            var flag = true;
            while (true)
            {
                Form1.WriteLine(100, "Tổng cộng có nhân vật số liệu 222" + dBToDataTable.Rows.Count);
                num3 = 0;
                while (true)
                {
                    IL_0352:
                    var flag2 = true;
                    while (true)
                    {
                        IL_0067:
                        if (num3 < dBToDataTable.Rows.Count)
                        {
                            dataTable = DBA.GetDBToDataTable(string.Format(
                                "select * from TBL_XWWL_Char where FLD_NAME='{0}'",
                                dBToDataTable.Rows[num3]["FLD_NAME"]));
                            while (true)
                            {
                                switch (dataTable.Rows.Count < 1 ? 1 : 10)
                                {
                                    case 11:
                                        break;
                                    case 5:
                                        goto IL_0067;
                                    case 1:
                                        num4++;
                                        array2 = new string[5]
                                        {
                                            "删除人物仓库[",
                                            dBToDataTable.Rows[num3]["FLD_ID"].ToString(),
                                            "] [",
                                            null,
                                            null
                                        };
                                        goto case 4;
                                    case 4:
                                    {
                                        var array4 = array2;
                                        array4[3] = dBToDataTable.Rows[num3]["FLD_NAME"]?.ToString();
                                        array2[4] = "]";
                                        Form1.WriteLine(100, string.Concat(array2));
                                        DBA.ExeSqlCommand(string.Format(
                                            "DELETE FROM TBL_XWWL_warehouse WHERE FLD_ID='{0}'and FLD_NAME  ='{1}'",
                                            dBToDataTable.Rows[num3]["FLD_ID"], dBToDataTable.Rows[num3]["FLD_NAME"]));
                                        goto case 9;
                                    }
                                    case 10:
                                        if (dataTable.Rows[0]["FLD_ID"].ToString() !=
                                            dBToDataTable.Rows[num3]["FLD_ID"].ToString()) goto case 6;
                                        goto case 9;
                                    case 6:
                                        num4++;
                                        array = new string[5]
                                        {
                                            "删除人物仓库[",
                                            dBToDataTable.Rows[num3]["FLD_ID"].ToString(),
                                            "] [",
                                            null,
                                            null
                                        };
                                        goto case 7;
                                    case 7:
                                    {
                                        var array3 = array;
                                        array3[3] = dBToDataTable.Rows[num3]["FLD_NAME"]?.ToString();
                                        array[4] = "]";
                                        Form1.WriteLine(100, string.Concat(array));
                                        DBA.ExeSqlCommand(string.Format(
                                            "DELETE FROM TBL_XWWL_warehouse WHERE FLD_ID='{0}'and FLD_NAME  ='{1}'",
                                            dBToDataTable.Rows[num3]["FLD_ID"], dBToDataTable.Rows[num3]["FLD_NAME"]));
                                        goto case 9;
                                    }
                                    case 9:
                                    case 13:
                                        dataTable.Dispose();
                                        num3++;
                                        goto IL_0352;
                                    default:
                                        num4 = 0;
                                        Form1.WriteLine(100, "Xóa bỏ nhân vật nhà kho số liệu bắt đầu 222");
                                        dBToDataTable = DBA.GetDBToDataTable("select * from TBL_XWWL_warehouse");
                                        goto end_IL_0031;
                                    case 12:
                                        continue;
                                    case 2:
                                        goto IL_033e;
                                    case 8:
                                    case 14:
                                        goto IL_0352;
                                    case 0:
                                        goto end_IL_0031;
                                    case 3:
                                        goto end_IL_0368;
                                }

                                break;
                            }

                            break;
                        }

                        IL_033e:
                        dBToDataTable.Dispose();
                        goto end_IL_0368;
                    }

                    break;
                }

                continue;
                end_IL_0031:
                break;
            }

            continue;
            end_IL_0368:
            break;
        }

        Form1.WriteLine(100, "Xóa bỏ nhân vật nhà kho số liệu hoàn thành " + num4);
    }

    public static void FlushAll3()
    {
        var num3 = 0;
        var num4 = 0;
        Form1.WriteLine(100, "Xóa bỏ tông hợp nhà kho bắt đầu 111");
        var dBToDataTable = DBA.GetDBToDataTable("select * from TBL_XWWL_PublicWarehouse");
        while (dBToDataTable != null)
        {
            var flag = true;
            while (true)
            {
                Form1.WriteLine(100, "Tổng cộng có tông hợp nhà kho số liệu" + dBToDataTable.Rows.Count);
                num3 = 0;
                while (true)
                {
                    IL_018f:
                    var flag2 = true;
                    while (true)
                    {
                        switch (num3 >= dBToDataTable.Rows.Count ? 5 : 0)
                        {
                            case 9:
                                break;
                            case 0:
                                if ((int)DBA.GetDBValue_3(
                                        string.Format("select count(*) from TBL_ACCOUNT where FLD_ID='{0}'",
                                            dBToDataTable.Rows[num3]["FLD_ID"]), "rxjhaccount") < 1) goto case 3;
                                goto case 1;
                            case 3:
                                num4++;
                                Form1.WriteLine(100,
                                    "Xóa bỏ tông hợp nhà kho [" + dBToDataTable.Rows[num3]["FLD_ID"] + "]");
                                DBA.ExeSqlCommand(string.Format(
                                    "DELETE FROM TBL_XWWL_PublicWarehouse WHERE FLD_ID  ='{0}'",
                                    dBToDataTable.Rows[num3]["FLD_ID"]));
                                goto case 1;
                            case 1:
                                num3++;
                                goto IL_018f;
                            default:
                                num4 = 0;
                                Form1.WriteLine(100, "Xóa bỏ tông hợp nhà kho bắt đầu 222");
                                dBToDataTable = DBA.GetDBToDataTable("select * from TBL_XWWL_PublicWarehouse");
                                goto end_IL_0029;
                            case 5:
                                dBToDataTable.Dispose();
                                goto end_IL_01a5;
                            case 2:
                                continue;
                            case 4:
                            case 7:
                                goto IL_018f;
                            case 6:
                                goto end_IL_0029;
                            case 8:
                                goto end_IL_01a5;
                        }

                        break;
                    }

                    break;
                }

                continue;
                end_IL_0029:
                break;
            }

            continue;
            end_IL_01a5:
            break;
        }

        Form1.WriteLine(100, "Xóa bỏ tông hợp nhà kho hoàn thành " + num4);
    }

    public static void FlushAll4()
    {
        var num2 = 0;
        string[] array = null;
        DataTable dataTable = null;
        var num3 = 0;
        Form1.WriteLine(100, "Xóa bỏ bang phái số liệu bắt đầu 111");
        var dBToDataTable = DBA.GetDBToDataTable("select * from TBL_XWWL_Guild");
        if (dBToDataTable != null)
        {
            Form1.WriteLine(100, "Tổng cộng có nhân vật số liệu 333" + dBToDataTable.Rows.Count);
            for (num2 = 0; num2 < dBToDataTable.Rows.Count; num2++)
            {
                dataTable = DBA.GetDBToDataTable(string.Format("select * from TBL_XWWL_Char where FLD_NAME='{0}'",
                    dBToDataTable.Rows[num2]["G_Master"]));
                if (dataTable.Rows.Count < 1)
                {
                    num3++;
                    array = new string[5]
                    {
                        "删除帮派[",
                        dBToDataTable.Rows[num2]["G_Name"].ToString(),
                        "] [",
                        null,
                        null
                    };
                    var array2 = array;
                    array2[3] = dBToDataTable.Rows[num2]["G_Master"]?.ToString();
                    array[4] = "]";
                    Form1.WriteLine(100, string.Concat(array));
                    DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_Guild WHERE G_Name  ='{0}'",
                        dBToDataTable.Rows[num2]["G_Name"]));
                    DBA.ExeSqlCommand(string.Format("DELETE FROM TBL_XWWL_GuildMember WHERE G_Name  ='{0}'",
                        dBToDataTable.Rows[num2]["G_Name"]));
                }

                dataTable.Dispose();
            }

            dBToDataTable.Dispose();
        }

        Form1.WriteLine(100, "Xóa bỏ bang phái số liệu hoàn thành" + num3);
        FlushAll5();
    }

    public static void FlushAll5()
    {
        var num3 = 0;
        DataTable dataTable = null;
        var num4 = 0;
        Form1.WriteLine(100, "Xóa bỏ bang phái số liệu bắt đầu 222");
        var dBToDataTable = DBA.GetDBToDataTable("select * from TBL_XWWL_GuildMember");
        while (dBToDataTable != null)
        {
            var flag = true;
            while (true)
            {
                Form1.WriteLine(100, "Tổng cộng có bang phái số liệu" + dBToDataTable.Rows.Count);
                num3 = 0;
                while (true)
                {
                    var flag2 = true;
                    while (true)
                    {
                        IL_02d7:
                        var flag3 = true;
                        while (true)
                        {
                            IL_0067:
                            if (num3 < dBToDataTable.Rows.Count)
                            {
                                dataTable = DBA.GetDBToDataTable(string.Format(
                                    "select * from TBL_XWWL_Char where FLD_NAME='{0}'",
                                    dBToDataTable.Rows[num3]["FLD_NAME"]));
                                while (dataTable.Rows.Count < 1)
                                {
                                    var flag4 = true;
                                    while (true)
                                    {
                                        IL_00b2:
                                        num4++;
                                        while (true)
                                        {
                                            switch (dBToDataTable.Rows[0]["leve"].ToString() == "6" ? 11 : 10)
                                            {
                                                case 6:
                                                    break;
                                                case 9:
                                                    goto IL_0067;
                                                case 0:
                                                    goto IL_00b2;
                                                case 10:
                                                {
                                                    var str2 = "删除帮派";
                                                    Form1.WriteLine(100,
                                                        str2 + dBToDataTable.Rows[num3]["G_Name"] + "    " +
                                                        dBToDataTable.Rows[num3]["FLD_NAME"]);
                                                    DBA.ExeSqlCommand(string.Format(
                                                        "DELETE FROM TBL_XWWL_GuildMember WHERE FLD_NAME  ='{0}'",
                                                        dBToDataTable.Rows[num3]["FLD_NAME"]));
                                                    goto end_IL_02b2;
                                                }
                                                case 11:
                                                case 14:
                                                {
                                                    var str = "删除帮派";
                                                    Form1.WriteLine(100, str + dBToDataTable.Rows[num3]["G_Name"]);
                                                    DBA.ExeSqlCommand(string.Format(
                                                        "DELETE FROM TBL_XWWL_Guild WHERE G_Name  ='{0}'",
                                                        dBToDataTable.Rows[0]["G_Name"]));
                                                    DBA.ExeSqlCommand(string.Format(
                                                        "DELETE FROM TBL_XWWL_GuildMember WHERE G_Name  ='{0}'",
                                                        dBToDataTable.Rows[0]["G_Name"]));
                                                    goto end_IL_02b2;
                                                }
                                                default:
                                                    num4 = 0;
                                                    Form1.WriteLine(100, "Xóa bỏ bang phái số liệu bắt đầu 333");
                                                    dBToDataTable =
                                                        DBA.GetDBToDataTable("select * from TBL_XWWL_GuildMember");
                                                    goto end_IL_002b;
                                                case 13:
                                                    continue;
                                                case 8:
                                                    goto IL_02b2;
                                                case 12:
                                                    goto IL_02cd;
                                                case 5:
                                                case 7:
                                                    goto IL_02d7;
                                                case 1:
                                                case 4:
                                                    goto end_IL_02b2;
                                                case 3:
                                                    goto end_IL_002b;
                                                case 2:
                                                    goto end_IL_030e;
                                            }

                                            break;
                                        }

                                        break;
                                    }

                                    goto end_IL_02f8;
                                    continue;
                                    end_IL_02b2:
                                    break;
                                    IL_02b2: ;
                                }

                                break;
                            }

                            IL_02cd:
                            dBToDataTable.Dispose();
                            goto end_IL_030e;
                        }

                        break;
                    }

                    dataTable.Dispose();
                    num3++;
                    continue;
                    end_IL_02f8:
                    break;
                }

                continue;
                end_IL_002b:
                break;
            }

            continue;
            end_IL_030e:
            break;
        }

        Form1.WriteLine(100, "Xóa bỏ bang phái số liệu hoàn thành " + num4);
    }

    private void Form2_Load(object sender, EventArgs e)
    {
        var num = 0;
        listView2.ListViewItemSorter = new ListViewColumnSorter();
        listView2.ColumnClick += ListViewHelper.ListView_ColumnClick;
        var enumerator = World.ItmeTeM.Values.GetEnumerator();
        try
        {
            num = 0;
            X_Mat_Dat_Vat_Pham_Loai 地面物品类 = null;
            while (true)
            {
                num = 2;
                if (!enumerator.MoveNext()) break;
                地面物品类 = enumerator.Current;
                num = 4;
                try
                {
                    var array = new string[17];
                    try
                    {
                        array[0] = BitConverter.ToInt64(地面物品类.VatPham.ItemGlobal_ID, 0).ToString();
                        array[1] = Buffer.ToInt32(地面物品类.VatPham.VatPham_ID, 0).ToString();
                        array[2] = 地面物品类.VatPham.DatDuocVatPhamTen_XungHao();
                        array[3] = 地面物品类.Rxjh_Map.ToString();
                        array[4] = 地面物品类.Rxjh_X.ToString();
                        array[5] = 地面物品类.Rxjh_Y.ToString();
                        array[6] = 地面物品类.VatPham.FLD_MAGIC0.ToString();
                        array[7] = 地面物品类.VatPham.FLD_MAGIC1.ToString();
                        array[8] = 地面物品类.VatPham.FLD_MAGIC2.ToString();
                        array[9] = 地面物品类.VatPham.FLD_MAGIC3.ToString();
                        array[10] = 地面物品类.VatPham.FLD_MAGIC4.ToString();
                        array[11] = 地面物品类.VatPham_QuyenUuTien.UserName;
                    }
                    catch
                    {
                        array[0] = BitConverter.ToInt64(地面物品类.VatPham.ItemGlobal_ID, 0).ToString();
                        array[1] = Buffer.ToInt32(地面物品类.VatPham.VatPham_ID, 0).ToString();
                        array[2] = 地面物品类.VatPham.DatDuocVatPhamTen_XungHao();
                        array[3] = 地面物品类.Rxjh_Map.ToString();
                        array[4] = 地面物品类.Rxjh_X.ToString();
                        array[5] = 地面物品类.Rxjh_Y.ToString();
                        array[6] = 地面物品类.VatPham.FLD_MAGIC0.ToString();
                        array[7] = 地面物品类.VatPham.FLD_MAGIC1.ToString();
                        array[8] = 地面物品类.VatPham.FLD_MAGIC2.ToString();
                        array[9] = 地面物品类.VatPham.FLD_MAGIC3.ToString();
                        array[10] = 地面物品类.VatPham.FLD_MAGIC4.ToString();
                    }

                    listView2.Items.Insert(listView2.Items.Count, new ListViewItem(array));
                }
                catch
                {
                }
            }

            num = 3;
            num = 1;
        }
        finally
        {
            num = 0;
            while (true)
            {
                switch (num)
                {
                    case 1:
                        break;
                    default:
                        if (enumerator != null)
                        {
                            num = 2;
                            continue;
                        }

                        break;
                    case 2:
                        enumerator.Dispose();
                        num = 1;
                        continue;
                }

                break;
            }
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        listView2 = new ListView();
        columnHeader2 = new ColumnHeader();
        columnHeader3 = new ColumnHeader();
        columnHeader4 = new ColumnHeader();
        columnHeader8 = new ColumnHeader();
        columnHeader10 = new ColumnHeader();
        columnHeader11 = new ColumnHeader();
        columnHeader12 = new ColumnHeader();
        columnHeader14 = new ColumnHeader();
        columnHeader13 = new ColumnHeader();
        columnHeader15 = new ColumnHeader();
        columnHeader16 = new ColumnHeader();
        columnHeader17 = new ColumnHeader();
        SuspendLayout();
        listView2.Columns.AddRange(new ColumnHeader[12]
        {
            columnHeader2, columnHeader3, columnHeader4, columnHeader8, columnHeader10, columnHeader11, columnHeader12,
            columnHeader14, columnHeader13, columnHeader15,
            columnHeader16, columnHeader17
        });
        listView2.Dock = DockStyle.Fill;
        listView2.ForeColor = SystemColors.WindowText;
        listView2.FullRowSelect = true;
        listView2.GridLines = true;
        listView2.Location = new Point(0, 0);
        listView2.Name = "listView2";
        listView2.Size = new Size(769, 384);
        listView2.TabIndex = 3;
        listView2.UseCompatibleStateImageBehavior = false;
        listView2.View = View.Details;
        columnHeader2.Text = "全局ID";
        columnHeader2.Width = 65;
        columnHeader3.Text = "物品ID";
        columnHeader3.TextAlign = HorizontalAlignment.Center;
        columnHeader3.Width = 79;
        columnHeader4.Text = "物品名";
        columnHeader4.TextAlign = HorizontalAlignment.Center;
        columnHeader4.Width = 98;
        columnHeader8.Text = "地图";
        columnHeader8.Width = 42;
        columnHeader10.Text = "X";
        columnHeader10.Width = 61;
        columnHeader11.Text = "Y";
        columnHeader11.Width = 61;
        columnHeader12.Text = "属性0";
        columnHeader12.TextAlign = HorizontalAlignment.Center;
        columnHeader14.Text = "属性1";
        columnHeader14.TextAlign = HorizontalAlignment.Center;
        columnHeader13.Text = "属性2";
        columnHeader13.TextAlign = HorizontalAlignment.Center;
        columnHeader15.Text = "属性3";
        columnHeader15.TextAlign = HorizontalAlignment.Center;
        columnHeader16.Text = "属性4";
        columnHeader16.TextAlign = HorizontalAlignment.Center;
        columnHeader17.Text = "优先";
        columnHeader17.TextAlign = HorizontalAlignment.Center;
        columnHeader17.Width = 56;
        AutoScaleDimensions = new SizeF(6f, 12f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(769, 384);
        Controls.Add(listView2);
        Name = "Form2";
        StartPosition = FormStartPosition.CenterScreen;
        Text = "地面物品";
        Load += Form2_Load;
        ResumeLayout(false);
    }
}