using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class Form12 : Form
{
    private static Dictionary<int, MonSterClss> List = new();

    private Button button1;

    private Button button2;

    private ComboBox comboBox1;

    private ComboBox comboBox2;

    private IContainer components;

    private int FLD_AT;

    private int FLD_AUTO;

    private int FLD_BOSS;

    private int FLD_DF;

    private int FLD_EXP;

    private int FLD_LEVEL;

    private int FLD_MAP;

    private string FLD_Name = "未知";

    private int FLD_NPC;

    private int FLD_PID;

    private Label label1;

    private Label label2;

    private Label label20;

    private Label label3;

    private Label label4;

    private Label label5;

    private Label label6;

    private int Rxjh_HP;

    private TextBox textBox1;

    private TextBox textBox2;

    private TextBox textBox3;

    private TextBox textBox4;

    public Form12()
    {
        InitializeComponent();
    }

    private void Form1_Load(object sender, EventArgs e)
    {
        List = new Dictionary<int, MonSterClss>();
        comboBox1.Items.Clear();
        comboBox2.Items.Clear();
        comboBox1.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox2.DropDownStyle = ComboBoxStyle.DropDownList;
        foreach (var value in World.Maplist.Values) comboBox2.Items.Add(value);
        using var enumerator2 = World.MonSter.Values.GetEnumerator();
        MonSterClss monSterClss = null;
        while (enumerator2.MoveNext())
        {
            monSterClss = enumerator2.Current;
            try
            {
                List.Add(monSterClss.FLD_PID, monSterClss);
                comboBox1.Items.Add(monSterClss.Name);
            }
            catch
            {
                MessageBox.Show(monSterClss.FLD_PID + "|" + monSterClss.Name);
            }
        }
    }

    private void comboBox2_SelectedIndexChanged(object sender, EventArgs e)
    {
        using var enumerator = World.Maplist.GetEnumerator();
        var keyValuePair = default(KeyValuePair<int, string>);
        while (enumerator.MoveNext())
        {
            keyValuePair = enumerator.Current;
            if (keyValuePair.Value == comboBox2.SelectedItem.ToString()) FLD_MAP = keyValuePair.Key;
        }
    }

    private void button1_Click(object sender, EventArgs e)
    {
        var string_ =
            $"INSERT INTO TBL_XWWL_NPC ([FLD_PID] ,[FLD_X] ,[FLD_Z] ,[FLD_Y] ,[FLD_FACE0] ,[FLD_FACE] ,[FLD_MID] ,[FLD_NAME] ,[FLD_HP] ,[FLD_AT] ,[FLD_DF] ,[FLD_NPC],[FLD_NEWTIME] ,[FLD_LEVEL],[FLD_EXP] ,[FLD_AUTO],[FLD_BOSS]) VALUES ({FLD_PID},{float.Parse(textBox1.Text)}, {15f}, {float.Parse(textBox2.Text)},{0}, {0}, {FLD_MAP}, '{FLD_Name}', {Rxjh_HP}, {FLD_AT}, {FLD_DF}, {FLD_NPC}, {10}, {FLD_LEVEL}, {FLD_EXP}, {FLD_AUTO}, {FLD_BOSS})";
        if (DBA.ExeSqlCommand(string_, "PublicDb") != -1) MessageBox.Show("添加成功");
    }

    private MonSterClss method_0(string string_0)
    {
        using (var enumerator = List.Values.GetEnumerator())
        {
            MonSterClss monSterClss = null;
            while (enumerator.MoveNext())
            {
                monSterClss = enumerator.Current;
                if (monSterClss.Name == string_0) return monSterClss;
            }
        }

        return null;
    }

    private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            var monSterClss = method_0(comboBox1.SelectedItem.ToString());
            if (monSterClss != null)
            {
                FLD_PID = monSterClss.FLD_PID;
                FLD_Name = monSterClss.Name;
                Rxjh_HP = monSterClss.Rxjh_HP;
                FLD_AT = (int)monSterClss.FLD_AT;
                FLD_DF = (int)monSterClss.FLD_DF;
                FLD_NPC = monSterClss.FLD_NPC;
                FLD_LEVEL = monSterClss.Level;
                FLD_EXP = monSterClss.Rxjh_Exp;
                FLD_BOSS = monSterClss.FLD_BOSS;
                FLD_AUTO = monSterClss.FLD_AUTO;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message);
        }
    }

    private void button2_Click(object sender, EventArgs e)
    {
        try
        {
            var num2 = 0.0;
            var num3 = 0.0;
            var num4 = 0;
            var num5 = 0.0;
            var num6 = 0.0;
            for (var i = 0; i < int.Parse(textBox4.Text); i++)
            {
                var random = new Random(World.GetRandomSeed());
                num4 = random.Next(0, 2);
                num2 = float.Parse(textBox1.Text);
                num3 = float.Parse(textBox2.Text);
                num5 = random.NextDouble() * float.Parse(textBox3.Text);
                num6 = random.NextDouble() * float.Parse(textBox3.Text);
                random.NextDouble();
                float.Parse(textBox3.Text);
                random.NextDouble();
                float.Parse(textBox3.Text);
                if (num4 == 0)
                {
                    num2 = float.Parse(textBox1.Text) + (float)num5;
                    num3 = float.Parse(textBox2.Text) + (float)num6;
                }
                else
                {
                    num2 = float.Parse(textBox1.Text) - (float)num5;
                    num3 = float.Parse(textBox2.Text) - (float)num6;
                }

                var string_ =
                    $"INSERT INTO TBL_XWWL_NPC ([FLD_PID] ,[FLD_X] ,[FLD_Z] ,[FLD_Y] ,[FLD_FACE0] ,[FLD_FACE] ,[FLD_MID] ,[FLD_NAME] ,[FLD_HP] ,[FLD_AT] ,[FLD_DF] ,[FLD_NPC],[FLD_NEWTIME] ,[FLD_LEVEL],[FLD_EXP] ,[FLD_AUTO],[FLD_BOSS]) VALUES ({FLD_PID},{(int)num2}, {15f}, {(int)num3},{0}, {0}, {FLD_MAP}, '{FLD_Name}', {Rxjh_HP}, {FLD_AT}, {FLD_DF}, {FLD_NPC}, {10}, {FLD_LEVEL}, {FLD_EXP}, {FLD_AUTO}, {FLD_BOSS})";
                DBA.ExeSqlCommand(string_, "PublicDb");
                Thread.Sleep(10);
            }

            MessageBox.Show("刷怪完成");
        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message);
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        comboBox2 = new ComboBox();
        label20 = new Label();
        comboBox1 = new ComboBox();
        label1 = new Label();
        button1 = new Button();
        button2 = new Button();
        textBox1 = new TextBox();
        label2 = new Label();
        label3 = new Label();
        textBox2 = new TextBox();
        label4 = new Label();
        textBox3 = new TextBox();
        label5 = new Label();
        textBox4 = new TextBox();
        label6 = new Label();
        SuspendLayout();
        comboBox2.FormattingEnabled = true;
        comboBox2.Location = new Point(77, 12);
        comboBox2.Name = "comboBox2";
        comboBox2.Size = new Size(184, 20);
        comboBox2.TabIndex = 37;
        comboBox2.SelectedIndexChanged += comboBox2_SelectedIndexChanged;
        label20.AutoSize = true;
        label20.Location = new Point(12, 55);
        label20.Name = "label20";
        label20.Size = new Size(59, 12);
        label20.TabIndex = 38;
        label20.Text = "怪物选择:";
        comboBox1.FormattingEnabled = true;
        comboBox1.Location = new Point(77, 52);
        comboBox1.Name = "comboBox1";
        comboBox1.Size = new Size(184, 20);
        comboBox1.TabIndex = 39;
        comboBox1.SelectedIndexChanged += comboBox1_SelectedIndexChanged;
        label1.AutoSize = true;
        label1.Location = new Point(12, 15);
        label1.Name = "label1";
        label1.Size = new Size(59, 12);
        label1.TabIndex = 40;
        label1.Text = "地图选择:";
        button1.Location = new Point(77, 240);
        button1.Name = "button1";
        button1.Size = new Size(75, 23);
        button1.TabIndex = 41;
        button1.Text = "固定刷怪";
        button1.UseVisualStyleBackColor = true;
        button1.Click += button1_Click;
        button2.Location = new Point(186, 240);
        button2.Name = "button2";
        button2.Size = new Size(75, 23);
        button2.TabIndex = 42;
        button2.Text = "批量刷怪";
        button2.UseVisualStyleBackColor = true;
        button2.Click += button2_Click;
        textBox1.Location = new Point(77, 91);
        textBox1.Name = "textBox1";
        textBox1.Size = new Size(184, 21);
        textBox1.TabIndex = 43;
        label2.AutoSize = true;
        label2.Location = new Point(30, 94);
        label2.Name = "label2";
        label2.Size = new Size(41, 12);
        label2.TabIndex = 44;
        label2.Text = "坐标X:";
        label3.AutoSize = true;
        label3.Location = new Point(30, 130);
        label3.Name = "label3";
        label3.Size = new Size(41, 12);
        label3.TabIndex = 46;
        label3.Text = "坐标Y:";
        textBox2.Location = new Point(77, 127);
        textBox2.Name = "textBox2";
        textBox2.Size = new Size(184, 21);
        textBox2.TabIndex = 45;
        label4.AutoSize = true;
        label4.Location = new Point(36, 164);
        label4.Name = "label4";
        label4.Size = new Size(35, 12);
        label4.TabIndex = 48;
        label4.Text = "范围:";
        textBox3.Location = new Point(77, 161);
        textBox3.Name = "textBox3";
        textBox3.Size = new Size(184, 21);
        textBox3.TabIndex = 47;
        label5.AutoSize = true;
        label5.Location = new Point(36, 201);
        label5.Name = "label5";
        label5.Size = new Size(35, 12);
        label5.TabIndex = 50;
        label5.Text = "数量:";
        textBox4.Location = new Point(77, 198);
        textBox4.Name = "textBox4";
        textBox4.Size = new Size(184, 21);
        textBox4.TabIndex = 49;
        label6.AutoSize = true;
        label6.ForeColor = Color.Red;
        label6.Location = new Point(52, 284);
        label6.Name = "label6";
        label6.Size = new Size(209, 12);
        label6.TabIndex = 52;
        label6.Text = "说明：固定刷怪/NPC不用写数量和范围";
        AutoScaleDimensions = new SizeF(6f, 12f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(305, 311);
        Controls.Add(label6);
        Controls.Add(label5);
        Controls.Add(textBox4);
        Controls.Add(label4);
        Controls.Add(textBox3);
        Controls.Add(label3);
        Controls.Add(textBox2);
        Controls.Add(label2);
        Controls.Add(textBox1);
        Controls.Add(button2);
        Controls.Add(button1);
        Controls.Add(comboBox1);
        Controls.Add(label1);
        Controls.Add(comboBox2);
        Controls.Add(label20);
        Name = "Form12";
        Text = "刷怪工具";
        Load += Form1_Load;
        ResumeLayout(false);
        PerformLayout();
    }
}