using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class SELL : Form
{
    private Button button1;

    private Button button2;

    private Button button3;

    private Button button43;

    private ColumnHeader columnHeader1;

    private ColumnHeader columnHeader11;

    private ColumnHeader columnHeader12;

    private ColumnHeader columnHeader13;

    private ColumnHeader columnHeader14;

    private ColumnHeader columnHeader15;

    private ColumnHeader columnHeader16;

    private ColumnHeader columnHeader17;

    private ColumnHeader columnHeader2;

    private ColumnHeader columnHeader3;

    private ColumnHeader columnHeader4;

    private ColumnHeader columnHeader5;

    private ComboBox comboBox11;

    private ComboBox comboBox12;
    private IContainer components;

    private ContextMenuStrip contextMenuStrip1;

    private Label label1;

    private Label label10;

    private Label label11;

    private Label label14;

    private Label label2;

    private Label label3;

    private Label label4;

    private Label label40;

    private Label label5;

    private Label label6;

    private Label label7;

    private Label label8;

    private Label label9;

    private ListBox listBox3;

    private ListView listView2;

    private Panel panel1;

    private StatusStrip statusStrip1;

    private TextBox textBox1;

    private TextBox textBox10;

    private TextBox textBox11;

    private TextBox textBox2;

    private TextBox textBox3;

    private TextBox textBox4;

    private TextBox textBox5;

    private TextBox textBox6;

    private TextBox textBox7;

    private TextBox textBox8;

    private TextBox textBox9;

    private ToolStripStatusLabel toolStripStatusLabel1;

    private ToolStripStatusLabel toolStripStatusLabel2;

    private ToolStripMenuItem 删除ToolStripMenuItem;

    private ToolStripMenuItem 编辑ToolStripMenuItem;

    public SELL()
    {
        InitializeComponent();
        listView2.ContextMenuStrip = contextMenuStrip1;
        contextMenuStrip1.Closed += contextMenuStrip1_Closed;
        toolStripStatusLabel2.Text = "Cập nhật thành công";
        button1.Enabled = false;
    }

    public string s_id { get; set; }

    private void contextMenuStrip1_Closed(object sender, ToolStripDropDownClosedEventArgs e)
    {
        listView2.ContextMenuStrip = contextMenuStrip1;
    }

    private void button43_Click(object sender, EventArgs e)
    {
        SetShop();
    }

    private void panel1_Paint(object sender, PaintEventArgs e)
    {
        ControlPaint.DrawBorder(e.Graphics, panel1.ClientRectangle, Color.Black, 1, ButtonBorderStyle.Solid,
            Color.Black, 1, ButtonBorderStyle.Solid, Color.Black, 1, ButtonBorderStyle.Solid, Color.Black, 1,
            ButtonBorderStyle.Solid);
    }

    private void contextMenuStrip1_Opening(object sender, CancelEventArgs e)
    {
    }

    private void toolStripMenuItem1_Click(object sender, EventArgs e)
    {
    }

    private void button1_Click(object sender, EventArgs e)
    {
        try
        {
            if (listView2.Items.Count == 0) toolStripStatusLabel1.Text = "Vui lòng lựa chọn Item để chỉnh sửa";
            var text = textBox1.Text;
            var num14 = int.Parse(textBox2.Text);
            var num15 = int.Parse(textBox3.Text);
            var num16 = int.Parse(textBox4.Text);
            var num17 = int.Parse(textBox5.Text);
            var num18 = int.Parse(textBox6.Text);
            var num19 = int.Parse(textBox7.Text);
            var num10 = int.Parse(textBox8.Text);
            var num11 = int.Parse(textBox9.Text);
            var num12 = int.Parse(textBox10.Text);
            var num13 = int.Parse(textBox11.Text);
            var text2 = "";
            text2 = string.Format(
                "UPDATE TBL_XWWL_SELL  SET FLD_NPCNAME='{1}',FLD_NID={2}, FLD_INDEX={3},FLD_PID={4},FLD_MAGIC0={5},FLD_MAGIC1={6},FLD_MAGIC2={7},FLD_MAGIC3={8},FLD_MAGIC4={9},FLD_CanVoHoang={10},FLD_MONEY={11} WHERE ID={0}",
                s_id, text, num14, num15, num16, num18, num19, num10, num11, num12, num13, num17);
            DBA.ExeSqlCommand(text2, "PublicDb");
            SetShot();
            var selectedIndices = listView2.SelectedIndices;
            listView2.Items[selectedIndices[0]].SubItems[1].Text = textBox1.Text;
            listView2.Items[selectedIndices[0]].SubItems[2].Text = textBox2.Text;
            listView2.Items[selectedIndices[0]].SubItems[3].Text = textBox3.Text;
            listView2.Items[selectedIndices[0]].SubItems[4].Text = textBox4.Text;
            listView2.Items[selectedIndices[0]].SubItems[5].Text = textBox5.Text;
            listView2.Items[selectedIndices[0]].SubItems[6].Text = textBox6.Text;
            listView2.Items[selectedIndices[0]].SubItems[7].Text = textBox7.Text;
            listView2.Items[selectedIndices[0]].SubItems[8].Text = textBox8.Text;
            listView2.Items[selectedIndices[0]].SubItems[9].Text = textBox9.Text;
            listView2.Items[selectedIndices[0]].SubItems[10].Text = textBox10.Text;
            listView2.Items[selectedIndices[0]].SubItems[11].Text = textBox11.Text;
        }
        catch (Exception)
        {
        }
    }

    private void listView2_MouseClick(object sender, MouseEventArgs e)
    {
        if (listView2.SelectedIndices != null && listView2.SelectedIndices.Count > 0)
        {
            var selectedIndices = listView2.SelectedIndices;
            s_id = listView2.Items[selectedIndices[0]].SubItems[0].Text;
            textBox1.Text = listView2.Items[selectedIndices[0]].SubItems[1].Text;
            textBox2.Text = listView2.Items[selectedIndices[0]].SubItems[2].Text;
            textBox3.Text = listView2.Items[selectedIndices[0]].SubItems[3].Text;
            textBox4.Text = listView2.Items[selectedIndices[0]].SubItems[4].Text;
            textBox5.Text = listView2.Items[selectedIndices[0]].SubItems[5].Text;
            textBox6.Text = listView2.Items[selectedIndices[0]].SubItems[6].Text;
            textBox7.Text = listView2.Items[selectedIndices[0]].SubItems[7].Text;
            textBox8.Text = listView2.Items[selectedIndices[0]].SubItems[8].Text;
            textBox9.Text = listView2.Items[selectedIndices[0]].SubItems[9].Text;
            textBox10.Text = listView2.Items[selectedIndices[0]].SubItems[10].Text;
            textBox11.Text = listView2.Items[selectedIndices[0]].SubItems[11].Text;
            if (!button1.Enabled) button1.Enabled = true;
        }
    }

    private void 编辑ToolStripMenuItem_Click(object sender, EventArgs e)
    {
        if (listView2.SelectedIndices != null && listView2.SelectedIndices.Count > 0)
        {
            var selectedIndices = listView2.SelectedIndices;
            s_id = listView2.Items[selectedIndices[0]].SubItems[0].Text;
            textBox1.Text = listView2.Items[selectedIndices[0]].SubItems[1].Text;
            textBox2.Text = listView2.Items[selectedIndices[0]].SubItems[2].Text;
            textBox3.Text = listView2.Items[selectedIndices[0]].SubItems[3].Text;
            textBox4.Text = listView2.Items[selectedIndices[0]].SubItems[4].Text;
            textBox5.Text = listView2.Items[selectedIndices[0]].SubItems[5].Text;
            textBox6.Text = listView2.Items[selectedIndices[0]].SubItems[6].Text;
            textBox7.Text = listView2.Items[selectedIndices[0]].SubItems[7].Text;
            textBox8.Text = listView2.Items[selectedIndices[0]].SubItems[8].Text;
            textBox9.Text = listView2.Items[selectedIndices[0]].SubItems[9].Text;
            textBox10.Text = listView2.Items[selectedIndices[0]].SubItems[10].Text;
            textBox11.Text = listView2.Items[selectedIndices[0]].SubItems[11].Text;
        }
    }

    private void 删除ToolStripMenuItem_Click(object sender, EventArgs e)
    {
        if (listView2.SelectedIndices != null && listView2.SelectedIndices.Count > 0)
        {
            var selectedIndices = listView2.SelectedIndices;
            var text = listView2.Items[selectedIndices[0]].Text;
            var string_ = "delete from tbl_xwwl_sell where id =" + text;
            DBA.ExeSqlCommand(string_, "PublicDb");
            SetShop();
        }
    }

    public void SetShop()
    {
        listView2.Items.Clear();
        var string_ = "select * from tbl_xwwl_sell";
        var dBToDataTable = DBA.GetDBToDataTable(string_, "PublicDb");
        for (var i = 0; i < dBToDataTable.Rows.Count; i++)
        {
            var listViewItem = new ListViewItem();
            listViewItem.SubItems.Clear();
            listViewItem.SubItems[0].Text = dBToDataTable.Rows[i]["id"].ToString();
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_NPCNAME"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_NID"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_INDEX"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_PID"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MONEY"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC0"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC1"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC2"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC3"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_MAGIC4"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_CanVoHoang"].ToString());
            listView2.Items.Add(listViewItem);
        }
    }

    internal static uint ComputeStringHash(string string_0)
    {
        var num = 0;
        var num2 = 0u;
        if (string_0 != null)
        {
            num2 = 2166136261u;
            for (num = 0; num < string_0.Length; num++) num2 = (string_0[num] ^ num2) * 16777619;
        }

        return num2;
    }

    private void comboBox11_SelectedIndexChanged(object sender, EventArgs e)
    {
        DataTable dataTable = null;
        var num2 = 0;
        var arg = "";
        var text = comboBox11.Text;
        switch (ComputeStringHash(text))
        {
            case 963180387u:
                if (text == "Vũ Khí") arg = "4";
                break;
            case 867671693u:
                if (text == "Quần - Áo") arg = "1";
                break;
            case 477010303u:
                if (text == "Áo Choàng Bang") arg = "14";
                break;
            case 1895148392u:
                if (text == "Hộ Thủ") arg = "2";
                break;
            case 1618293597u:
                if (text == "Mũi Tên - Etc") arg = "13";
                break;
            case 1502406078u:
                if (text == "Dây Chuyền") arg = "7";
                break;
            case 3393768302u:
                if (text == "Giày") arg = "Giầy";
                break;
            case 3322318871u:
                if (text == "Bông Tai") arg = "8";
                break;
            case 2913798006u:
                if (text == "Nhẫn") arg = "Nhẫn";
                break;
            case 4183737334u:
                if (text == "Hộ Giáp") arg = "6";
                break;
            case 3578152463u:
                if (text == "Thú Nuôi") arg = "15";
                break;
            case 3558870252u:
                if (text == "Áo Choàng") arg = "12";
                break;
        }

        listBox3.Items.Clear();
        comboBox12.Items.Clear();
        var string_ = "select * from TBL_XWWL_ITEM where FLD_RESIDE2='" + arg + "'";
        dataTable = DBA.GetDBToDataTable(string_, "PublicDb");
        for (num2 = 0; num2 < dataTable.Rows.Count; num2++)
        {
            listBox3.Items.Add(dataTable.Rows[num2]["FLD_NAME"].ToString());
            comboBox12.Items.Add(dataTable.Rows[num2]["FLD_PID"].ToString());
        }

        dataTable.Dispose();
    }

    private void listBox3_SelectedIndexChanged(object sender, EventArgs e)
    {
        comboBox12.SelectedIndex = listBox3.SelectedIndex;
        textBox4.Text = comboBox12.Text;
    }

    public void SetShot()
    {
        try
        {
            var num2 = 0;
            var string_ = "SELECT * FROM TBL_XWWL_SELL ORDER BY FLD_INDEX";
            var dBToDataTable = DBA.GetDBToDataTable(string_, "PublicDb");
            if (dBToDataTable == null) return;
            if (dBToDataTable.Rows.Count == 0)
            {
                Form1.WriteLine(1, "Tăng thêm vật phẩm cửa hàng ---- Không có vật phẩm số liệu");
            }
            else
            {
                World.Shop.Clear();
                for (num2 = 0; num2 < dBToDataTable.Rows.Count; num2++)
                {
                    var shopClass = new ShopClass();
                    shopClass.FLD_NID = int.Parse(dBToDataTable.Rows[num2]["FLD_NID"].ToString());
                    shopClass.FLD_INDEX = (int)dBToDataTable.Rows[num2]["FLD_INDEX"];
                    shopClass.FLD_PID = int.Parse(dBToDataTable.Rows[num2]["FLD_PID"].ToString());
                    shopClass.FLD_MAGIC0 = (int)dBToDataTable.Rows[num2]["FLD_MAGIC0"];
                    shopClass.FLD_MAGIC1 = (int)dBToDataTable.Rows[num2]["FLD_MAGIC1"];
                    shopClass.FLD_MAGIC2 = (int)dBToDataTable.Rows[num2]["FLD_MAGIC2"];
                    shopClass.FLD_MAGIC3 = (int)dBToDataTable.Rows[num2]["FLD_MAGIC3"];
                    shopClass.FLD_MAGIC4 = (int)dBToDataTable.Rows[num2]["FLD_MAGIC4"];
                    shopClass.FLD_MONEY = long.Parse(dBToDataTable.Rows[num2]["FLD_MONEY"].ToString());
                    shopClass.CanVoHoang = (int)dBToDataTable.Rows[num2]["FLD_CanVoHoang"];
                    World.Shop.Add(shopClass);
                }
            }

            dBToDataTable.Dispose();
        }
        catch (Exception)
        {
        }
    }

    private void button2_Click(object sender, EventArgs e)
    {
        var text7 = "";
        text7 =
            $"INSERT INTO tbl_xwwl_sell  (FLD_NPCNAME,FLD_NID, FLD_INDEX,FLD_PID,FLD_MONEY,FLD_MAGIC0,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4,FLD_CanVoHoang)VALUES('{textBox1.Text}',{int.Parse(textBox2.Text)},{int.Parse(textBox3.Text)},{int.Parse(textBox4.Text)},{int.Parse(textBox5.Text)},{int.Parse(textBox6.Text)},{int.Parse(textBox7.Text)},{int.Parse(textBox8.Text)},{int.Parse(textBox9.Text)},{int.Parse(textBox10.Text)},{int.Parse(textBox11.Text)})";
        DBA.ExeSqlCommand(text7, "PublicDb");
        SetShot();
        SetShop();
    }

    private void button3_Click(object sender, EventArgs e)
    {
        if (listView2.SelectedIndices != null && listView2.SelectedIndices.Count > 0)
        {
            var selectedIndices = listView2.SelectedIndices;
            var text = listView2.Items[selectedIndices[0]].Text;
            var string_ = "delete from tbl_xwwl_sell where id =" + text;
            DBA.ExeSqlCommand(string_, "PublicDb");
            SetShot();
            SetShop();
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        components = new Container();
        listView2 = new ListView();
        columnHeader11 = new ColumnHeader();
        columnHeader12 = new ColumnHeader();
        columnHeader13 = new ColumnHeader();
        columnHeader14 = new ColumnHeader();
        columnHeader15 = new ColumnHeader();
        columnHeader16 = new ColumnHeader();
        columnHeader17 = new ColumnHeader();
        columnHeader1 = new ColumnHeader();
        columnHeader2 = new ColumnHeader();
        columnHeader3 = new ColumnHeader();
        columnHeader4 = new ColumnHeader();
        columnHeader5 = new ColumnHeader();
        button43 = new Button();
        panel1 = new Panel();
        label7 = new Label();
        textBox11 = new TextBox();
        label11 = new Label();
        label10 = new Label();
        label9 = new Label();
        label8 = new Label();
        textBox10 = new TextBox();
        textBox9 = new TextBox();
        textBox8 = new TextBox();
        textBox7 = new TextBox();
        textBox6 = new TextBox();
        label6 = new Label();
        textBox5 = new TextBox();
        label2 = new Label();
        textBox4 = new TextBox();
        label5 = new Label();
        textBox3 = new TextBox();
        label4 = new Label();
        textBox2 = new TextBox();
        label3 = new Label();
        textBox1 = new TextBox();
        label1 = new Label();
        button2 = new Button();
        button1 = new Button();
        contextMenuStrip1 = new ContextMenuStrip(components);
        编辑ToolStripMenuItem = new ToolStripMenuItem();
        删除ToolStripMenuItem = new ToolStripMenuItem();
        label14 = new Label();
        comboBox12 = new ComboBox();
        comboBox11 = new ComboBox();
        label40 = new Label();
        listBox3 = new ListBox();
        statusStrip1 = new StatusStrip();
        toolStripStatusLabel1 = new ToolStripStatusLabel();
        toolStripStatusLabel2 = new ToolStripStatusLabel();
        button3 = new Button();
        panel1.SuspendLayout();
        contextMenuStrip1.SuspendLayout();
        statusStrip1.SuspendLayout();
        SuspendLayout();
        listView2.Columns.AddRange(new ColumnHeader[12]
        {
            columnHeader11, columnHeader12, columnHeader13, columnHeader14, columnHeader15, columnHeader16,
            columnHeader17, columnHeader1, columnHeader2, columnHeader3,
            columnHeader4, columnHeader5
        });
        listView2.FullRowSelect = true;
        listView2.GridLines = true;
        listView2.HideSelection = false;
        listView2.Location = new Point(22, 30);
        listView2.Name = "listView2";
        listView2.Size = new Size(400, 362);
        listView2.TabIndex = 1;
        listView2.UseCompatibleStateImageBehavior = false;
        listView2.View = View.Details;
        listView2.MouseClick += listView2_MouseClick;
        columnHeader11.Text = "id";
        columnHeader11.Width = 40;
        columnHeader12.Text = "NPCname";
        columnHeader12.Width = 84;
        columnHeader13.Text = "NPCID";
        columnHeader13.Width = 68;
        columnHeader14.Text = "location";
        columnHeader14.Width = 76;
        columnHeader15.Text = "Item ID";
        columnHeader15.Width = 73;
        columnHeader16.Text = "price";
        columnHeader16.Width = 73;
        columnHeader17.Text = "Option1";
        columnHeader17.Width = 71;
        columnHeader1.Text = "Option2";
        columnHeader2.Text = "Option3";
        columnHeader3.Text = "Option4";
        columnHeader4.Text = "Option5";
        columnHeader5.Text = "Cần Võ Huân";
        button43.Location = new Point(16, 402);
        button43.Name = "button43";
        button43.Size = new Size(75, 25);
        button43.TabIndex = 3;
        button43.Text = "View";
        button43.UseVisualStyleBackColor = true;
        button43.Click += button43_Click;
        panel1.Controls.Add(label7);
        panel1.Controls.Add(textBox11);
        panel1.Controls.Add(label11);
        panel1.Controls.Add(label10);
        panel1.Controls.Add(label9);
        panel1.Controls.Add(label8);
        panel1.Controls.Add(textBox10);
        panel1.Controls.Add(textBox9);
        panel1.Controls.Add(textBox8);
        panel1.Controls.Add(textBox7);
        panel1.Controls.Add(textBox6);
        panel1.Controls.Add(label6);
        panel1.Controls.Add(textBox5);
        panel1.Controls.Add(label2);
        panel1.Controls.Add(textBox4);
        panel1.Controls.Add(label5);
        panel1.Controls.Add(textBox3);
        panel1.Controls.Add(label4);
        panel1.Controls.Add(textBox2);
        panel1.Controls.Add(label3);
        panel1.Controls.Add(textBox1);
        panel1.Controls.Add(label1);
        panel1.Location = new Point(444, 13);
        panel1.Name = "panel1";
        panel1.Size = new Size(252, 403);
        panel1.TabIndex = 4;
        panel1.Paint += panel1_Paint;
        label7.AutoSize = true;
        label7.Location = new Point(24, 323);
        label7.Name = "label7";
        label7.Size = new Size(41, 13);
        label7.TabIndex = 24;
        label7.Text = "Wuxun";
        textBox11.Location = new Point(113, 315);
        textBox11.Name = "textBox11";
        textBox11.Size = new Size(129, 20);
        textBox11.TabIndex = 23;
        label11.AutoSize = true;
        label11.Location = new Point(24, 295);
        label11.Name = "label11";
        label11.Size = new Size(42, 13);
        label11.TabIndex = 22;
        label11.Text = "Magic5";
        label10.AutoSize = true;
        label10.Location = new Point(24, 266);
        label10.Name = "label10";
        label10.Size = new Size(42, 13);
        label10.TabIndex = 21;
        label10.Text = "Magic4";
        label9.AutoSize = true;
        label9.Location = new Point(24, 236);
        label9.Name = "label9";
        label9.Size = new Size(42, 13);
        label9.TabIndex = 20;
        label9.Text = "Magic3";
        label8.AutoSize = true;
        label8.Location = new Point(24, 207);
        label8.Name = "label8";
        label8.Size = new Size(42, 13);
        label8.TabIndex = 19;
        label8.Text = "Magic2";
        textBox10.Location = new Point(113, 285);
        textBox10.Name = "textBox10";
        textBox10.Size = new Size(129, 20);
        textBox10.TabIndex = 18;
        textBox9.Location = new Point(113, 256);
        textBox9.Name = "textBox9";
        textBox9.Size = new Size(129, 20);
        textBox9.TabIndex = 17;
        textBox8.Location = new Point(113, 227);
        textBox8.Name = "textBox8";
        textBox8.Size = new Size(129, 20);
        textBox8.TabIndex = 16;
        textBox7.Location = new Point(113, 197);
        textBox7.Name = "textBox7";
        textBox7.Size = new Size(129, 20);
        textBox7.TabIndex = 15;
        textBox6.Location = new Point(113, 168);
        textBox6.Name = "textBox6";
        textBox6.Size = new Size(129, 20);
        textBox6.TabIndex = 12;
        label6.AutoSize = true;
        label6.Location = new Point(24, 178);
        label6.Name = "label6";
        label6.Size = new Size(42, 13);
        label6.TabIndex = 11;
        label6.Text = "Magic1";
        textBox5.Location = new Point(113, 139);
        textBox5.Name = "textBox5";
        textBox5.Size = new Size(129, 20);
        textBox5.TabIndex = 10;
        label2.AutoSize = true;
        label2.Location = new Point(24, 149);
        label2.Name = "label2";
        label2.Size = new Size(30, 13);
        label2.TabIndex = 9;
        label2.Text = "price";
        textBox4.Location = new Point(113, 110);
        textBox4.Name = "textBox4";
        textBox4.Size = new Size(129, 20);
        textBox4.TabIndex = 8;
        label5.AutoSize = true;
        label5.Location = new Point(24, 119);
        label5.Name = "label5";
        label5.Size = new Size(43, 13);
        label5.TabIndex = 7;
        label5.Text = "ItemsID";
        textBox3.Location = new Point(113, 80);
        textBox3.Name = "textBox3";
        textBox3.Size = new Size(129, 20);
        textBox3.TabIndex = 6;
        label4.AutoSize = true;
        label4.Location = new Point(24, 90);
        label4.Name = "label4";
        label4.Size = new Size(44, 13);
        label4.TabIndex = 5;
        label4.Text = "location";
        textBox2.Location = new Point(113, 51);
        textBox2.Name = "textBox2";
        textBox2.Size = new Size(129, 20);
        textBox2.TabIndex = 4;
        label3.AutoSize = true;
        label3.Location = new Point(24, 61);
        label3.Name = "label3";
        label3.Size = new Size(37, 13);
        label3.TabIndex = 3;
        label3.Text = "NPCid";
        textBox1.Location = new Point(113, 22);
        textBox1.Name = "textBox1";
        textBox1.Size = new Size(129, 20);
        textBox1.TabIndex = 1;
        label1.AutoSize = true;
        label1.Location = new Point(24, 32);
        label1.Name = "label1";
        label1.Size = new Size(61, 13);
        label1.TabIndex = 0;
        label1.Text = "NPC name ";
        button2.Location = new Point(713, 355);
        button2.Name = "button2";
        button2.Size = new Size(118, 25);
        button2.TabIndex = 14;
        button2.Text = "Increase";
        button2.UseVisualStyleBackColor = true;
        button2.Click += button2_Click;
        button1.Location = new Point(713, 324);
        button1.Name = "button1";
        button1.Size = new Size(121, 25);
        button1.TabIndex = 13;
        button1.Text = "Save changes";
        button1.UseVisualStyleBackColor = true;
        button1.Click += button1_Click;
        contextMenuStrip1.ImageScalingSize = new Size(20, 20);
        contextMenuStrip1.Items.AddRange(new ToolStripItem[2] { 编辑ToolStripMenuItem, 删除ToolStripMenuItem });
        contextMenuStrip1.Name = "contextMenuStrip1";
        contextMenuStrip1.Size = new Size(108, 48);
        编辑ToolStripMenuItem.Name = "编辑ToolStripMenuItem";
        编辑ToolStripMenuItem.Size = new Size(107, 22);
        编辑ToolStripMenuItem.Text = "Edit";
        编辑ToolStripMenuItem.Click += 编辑ToolStripMenuItem_Click;
        删除ToolStripMenuItem.Name = "删除ToolStripMenuItem";
        删除ToolStripMenuItem.Size = new Size(107, 22);
        删除ToolStripMenuItem.Text = "Delete";
        删除ToolStripMenuItem.Click += 删除ToolStripMenuItem_Click;
        label14.AutoSize = true;
        label14.Location = new Point(701, 228);
        label14.Name = "label14";
        label14.Size = new Size(41, 13);
        label14.TabIndex = 42;
        label14.Text = "Item ID";
        comboBox12.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox12.FormattingEnabled = true;
        comboBox12.Location = new Point(702, 257);
        comboBox12.Name = "comboBox12";
        comboBox12.Size = new Size(129, 21);
        comboBox12.TabIndex = 41;
        comboBox11.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox11.FormattingEnabled = true;
        comboBox11.Items.AddRange(new object[13]
        {
            "Quần - Áo", "Hộ Thủ", "Giầy", "Vũ Khí", "Hộ Giáp", "Bông Tai", "Dây Chuyền", "Nhẫn", "Áo Choàng",
            "Bang Phục",
            "Thú Nuôi", "Mũi Tên", "Khác"
        });
        comboBox11.Location = new Point(702, 32);
        comboBox11.MaxDropDownItems = 20;
        comboBox11.Name = "comboBox11";
        comboBox11.Size = new Size(129, 21);
        comboBox11.TabIndex = 40;
        comboBox11.SelectedIndexChanged += comboBox11_SelectedIndexChanged;
        label40.AutoSize = true;
        label40.Location = new Point(700, 13);
        label40.Name = "label40";
        label40.Size = new Size(42, 13);
        label40.TabIndex = 39;
        label40.Text = "Item list";
        listBox3.FormattingEnabled = true;
        listBox3.Location = new Point(702, 59);
        listBox3.Name = "listBox3";
        listBox3.Size = new Size(129, 147);
        listBox3.TabIndex = 38;
        listBox3.SelectedIndexChanged += listBox3_SelectedIndexChanged;
        statusStrip1.ImageScalingSize = new Size(20, 20);
        statusStrip1.Items.AddRange(new ToolStripItem[2] { toolStripStatusLabel1, toolStripStatusLabel2 });
        statusStrip1.Location = new Point(0, 443);
        statusStrip1.Name = "statusStrip1";
        statusStrip1.Size = new Size(846, 22);
        statusStrip1.TabIndex = 43;
        statusStrip1.Text = "statusStrip1";
        toolStripStatusLabel1.ForeColor = Color.Black;
        toolStripStatusLabel1.Name = "toolStripStatusLabel1";
        toolStripStatusLabel1.Size = new Size(66, 17);
        toolStripStatusLabel1.Text = "Thông Tin：";
        toolStripStatusLabel2.ForeColor = Color.Red;
        toolStripStatusLabel2.Name = "toolStripStatusLabel2";
        toolStripStatusLabel2.Size = new Size(118, 17);
        toolStripStatusLabel2.Text = "toolStripStatusLabel2";
        button3.Location = new Point(713, 391);
        button3.Name = "button3";
        button3.Size = new Size(118, 25);
        button3.TabIndex = 44;
        button3.Text = "Deletion";
        button3.UseVisualStyleBackColor = true;
        button3.Click += button3_Click;
        AutoScaleDimensions = new SizeF(6f, 13f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(846, 465);
        Controls.Add(button3);
        Controls.Add(statusStrip1);
        Controls.Add(label14);
        Controls.Add(comboBox12);
        Controls.Add(comboBox11);
        Controls.Add(label40);
        Controls.Add(listBox3);
        Controls.Add(panel1);
        Controls.Add(button1);
        Controls.Add(button2);
        Controls.Add(listView2);
        Controls.Add(button43);
        Name = "SELL";
        Text = "Stored Setting";
        panel1.ResumeLayout(false);
        panel1.PerformLayout();
        contextMenuStrip1.ResumeLayout(false);
        statusStrip1.ResumeLayout(false);
        statusStrip1.PerformLayout();
        ResumeLayout(false);
        PerformLayout();
    }
}