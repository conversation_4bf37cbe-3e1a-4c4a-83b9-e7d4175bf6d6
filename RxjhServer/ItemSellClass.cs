namespace RxjhServer;

public class ItemSellClass
{
    public int 中级附魂 { get; set; }

    public string name { get; set; }

    public string String_0 { get; set; }

    public int NJ { get; set; }

    public int DAYS { get; set; }

    public int BD { get; set; }

    public int Magic0 { get; set; }

    public int ID { get; set; }

    public int Magic4 { get; set; }

    public int Type { get; set; }

    public int Magic1 { get; set; }

    public int Magic2 { get; set; }

    public int Magic5 { get; set; }

    public int Magic3 { get; set; }

    public int 觉醒 { get; set; }

    public int 进化 { get; set; }

    public int Reside { get; set; }

    public static ItemSellClass 取物品数据(int int_15, int int_16)
    {
        using (var enumerator = World.Set_SoLieu.GetEnumerator())
        {
            ItemSellClass itemSellClass = null;
            while (enumerator.MoveNext())
            {
                itemSellClass = enumerator.Current;
                if (itemSellClass.Type == int_15 && itemSellClass.Reside == int_16) return itemSellClass;
            }
        }

        return null;
    }
}