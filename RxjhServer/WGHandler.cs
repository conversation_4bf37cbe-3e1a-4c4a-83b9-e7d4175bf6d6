using System;
using System.Net.Sockets;
using System.Text;

namespace RxjhServer;

public class WGHandler : WGSockClient
{
    public WGHandler(Socket socket_0, RemoveWGClientDelegate removeWGClientDelegate_0)
        : base(socket_0, removeWGClientDelegate_0)
    {
    }

    public string ServerId { get; set; }

    public void Logout()
    {
        Form1.WriteLine(3, "Server lấy cắt ra ID: " + ServerId);
    }

    public override byte[] ProcessDataReceived(byte[] data, int length)
    {
        try
        {
            byte[] array2 = null;
            var num2 = 0;
            var num3 = 0;
            if (170 == data[0] && 102 == data[1])
            {
                array2 = new byte[4];
                System.Buffer.BlockCopy(data, 2, array2, 0, 4);
                num2 = Buffer.ToInt32(array2, 0);
                if (length < num2 + 6) return null;
                while (true)
                {
                    var array3 = new byte[num2];
                    System.Buffer.BlockCopy(data, num3 + 6, array3, 0, num2);
                    num3 += num2 + 6;
                    DataReceived(array3, num2);
                    if (num3 >= length || data[num3] != 170 || data[num3 + 1] != 102) break;
                    System.Buffer.BlockCopy(data, num3 + 2, array2, 0, 4);
                    num2 = Buffer.ToInt16(array2, 0);
                }
            }
            else
            {
                Form1.WriteLine(1, "Sai bao: " + data[0] + " " + data[1]);
            }

            return null;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Phạm sai lầm 222: " + ex.Message);
            Console.WriteLine(ex.Message);
            Console.WriteLine(ex.Source);
            Console.WriteLine(ex.StackTrace);
            return null;
        }
    }

    public byte[] DataReceived(byte[] byte_0, int int_0)
    {
        try
        {
            var @string = Encoding.Default.GetString(byte_0);
            Form1.WriteLine(3, "Số tài khoản nghiệm chứng thu được: " + @string);
            var array2 = @string.Split('|');
            var text = array2[0];
            if (text != null && text == "服务器连接登陆")
            {
                ServerId = array2[1];
                World.PortReplacementNotice(ServerId);
                Form1.WriteLine(3, "Server kết nối thành côngID: " + array2[1]);
            }

            return null;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Phạm sai lầm 333: " + ex.Message);
            Console.WriteLine(ex.Message);
            Console.WriteLine(ex.Source);
            Console.WriteLine(ex.StackTrace);
            return null;
        }
    }
}