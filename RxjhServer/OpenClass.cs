using System.Collections;

namespace RxjhServer;

public class OpenClass
{
    public int FLD_PID { get; set; }

    public string FLD_NAME { get; set; }

    public int FLD_PIDX { get; set; }

    public string FLD_NAMEX { get; set; }

    public int FLD_NUMBER { get; set; }

    public int FLD_PP { get; set; }

    public int FLD_MAGIC1 { get; set; }

    public int FLD_MAGIC2 { get; set; }

    public int FLD_MAGIC3 { get; set; }

    public int FLD_MAGIC4 { get; set; }

    public int FLD_MAGIC5 { get; set; }

    public int FLD_ThucTinh { get; set; }

    public int FLD_TienHoa { get; set; }

    public int FLD_TrungCapPhuHon { get; set; }

    public int FLD_BD { get; set; }

    public int FLD_DAYS { get; set; }

    public int CoMoThongBao { get; internal set; }

    public static OpenClass GetOpen(int int_14, int int_15, int int_16)
    {
        var arrayList = new ArrayList();
        if (int_14 == 1000000071)
        {
            foreach (var value in World.Itme.Values)
                if ((value.FLD_RESIDE2 == 19 || value.FLD_RESIDE2 == 1792) &&
                    (value.FLD_RESIDE1 == int_15 || value.FLD_RESIDE1 == 0) &&
                    (value.FLD_ZX == 0 || value.FLD_ZX == int_16) && value.FLD_LEVEL != 100 && value.FLD_LEVEL != 104 &&
                    value.FLD_LEVEL != 108 && value.FLD_PID != 1000000200 && value.FLD_PID != 1000000213)
                    arrayList.Add(new OpenClass
                    {
                        FLD_PID = 1000000071,
                        FLD_PIDX = value.FLD_PID,
                        FLD_NAME = "上古宝箱",
                        FLD_NAMEX = value.ItmeNAME
                    });
        }
        else
        {
            var num = RNG.Next(1, 500);
            foreach (var item in World.Open)
                if (item.FLD_PID == int_14 && item.FLD_PP >= num)
                    arrayList.Add(item);
            if (arrayList.Count == 0) return null;
        }

        return (OpenClass)arrayList[RNG.Next(0, arrayList.Count - 1)];
    }
}