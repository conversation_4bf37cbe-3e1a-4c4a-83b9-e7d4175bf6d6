using System.Collections;

namespace RxjhServer;

public class ConfigClass
{
    public int ToDoi { get; set; }

    public int GiaoDich { get; set; }

    public int TruyenAm { get; set; }

    public int YPhuc_BanDau { get; set; }

    public int KiemTraTrangBi { get; set; }

    public int PetKinhNghiem { get; set; }

    public int VoHuanSwitchOnOff { get; set; }

    public int ChuyenDoiToc_OnOff { get; set; }

    public int ToTinh_SwitchOnOff { get; set; }

    public int SearchSwitchOnOff { get; set; }

    public int RauQua_SwitchOnOff { get; set; }

    public int 荣誉排名效果 { get; set; }

    public static int GetConfig(ConfigClass configClass_0, int int_12)
    {
        var int_13 = 0;
        if (configClass_0.ChuyenDoiToc_OnOff == 1)
            SetFlags(ref int_13, 7, true);
        else if (configClass_0.ChuyenDoiToc_OnOff == 3) SetFlags(ref int_13, 7, true);
        if (int_12 == 801)
            SetFlags(ref int_13, 4, true);
        else if (configClass_0.YPhuc_BanDau == 1)
            SetFlags(ref int_13, 4, true);
        else if (configClass_0.YPhuc_BanDau == 2) SetFlags(ref int_13, 6, true);
        return int_13;
    }

    public static void SetFlags(ref int int_12, int int_13, bool bool_0)
    {
        var flag = true;
        while (true)
        {
            var bitArray = new BitArray(new int[1] { int_12 });
            bitArray.Set(int_13, bool_0);
            int_12 = 0;
            var num = 0;
            while (true)
            {
                IL_0099:
                var flag2 = true;
                while (true)
                {
                    switch (num < bitArray.Length ? 1 : 5)
                    {
                        case 5:
                            return;
                        case 1:
                            if (bitArray.Get(num)) goto case 4;
                            goto case 0;
                        case 4:
                            int_12 |= 1 << num;
                            goto case 0;
                        case 0:
                            num++;
                            goto IL_0099;
                        case 3:
                            continue;
                        case 2:
                        case 6:
                            goto IL_0099;
                    }

                    break;
                }

                break;
            }
        }
    }
}