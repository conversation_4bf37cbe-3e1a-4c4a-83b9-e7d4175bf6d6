using System;

namespace RxjhServer;

public class MemberOfflineClass
{
    public X_Vat_Pham_Loai[] Packet_Item_Offline;

    public X_Vat_Pham_Loai[] Packet_ItemWear_Offline;

    public X_Vat_Pham_Loai[] Packet_Store_Offline;

    public int ID { get; set; }

    public string UserID { get; set; }

    public string UserName { get; set; }

    public byte[] Item_In_Bag_Offline { get; set; }

    public byte[] Item_Wear_Offline { get; set; }

    public byte[] Item_Store_Offline { get; set; }

    public void Set_Packet_Item_Offline()
    {
        Packet_Item_Offline = new X_Vat_Pham_Loai[96];
        var buffer_item = Item_In_Bag_Offline;
        for (var numIndex = 0; numIndex < 96; numIndex++)
        {
            var buffer22 = new byte[World.Item_Db_Byte_Length];
            if (buffer_item.Length >= numIndex * World.Item_Db_Byte_Length +
                World.Item_Db_Byte_Length)
            {
                try
                {
                    System.Buffer.BlockCopy(buffer_item, numIndex * World.Item_Db_Byte_Length, buffer22,
                        0, World.Item_Db_Byte_Length);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(numIndex + " " + ex);
                }

                Packet_Item_Offline[numIndex] = new X_Vat_Pham_Loai(buffer22, numIndex);
                var buffer23 = new byte[4];
                System.Buffer.BlockCopy(Packet_Item_Offline[numIndex].VatPham_byte, 56, buffer23, 0, 4);
            }
            else
            {
                Packet_Item_Offline[numIndex] = new X_Vat_Pham_Loai(buffer22, numIndex);
            }
        }
    }

    public void Set_Packet_ItemWear_Offline()
    {
        Packet_ItemWear_Offline = new X_Vat_Pham_Loai[16];
        var buffer_item = Item_Wear_Offline;
        for (var numIndex = 0; numIndex < 16; numIndex++)
        {
            var buffer22 = new byte[World.Item_Db_Byte_Length];
            if (buffer_item.Length >= numIndex * World.Item_Db_Byte_Length +
                World.Item_Db_Byte_Length)
            {
                try
                {
                    System.Buffer.BlockCopy(buffer_item, numIndex * World.Item_Db_Byte_Length, buffer22,
                        0, World.Item_Db_Byte_Length);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(numIndex + " " + ex);
                }

                Packet_ItemWear_Offline[numIndex] = new X_Vat_Pham_Loai(buffer22, numIndex);
                var buffer23 = new byte[4];
                System.Buffer.BlockCopy(Packet_ItemWear_Offline[numIndex].VatPham_byte, 56, buffer23, 0, 4);
            }
            else
            {
                Packet_ItemWear_Offline[numIndex] = new X_Vat_Pham_Loai(buffer22, numIndex);
            }
        }
    }

    public void Set_Packet_Store_Offline()
    {
        if (Item_Store_Offline == null) return;
        Packet_Store_Offline = new X_Vat_Pham_Loai[60];
        var buffer_item = Item_Store_Offline;
        for (var numIndex = 0; numIndex < 60; numIndex++)
        {
            var buffer22 = new byte[World.Item_Db_Byte_Length];
            if (buffer_item.Length >= numIndex * World.Item_Db_Byte_Length +
                World.Item_Db_Byte_Length)
            {
                try
                {
                    System.Buffer.BlockCopy(buffer_item, numIndex * World.Item_Db_Byte_Length, buffer22,
                        0, World.Item_Db_Byte_Length);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(numIndex + " " + ex);
                }

                Packet_Store_Offline[numIndex] = new X_Vat_Pham_Loai(buffer22, numIndex);
                var buffer23 = new byte[4];
                System.Buffer.BlockCopy(Packet_Store_Offline[numIndex].VatPham_byte, 56, buffer23, 0, 4);
            }
            else
            {
                Packet_Store_Offline[numIndex] = new X_Vat_Pham_Loai(buffer22, numIndex);
            }
        }
    }
}