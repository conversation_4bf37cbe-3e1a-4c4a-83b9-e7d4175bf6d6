#define TRACE
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;

namespace RxjhServer;

public class WUserManager<TUser> where TUser : IWUser<string>, new()
{
    private static volatile bool _initialized;

    private static WUserManager<TUser> _userManager;

    private int _maxUsers;

    private Dictionary<int, TUser> _users;

    public static void Init(int maxUsers)
    {
        var num2 = 0;
        if (_initialized) throw new ApplicationException("alread inited");
        _userManager = new WUserManager<TUser>();
        _userManager._maxUsers = maxUsers;
        _userManager._users = new Dictionary<int, TUser>(maxUsers);
        for (num2 = 0; num2 < maxUsers; num2++)
        {
            var users = _userManager._users;
            var key = num2;
            users[key] = new TUser
            {
                Index = num2
            };
        }

        var thread = new Thread(threadProce);
        thread.Name = "user manager clearner";
        thread.IsBackground = true;
        thread.Priority = ThreadPriority.Highest;
        thread.Start();
        _initialized = true;
    }

    private static void threadProce(object State)
    {
        try
        {
            var val = default(TUser);
            IDisposable disposable = null;
            IDisposable disposable2 = null;
            IDisposable disposable3 = null;
            while (true)
            {
                var num2 = 0;
                using (var enumerator = _userManager._users.Values.GetEnumerator())
                {
                    num2 = 0;
                    while (true)
                    {
                        num2 = 3;
                        if (!enumerator.MoveNext()) break;
                        var current = enumerator.Current;
                        val = current;
                        disposable3 = val.ReadLock;
                        num2 = 4;
                        try
                        {
                            num2 = 3;
                            if (!val.Invalid)
                            {
                                num2 = 2;
                                disposable = val.UpdateLock;
                                num2 = 1;
                                try
                                {
                                    num2 = 3;
                                    if (DateTime.Now - val.Timestamp > TimeSpan.FromMinutes(3.0))
                                    {
                                        num2 = 2;
                                        disposable2 = val.WriteLock;
                                        num2 = 1;
                                        try
                                        {
                                            val.Invalid = true;
                                        }
                                        finally
                                        {
                                            num2 = 2;
                                            while (true)
                                            {
                                                switch (num2)
                                                {
                                                    case 0:
                                                        break;
                                                    default:
                                                        if (disposable2 != null)
                                                        {
                                                            num2 = 1;
                                                            continue;
                                                        }

                                                        break;
                                                    case 1:
                                                        disposable2.Dispose();
                                                        num2 = 0;
                                                        continue;
                                                }

                                                break;
                                            }
                                        }
                                    }

                                    num2 = 0;
                                }
                                finally
                                {
                                    num2 = 1;
                                    while (true)
                                    {
                                        switch (num2)
                                        {
                                            case 0:
                                                break;
                                            default:
                                                if (disposable != null)
                                                {
                                                    num2 = 2;
                                                    continue;
                                                }

                                                break;
                                            case 2:
                                                disposable.Dispose();
                                                num2 = 0;
                                                continue;
                                        }

                                        break;
                                    }
                                }
                            }

                            num2 = 0;
                        }
                        finally
                        {
                            num2 = 1;
                            while (true)
                            {
                                switch (num2)
                                {
                                    case 0:
                                        break;
                                    default:
                                        if (disposable3 != null)
                                        {
                                            num2 = 2;
                                            continue;
                                        }

                                        break;
                                    case 2:
                                        disposable3.Dispose();
                                        num2 = 0;
                                        continue;
                                }

                                break;
                            }
                        }
                    }

                    num2 = 2;
                    num2 = 1;
                }

                Thread.Sleep(TimeSpan.FromMinutes(1.0));
            }
        }
        catch (Exception arg)
        {
            Trace.TraceError("user manager clear error:", arg);
        }
    }

    public static WUserManager<TUser> GetInstance()
    {
        if (!_initialized) throw new ApplicationException("no init");
        return _userManager;
    }

    public TUser GetUser(int index, string credentials)
    {
        var num2 = 0;
        num2 = 3;
        var val = default(TUser);
        IDisposable disposable = null;
        IDisposable disposable2 = null;
        TUser val2;
        if (index >= 0)
        {
            num2 = 1;
            num2 = 6;
            if (index < _maxUsers)
            {
                num2 = 2;
                num2 = 5;
                if (string.IsNullOrEmpty(credentials))
                {
                    num2 = 0;
                    val2 = default;
                    return val2;
                }

                val = _users[index];
                disposable = val.UpdateLock;
                num2 = 4;
                try
                {
                    num2 = 2;
                    if (!val.Authentication(credentials))
                    {
                        num2 = 4;
                        throw new ApplicationException("Authentication failed");
                    }

                    num2 = 0;
                    if (!val.Invalid)
                    {
                        num2 = 5;
                        disposable2 = val.WriteLock;
                        num2 = 1;
                        try
                        {
                            ref var reference = ref val;
                            val2 = default;
                            if (val2 == null)
                            {
                                val2 = reference;
                                reference = ref val2;
                            }

                            reference.Timestamp = DateTime.Now;
                            return val;
                        }
                        finally
                        {
                            num2 = 1;
                            while (true)
                            {
                                switch (num2)
                                {
                                    case 0:
                                        break;
                                    default:
                                        if (disposable2 != null)
                                        {
                                            num2 = 2;
                                            continue;
                                        }

                                        break;
                                    case 2:
                                        disposable2.Dispose();
                                        num2 = 0;
                                        continue;
                                }

                                break;
                            }
                        }
                    }

                    num2 = 3;
                }
                finally
                {
                    num2 = 1;
                    while (true)
                    {
                        switch (num2)
                        {
                            case 0:
                                break;
                            default:
                                if (disposable != null)
                                {
                                    num2 = 2;
                                    continue;
                                }

                                break;
                            case 2:
                                disposable.Dispose();
                                num2 = 0;
                                continue;
                        }

                        break;
                    }
                }

                val2 = default;
                return val2;
            }
        }

        val2 = default;
        return val2;
    }

    public TUser AddUser()
    {
        var num = 0;
        var val = default(TUser);
        IDisposable disposable = null;
        IDisposable disposable2 = null;
        var num2 = 0;
        num = 1;
        TUser val2;
        while (true)
        {
            num = 2;
            if (num2 >= _maxUsers) break;
            val = _users[num2];
            disposable = val.UpdateLock;
            num = 0;
            try
            {
                num = 1;
                while (true)
                {
                    switch (val.Invalid ? 3 : 0)
                    {
                        case 0:
                            goto end_IL_0142;
                        case 3:
                            disposable2 = val.WriteLock;
                            num = 2;
                            break;
                        case 2:
                            break;
                        default:
                            continue;
                    }

                    try
                    {
                        val.Reset();
                        val.Invalid = false;
                        ref var reference = ref val;
                        val2 = default;
                        if (val2 == null)
                        {
                            val2 = reference;
                            reference = ref val2;
                        }

                        reference.Timestamp = DateTime.Now;
                        return val;
                    }
                    finally
                    {
                        num = 0;
                        while (true)
                        {
                            switch (num)
                            {
                                case 1:
                                    break;
                                default:
                                    if (disposable2 != null)
                                    {
                                        num = 2;
                                        continue;
                                    }

                                    break;
                                case 2:
                                    disposable2.Dispose();
                                    num = 1;
                                    continue;
                            }

                            break;
                        }
                    }

                    continue;
                    end_IL_0142:
                    break;
                }
            }
            finally
            {
                num = 0;
                while (true)
                {
                    switch (num)
                    {
                        case 1:
                            break;
                        default:
                            if (disposable != null)
                            {
                                num = 2;
                                continue;
                            }

                            break;
                        case 2:
                            disposable.Dispose();
                            num = 1;
                            continue;
                    }

                    break;
                }
            }

            num2++;
            num = 3;
        }

        num = 4;
        val2 = default;
        return val2;
    }

    public bool RemoveUser(int index, string credentials)
    {
        var num = 0;
        var val = default(TUser);
        IDisposable disposable = null;
        if (index >= 0)
        {
            num = 2;
            num = 7;
            if (index < _maxUsers)
            {
                num = 3;
                num = 8;
                if (!string.IsNullOrEmpty(credentials))
                {
                    num = 1;
                    val = _users[index];
                    num = 4;
                    if (val == null)
                    {
                        num = 6;
                        return false;
                    }

                    disposable = val.WriteLock;
                    num = 5;
                    try
                    {
                        val.Invalid = true;
                    }
                    finally
                    {
                        num = 0;
                        while (true)
                        {
                            switch (num)
                            {
                                case 1:
                                    break;
                                default:
                                    if (disposable != null)
                                    {
                                        num = 2;
                                        continue;
                                    }

                                    break;
                                case 2:
                                    disposable.Dispose();
                                    num = 1;
                                    continue;
                            }

                            break;
                        }
                    }

                    return true;
                }
            }
        }

        return false;
    }
}