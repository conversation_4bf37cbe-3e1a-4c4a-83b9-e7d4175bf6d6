using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Threading;
using System.Timers;
using System.Windows.Forms;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class Show1 : Form
{
    private static int b2;

    private static List<TxtClass> f;
    private System.Windows.Forms.Timer al;

    private Button button1;

    private CheckBox checkBox1;

    private ComboBox comboBox1;

    private IContainer components;

    private MainMenu eval_o_o;

    private ImageList eval_w_w;

    private DateTime g = DateTime.Now;

    private FlickerFreePanel KhungNen;

    private Label label1;

    private Label label2;

    private Label label3;

    private TextBox NguoiGui;

    private TextBox NguoiNhan;

    private TextBox TinNhan;

    static Show1()
    {
        old_acctor_mc();
    }

    public Show1()
    {
        InitializeComponent();
    }

    private void a(object sender, ElapsedEventArgs e)
    {
    }

    private void a(string A_0, int A_1)
    {
    }

    [STAThread]
    private static void c()
    {
        try
        {
            Application.Run(new Show1());
        }
        catch (Exception ex)
        {
            MessageBox.Show("Main 错误" + ex);
        }
    }

    private void eval_a(object sender, EventArgs e)
    {
    }

    private void eval_a(object sender, FormClosingEventArgs e)
    {
    }

    private void eval_a(object sender, LayoutEventArgs e)
    {
        if (KhungNen.Height != 0) b2 = KhungNen.Height;
    }

    private void eval_a(object sender, PaintEventArgs e)
    {
        try
        {
            var graphics = e.Graphics;
            graphics.SmoothingMode = SmoothingMode.AntiAlias;
            graphics.PixelOffsetMode = PixelOffsetMode.None;
            var num3 = 0;
            foreach (var class2 in f)
                switch (class2.type)
                {
                    case 0:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.White, new Point(5, num3 += 17));
                        break;
                    case 1:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.Green, new Point(5, num3 += 17));
                        break;
                    case 2:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.Orange, new Point(5, num3 += 17));
                        break;
                    case 3:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.Pink, new Point(5, num3 += 17));
                        break;
                    case 4:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.Yellow, new Point(5, num3 += 17));
                        break;
                    case 5:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.SaddleBrown, new Point(5, num3 += 17));
                        break;
                    case 6:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.Red, new Point(5, num3 += 17));
                        break;
                    case 7:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.White, new Point(5, num3 += 17));
                        break;
                    case 8:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.Yellow, new Point(5, num3 += 17));
                        break;
                    case 9:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.RoyalBlue, new Point(5, num3 += 17));
                        break;
                    case 10:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.MediumSpringGreen,
                            new Point(5, num3 += 17));
                        break;
                    case 11:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.Teal, new Point(5, num3 += 17));
                        break;
                    case 12:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.Teal, new Point(5, num3 += 17));
                        break;
                    case 13:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.MediumVioletRed, new Point(5, num3 += 17));
                        break;
                    case 14:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.Tan, new Point(5, num3 += 17));
                        break;
                    default:
                        graphics.DrawString(class2.Txt, DefaultFont, Brushes.Lime, new Point(5, num3 += 17));
                        break;
                }
        }
        catch
        {
        }
    }

    private void eval_a(byte[] A_0, int A_1)
    {
        var dst = new byte[A_1 + 15];
        dst[0] = 170;
        dst[1] = 85;
        Buffer.BlockCopy(BitConverter.GetBytes(A_1 + 9), 0, dst, 2, 2);
        Buffer.BlockCopy(A_0, 0, dst, 5, A_1);
        dst[dst.Length - 2] = 85;
        dst[dst.Length - 1] = 170;
        Console.WriteLine(Converter.ToString(dst));
    }

    private void eval_a6(object sender, EventArgs e)
    {
        KhungNen.Invalidate();
    }

    private void eval_a7(object sender, EventArgs e)
    {
    }

    private void eval_a8(object sender, EventArgs e)
    {
    }

    private void eval_a9(object sender, EventArgs e)
    {
    }

    private void eval_aa(object sender, EventArgs e)
    {
        var thread = new Thread(Form2.FlushAll2);
        thread.Name = "Timer Thread";
        thread.Start();
    }

    private void eval_ab(object sender, EventArgs e)
    {
        var thread = new Thread(Form2.FlushAll1);
        thread.Name = "Timer Thread";
        thread.Start();
    }

    private void eval_ac(object sender, EventArgs e)
    {
        new BinIP().ShowDialog();
    }

    private void eval_ba(object sender, EventArgs e)
    {
        try
        {
        }
        catch (Exception)
        {
        }
    }

    private void InitializeComponent()
    {
        components = new Container();
        eval_w_w = new ImageList(components);
        eval_o_o = new MainMenu(components);
        TinNhan = new TextBox();
        NguoiGui = new TextBox();
        button1 = new Button();
        comboBox1 = new ComboBox();
        KhungNen = new FlickerFreePanel();
        label1 = new Label();
        label2 = new Label();
        label3 = new Label();
        NguoiNhan = new TextBox();
        checkBox1 = new CheckBox();
        al = new System.Windows.Forms.Timer(components);
        SuspendLayout();
        eval_w_w.ColorDepth = ColorDepth.Depth8Bit;
        eval_w_w.ImageSize = new Size(16, 16);
        eval_w_w.TransparentColor = Color.Transparent;
        TinNhan.Location = new Point(2, 302);
        TinNhan.Name = "TinNhan";
        TinNhan.Size = new Size(738, 21);
        TinNhan.TabIndex = 0;
        TinNhan.TextChanged += TinNhan_TextChanged;
        TinNhan.KeyPress += CheckKeys;
        NguoiGui.Location = new Point(2, 324);
        NguoiGui.Name = "NguoiGui";
        NguoiGui.Size = new Size(88, 21);
        NguoiGui.TabIndex = 0;
        NguoiGui.Text = "GameMaster";
        NguoiGui.KeyPress += CheckKeys;
        button1.Enabled = false;
        button1.Location = new Point(462, 302);
        button1.Name = "button1";
        button1.Size = new Size(78, 46);
        button1.TabIndex = 1;
        button1.Text = "Send";
        button1.UseVisualStyleBackColor = true;
        button1.Click += button1_Click;
        comboBox1.FormattingEnabled = true;
        comboBox1.Items.AddRange(new object[28]
        {
            "0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
            "10", "11", "12", "13", "14", "15", "16", "17", "18", "19",
            "20", "21", "22", "23", "24", "25", "26", "204"
        });
        comboBox1.Location = new Point(269, 324);
        comboBox1.Name = "comboBox1";
        comboBox1.Size = new Size(56, 20);
        comboBox1.TabIndex = 0;
        comboBox1.Text = "21";
        KhungNen.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
        KhungNen.BackColor = Color.Black;
        KhungNen.BorderStyle = BorderStyle.Fixed3D;
        KhungNen.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Regular, GraphicsUnit.Point, 0);
        KhungNen.ForeColor = SystemColors.ControlText;
        KhungNen.Location = new Point(2, 0);
        KhungNen.Name = "KhungNen";
        KhungNen.Size = new Size(539, 302);
        KhungNen.TabIndex = 0;
        KhungNen.Paint += eval_a;
        label1.AutoSize = true;
        label1.Location = new Point(235, 325);
        label1.Name = "label1";
        label1.Size = new Size(34, 12);
        label1.TabIndex = 2;
        label1.Text = "Type";
        label2.AutoSize = true;
        label2.Location = new Point(90, 328);
        label2.Name = "label2";
        label2.Size = new Size(39, 12);
        label2.TabIndex = 3;
        label2.Text = "Name";
        label3.AutoSize = true;
        label3.Location = new Point(126, 328);
        label3.Name = "label3";
        label3.Size = new Size(20, 12);
        label3.TabIndex = 4;
        label3.Text = "To";
        NguoiNhan.Location = new Point(149, 324);
        NguoiNhan.Name = "NguoiNhan";
        NguoiNhan.Size = new Size(87, 21);
        NguoiNhan.TabIndex = 5;
        checkBox1.AutoSize = true;
        checkBox1.Location = new Point(332, 328);
        checkBox1.Name = "checkBox1";
        checkBox1.Size = new Size(15, 14);
        checkBox1.TabIndex = 6;
        checkBox1.UseVisualStyleBackColor = true;
        checkBox1.CheckedChanged += checkBox1_CheckedChanged;
        al.Enabled = true;
        al.Interval = 1000;
        al.Tick += eval_a6;
        AutoScaleBaseSize = new Size(6, 14);
        BackColor = SystemColors.ControlLightLight;
        BackgroundImageLayout = ImageLayout.Center;
        ClientSize = new Size(541, 348);
        Controls.Add(button1);
        Controls.Add(comboBox1);
        Controls.Add(NguoiNhan);
        Controls.Add(label3);
        Controls.Add(label2);
        Controls.Add(label1);
        Controls.Add(TinNhan);
        Controls.Add(NguoiGui);
        Controls.Add(KhungNen);
        Controls.Add(checkBox1);
        MaximizeBox = false;
        Menu = eval_o_o;
        Name = "Show1";
        Text = "서버 채팅 모니터";
        FormClosing += eval_a;
        Load += eval_ba;
        Layout += eval_a;
        ResumeLayout(false);
        PerformLayout();
    }

    private static void old_acctor_mc()
    {
        f = new List<TxtClass>();
        b2 = 300;
    }

    public void Send单包(byte[] toSendBuff, int len)
    {
        var dst = new byte[BitConverter.ToInt16(toSendBuff, 9) + 7];
        Buffer.BlockCopy(toSendBuff, 5, dst, 0, dst.Length);
        eval_a(dst, dst.Length);
    }

    public static void WriteLine(int type, string txtt)
    {
        var num = b2 / 18;
        lock (f)
        {
            f.Add(new TxtClass(type, $"{DateTime.Now:HH:mm:ss}" + " | " + txtt));
            var count = f.Count;
            if (num <= 0) num = 20;
            if (count > num)
                for (var i = 0; i < count - num; i++)
                    f.RemoveAt(0);
        }
    }

    private void button1_Click(object sender, EventArgs e)
    {
        try
        {
            if (NguoiNhan.Text != "")
                WriteLine(int.Parse(comboBox1.SelectedItem.ToString()),
                    NguoiGui.Text + " to " + NguoiNhan.Text + ": " + TinNhan.Text);
            else if (comboBox1.SelectedItem.ToString() != "")
                WriteLine(int.Parse(comboBox1.SelectedItem.ToString()), NguoiGui.Text + ": " + TinNhan.Text);
            foreach (var players in World.allConnectedChars.Values)
                if (NguoiNhan.Text != "")
                {
                    if (players.UserName.ToLower() == NguoiNhan.Text.ToLower() && TinNhan.Text.Length <= 100 &&
                        !players.ParseCommand(TinNhan.Text))
                    {
                        players.HeThongNhacNho(TinNhan.Text, int.Parse(comboBox1.SelectedItem.ToString()),
                            NguoiGui.Text);
                        break;
                    }
                }
                else
                {
                    players?.HeThongNhacNho(TinNhan.Text, int.Parse(comboBox1.SelectedItem.ToString()), NguoiGui.Text);
                }
        }
        catch
        {
        }
    }

    private void CheckKeys(object sender, KeyPressEventArgs e)
    {
        if (e.KeyChar == '\r' && TinNhan.Text != "") button1_Click(sender, e);
    }

    private void TinNhan_TextChanged(object sender, EventArgs e)
    {
        try
        {
            if (TinNhan.Text == "")
                button1.Enabled = false;
            else
                button1.Enabled = true;
        }
        catch
        {
        }
    }

    public static void TB_Baotri()
    {
        if (World.jlMsg == 1) WriteLine(0, "Tu dong thong bao bao tri !!!");
        if (World.AutoTbBaotri != 1 || !(World.TextTbBaotri != "")) return;
        foreach (var players in World.allConnectedChars.Values) players.HeThongNhacNho(World.TextTbBaotri, 8);
    }

    private void checkBox1_CheckedChanged(object sender, EventArgs e)
    {
        if (checkBox1.Checked)
        {
            World.TextTbBaotri = TinNhan.Text;
            World.AutoTbBaotri = 1;
        }
        else
        {
            World.AutoTbBaotri = 0;
        }
    }
}