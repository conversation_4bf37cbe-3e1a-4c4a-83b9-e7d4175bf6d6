using System;

namespace RxjhServer;

public class X_Nguoi_Cua_Hang_Loai : IDisposable
{
    public Players NhanVaoNguoi<PERSON>ua;

    public int StoreType;

    public X_Nguoi_Cua_Hang_Loai()
    {
        StoreItemList = new ThreadSafeDictionary<long, X_Nguoi_Cua_Hang_Vat_Pham_Loai>();
    }

    public bool CuaHangCaNhanPhaiChangMoRa { get; set; }

    public bool CuaHangCaNhan_PhaiChangDangSuDungBenTrong { get; set; }

    public byte[] StoreName { get; set; }

    public ThreadSafeDictionary<long, X_Nguoi_Cua_Hang_Vat_Pham_Loai> StoreItemList { get; set; }

    public void Dispose()
    {
        var num = 2;
        while (true)
        {
            switch (num)
            {
                case 0:
                    StoreItemList.Clear();
                    StoreItemList.Dispose();
                    StoreItemList = null;
                    num = 1;
                    continue;
                default:
                    if (StoreItemList != null)
                    {
                        num = 0;
                        continue;
                    }

                    break;
                case 1:
                    break;
            }

            break;
        }

        StoreName = null;
        NhanVaoNguoiMua = null;
    }

    ~X_Nguoi_Cua_Hang_Loai()
    {
    }
}