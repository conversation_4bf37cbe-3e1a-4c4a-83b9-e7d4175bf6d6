using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class 游戏公告 : Form
{
    private Button button39;

    private Button button40;

    private Button button41;

    private Button button42;
    private IContainer components;

    private DateTimePicker dateTimePicker1;

    private Label label116;

    private Label label117;

    private Label label118;

    private ListBox listBox17;

    private TextBox textBox59;

    private TextBox textBox60;

    public 游戏公告()
    {
        InitializeComponent();
    }

    private void button39_Click(object sender, EventArgs e)
    {
        selectgg();
    }

    private void selectgg()
    {
        try
        {
            var string_ = "select * from tbl_xwwl_gg";
            var dBToDataTable = DBA.GetDBToDataTable(string_, "PublicDb");
            listBox17.Items.Clear();
            for (var i = 0; i < dBToDataTable.Rows.Count; i++)
                listBox17.Items.Add(dBToDataTable.Rows[i]["txt"].ToString());
            dBToDataTable.Dispose();
        }
        catch
        {
        }
    }

    private void button40_Click(object sender, EventArgs e)
    {
        if (textBox59.Text.Length == 0)
        {
            MessageBox.Show("公告内容不可以为空");
            return;
        }

        var string_ = "INSERT into TBL_XWWL_Gg(txt,type)values('" + textBox59.Text + "'," + textBox60.Text + ")";
        DBA.ExeSqlCommand(string_, "PublicDb");
        selectgg();
    }

    private void button41_Click(object sender, EventArgs e)
    {
        if (listBox17.SelectedIndex < 0)
        {
            MessageBox.Show("请选择要删除的公告");
            return;
        }

        var string_ = "DELETE TBL_XWWL_GG WHERE TXT='" + textBox59.Text + "'";
        DBA.ExeSqlCommand(string_, "PublicDb");
        selectgg();
    }

    private void button42_Click(object sender, EventArgs e)
    {
        var string_ = "update TBL_XWWL_GG SET txt='" + textBox59.Text + "',type='" + textBox60.Text + "' where txt='" +
                      listBox17.Text + "' AND TYPE='" + textBox60.Text + "'";
        DBA.ExeSqlCommand(string_, "PublicDb");
        selectgg();
    }

    private void textBox59_TextChanged(object sender, EventArgs e)
    {
    }

    private void listBox17_SelectedIndexChanged(object sender, EventArgs e)
    {
        var string_ = "select * from tbl_xwwl_gg where txt='" + listBox17.Text + "'";
        var dBToDataTable = DBA.GetDBToDataTable(string_, "PublicDb");
        textBox60.Text = dBToDataTable.Rows[0]["type"].ToString();
        textBox59.Text = listBox17.Text;
        dBToDataTable.Dispose();
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        label118 = new Label();
        textBox60 = new TextBox();
        label117 = new Label();
        textBox59 = new TextBox();
        button42 = new Button();
        button41 = new Button();
        button40 = new Button();
        button39 = new Button();
        listBox17 = new ListBox();
        label116 = new Label();
        dateTimePicker1 = new DateTimePicker();
        SuspendLayout();
        label118.AutoSize = true;
        label118.Location = new Point(229, 14);
        label118.Name = "label118";
        label118.Size = new Size(53, 12);
        label118.TabIndex = 19;
        label118.Text = "公告内容";
        textBox60.Location = new Point(173, 149);
        textBox60.Name = "textBox60";
        textBox60.Size = new Size(44, 21);
        textBox60.TabIndex = 18;
        textBox60.Text = "0";
        label117.AutoSize = true;
        label117.Location = new Point(138, 154);
        label117.Name = "label117";
        label117.Size = new Size(29, 12);
        label117.TabIndex = 17;
        label117.Text = "类型";
        textBox59.Location = new Point(219, 33);
        textBox59.Multiline = true;
        textBox59.Name = "textBox59";
        textBox59.Size = new Size(373, 168);
        textBox59.TabIndex = 16;
        textBox59.TextChanged += textBox59_TextChanged;
        button42.Location = new Point(138, 116);
        button42.Name = "button42";
        button42.Size = new Size(75, 23);
        button42.TabIndex = 15;
        button42.Text = "修改";
        button42.UseVisualStyleBackColor = true;
        button42.Click += button42_Click;
        button41.Location = new Point(138, 87);
        button41.Name = "button41";
        button41.Size = new Size(75, 23);
        button41.TabIndex = 14;
        button41.Text = "删除";
        button41.UseVisualStyleBackColor = true;
        button41.Click += button41_Click;
        button40.Location = new Point(138, 58);
        button40.Name = "button40";
        button40.Size = new Size(75, 23);
        button40.TabIndex = 13;
        button40.Text = "添加";
        button40.UseVisualStyleBackColor = true;
        button40.Click += button40_Click;
        button39.Location = new Point(138, 29);
        button39.Name = "button39";
        button39.Size = new Size(75, 23);
        button39.TabIndex = 12;
        button39.Text = "查看";
        button39.UseVisualStyleBackColor = true;
        button39.Click += button39_Click;
        listBox17.FormattingEnabled = true;
        listBox17.ItemHeight = 12;
        listBox17.Location = new Point(12, 31);
        listBox17.Name = "listBox17";
        listBox17.Size = new Size(120, 172);
        listBox17.TabIndex = 11;
        listBox17.SelectedIndexChanged += listBox17_SelectedIndexChanged;
        label116.AutoSize = true;
        label116.Location = new Point(14, 14);
        label116.Name = "label116";
        label116.Size = new Size(53, 12);
        label116.TabIndex = 10;
        label116.Text = "公告列表";
        dateTimePicker1.Location = new Point(288, 7);
        dateTimePicker1.Name = "dateTimePicker1";
        dateTimePicker1.Size = new Size(200, 21);
        dateTimePicker1.TabIndex = 20;
        AutoScaleDimensions = new SizeF(6f, 12f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(613, 233);
        Controls.Add(dateTimePicker1);
        Controls.Add(label118);
        Controls.Add(textBox60);
        Controls.Add(label117);
        Controls.Add(textBox59);
        Controls.Add(button42);
        Controls.Add(button41);
        Controls.Add(button40);
        Controls.Add(button39);
        Controls.Add(listBox17);
        Controls.Add(label116);
        Name = "游戏公告";
        Text = "游戏公告";
        ResumeLayout(false);
        PerformLayout();
    }
}