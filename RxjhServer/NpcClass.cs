using System;
using System.Collections;
using System.Collections.Generic;
using System.Timers;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace RxjhServer;

public class NpcClass : IDisposable
{
    private static readonly PlayGjClass asfd = new();

    private static Random Ran;

    private readonly object _lock = new();

    private readonly ArrayList arrayList_0 = new();

    private readonly object AsyncLocksw = new();

    public System.Timers.Timer AutomaticAttack;

    public System.Timers.Timer AutomaticMove;

    public System.Timers.Timer AutomaticRecover;

    public double FLD_TRUDEF_NPC_NINJA = 0.0;

    public Reverser<PlayGjClass> reverser = new(asfd.GetType(), "Gjsl", ReverserInfo.Direction.DESC);

    public Dictionary<int, X_Di_Thuong_Trang_Thai_Loai> TrangThai_BatThuong;

    public Dictionary<int, X_Than_Nu_Di_Thuong_Trang_Thai_Loai> TrangThai_BatThuong_ThanNu;

    public Dictionary<int, X_Di_Thuong_Mat_Mau_Trang_Thai_Loai> TrangThai_MatMau_BatThuong;

    public System.Timers.Timer TuDongHoiSinh;

    public NpcClass(int npc = 0)
    {
        Ran = new Random(DateTime.Now.Millisecond);
        _PlayList = new ThreadSafeDictionary<int, Players>();
        double interval = Ran.Next(2000, 6000);
        AutomaticMove = new System.Timers.Timer(interval);
        AutomaticMove.Elapsed += AutomaticMoveEvent;
        AutomaticMove.AutoReset = true;
        AutomaticMove.Enabled = true;
        AutomaticAttack = new System.Timers.Timer(1000.0);
        AutomaticAttack.Elapsed += AutomaticAttackEvent;
        AutomaticAttack.AutoReset = true;
        TrangThai_BatThuong = new Dictionary<int, X_Di_Thuong_Trang_Thai_Loai>();
        TrangThai_MatMau_BatThuong = new Dictionary<int, X_Di_Thuong_Mat_Mau_Trang_Thai_Loai>();
        TrangThai_BatThuong_ThanNu = new Dictionary<int, X_Than_Nu_Di_Thuong_Trang_Thai_Loai>();
    }

    public X_Linh_Thu_Loai PlayCw { get; set; }

    public ThreadSafeDictionary<int, Players> _PlayList { get; private set; }

    public List<PlayGjClass> PlayGj { get; set; } = new();

    public int PlayerWid
    {
        get
        {
            var num2 = 0;
            while (true)
            {
                int num6;
                switch (num2)
                {
                    default:
                        num6 = PlayGj.Count > 0 ? 1 : 2;
                        break;
                    case 2:
                        return 0;
                    case 1:
                        try
                        {
                            PlayGj.Sort(new Reverser<PlayGjClass>(new PlayGjClass().GetType(), "Gjsl",
                                ReverserInfo.Direction.DESC));
                            return PlayGj[0].PlayID;
                        }
                        catch (Exception)
                        {
                            return 0;
                        }
                }

                var num5 = num6;
                var num3 = num5;
                var num4 = num3;
                num2 = num4;
            }
        }
    }

    public int BossPlayerWid
    {
        get
        {
            var num2 = 0;
            var num3 = 0;
            var num4 = 0;
            while (true)
            {
                int num9;
                switch (num2)
                {
                    default:
                        num9 = PlayGj.Count > 0 ? 1 : 2;
                        break;
                    case 2:
                        return 0;
                    case 1:
                        try
                        {
                            var num5 = 0;
                            num2 = 4;
                            while (true)
                            {
                                num3 = RNG.Next(0, PlayGj.Count - 1);
                                num2 = 3;
                                if (PlayGj[num3].Gjxl >= 100000) break;
                                num2 = 6;
                                num5++;
                                num2 = 0;
                                if (num5 >= PlayGj.Count)
                                {
                                    num2 = 1;
                                    PlayGj.Sort(new Reverser<PlayGjClass>(new PlayGjClass().GetType(), "Gjsl",
                                        ReverserInfo.Direction.DESC));
                                    num4 = PlayGj[0].PlayID;
                                    num2 = 5;
                                    return num4;
                                }
                            }

                            num4 = PlayGj[num3].PlayID;
                            num2 = 2;
                            return num4;
                        }
                        catch (Exception)
                        {
                            return 0;
                        }
                }

                var num8 = num9;
                var num6 = num8;
                var num7 = num6;
                num2 = num7;
            }
        }
    }

    public float FLD_FACE1 { get; set; }

    public float FLD_FACE2 { get; set; }

    public int IsNpc { get; set; }

    public string Name { get; set; }

    public int FLD_INDEX { get; set; }

    public int FLD_PID { get; set; }

    public double FLD_AT { get; set; }

    public float Rxjh_X { get; set; }

    public float Rxjh_Y { get; set; }

    public float Rxjh_Z { get; set; }

    public float Rxjh_cs_X { get; set; }

    public float Rxjh_cs_Y { get; set; }

    public float Rxjh_cs_Z { get; set; }

    public int Rxjh_Map { get; set; }

    public int FLD_PhiDiChuyen { get; set; }

    public int Rxjh_Exp { get; set; }

    public int Rxjh_Gold { get; set; }

    public int Max_Rxjh_HP { get; set; }

    public int Rxjh_HP { get; set; }

    public int Level { get; set; }

    public double FLD_DF { get; set; }

    public int FLD_AUTO { get; set; }

    public int FLD_BOSS { get; set; }

    public int FLD_NEWTIME { get; set; }

    public bool NPCDeath { get; set; }

    public int MeegoDebuff_DVB_DamageCount { get; set; }

    public int MeegoDebuff_DVB_CasterSessionID { get; set; }

    public bool QuaiXuatHien_DuyNhatMotLan { get; set; }

    public void Dispose()
    {
        try
        {
            MapClass.delnpc(Rxjh_Map, FLD_INDEX);
            if (AutomaticAttack != null)
            {
                AutomaticAttack.Enabled = false;
                AutomaticAttack.Close();
                AutomaticAttack.Dispose();
            }

            if (AutomaticMove != null)
            {
                AutomaticMove.Enabled = false;
                AutomaticMove.Close();
                AutomaticMove.Dispose();
            }

            if (TuDongHoiSinh != null)
            {
                TuDongHoiSinh.Enabled = false;
                TuDongHoiSinh.Close();
                TuDongHoiSinh.Dispose();
                TuDongHoiSinh = null;
            }

            if (AutomaticRecover != null)
            {
                AutomaticRecover.Enabled = false;
                AutomaticRecover.Close();
                AutomaticRecover.Dispose();
                AutomaticRecover = null;
            }

            Play_null();
            _PlayList.Clear();
            if (PlayCw != null) PlayCw = null;
            获取范围玩家发送消失数据包();
            arrayList_0?.Clear();
            if (_PlayList != null)
            {
                _PlayList.Dispose();
                _PlayList = null;
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "NPC Quan bế số liệu Dispose Phạm sai lầm: " + ex);
        }
    }

    public void Cw_Add(X_Linh_Thu_Loai SpiritBeast)
    {
        foreach (var playGj in PlayGj)
            if (playGj.PlayID == SpiritBeast.FullServiceID)
            {
                var gjsl = playGj.Gjsl + 1;
                playGj.Gjsl = gjsl;
                return;
            }

        PlayGj.Add(new PlayGjClass
        {
            Gjsl = 1,
            PlayID = SpiritBeast.FullServiceID
        });
        PlayCw = SpiritBeast;
    }

    public void PlayList_Add(Players Play)
    {
        if (!Contains(Play)) _PlayList.Add(Play.CharacterFullServerID, Play);
    }

    public bool Check_NpcAttack(Players play)
    {
        if (_PlayList.ContainsKey(play.CharacterFullServerID)) return true;
        return false;
    }

    public void PlayList_Remove(Players payer)
    {
        if (Contains(payer)) _PlayList.Remove(payer.CharacterFullServerID);
    }

    public bool Contains(Players payer)
    {
        Players value;
        if (_PlayList != null && _PlayList.Count != 0)
            return _PlayList.TryGetValue(payer.CharacterFullServerID, out value);
        return false;
    }

    public void Play_dell(Players payer)
    {
    }

    public void CheckQuestDrop(Players players, double per = 1.0)
    {
        foreach (var Quest in World.TBL_QUESTDROP.Values)
        {
            if (Quest.MonsterID == FLD_PID)
            {
                var rateDrop2 = new Random(World.GetRandomSeed()).Next(0, (int)(8000.0 * per));
                if (players.GMMode != 0 && players.NhiemVu.ContainsKey(Quest.QuestID) &&
                    players.GetTaskStage(Quest.QuestID) != 255)
                    players.HeThongNhacNho(
                        "Status: QID: " + Quest.QuestID + " -> " + players.GetTaskStage(Quest.QuestID) + "/" +
                        Quest.QuestLevel + " - Rate: " + rateDrop2 + "/" + Quest.DropRatePercent, 9, "DROPQUEST");
                if (players.GetTaskStage(Quest.QuestID) == Quest.QuestLevel &&
                    !players.ObtainQuestItems(Quest.QuestDropID, Quest.QuestItemMax) &&
                    (rateDrop2 <= Quest.DropRatePercent || Quest.QuestItemMax == 1))
                    players.SetUpQuestItems(Quest.QuestDropID, 1);
            }

            if (Quest.MonsterID2 == FLD_PID)
            {
                var rateDrop3 = new Random(World.GetRandomSeed()).Next(0, (int)(8000.0 * per));
                if (players.GMMode != 0 && players.GetTaskStage(Quest.QuestID) != 255)
                    players.HeThongNhacNho("Status: QID: " + Quest.QuestID + " -> " +
                                           players.GetTaskStage(Quest.QuestID) + "/" + Quest.QuestLevel + " - Rate: " +
                                           rateDrop3 + "/" + Quest.DropRatePercent);
                if (players.GetTaskStage(Quest.QuestID) == Quest.QuestLevel &&
                    !players.ObtainQuestItems(Quest.QuestDropID2, Quest.QuestItemMax2) &&
                    (rateDrop3 <= Quest.DropRatePercent || Quest.QuestItemMax2 == 1))
                    players.SetUpQuestItems(Quest.QuestDropID2, 1);
            }

            if (Quest.MonsterID == 6688 && players.Player_Level - Level < World.LayDuoc_KinhNghiem_CapDo_ChenhLech)
            {
                var rateDrop4 = new Random(World.GetRandomSeed()).Next(0, (int)(8000.0 * per));
                if (players.GMMode != 0 && players.GetTaskStage(Quest.QuestID) != 255)
                    players.HeThongNhacNho("Status: QID: " + Quest.QuestID + " -> " +
                                           players.GetTaskStage(Quest.QuestID) + "/" + Quest.QuestLevel + " - Rate: " +
                                           rateDrop4 + "/" + Quest.DropRatePercent);
                if (players.GetTaskStage(Quest.QuestID) == Quest.QuestLevel &&
                    !players.ObtainQuestItems(Quest.QuestDropID, Quest.QuestItemMax) &&
                    rateDrop4 <= Quest.DropRatePercent) players.SetUpQuestItems(Quest.QuestDropID, 1);
            }

            if (Quest.MonsterID == 6996 && players.Player_Level - Level < World.LayDuoc_KinhNghiem_CapDo_ChenhLech)
            {
                var rateDrop5 = new Random(World.GetRandomSeed()).Next(0, (int)(8000.0 * per));
                if (players.GMMode != 0 && players.GetTaskStage(Quest.QuestID) != 255)
                    players.HeThongNhacNho("Status: QID: " + Quest.QuestID + " -> " +
                                           players.GetTaskStage(Quest.QuestID) + "/" + Quest.QuestLevel + " - Rate: " +
                                           rateDrop5 + "/" + Quest.DropRatePercent);
                if (players.GetTaskStage(Quest.QuestID) == Quest.QuestLevel &&
                    !players.ObtainQuestItems(Quest.QuestDropID, Quest.QuestItemMax) &&
                    rateDrop5 <= Quest.DropRatePercent) players.SetUpQuestItems(Quest.QuestDropID, 1);
            }

            if (Quest.MonsterID2 == 6996 && players.Player_Level - Level < World.LayDuoc_KinhNghiem_CapDo_ChenhLech)
            {
                var rateDrop = new Random(World.GetRandomSeed()).Next(0, (int)(8000.0 * per));
                if (players.GMMode != 0 && players.GetTaskStage(Quest.QuestID) != 255)
                    players.HeThongNhacNho("Status: QID: " + Quest.QuestID + " -> " +
                                           players.GetTaskStage(Quest.QuestID) + "/" + Quest.QuestLevel + " - Rate: " +
                                           rateDrop + "/" + Quest.DropRatePercent);
                if (players.GetTaskStage(Quest.QuestID) == Quest.QuestLevel &&
                    !players.ObtainQuestItems(Quest.QuestDropID2, Quest.QuestItemMax2) &&
                    rateDrop <= Quest.DropRatePercent) players.SetUpQuestItems(Quest.QuestDropID2, 1);
            }
        }
    }

    public void DropQuest(int userWorldId)
    {
        var players = World.KiemTra_PlayerWorld_ID(userWorldId);
        if (players == null) return;
        if (players.TeamID != 0 && World.WToDoi.TryGetValue(players.TeamID, out var class8))
        {
            foreach (var players2 in class8.ToDoi_NguoiChoi.Values)
                if (players.FindPlayers(300, players2))
                {
                    if (class8.ToDoi_NguoiChoi.Count >= 2 && class8.ToDoi_NguoiChoi.Count <= 8)
                        CheckQuestDrop(players2, class8.ToDoi_NguoiChoi.Count / 2);
                    else
                        CheckQuestDrop(players2);
                }

            return;
        }

        CheckQuestDrop(players);
    }

    public void Play_Add(Players payer, int SoLuongHP)
    {
        if (SoLuongHP < 0 || IsNpc == 1) return;
        using (new Lock(AsyncLocksw, "Play_Add"))
        {
            if (FLD_PID == World.WorldBoss_BossID && World.WorldBossEvent != null && World.WorldBoss_Process >= 2)
                World.ThietLap_WorldBoss_SatThuong(payer, SoLuongHP);
            if (FLD_BOSS == 0 && payer.CharacterFullServerID == MeegoDebuff_DVB_CasterSessionID &&
                TrangThai_BatThuong_ThanNu.ContainsKey(44)) MeegoDebuff_DVB_DamageCount += SoLuongHP;
            foreach (var playGj in PlayGj)
                if (playGj.PlayID == payer.CharacterFullServerID)
                {
                    var gjsl = playGj.Gjsl + 1;
                    playGj.Gjsl = gjsl;
                    playGj.Gjxl += SoLuongHP;
                    if (playGj.Gjxl >= Max_Rxjh_HP) playGj.Gjxl = Max_Rxjh_HP;
                    return;
                }

            var playGjClass = new PlayGjClass();
            playGjClass.Gjsl = 1;
            playGjClass.Gjxl = SoLuongHP;
            if (playGjClass.Gjxl >= Max_Rxjh_HP) playGjClass.Gjxl = Max_Rxjh_HP;
            playGjClass.PlayID = payer.CharacterFullServerID;
            PlayGj.Add(playGjClass);
            if (PlayGj[0].PlayID == payer.CharacterFullServerID && playGjClass.Gjxl > 0)
                GuiDi_DiDongSoLieu(payer.NhanVatToaDo_X, payer.NhanVatToaDo_Y, 10, 2);
        }
    }

    public void Play_null()
    {
        if (PlayGj != null && PlayGj.Count > 0) PlayGj.Clear();
    }

    public void GetRangePlayersSendIncreaseQuantityPackage()
    {
        try
        {
            foreach (var value in World.allConnectedChars.Values)
                if (FindPlayers(400, value))
                    value.GetReviewScopeNpc();
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "GetARangeOfPlayersSendGround增加NpcSoLieuPackage   error：" + ex);
        }
    }

    public void GetRangePlayersSendIncreaseQuantityPackage_OK()
    {
        try
        {
            var enumerator = World.allConnectedChars.Values.GetEnumerator();
            try
            {
                Players players = null;
                while (enumerator.MoveNext())
                {
                    players = enumerator.Current;
                    if (FindPlayers(400, players)) players.GetReviewScopeNpc();
                }
            }
            finally
            {
                var num2 = 2;
                while (true)
                {
                    switch (num2)
                    {
                        case 1:
                            break;
                        case 0:
                            enumerator.Dispose();
                            num2 = 1;
                            continue;
                        default:
                            if (enumerator != null)
                            {
                                num2 = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1,
                "Thu hoạch phạm vi người chơi gửi đi mặt đất gia tăng Npc Số liệu bao Phạm sai lầm: " + ex);
        }
    }

    public void 获取范围玩家发送消失数据包()
    {
        try
        {
            foreach (var value in _PlayList.Values)
                if (value.Client != null)
                    value.GetReviewScopeNpc();
            if (_PlayList != null) _PlayList.Clear();
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "NPC Thu hoạch phạm vi người chơi gửi đi biến mất số liệu bao 3 Phạm sai lầm: " + ex);
        }
    }

    ~NpcClass()
    {
    }

    public bool ContainsKeyInAbnormalState(int Key)
    {
        X_Di_Thuong_Trang_Thai_Loai value;
        if (TrangThai_BatThuong != null && TrangThai_BatThuong.Count != 0)
            return TrangThai_BatThuong.TryGetValue(Key, out value);
        return false;
    }

    public void getbl()
    {
        if (_PlayList.Count > 0)
        {
            Form1.WriteLine(2, Name + " Nhân vật: " + _PlayList.Count);
            if (AutomaticMove != null)
                Form1.WriteLine(2, Name + " Nhân vật _ Tự động di động: " + AutomaticMove.Enabled);
            if (AutomaticAttack != null)
                Form1.WriteLine(2, Name + " Nhân vật _ Tự động công kích: " + AutomaticAttack.Enabled);
            if (TuDongHoiSinh != null)
                Form1.WriteLine(2, Name + " Nhân vật _ Tự động phục sinh: " + TuDongHoiSinh.Enabled);
        }

        if (PlayGj.Count > 0) Form1.WriteLine(2, Name + " Công kích: " + PlayGj.Count);
        if (PlayCw != null) Form1.WriteLine(2, Name + " Linh thú: " + PlayCw.Name + "Chủ nhân tên: " + PlayCw.ZrName);
    }

    public int ThuDuocKinhNghiem()
    {
        var num = Rxjh_Exp * World.KinhNghiem_BoiSo;
        if (World.GioiHanKinhNghiem_BatTat != 0)
        {
            var CheckTime = World.GioiHanKinhNghiem_ThoiGian.AddMinutes(World.GioiHanKinhNghiem_ThoiGianKeoDai);
            if (DateTime.Now > World.GioiHanKinhNghiem_ThoiGian && DateTime.Now < CheckTime)
                num = (int)((1.0 - World.GioiHanKinhNghiem_GiamDiTyLe / 100.0) * num);
        }

        return num / 3;
    }

    public int ThuDuocTien()
    {
        var num = Rxjh_Gold * World.Tien_BoiSo;
        return num / 3;
    }

    public int ThuDuocLichLuyen()
    {
        try
        {
            var num2 = Rxjh_Exp * World.LichLuyenBoiSo / Level / 2;
            return num2 / 3;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Thu hoạch được lịch luyện Sai lầm" + ex.Message);
            return 0;
        }
    }

    public int ThuDuocThangThienLichLuyen()
    {
        try
        {
            var num2 = (int)(Rxjh_Exp * World.ThangThien_LichLuyen_BoiSo);
            return num2 / 3;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Thu hoạch được thăng thiên lịch luyện Sai lầm" + ex.Message);
            return 0;
        }
    }

    private void AutomaticMoveEvent(object sender, ElapsedEventArgs e)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "AutomaticMoveEvent");
        try
        {
            if (IsNpc != 0)
            {
                AutomaticMove.Enabled = false;
            }
            else
            {
                if (_PlayList.Count < 1 || NPCDeath) return;
                if (IsNpc != 1 && FLD_AT > 0.0)
                {
                    AutomaticMove.Interval = new Random(DateTime.Now.Millisecond).Next(3000, 15000);
                    if (FLD_AUTO == 1 && GetRangePlayers())
                    {
                        AutomaticMove.Enabled = false;
                        AutomaticAttack.Enabled = true;
                    }
                    else if (FLD_PID != 16431 && FLD_PID != 16430 && FLD_PID != 16435)
                    {
                        GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, FLD_PID != 5 ? 50 : 20, 1);
                    }
                    else
                    {
                        AutomaticMove.Enabled = false;
                        AutomaticAttack.Enabled = false;
                    }

                    return;
                }

                AutomaticMove.Interval = 20000.0;
                foreach (var value in _PlayList.Values)
                    if (value.Client != null)
                    {
                        if (!World.allConnectedChars.ContainsKey(value.Client.WorldId)) arrayList_0.Add(value);
                    }
                    else
                    {
                        arrayList_0.Add(value);
                    }

                foreach (Players item in arrayList_0)
                {
                    Form1.WriteLine(2, "NPCQuangBaSoLieu 删除卡号人物：[" + item.Userid + "] [" + item.UserName + "]");
                    item.Client?.Dispose();
                    _PlayList.Remove(item.CharacterFullServerID);
                }

                arrayList_0.Clear();
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "AutomaticMoveEvent error：" + ex);
        }
    }

    private void TuDongHoiSinhEvent(object sender, ElapsedEventArgs e)
    {
        try
        {
            if (IsNpc == 1)
            {
                TuDongHoiSinh.Enabled = false;
                return;
            }

            AutomaticMove.Enabled = true;
            if (NPCDeath) DoiMoi_NPC_HoiSinhSoLieu();
        }
        catch (Exception ex)
        {
            if (TuDongHoiSinh != null)
            {
                TuDongHoiSinh.Enabled = false;
                TuDongHoiSinh.Close();
                TuDongHoiSinh.Dispose();
                TuDongHoiSinh = null;
            }

            Form1.WriteLine(1, "TuDongHoiSinhEvent1   error：" + ex);
        }
        finally
        {
            if (TuDongHoiSinh != null)
            {
                TuDongHoiSinh.Enabled = false;
                TuDongHoiSinh.Close();
                TuDongHoiSinh.Dispose();
                TuDongHoiSinh = null;
            }
        }
    }

    public void AutomaticRecoverEvent(object sender, ElapsedEventArgs e)
    {
        if (IsNpc == 1)
        {
            if (Rxjh_HP < Max_Rxjh_HP)
            {
                Rxjh_HP = Max_Rxjh_HP;
                var text = $"Index: {FLD_INDEX} -PID: {FLD_PID} -FLD_HP{Rxjh_HP}";
                logo.LogNPC_Recover(text);
            }
        }
        else if (AutomaticRecover != null)
        {
            AutomaticRecover.Enabled = false;
            AutomaticRecover.Close();
            AutomaticRecover.Dispose();
            AutomaticRecover = null;
        }
    }

    private void AutomaticAttackEvent(object sender, ElapsedEventArgs e)
    {
        var num23 = 28;
        try
        {
            if (IsNpc != 0)
            {
                AutomaticAttack.Enabled = false;
            }
            else if (Rxjh_HP < 0)
            {
                AutomaticAttack.Enabled = false;
            }
            else
            {
                if (FLD_AT <= 0.0) return;
                var num13 = (int)FLD_AT;
                var random = new Random(DateTime.Now.Millisecond);
                AutomaticAttack.Interval = random.Next(1000, 2000);
                var dmgnpc = random.Next(num13 - 8, num13 + 8);
                var tyledoben = random.Next(1, 100);
                if (_PlayList.TryGetValue(PlayerWid, out var value))
                {
                    if (value.NhanVat_HP > 0 && !value.PlayerTuVong && value.GMMode != 8 && value.ThuBay != 1)
                    {
                        if (tyledoben <= 25) value.giamDoBenTrangBi();
                        double phongngu = value.FLD_NhanVatCoBan_PhongNgu;
                        if (!value.KiemTraDocXaXuatDongTrangThai() && value.Player_Job == 12)
                        {
                            value.TriggerAttributePromotion = 0;
                            if (value.TuHao_ToiCuongHoaDiem >= RNG.Next(1, 100) &&
                                !value.AppendStatusList.ContainsKey(700291))
                            {
                                var ThemVaoTrangThaiClass65 = new X_Them_Vao_Trang_Thai_Loai(value, 10000, 700291, 0);
                                value.AppendStatusList.Add(700291, ThemVaoTrangThaiClass65);
                                value.StatusEffect(BitConverter.GetBytes(700291), 1, 10000);
                                value.UpdateMartialArtsAndStatus();
                                value.UpdateCharacterData(value);
                                value.ShowBigPrint(value.CharacterFullServerID, 1010);
                            }
                        }

                        if (value.AppendStatusList.ContainsKey(700291))
                        {
                            phongngu *= 1.15;
                            value.TriggerAttributePromotion = 1;
                        }

                        var defplayer = phongngu + value.TrangBiThemVao_DoiQuai_PhongNgu +
                                        value.DuocPham_ThemVao_DoiQuai_CongKich +
                                        value.HeavenPetThemVao_DoiQuai_PhongNgu +
                                        value.FLD_TrangBi_ThemVao_PvE_Defense + value.ThangThien_5_MaHonChiLuc +
                                        value.NhanVat_KhiCong_DefQuai;
                        var flag = false;
                        if (value.FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich > 0.0)
                            dmgnpc = (int)(dmgnpc * (1.0 - value.FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich));
                        if (value.TrungCapPhuHon_DiTinh != 0 && RNG.Next(1, 79) <= (double)value.TrungCapPhuHon_DiTinh)
                            value.ShowBigPrint(value.CharacterFullServerID, 405);
                        if (value.Player_Job == 3 && RNG.Next(1, 100) <= value.THUONG_ChuyenCongViThu)
                        {
                            value.ShowBigPrint(value.CharacterFullServerID, 130);
                            defplayer += dmgnpc * 0.2;
                        }

                        if (value.Player_Job == 12 && RNG.Next(1, 100) <= value.TuHao_ChuyenCongViThu)
                        {
                            value.ShowBigPrint(value.CharacterFullServerID, 130);
                            defplayer += dmgnpc * 0.2;
                        }

                        if (value.Player_Job == 10)
                        {
                            if (RNG.Next(1, 100) <= value.QuyenSu_ChuyenCongViThu)
                            {
                                value.ShowBigPrint(value.CharacterFullServerID, 130);
                                defplayer += dmgnpc * 0.2;
                            }

                            if (num23 == 29 && RNG.Next(1, 100) <= value.QuyenSu_KimCuongBatHoai)
                            {
                                value.ShowBigPrint(value.CharacterFullServerID, 554);
                                dmgnpc = (int)(dmgnpc * 0.1);
                            }

                            if (RNG.Next(1, 100) <= value.ThangThien_5_BatTu_ChiKhu)
                            {
                                value.ShowBigPrint(value.CharacterFullServerID, 1021);
                                dmgnpc = 0;
                            }
                        }

                        if (value.TrungCapPhuHon_HoThe != 0 && RNG.Next(1, 50) <= value.TrungCapPhuHon_HoThe)
                        {
                            value.ShowBigPrint(value.CharacterFullServerID, 406);
                            value.NhanVat_MP += dmgnpc;
                            value.CapNhat_HP_MP_SP();
                            dmgnpc = 0;
                        }

                        var RandumDame = new Random().Next(1, 10);
                        var num14 = !(dmgnpc > defplayer) ? RandumDame : dmgnpc - (int)defplayer;
                        var num21 = num14;
                        if (FindPlayers(20, value))
                        {
                            if (value.TrungCapPhuHon_HonNguyen != 0 &&
                                RNG.Next(1, 50) <= value.TrungCapPhuHon_HonNguyen)
                            {
                                value.ShowBigPrint(value.CharacterFullServerID, 407);
                                num21 = (int)(num21 * 0.5);
                            }

                            if (value.Player_Job == 2)
                            {
                                var num20 = num21;
                                if (RNG.Next(1, 100) <= value.KIEM_ThangThien_3_KhiCong_HoThan_CuongKhi)
                                {
                                    value.ShowBigPrint(value.CharacterFullServerID, 25);
                                    num21 = (int)(num20 * 0.5);
                                }

                                if (RNG.Next(1, 100) <= value.Kiem_BachBienThanHanh) num21 = 0;
                            }
                            else if (value.Player_Job == 6)
                            {
                                double check_rate = RNG.Next(1, 130);
                                if (check_rate <= value.NINJA_TamHoaTuDinh)
                                {
                                    value.NINJA_LienTieuDaiDa_SoLuong = value.NINJA_NguKhiXungTieu * num21;
                                    num21 = 0;
                                }

                                if (RNG.Next(1, 110) <= value.NINJA_ThangThien_1_KhiCong_ViemDoanDietQuyet)
                                {
                                    num21 = (int)(num21 * 0.7);
                                    value.ShowBigPrint(value.CharacterFullServerID, 370);
                                }

                                if (RNG.Next(1, 100) <= value.NINJA_ThangThien_2_KhiCong_CoLapVoY)
                                {
                                    value.AddBlood((int)(num21 * 0.2));
                                    value.ShowBigPrint(value.CharacterFullServerID, 371);
                                }
                            }
                            else if (value.Player_Job == 8)
                            {
                                if (RNG.Next(1, 100) <= value.HanBaoQuan_TruyCotHapNguyen)
                                {
                                    value.ShowBigPrint(value.CharacterFullServerID, 255);
                                    num21 = (int)(num21 * (1.0 - value.HanBaoQuan_TruyCotHapNguyen * 0.005));
                                }
                            }
                            else if (value.Player_Job == 9)
                            {
                                var num12 = num21;
                                if (RNG.Next(1, 100) <= value.DamHoaLien_HoThan_CuongKhi)
                                {
                                    value.ShowBigPrint(value.CharacterFullServerID, 25);
                                    num21 = (int)(num12 * 0.5);
                                }

                                if (RNG.Next(1, 100) <= value.DamHoaLien_HoiLieu_ThanPhap) num21 = 0;
                                if (RNG.Next(1, 100) <= value.DamHoaLien_ThangThien_1_BaVuongQuyDienGiap +
                                    value.ThangThien_5_KinhDaoHaiLang)
                                {
                                    value.ShowBigPrint(value.CharacterFullServerID, 700);
                                    num21 = 0;
                                }
                            }

                            if (value.AppendStatusList.ContainsKey(700700))
                                num21 = (int)(num21 * (1.0 - value.DamHoaLien_ThangThien_1_BaVuongQuyDienGiap / 100.0));
                            if (num21 <= 0) num21 = 0;
                            var num22 = 0;
                            if (value.Player_Job == 11 && value.MaiLieuChan_ChuongLucKichHoat > 0.0)
                            {
                                num22 = (int)(num21 * (value.MaiLieuChan_ChuongLucKichHoat * 0.02));
                                if (num22 > value.NhanVat_AP) num22 = value.NhanVat_AP;
                                value.NhanVat_AP -= num22;
                            }

                            var num19 = num21 - num22;
                            if (FLD_PID == World.WorldBoss_BossID)
                            {
                                var ranzz = RNG.Next(1, 100);
                                if (ranzz <= 20)
                                {
                                    num23 = 29;
                                    WorldBoss_GroupAttack(this);
                                }
                            }

                            GuiDi_CongKichSoLieu(num19, num23, value.CharacterFullServerID, num22);
                            if (num19 > 0)
                            {
                                if (!value.Player_VoDich) value.NhanVat_HP -= num19;
                            }
                            else
                            {
                                value.NhanVat_HP += num19;
                            }

                            if (value.Player_Job != 1 && value.Player_Job != 7)
                            {
                                if (value.Player_Job == 2)
                                {
                                    if (RNG.Next(1, 100) <= value.KIEM_ThangThien_3_KhiCong_HoaPhuongLamTrieu &&
                                        value.NhanVat_HP <= 0)
                                    {
                                        value.NhanVat_HP = 10;
                                        value.ShowBigPrint(value.CharacterFullServerID, 322);
                                    }

                                    if (num19 <= defplayer)
                                    {
                                        if (!value.NoKhi) value.NhanVat_SP++;
                                    }
                                    else if (!value.NoKhi)
                                    {
                                        value.NhanVat_SP += 2;
                                    }
                                }
                                else if (value.Player_Job == 3)
                                {
                                    if (value.THUONG_CuongThanHangThe != 0.0)
                                    {
                                        if (!value.NoKhi)
                                            value.NhanVat_SP +=
                                                (int)(3.0 + value.Player_Level * 2 * value.THUONG_CuongThanHangThe);
                                    }
                                    else if (num19 <= defplayer)
                                    {
                                        if (!value.NoKhi) value.NhanVat_SP++;
                                    }
                                    else if (!value.NoKhi)
                                    {
                                        value.NhanVat_SP += 2;
                                    }
                                }
                                else if (value.Player_Job == 6)
                                {
                                    if (value.NINJA_KinhKhaChiNo != 0.0)
                                        value.NhanVat_SP +=
                                            (int)(3.0 + value.Player_Level * 0.5 * 0.01 * value.NINJA_KinhKhaChiNo);
                                    else if (num19 <= defplayer)
                                        value.NhanVat_SP++;
                                    else
                                        value.NhanVat_SP += 2;
                                }
                                else if (value.Player_Job == 8)
                                {
                                    if (num19 <= defplayer)
                                    {
                                        if (!value.NoKhi) value.NhanVat_SP++;
                                    }
                                    else if (!value.NoKhi)
                                    {
                                        value.NhanVat_SP += 2;
                                    }

                                    try
                                    {
                                    }
                                    catch (Exception ex)
                                    {
                                        Form1.WriteLine(1,
                                            "Tự động công kích sự kiện Hàn Phi quan truy xương hút nguyên phản tổn thương Phạm sai lầm: " +
                                            ex);
                                    }
                                }
                                else if (value.Player_Job == 9)
                                {
                                    if (RNG.Next(1, 100) <= value.DamHoaLien_ThangThien_3_KhiCong_HoaPhuongLamTrieu &&
                                        value.NhanVat_HP <= 0)
                                    {
                                        value.NhanVat_HP = 10;
                                        value.ShowBigPrint(value.CharacterFullServerID, 322);
                                    }

                                    if (num19 <= defplayer)
                                    {
                                        if (!value.NoKhi) value.NhanVat_SP++;
                                    }
                                    else if (!value.NoKhi)
                                    {
                                        value.NhanVat_SP += 2;
                                    }
                                }
                                else if (value.Player_Job == 10)
                                {
                                    if (value.QuyenSu_CuongThanHangThe != 0.0)
                                    {
                                        if (!value.NoKhi)
                                            value.NhanVat_SP += (int)(3.0 + value.Player_Level * 2 *
                                                value.QuyenSu_CuongThanHangThe);
                                    }
                                    else if (num19 <= defplayer)
                                    {
                                        if (!value.NoKhi) value.NhanVat_SP++;
                                    }
                                    else if (!value.NoKhi)
                                    {
                                        value.NhanVat_SP += 2;
                                    }
                                }
                                else if (value.Player_Job == 11)
                                {
                                    if (num19 <= defplayer)
                                    {
                                        if (!value.NoKhi) value.NhanVat_SP++;
                                    }
                                    else if (!value.NoKhi)
                                    {
                                        value.NhanVat_SP += 2;
                                    }

                                    if (value.NhanVat_AP * 2 <= value.Player_Shield_Max &&
                                        RNG.Next(1, 100) <= value.MaiLieuChan_ChuongLucKhoiPhuc)
                                    {
                                        value.NhanVat_AP += value.Player_Shield_Max / 2;
                                        value.ShowBigPrint(value.CharacterFullServerID, 801);
                                    }

                                    if (value.MaiLieuChan_PhanNoBaoPhat > 0.0 && RNG.Next(1, 100) <= 30 &&
                                        value.NoKhi_Point < 3)
                                    {
                                        var players = value;
                                        players.NoKhi_Point++;
                                    }
                                }
                                else if (num19 <= defplayer)
                                {
                                    if (!value.NoKhi) value.NhanVat_SP++;
                                }
                                else if (!value.NoKhi)
                                {
                                    value.NhanVat_SP += 2;
                                }
                            }
                            else
                            {
                                if (num19 <= defplayer)
                                {
                                    if (!value.NoKhi) value.NhanVat_SP++;
                                }
                                else if (!value.NoKhi)
                                {
                                    value.NhanVat_SP += 2;
                                }

                                try
                                {
                                    double randum = RNG.Next(1, 100);
                                    if (randum <= (value.Player_Job != 1
                                            ? value.CAMSU_ThangThien_2_KhiCong_TamDamAnhNguyet + 10.0
                                            : value.QuaiVat_PhanSatThuong_TiLe + 10.0) && num19 > 0 &&
                                        !value.Auto_Offline)
                                    {
                                        if (value.Player_Job == 7)
                                        {
                                            value.ShowBigPrint(value.CharacterFullServerID, 391);
                                        }
                                        else
                                        {
                                            double ran = RNG.Next(1, 100);
                                            if (ran < value.DAO_ThangThien_2_KhiCong_CungDoMatLo)
                                            {
                                                num19 *= 2;
                                                value.ShowBigPrint(value.CharacterFullServerID, 19);
                                            }

                                            GuiDi_PhanSatThuong_CongKichSoLieu(num19, value.CharacterFullServerID);
                                        }

                                        if (num19 <= 0) num19 = 0;
                                        if (Rxjh_HP > num19)
                                        {
                                            Play_Add(value, num19);
                                            Rxjh_HP -= num19;
                                        }
                                        else
                                        {
                                            Play_Add(value, Rxjh_HP);
                                            Rxjh_HP = 0;
                                        }

                                        if (Rxjh_HP <= 0 && !NPCDeath)
                                        {
                                            flag = true;
                                            double num24 = (uint)ThuDuocTien();
                                            double num10 = ThuDuocKinhNghiem();
                                            double num11 = ThuDuocLichLuyen();
                                            if (value.TrungCapPhuHon_KyDuyen != 0 &&
                                                RNG.Next(1, 100) <= value.TrungCapPhuHon_KyDuyen)
                                            {
                                                num10 *= 2.0;
                                                value.ShowBigPrint(value.CharacterFullServerID, 403);
                                            }

                                            value.PhanPhoiKinhNghiemLichLuyenTienTai(this, num10, num11, num24, 0.0);
                                        }
                                    }
                                }
                                catch (Exception ex2)
                                {
                                    Form1.WriteLine(1,
                                        "Tự động công kích sự kiện Nhạc công / Đao khách phản tổn thương Phạm sai lầm: " +
                                        ex2);
                                }
                            }

                            if (value.FLD_TrangBi_ThemVao_Phan_NoKhi > 0 && !value.NoKhi)
                                value.NhanVat_SP += value.FLD_TrangBi_ThemVao_Phan_NoKhi;
                            if (value.FLD_TrangBi_ThemVao_TrungDocXacSuatTiLePhanTram > 0.0 &&
                                RNG.Next(1, 100) <= value.FLD_TrangBi_ThemVao_TrungDocXacSuatTiLePhanTram &&
                                !ContainsKeyInAbnormalState(3))
                                TrangThai_BatThuong.Add(3,
                                    new X_Di_Thuong_Trang_Thai_Loai(this, PlayerWid, 60000, 3, 0.0));
                            if (value.NhanVat_HP <= 0)
                            {
                                if (World.ChetCoMatKinhNghiemKhong == 1 && value.NhanVatToaDo_BanDo != 801 &&
                                    value.Player_Level >= 65)
                                {
                                    var num16 = ((long)World.Level[value.Player_Level + 1] -
                                                 (long)World.Level[value.Player_Level]) / 1000;
                                    if (value.PublicDrugs != null)
                                    {
                                        if (value.KiemTra_Phu() || value.KiemTra_Phu2()) num16 = num16;
                                    }
                                    else
                                    {
                                        var num18 = RNG.Next(1, 100);
                                        var num15 = num18 >= 1 && num18 <= 10 ? num16 :
                                            num18 >= 11 && num18 <= 20 ? num16 * 2 :
                                            num18 >= 21 && num18 <= 30 ? num16 * 3 :
                                            num18 >= 31 && num18 <= 40 ? num16 * 4 :
                                            num18 >= 41 && num18 <= 50 ? num16 * 5 :
                                            num18 >= 51 && num18 <= 60 ? num16 * 6 :
                                            num18 >= 61 && num18 <= 70 ? num16 * 7 :
                                            num18 >= 71 && num18 <= 80 ? num16 * 8 :
                                            num18 < 81 || num18 > 90 ? num16 * 10 : num16 * 9;
                                        num16 = num15;
                                    }

                                    if (value.FLD_TrangBi_ThemVao_TuVong_TonThat_KinhNghiem_GiamBot > 0.0)
                                    {
                                        num16 = (long)(num16 *
                                                       (1.0 - value
                                                           .FLD_TrangBi_ThemVao_TuVong_TonThat_KinhNghiem_GiamBot));
                                        if (num16 < 0) num16 = 0L;
                                    }

                                    for (var num17 = 0; num17 < 15; num17++)
                                        if (Buffer.ToInt32(value.Item_Wear[num17].VatPham_ID, 0) ==
                                            700004)
                                        {
                                            num16 = 0L;
                                            value.Item_Wear[num17].VatPham_byte =
                                                new byte[World.Item_Db_Byte_Length];
                                            value.LoadCharacterWearItem();
                                            break;
                                        }

                                    if (value.GetAddState(1008000160) || value.GetAddState(1008000159)) num16 = 0L;
                                    value.CharacterExperience -= num16;
                                    value.TinhToan_NhanVatCoBan_DuLieu3();
                                    value.UpdateKinhNghiemVaTraiNghiem();
                                }

                                AutomaticAttack.Enabled = false;
                                AutomaticMove.Enabled = true;
                                Rxjh_X = Rxjh_cs_X;
                                Rxjh_Y = Rxjh_cs_Y;
                                Rxjh_Z = Rxjh_cs_Z;
                                GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
                                value.NhanVat_HP = 0;
                                value.PKTuVong = false;
                                value.Death();
                                PlayCw = null;
                                Play_null();
                            }

                            if (flag) GuiDiTuVongSoLieu(value.CharacterFullServerID);
                            value.CapNhat_HP_MP_SP();
                        }
                        else if (FindPlayers(80, value))
                        {
                            float x = value.NhanVatToaDo_X;
                            float y = value.NhanVatToaDo_Y;
                            double dx = x - Rxjh_X;
                            double dy = y - Rxjh_Y;
                            double distance = Math.Sqrt(dx * dx + dy * dy);

                            if (distance <= 500)
                            {
                                GuiDi_DiDongSoLieu(x, y, 10, 2);
                            }
                            else
                            {
                                // Player đã quá xa khỏi vùng hoạt động của NPC
                                PlayCw = null;
                                Play_null();
                                AutomaticAttack.Enabled = false;
                                AutomaticMove.Enabled = true;
                                Rxjh_X = Rxjh_cs_X;
                                Rxjh_Y = Rxjh_cs_Y;
                                Rxjh_Z = Rxjh_cs_Z;
                                GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
                            }
                        }
                        else
                        {
                            PlayCw = null;
                            Play_null();
                            AutomaticAttack.Enabled = false;
                            AutomaticMove.Enabled = true;
                            Rxjh_X = Rxjh_cs_X;
                            Rxjh_Y = Rxjh_cs_Y;
                            Rxjh_Z = Rxjh_cs_Z;
                            GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
                        }
                    }
                    else
                    {
                        if (value.AutomaticRecovery != null)
                        {
                            value.AutomaticRecovery.Enabled = false;
                            value.AutomaticRecovery.Close();
                            value.AutomaticRecovery.Dispose();
                            value.AutomaticRecovery = null;
                        }

                        if (value.InvincibleTimeCounter != null)
                        {
                            value.InvincibleTimeCounter.Enabled = false;
                            value.InvincibleTimeCounter.Close();
                            value.InvincibleTimeCounter.Dispose();
                        }

                        PlayCw = null;
                        Play_null();
                        AutomaticAttack.Enabled = false;
                        AutomaticMove.Enabled = true;
                        Rxjh_X = Rxjh_cs_X;
                        Rxjh_Y = Rxjh_cs_Y;
                        Rxjh_Z = Rxjh_cs_Z;
                        GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
                    }
                }
                else
                {
                    PlayCw = null;
                    Play_null();
                    AutomaticAttack.Enabled = false;
                    AutomaticMove.Enabled = true;
                    Rxjh_X = Rxjh_cs_X;
                    Rxjh_Y = Rxjh_cs_Y;
                    Rxjh_Z = Rxjh_cs_Z;
                    GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
                }
            }
        }
        catch (Exception)
        {
            PlayCw = null;
            Play_null();
            AutomaticAttack.Enabled = false;
            AutomaticMove.Enabled = true;
            Rxjh_X = Rxjh_cs_X;
            Rxjh_Y = Rxjh_cs_Y;
            Rxjh_Z = Rxjh_cs_Z;
            GuiDi_DiDongSoLieu(Rxjh_cs_X, Rxjh_cs_Y, 50, 1);
        }
    }

    public List<NpcClass> DanhNhieuMucTieu_TraTimPhamVi_Npc2(Players player, int SoLuong, int phamvi = 25)
    {
        try
        {
            var list = new List<NpcClass>();
            var num = 0;
            foreach (var value in player.NpcList.Values)
                if (!value.NPCDeath && value.IsNpc == 0 && LookInNpc(phamvi, value) && value.FLD_INDEX != FLD_INDEX)
                {
                    list.Add(value);
                    if (num >= SoLuong) break;
                    num++;
                }

            return list;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "DanhNhieuMucTieuNpc   error：" + ex);
            return null;
        }
    }

    public static void UpdateNPCSoLieu(Dictionary<int, NpcClass> NpcList, Players Playe)
    {
        if (NpcList == null || NpcList.Count <= 0) return;
        using var SendingClass = new SendingClass();
        SendingClass.Write4(NpcList.Count);
        foreach (var value in NpcList.Values)
        {
            SendingClass.Write4(value.FLD_INDEX);
            SendingClass.Write4(value.FLD_INDEX);
            SendingClass.Write2(value.FLD_PID);
            SendingClass.Write2(1);
            SendingClass.Write4(value.Rxjh_HP);
            SendingClass.Write4(value.Max_Rxjh_HP);
            SendingClass.Write(value.Rxjh_X);
            SendingClass.Write(value.Rxjh_Z);
            SendingClass.Write(value.Rxjh_Y);
            SendingClass.Write4(1082130432);
            SendingClass.Write(value.FLD_FACE1);
            SendingClass.Write(value.FLD_FACE2);
            SendingClass.Write(value.Rxjh_X);
            SendingClass.Write(value.Rxjh_Z);
            SendingClass.Write(value.Rxjh_Y);
            SendingClass.Write4(0);
            SendingClass.Write4(value.FLD_BOSS >= 1 ? 1 : 0);
            SendingClass.Write4(12);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(uint.MaxValue);
            if (value.NPCDeath)
            {
                value.Cap_nhat_NPC_de_xoa_du_lieu(Playe);
                value.LamMoi_NPCDeathSoLieu(Playe);
            }
        }

        Playe.Client?.SendPak(SendingClass, 26368, Playe.CharacterFullServerID);
    }

    public static void UpdateNPC_DeXoaSoLieu(Dictionary<int, NpcClass> NpcList, Players Playe)
    {
        if (NpcList == null || NpcList.Count <= 0) return;
        using var packetDataClass = new SendingClass();
        packetDataClass.Write4(NpcList.Count);
        foreach (var value in NpcList.Values)
        {
            packetDataClass.Write4(value.FLD_INDEX);
            packetDataClass.Write4(value.FLD_INDEX);
            packetDataClass.Write2(value.FLD_PID);
            packetDataClass.Write4(1);
            packetDataClass.Write4((uint)value.Rxjh_HP);
            packetDataClass.Write4((uint)value.Max_Rxjh_HP);
            packetDataClass.Write(value.Rxjh_X);
            packetDataClass.Write(value.Rxjh_Z);
            packetDataClass.Write(value.Rxjh_Y);
            packetDataClass.Write4(1082130432);
            packetDataClass.Write(value.FLD_FACE1);
            packetDataClass.Write(value.FLD_FACE2);
            packetDataClass.Write(value.Rxjh_X);
            packetDataClass.Write(value.Rxjh_Z);
            packetDataClass.Write(value.Rxjh_Y);
            packetDataClass.Write4(0);
            packetDataClass.Write4(0);
            packetDataClass.Write4(12);
            packetDataClass.Write4(0);
            packetDataClass.Write4(2359296);
            packetDataClass.Write4(uint.MaxValue);
        }

        Playe.Client?.SendPak(packetDataClass, 26624, Playe.CharacterFullServerID);
    }

    public static void UpdateNPC_DeXoaSoLieu_Old(Dictionary<int, NpcClass> NpcList, Players Playe)
    {
        var num = 1;
        SendingClass 发包类 = null;
        if (NpcList == null) return;
        num = 3;
        num = 4;
        if (NpcList.Count <= 0) return;
        num = 2;
        发包类 = new SendingClass();
        num = 0;
        try
        {
            发包类.Write4(NpcList.Count);
            var enumerator = NpcList.Values.GetEnumerator();
            num = 1;
            try
            {
                num = 1;
                while (true)
                {
                    num = 0;
                    if (!enumerator.MoveNext()) break;
                    var current = enumerator.Current;
                    发包类.WriteInt(current.FLD_INDEX);
                    发包类.WriteInt(current.FLD_INDEX);
                    发包类.WriteInt(current.FLD_PID);
                    发包类.Write4(1);
                    发包类.Write4(current.Rxjh_HP);
                    发包类.Write4(current.Max_Rxjh_HP);
                    发包类.Write(current.Rxjh_X);
                    发包类.Write(current.Rxjh_Z);
                    发包类.Write(current.Rxjh_Y);
                    发包类.Write4(1082130432);
                    发包类.Write(current.FLD_FACE1);
                    发包类.Write(current.FLD_FACE2);
                    发包类.Write(current.Rxjh_X);
                    发包类.Write(current.Rxjh_Z);
                    发包类.Write(current.Rxjh_Y);
                    发包类.Write4(0);
                    发包类.Write4(0);
                    发包类.Write4(10);
                    发包类.Write4(0);
                    发包类.Write4(2359296);
                    发包类.Write4(uint.MaxValue);
                    num = 3;
                }

                num = 4;
                num = 2;
            }
            finally
            {
                enumerator.Dispose();
            }

            num = 0;
            if (Playe.Client != null)
            {
                num = 4;
                Playe.Client.SendPak(发包类, 26624, Playe.CharacterFullServerID);
                num = 3;
            }

            num = 2;
        }
        finally
        {
            num = 1;
            while (true)
            {
                switch (num)
                {
                    case 2:
                        break;
                    case 0:
                        ((IDisposable)发包类).Dispose();
                        num = 2;
                        continue;
                    default:
                        if (发包类 != null)
                        {
                            num = 0;
                            continue;
                        }

                        break;
                }

                break;
            }
        }
    }

    public void Cap_nhat_du_lieu_NPC(Players Playe)
    {
        var 发包类 = new SendingClass();
        var num = 0;
        try
        {
            发包类.Write4(1);
            发包类.WriteInt(FLD_INDEX);
            发包类.WriteInt(FLD_INDEX);
            发包类.WriteInt(FLD_PID);
            发包类.Write4(1);
            发包类.Write4(Rxjh_HP);
            发包类.Write4(Max_Rxjh_HP);
            发包类.Write(Rxjh_X);
            发包类.Write(Rxjh_Z);
            发包类.Write(Rxjh_Y);
            发包类.Write4(1082130432);
            发包类.Write(FLD_FACE1);
            发包类.Write(FLD_FACE2);
            发包类.Write(Rxjh_X);
            发包类.Write(Rxjh_Z);
            发包类.Write(Rxjh_Y);
            发包类.Write4(0);
            发包类.Write4(0);
            发包类.Write4(10);
            发包类.Write4(0);
            发包类.Write4(2359296);
            发包类.Write4(uint.MaxValue);
            num = 0;
            if (Playe.Client != null)
            {
                num = 2;
                Playe.Client.SendPak(发包类, 26368, FLD_INDEX);
                num = 3;
            }

            num = 1;
        }
        finally
        {
            num = 0;
            while (true)
            {
                switch (num)
                {
                    case 1:
                        break;
                    default:
                        if (发包类 != null)
                        {
                            num = 2;
                            continue;
                        }

                        break;
                    case 2:
                        ((IDisposable)发包类).Dispose();
                        num = 1;
                        continue;
                }

                break;
            }
        }

        num = 2;
        if (NPCDeath)
        {
            num = 3;
            Cap_nhat_NPC_de_xoa_du_lieu(Playe);
            LamMoi_NPCDeathSoLieu(Playe);
            num = 1;
        }
    }

    public void Cap_nhat_NPC_de_xoa_du_lieu(Players Playe)
    {
        using var SendingClass = new SendingClass();
        SendingClass.Write4(1);
        SendingClass.Write4(FLD_INDEX);
        SendingClass.Write4(FLD_INDEX);
        SendingClass.Write2(FLD_PID);
        SendingClass.Write2(1);
        SendingClass.Write4(Rxjh_HP);
        SendingClass.Write4(Max_Rxjh_HP);
        SendingClass.Write(Rxjh_X);
        SendingClass.Write(Rxjh_Z);
        SendingClass.Write(Rxjh_Y);
        SendingClass.Write4(1082130432);
        SendingClass.Write(FLD_FACE1);
        SendingClass.Write(FLD_FACE2);
        SendingClass.Write(Rxjh_X);
        SendingClass.Write(Rxjh_Z);
        SendingClass.Write(Rxjh_Y);
        SendingClass.Write4(0);
        SendingClass.Write4(0);
        SendingClass.Write4(12);
        SendingClass.Write4(0);
        SendingClass.Write4(0);
        SendingClass.Write4(uint.MaxValue);
        Playe.Client?.SendPak(SendingClass, 26624, Playe.CharacterFullServerID);
    }

    private void DoiMoi_NPC_HoiSinhSoLieu()
    {
        try
        {
            NPCDeath = false;
            Rxjh_HP = Max_Rxjh_HP;
            GetRangePlayersSendIncreaseQuantityPackage();
            if (FLD_PID != 15349 && FLD_PID != 15350)
            {
                if (FLD_PID != 15121 && FLD_PID != 15122)
                {
                    var random = new Random();
                    var num = random.Next(0, 2);
                    var num2 = random.NextDouble() * 50.0;
                    var num3 = random.NextDouble() * 50.0;
                    if (num == 0)
                    {
                        Rxjh_X = Rxjh_cs_X + (float)num2;
                        Rxjh_Y = Rxjh_cs_Y + (float)num3;
                    }
                    else
                    {
                        Rxjh_X = Rxjh_cs_X - (float)num2;
                        Rxjh_Y = Rxjh_cs_Y - (float)num3;
                    }
                }
                else
                {
                    Rxjh_X = Rxjh_cs_X;
                    Rxjh_Y = Rxjh_cs_Y;
                }
            }
            else
            {
                Rxjh_X = Rxjh_cs_X;
                Rxjh_Y = Rxjh_cs_Y;
            }

            Rxjh_Z = Rxjh_cs_Z;
            if (FLD_BOSS == 1)
            {
                X_Toa_Do_Class.getname(Rxjh_Map);
                foreach (var players in World.allConnectedChars.Values)
                    players.HeThongNhacNho(
                        "Boss vừa xuất hiện tại bản đồ: " + Rxjh_Map + " - toạ độ: (" + (int)Rxjh_X + "," +
                        (int)Rxjh_Y + ")", 8);
                logo.BossLog("Boss hồi sinh ID:" + FLD_PID + " Map" + Rxjh_Map);
            }

            

            if (TuDongHoiSinh != null)
            {
                TuDongHoiSinh.Enabled = false;
                TuDongHoiSinh?.Close();
                TuDongHoiSinh.Dispose();
                TuDongHoiSinh = null;
            }
        }
        catch (Exception ex)
        {
            if (TuDongHoiSinh != null)
            {
                TuDongHoiSinh?.Close();
                TuDongHoiSinh.Dispose();
                TuDongHoiSinh = null;
            }

            Form1.WriteLine(1, "DoiMoi_NPC_HoiSinhSoLieu   error" + FLD_PID + "|" + Name + "   " + ex.Message);
        }
        finally
        {
            var SendingClass = new SendingClass();
            SendingClass.Write4(1);
            SendingClass.Write4(FLD_INDEX);
            SendingClass.Write4(FLD_INDEX);
            SendingClass.Write2(FLD_PID);
            SendingClass.Write2(1);
            SendingClass.Write4(Rxjh_HP);
            SendingClass.Write4(Max_Rxjh_HP);
            SendingClass.Write(Rxjh_X);
            SendingClass.Write(Rxjh_Z);
            SendingClass.Write(Rxjh_Y);
            SendingClass.Write4(0);
            SendingClass.Write(FLD_FACE1);
            SendingClass.Write(FLD_FACE2);
            SendingClass.Write(Rxjh_X);
            SendingClass.Write(Rxjh_Z);
            SendingClass.Write(Rxjh_Y);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(12);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(uint.MaxValue);
            SendCurrentRangeBroadcastData(SendingClass, 31488, FLD_INDEX);
            if (TuDongHoiSinh != null)
            {
                TuDongHoiSinh?.Close();
                TuDongHoiSinh.Dispose();
                TuDongHoiSinh = null;
            }
        }
    }

    private void LamMoi_NPCDeathSoLieu(Players Playe)
    {
        using var pak = new SendingClass();
        Playe.Client?.SendPak(pak, 34816, FLD_INDEX);
    }

    public void QuangBa_NPCDeathSoLieu()
    {
        using var pak = new SendingClass();
        SendCurrentRangeBroadcastData(pak, 34816, FLD_INDEX);
    }

    public void GuiDi_DiDongSoLieu(float X, float Y, int sl, int DiDong_PhuongThuc)
    {
        try
        {
            using var SendingClass = new SendingClass();
            var random = new Random(DateTime.Now.Millisecond);
            var num = RNG.Next(0, 1);
            var num2 = random.NextDouble() * sl;
            var num3 = random.NextDouble() * sl;
            if (num == 0)
            {
                Rxjh_X = X + (float)num2;
                Rxjh_Y = Y + (float)num3;
            }
            else
            {
                Rxjh_X = X - (float)num2;
                Rxjh_Y = Y - (float)num3;
            }

            SendingClass.Write(Rxjh_X);
            SendingClass.Write(Rxjh_Y);
            SendingClass.Write(Rxjh_Z);
            SendingClass.Write4(-1);
            if (FLD_PID == 5)
                SendingClass.Write4(0);
            else
                SendingClass.Write4(RNG.Next(0, 2));
            SendingClass.Write((float)num2);
            SendingClass.Write4(Rxjh_HP);
            SendingClass.Write(Rxjh_X);
            SendingClass.Write(Rxjh_Z);
            SendingClass.Write(Rxjh_Y);
            SendingClass.Write4(0);
            SendingClass.WriteInt(0);
            SendCurrentRangeBroadcastData(SendingClass, 29696, FLD_INDEX);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "GuiDi_DiDongSoLieu   error" + FLD_PID + "|" + Name + "   " + ex.Message);
        }
    }

    public void SendAttackSkillNPC(int CharacterFullServerID, int VoCong_ID, int CongKichLuc, int CongKichLoaiHinh,
        int KhoiPhuc_LaChan)
    {
        try
        {
            if (!World.TBL_KONGFU.TryGetValue(VoCong_ID, out var value)) return;
            using var SendingClass = new SendingClass();
            SendingClass.WriteInt(CharacterFullServerID);
            SendingClass.WriteInt(1);
            SendingClass.WriteInt(VoCong_ID);
            SendingClass.WriteInt(0);
            SendingClass.Write4(CongKichLuc);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(VoCong_ID);
            SendingClass.Write4(value.FLD_EFFERT);
            SendingClass.Write(Rxjh_X);
            SendingClass.Write(15f);
            SendingClass.Write(Rxjh_Y);
            SendingClass.Write(0);
            SendingClass.Write(1);
            SendingClass.WriteInt(0);
            SendingClass.Write4(KhoiPhuc_LaChan);
            if (CongKichLuc < 1)
                SendingClass.Write4(1);
            else
                SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(-1);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendingClass.Write4(0);
            SendCurrentRangeBroadcastData(SendingClass, 2560, FLD_INDEX);
        }
        catch (Exception ex)
        {
            var obj = new string[6]
            {
                "GuiDi_CongKichSoLieu   error",
                FLD_PID.ToString(),
                "|",
                Name,
                "   ",
                null
            };
            obj[5] = ex?.ToString();
            Form1.WriteLine(1, string.Concat(obj));
        }
    }

    public void WorldBoss_GroupAttack(NpcClass Npc)
    {
        if (Npc._PlayList.Count <= 1) return;
        foreach (var value in Npc._PlayList.Values)
        {
            var type = 0;
            var hieuung = 0;
            var thoigian = 0;
            var dmg = 0;
            var rate = RNG.Next(1, 100);
            if (rate <= 10)
            {
                type = 1;
                hieuung = 10;
                thoigian = 1000;
                dmg = value.CharacterMax_HP + 5000;
            }
            else if (rate <= 40)
            {
                type = 2;
                hieuung = 26;
                thoigian = 10000;
            }
            else if (rate <= 70)
            {
                type = 3;
                hieuung = 31;
                thoigian = 10000;
            }
            else if (rate <= 100)
            {
                type = 4;
                hieuung = 13;
                thoigian = 20000;
            }

            if (type != 0)
            {
                var TrangThai_BatThuongClass9 = new X_Di_Thuong_Trang_Thai_Loai(value, thoigian, hieuung, dmg);
                value.TrangThai_BatThuong.Add(hieuung, TrangThai_BatThuongClass9);
                switch (type)
                {
                    case 1:
                        TrangThai_BatThuongClass9.TrangThai_BatThuong_LoaiChayMau(dmg);
                        break;
                    case 4:
                        value.FLD_WorldBoss_GiamBotCongKich -= 0.5;
                        break;
                }

                value.UpdateMartialArtsAndStatus();
            }
        }
    }

    public void GuiDi_CongKichSoLieu(int CongKichLuc, int CongKichLoaiHinh, int CharacterFullServerID,
        int KhoiPhuc_LaChan)
    {
        try
        {
            using var sendingClass = new SendingClass();
            sendingClass.Write4(CharacterFullServerID);
            sendingClass.WriteInt(1);
            sendingClass.WriteInt(0);
            sendingClass.Write4(CongKichLuc);
            sendingClass.Write4(19356438);
            sendingClass.Write4(0);
            sendingClass.Write4(0);
            sendingClass.Write4(0);
            sendingClass.Write4(KhoiPhuc_LaChan);
            WriteMultipleZeroes(sendingClass, 5);
            sendingClass.Write4(CongKichLoaiHinh);
            sendingClass.Write(Rxjh_X);
            sendingClass.Write(15f);
            sendingClass.Write(Rxjh_Y);
            sendingClass.Write(0);
            sendingClass.Write(1);
            sendingClass.WriteInt(0);
            WriteMultipleZeroes(sendingClass, 15);
            sendingClass.Write4(-1);
            WriteMultipleZeroes(sendingClass, 3);
            WriteMultipleZeroes(sendingClass, 4);
            //Converter.ToString(sendingClass.ToArray3());
            SendCurrentRangeBroadcastData(sendingClass, 3072, FLD_INDEX);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, $"GuiDi_CongKichSoLieu error {FLD_PID}|{Name} {ex.Message}");
        }
    }

    private void WriteMultipleZeroes(SendingClass sendingClass, int count)
    {
        for (var i = 0; i < count; i++) sendingClass.Write4(0);
    }

    public void SendMultiplePacketsOfCurrentRangeBroadcastData(byte[] data, int length)
    {
        try
        {
            var queue = Queue.Synchronized(new Queue());
            foreach (var current in _PlayList.Values) queue.Enqueue(current);
            while (queue.Count > 0)
            {
                var current = (Players)queue.Dequeue();
                if (current.Client != null)
                {
                    if (current.Client.Running)
                    {
                        current.Client.Send_Map_Data(data, length);
                    }
                    else
                    {
                        current.Client.Dispose();
                        _PlayList.Remove(current.CharacterFullServerID);
                    }
                }

                if (!World.allConnectedChars.ContainsKey(current.Client.WorldId))
                {
                    Form1.WriteLine(2,
                        "NPC broadcast data delete card number character:[" + current.Userid + "] [" +
                        current.UserName + "]");
                    current.Client?.Dispose();
                    _PlayList.Remove(current.CharacterFullServerID);
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "NPC broadcast data 2 error 2：" + ex);
        }
    }

    public void SendCurrentRangeBroadcastData(SendingClass pak, int id, int wordid)
    {
        try
        {
            if (_PlayList == null) return;
            var playersToRemove = new List<Players>();
            lock (_lock)
            {
                foreach (var player2 in _PlayList.Values)
                {
                    if (player2.Client != null && player2.Client.Running)
                    {
                        player2.Client.SendPak(pak, id, wordid);
                        continue;
                    }

                    playersToRemove.Add(player2);
                    if (player2.Client != null)
                    {
                        Form1.WriteLine(2,
                            "NPCTransmit当前范围QuangBaSoLieu   删除卡号人物：[" + player2.Userid + "]   [" + player2.UserName +
                            "]  [" + (player2.Client == null ? "NULL" : "Not NULL") + "]   [" + player2.Client.Running +
                            "]");
                        player2.Client.Dispose();
                    }
                }

                foreach (var player in playersToRemove)
                {
                    _PlayList.Remove(player.CharacterFullServerID);
                    if (player.NpcList == null || player.NpcList.Count <= 0) continue;
                    foreach (var npc in player.NpcList.Values) npc._PlayList.Remove(player.CharacterFullServerID);
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "NPCQuangBaSoLieu   error3：" + ex);
        }
    }

    public void LamMoiTuVongSoLieu()
    {
        try
        {
            if (NPCDeath) return;
            NPCDeath = true;
            if (QuaiXuatHien_DuyNhatMotLan)
            {
                if (PlayCw != null) PlayCw = null;
                Play_null();
                QuangBa_NPCDeathSoLieu();
                Dispose();
                return;
            }

            if (AutomaticAttack != null) AutomaticAttack.Enabled = false;
            if (AutomaticMove != null) AutomaticMove.Enabled = false;
            if (TuDongHoiSinh != null)
            {
                TuDongHoiSinh.Interval = FLD_NEWTIME * 1000;
                TuDongHoiSinh.Enabled = true;
            }
            else
            {
                TuDongHoiSinh = new System.Timers.Timer(FLD_NEWTIME * 1000);
                TuDongHoiSinh.Elapsed += TuDongHoiSinhEvent;
                TuDongHoiSinh.Enabled = true;
            }

            if (PlayCw != null) PlayCw = null;
            Play_null();
            QuangBa_NPCDeathSoLieu();
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "LamMoiTuVongSoLieu   error" + FLD_PID + "|" + Name + "   " + ex.Message);
        }
    }

    public void Gui_du_lieu_thuoc()
    {
        if (IsNpc == 2)
        {
            if (TuDongHoiSinh != null)
            {
                TuDongHoiSinh.Interval = FLD_NEWTIME * 1000;
                TuDongHoiSinh.Enabled = true;
            }
            else
            {
                TuDongHoiSinh = new System.Timers.Timer(FLD_NEWTIME * 1000);
                TuDongHoiSinh.Elapsed += TuDongHoiSinhEvent;
                TuDongHoiSinh.Enabled = true;
            }

            PlayCw = null;
            Play_null();
            QuangBa_NPCDeathSoLieu();
        }
    }

    public void GuiDuLieu_TuVong_MotLanCuaQuaiVat()
    {
        try
        {
            AbnormalStatusList();
            EndAbnormalBloodDropStatusList();
            if (IsNpc != 1 && !NPCDeath)
            {
                if (PlayCw != null) PlayCw = null;
                Play_null();
                QuangBa_NPCDeathSoLieu();
                Dispose();
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Gửi đi tử vong số liệu Phạm sai lầm 111 " + FLD_PID + "|" + Name + " " + ex.Message);
        }
    }

    public void GuiDiTuVongSoLieu(int UserWorldId = 0)
    {
        var num = 0;
        if (!_PlayList.TryGetValue(UserWorldId, out var value)) return;
        try
        {
            HandlePlayerQuest(value);
        }
        catch (Exception ex2)
        {
            Form1.WriteLine(2,
                "NhiemVu_VatPham error QuaiVat_ID--" + FLD_PID + " Nhiệm vụ ID-" + num + "|" + ex2.Message);
        }

        if (UserWorldId != 0)
            try
            {
                DropQuest(UserWorldId);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(2, "DROPQUEST ERROR -- " + UserWorldId + " --" + ex.Message);
            }

        try
        {
            HandleAbnormalStatuses();
            if (IsNpc != 1 && !NPCDeath)
            {
                NPCDeath = true;
                HandleBossOrPlayer(value, UserWorldId);
                HandleThienMaThanCungEvents(UserWorldId);
                HandleWorldBossEvent(UserWorldId);
                NotifyBossDeath();
                HandleNpcDeath();
            }
        }
        catch (Exception ex3)
        {
            Form1.WriteLine(1, "GuiDiTuVongSoLieu error " + FLD_PID + "|" + Name + "   " + ex3.Message);
        }
    }

    private void HandlePlayerQuest(Players player)
    {
        foreach (var quest in player.NhiemVu.Values)
        foreach (var stage in quest.NhiemVu_GiaiDoan)
        {
            if (stage.GiaiDoan_TrangThai != 1 || stage.GiaiDoanCanVatPham_.Count <= 0) continue;
            var difficulty = 15;
            var questData = new X_Nhiem_Vu_Loai().GetRW(quest.RwID);
            foreach (var questStage in questData.NhiemVu_GiaiDoan)
                if (questStage.GiaiDoanID == stage.GiaiDoanID)
                {
                    difficulty = questStage.MucDo_KhoKhan;
                    stage.GiaiDoanCanVatPham_ = questStage.GiaiDoanCanVatPham_;
                    break;
                }

            if (quest.RwID == 45 && FLD_PID == 15062) player.SetUpQuestItems(900000099, 1);
            if (quest.RwID == 46 && FLD_PID == 15072) player.SetUpQuestItems(900000101, 1);
            foreach (var item in stage.GiaiDoanCanVatPham_.Values)
                if (item.QuaiVat_ID == FLD_PID && !player.CheckItem(item.VatPham_ID, item.TongSoVatPham) &&
                    RNG.Next(1, 100) >= difficulty)
                {
                    player.SetUpQuestItems(item.VatPham_ID, 1);
                    HandleTeamQuestItems(player, item);
                }
        }
    }

    private void HandleTeamQuestItems(Players player, X_Giai_Doan_Can_Vat_Pham_Loai item)
    {
        if (player.TeamID == 0 || player.TeamingStage != 2 ||
            !World.WToDoi.TryGetValue(player.TeamID, out var team)) return;
        foreach (var teammate in team.ToDoi_NguoiChoi.Values)
        {
            if (!FindPlayers(1000, teammate) || teammate.NhanVat_HP <= 0 || teammate.PlayerTuVong ||
                player.CharacterFullServerID == teammate.CharacterFullServerID) continue;
            foreach (var quest in teammate.NhiemVu.Values)
            foreach (var stage in quest.NhiemVu_GiaiDoan)
            {
                if (stage.GiaiDoanCanVatPham_.Count <= 0 || stage.GiaiDoan_TrangThai != 1) continue;
                foreach (var questStage in new X_Nhiem_Vu_Loai().GetRW(quest.RwID).NhiemVu_GiaiDoan)
                    if (questStage.GiaiDoanID == stage.GiaiDoanID)
                    {
                        stage.GiaiDoanCanVatPham_ = questStage.GiaiDoanCanVatPham_;
                        break;
                    }

                foreach (var questItem in stage.GiaiDoanCanVatPham_.Values)
                    if (questItem.QuaiVat_ID == FLD_PID &&
                        !teammate.CheckItem(questItem.VatPham_ID, questItem.TongSoVatPham))
                        teammate.SetUpQuestItems(questItem.VatPham_ID, 1);
            }
        }
    }

    private void HandleAbnormalStatuses()
    {
        AbnormalStatusList();
        EndAbnormalBloodDropStatusList();
    }

    private void HandleBossOrPlayer(Players player, int userWorldId)
    {
        if (FLD_BOSS == 1 && Rxjh_Map == 101)
        {
            var num = RNG.Next(1, 3);
            for (var i = 0; i < num; i++)
                if (World.allConnectedChars.TryGetValue(BossPlayerWid, out player) && FindPlayers(1000, player))
                    Boss_BaoSuat_VatPham(player);
        }
        else if (World.allConnectedChars.TryGetValue(PlayerWid, out player))
        {
            if (Rxjh_Map == 32002)
            {
                var Random = new Random(World.GetRandomSeed()).Next(0, 10000);
                var Success = 1000;
                if (FLD_PID == 15355) Success = 2000;
                if (Random <= Success) RoiRaVatPham_Custom(1000000348, player);
            }
            else if (player.Player_Level - Level < World.LayDuoc_KinhNghiem_CapDo_ChenhLech || FLD_BOSS != 0)
            {
                BaoSuat_VatPham(player);
            }
        }
        else
        {
            HandleFallbackPlayerLoot(userWorldId);
        }
    }

    private void HandleFallbackPlayerLoot(int userWorldId)
    {
        if (PlayCw != null && PlayCw.Playe != null &&
            PlayCw.Playe.Player_Level - Level < World.LayDuoc_KinhNghiem_CapDo_ChenhLech)
            BaoSuat_VatPham(PlayCw.Playe);
        else
            BaoSuat_VatPham(null);
    }

    private void HandleWorldBossEvent(int userWorldId)
    {
        if (World.WorldBossEvent != null && FLD_PID == World.WorldBoss_BossID &&
            World.allConnectedChars.TryGetValue(userWorldId, out var value) && World.WorldBoss_LastHitName == "" &&
            World.WorldBoss_BossStatus == 1)
        {
            World.WorldBoss_LastHitName = value.UserName;
            World.WorldBoss_BossStatus = -1;
        }
    }

    private void HandleThienMaThanCungEvents(int userWorldId)
    {
        foreach (var player in World.allConnectedChars.Values)
            if (World.allConnectedChars.TryGetValue(userWorldId, out var value))
                switch (FLD_PID)
                {
                    case 16430:
                        player.ThienMaThanCungCuaChinhThanhDaMoRa();
                        World.ThienMaThanCungDaiMon_PhaiChangTuVong = 1;
                        break;
                    case 16431:
                        player.ThienMaThanCungCuaThanhDong_DaMo();
                        World.ThienMaThanCungDaiMon_PhaiChangTuVong = 1;
                        break;
                    case 16435:
                        DBA.ExeSqlCommand("DELETE FROM CongThanhChien_ThanhChu");
                        value.ThienMaThanCungPhoTuongKichSat();
                        player.ThienMaThanCungCongThanhChienThangPacket(value);
                        player.ThienMaThanCungPhoTuongKichSat_DiDong(player);
                        World.ThienMaThanCungPhoTuong_PhaiChangTuVong = 1;
                        break;
                }
    }

    private void NotifyBossDeath()
    {
        if (FLD_BOSS != 1 || FLD_NEWTIME <= 60) return;
        var mapName = X_Toa_Do_Class.getname(Rxjh_Map);
        foreach (var player in World.allConnectedChars.Values)
            player.HeThongNhacNho("Boss vừa bị giết tại bản đồ: " + mapName, 8);
        logo.BossLog("Boss chết ID:" + FLD_PID + " Map" + Rxjh_Map);
    }

    private void HandleNpcDeath()
    {
        if (QuaiXuatHien_DuyNhatMotLan)
        {
            if (PlayCw != null) PlayCw = null;
            Play_null();
            QuangBa_NPCDeathSoLieu();
            Dispose();
            return;
        }

        if (AutomaticAttack != null) AutomaticAttack.Enabled = false;
        if (AutomaticMove != null) AutomaticMove.Enabled = true;
        HandleTheLucChienEvents();
        HandleAutoRespawn();
        PlayCw = null;
        Play_null();
        QuangBa_NPCDeathSoLieu();
    }

    private void HandleTheLucChienEvents()
    {
        if (World.TheLucChien_Progress == 3)
            switch (FLD_PID)
            {
                case 15491:
                    World.TheLucChien_TaPhai_DiemBOSS += 500;
                    break;
                case 15492:
                    World.TheLucChien_TaPhai_DiemBOSS += 100;
                    ApplyStatusEffectToPlayers(801, 2, 700500, 120000, 0.06, 0.06);
                    break;
                case 15493:
                    World.TheLucChien_ChinhPhai_DiemBOSS += 500;
                    break;
                case 15494:
                    World.TheLucChien_ChinhPhai_DiemBOSS += 100;
                    ApplyStatusEffectToPlayers(801, 1, 700501, 180000, 0.03, 0.03);
                    break;
            }
    }

    private void ApplyStatusEffectToPlayers(int mapId, int playerZx, int statusEffectId, int duration,
        double defIncrease, double attIncrease)
    {
        foreach (var player in World.allConnectedChars.Values)
            if (player.NhanVatToaDo_BanDo == mapId && player.Player_Zx == playerZx &&
                !player.AppendStatusList.ContainsKey(statusEffectId))
            {
                if (player.AppendStatusList.ContainsKey(statusEffectId))
                    player.AppendStatusList[statusEffectId].ThoiGianKetThucSuKien();
                player.AppendStatusList.Add(statusEffectId,
                    new X_Them_Vao_Trang_Thai_Loai(player, duration, statusEffectId, 0));
                player.StatusEffect(BitConverter.GetBytes(statusEffectId), 1, duration);
                player.ADD_DEF_TLC(defIncrease);
                player.ADD_ATT_TLC(attIncrease);
                player.UpdateMartialArtsAndStatus();
                player.UpdateCharacterData(player);
                player.UpdateBroadcastCharacterData();
            }
    }

    private void HandleAutoRespawn()
    {
        if (TuDongHoiSinh != null)
        {
            TuDongHoiSinh.Interval = FLD_NEWTIME * 1000;
            TuDongHoiSinh.Enabled = true;
        }
        else
        {
            TuDongHoiSinh = new System.Timers.Timer(FLD_NEWTIME * 1000);
            TuDongHoiSinh.Elapsed += TuDongHoiSinhEvent;
            TuDongHoiSinh.Enabled = true;
        }
    }

    public void GuiDiTuVongSoLieuCu(int UserWorldId = 0)
    {
        var num = 0;
        if (!_PlayList.TryGetValue(UserWorldId, out var value)) return;
        try
        {
            foreach (var value4 in value.NhiemVu.Values)
            foreach (var item in value4.NhiemVu_GiaiDoan)
            {
                if (item.GiaiDoan_TrangThai != 1 || item.GiaiDoanCanVatPham_.Count <= 0) continue;
                var num2 = 15;
                var rW = new X_Nhiem_Vu_Loai().GetRW(value4.RwID);
                foreach (var item2 in rW.NhiemVu_GiaiDoan)
                    if (item2.GiaiDoanID == item.GiaiDoanID)
                    {
                        num2 = item2.MucDo_KhoKhan;
                        item.GiaiDoanCanVatPham_ = item2.GiaiDoanCanVatPham_;
                        break;
                    }

                num = value4.RwID;
                if (num == 45 && 15062 == FLD_PID) value.SetUpQuestItems(900000099, 1);
                if (num == 46 && 15072 == FLD_PID) value.SetUpQuestItems(900000101, 1);
                foreach (var value5 in item.GiaiDoanCanVatPham_.Values)
                {
                    if (value5.QuaiVat_ID != FLD_PID || value.CheckItem(value5.VatPham_ID, value5.TongSoVatPham) ||
                        RNG.Next(1, 100) < num2) continue;
                    value.SetUpQuestItems(value5.VatPham_ID, 1);
                    if (value.TeamID == 0 || value.TeamingStage != 2 ||
                        !World.WToDoi.TryGetValue(value.TeamID, out var value2)) continue;
                    foreach (var value6 in value2.ToDoi_NguoiChoi.Values)
                    {
                        if (!FindPlayers(1000, value6) || value6.NhanVat_HP <= 0 || value6.PlayerTuVong ||
                            value.CharacterFullServerID == value6.CharacterFullServerID) continue;
                        foreach (var value7 in value6.NhiemVu.Values)
                        foreach (var item3 in value7.NhiemVu_GiaiDoan)
                        {
                            if (item3.GiaiDoanCanVatPham_.Count <= 0 || item3.GiaiDoan_TrangThai != 1) continue;
                            foreach (var item4 in rW.NhiemVu_GiaiDoan)
                                if (item4.GiaiDoanID == item3.GiaiDoanID)
                                {
                                    item3.GiaiDoanCanVatPham_ = item4.GiaiDoanCanVatPham_;
                                    break;
                                }

                            foreach (var value8 in item3.GiaiDoanCanVatPham_.Values)
                                if (value8.QuaiVat_ID == FLD_PID &&
                                    !value6.CheckItem(value8.VatPham_ID, value8.TongSoVatPham))
                                    value6.SetUpQuestItems(value8.VatPham_ID, 1);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(2,
                "NhiemVu_VatPhamerror   QuaiVat_ID--" + FLD_PID + "Nhiệm vụ ID-" + num + "|" + ex.Message);
        }

        var error2 = 0;
        if (UserWorldId != 0)
            try
            {
                DropQuest(UserWorldId);
                error2 = 1;
            }
            catch (Exception exception)
            {
                Form1.WriteLine(2, "DROPQUEST ERROR -- " + UserWorldId + " --" + exception.Message);
            }

        try
        {
            AbnormalStatusList();
            error2 = 2;
            EndAbnormalBloodDropStatusList();
            error2 = 3;
            if (IsNpc == 1 || NPCDeath) return;
            error2 = 4;
            if (FLD_BOSS == 1 && Rxjh_Map == 101)
            {
                error2 = 5;
                var num3 = RNG.Next(1, 3);
                for (var i = 0; i < num3; i++)
                {
                    error2 = 6;
                    if (World.allConnectedChars.TryGetValue(BossPlayerWid, out value) && FindPlayers(1000, value))
                    {
                        Boss_BaoSuat_VatPham(value);
                        error2 = 7;
                    }
                }
            }
            else if (World.allConnectedChars.TryGetValue(PlayerWid, out value))
            {
                error2 = 8;
                if (value.Player_Level - Level < World.LayDuoc_KinhNghiem_CapDo_ChenhLech || FLD_BOSS != 0)
                {
                    BaoSuat_VatPham(value);
                    error2 = 9;
                }
            }
            else
            {
                error2 = 10;
                Math.Abs(PlayCw.Playe.Player_Level - Level);
                if (UserWorldId > 40000 && PlayCw != null && PlayCw.Playe != null && PlayCw.Playe.Player_Level - Level <
                    World.LayDuoc_KinhNghiem_CapDo_ChenhLech)
                {
                    BaoSuat_VatPham(PlayCw.Playe);
                    error2 = 11;
                }
                else
                {
                    BaoSuat_VatPham(null);
                    error2 = 12;
                }
            }

            foreach (var value9 in World.allConnectedChars.Values)
            {
                error2 = 13;
                if (World.allConnectedChars.TryGetValue(UserWorldId, out var value3))
                {
                    error2 = 14;
                    if (FLD_PID == 16430)
                    {
                        value9.ThienMaThanCungCuaChinhThanhDaMoRa();
                        error2 = 15;
                        World.ThienMaThanCungDaiMon_PhaiChangTuVong = 1;
                    }

                    if (FLD_PID == 16431)
                    {
                        value9.ThienMaThanCungCuaThanhDong_DaMo();
                        error2 = 16;
                        World.ThienMaThanCungDaiMon_PhaiChangTuVong = 1;
                    }

                    if (FLD_PID == 16435)
                    {
                        error2 = 17;
                        DBA.ExeSqlCommand("DELETE FROM  CongThanhChien_ThanhChu");
                        value3.ThienMaThanCungPhoTuongKichSat();
                        error2 = 18;
                        value9.ThienMaThanCungCongThanhChienThangPacket(value3);
                        error2 = 19;
                        value9.ThienMaThanCungPhoTuongKichSat_DiDong(value9);
                        error2 = 20;
                        World.ThienMaThanCungPhoTuong_PhaiChangTuVong = 1;
                    }
                }
            }

            if (FLD_BOSS == 1 && FLD_NEWTIME > 60)
            {
                error2 = 21;
                var mapName = X_Toa_Do_Class.getname(Rxjh_Map);
                error2 = 22;
                foreach (var p in World.allConnectedChars.Values)
                {
                    error2 = 23;
                    p.HeThongNhacNho("Boss vừa bị giết tại bản đồ: " + mapName, 8);
                }

                logo.BossLog("Boss chết ID:" + FLD_PID + " Map" + Rxjh_Map);
            }

            NPCDeath = true;
            if (QuaiXuatHien_DuyNhatMotLan)
            {
                error2 = 24;
                if (PlayCw != null)
                {
                    PlayCw = null;
                    error2 = 25;
                }

                Play_null();
                error2 = 26;
                QuangBa_NPCDeathSoLieu();
                error2 = 27;
                Dispose();
                error2 = 28;
                return;
            }

            error2 = 29;
            try
            {
                if (AutomaticAttack != null)
                {
                    error2 = 291;
                    AutomaticAttack.Enabled = false;
                    error2 = 30;
                }
            }
            catch (Exception e2)
            {
                var text3 =
                    $"AutoMatic Attack Error : {value.Userid}-{value.UserName} | {FLD_PID}-{Name} [{error2}] Error : {e2}";
                logo.LogGuiDiTuVongSoLieu(text3);
                AutomaticAttack = null;
                AutomaticAttack = new System.Timers.Timer(1000.0);
                AutomaticAttack.Elapsed += AutomaticAttackEvent;
                AutomaticAttack.AutoReset = true;
            }

            try
            {
                error2 = 301;
                if (AutomaticMove != null)
                {
                    error2 = 302;
                    AutomaticMove.Enabled = true;
                    error2 = 31;
                }
            }
            catch (Exception e)
            {
                var text2 =
                    $"AutoMatic Move Error : {value.Userid}-{value.UserName} | {FLD_PID}-{Name} [{error2}] Error : {e}";
                logo.LogGuiDiTuVongSoLieu(text2);
                AutomaticMove = null;
                AutomaticMove = new System.Timers.Timer(1000.0);
                AutomaticMove.Elapsed += AutomaticAttackEvent;
                AutomaticMove.AutoReset = true;
                AutomaticMove.Enabled = true;
            }

            if (World.TheLucChien_Progress == 3 &&
                (FLD_PID == 15491 || FLD_PID == 15492 || FLD_PID == 15493 || FLD_PID == 15494))
            {
                error2 = 32;
                if (FLD_PID == 15491)
                {
                    World.TheLucChien_TaPhai_DiemBOSS += 500;
                    error2 = 33;
                }

                if (FLD_PID == 15493)
                {
                    World.TheLucChien_ChinhPhai_DiemBOSS += 500;
                    error2 = 34;
                }

                if (FLD_PID == 15494)
                {
                    error2 = 35;
                    World.TheLucChien_ChinhPhai_DiemBOSS += 100;
                    foreach (var playerss2 in World.allConnectedChars.Values)
                    {
                        error2 = 36;
                        if (playerss2.NhanVatToaDo_BanDo != 801) continue;
                        error2 = 37;
                        if (playerss2.Player_Zx == 1)
                        {
                            error2 = 38;
                            if (!playerss2.AppendStatusList.ContainsKey(700500))
                            {
                                error2 = 39;
                                if (playerss2.AppendStatusList.ContainsKey(700500))
                                {
                                    error2 = 40;
                                    playerss2.AppendStatusList[700500].ThoiGianKetThucSuKien();
                                    error2 = 41;
                                }

                                playerss2.AppendStatusList.Add(700500,
                                    new X_Them_Vao_Trang_Thai_Loai(playerss2, 120000, 700500, 0));
                                playerss2.StatusEffect(BitConverter.GetBytes(700500), 1, 120000);
                                playerss2.ADD_DEF_TLC(0.06);
                                playerss2.ADD_ATT_TLC(0.06);
                                playerss2.UpdateMartialArtsAndStatus();
                                error2 = 42;
                                playerss2.UpdateCharacterData(playerss2);
                                error2 = 43;
                                playerss2.UpdateBroadcastCharacterData();
                                error2 = 44;
                            }
                        }
                        else
                        {
                            if (playerss2.Player_Zx != 2) continue;
                            error2 = 45;
                            if (!playerss2.AppendStatusList.ContainsKey(700501))
                            {
                                error2 = 46;
                                if (playerss2.AppendStatusList.ContainsKey(700501))
                                {
                                    error2 = 47;
                                    playerss2.AppendStatusList[700501].ThoiGianKetThucSuKien();
                                    error2 = 48;
                                }

                                playerss2.AppendStatusList.Add(700501,
                                    new X_Them_Vao_Trang_Thai_Loai(playerss2, 180000, 700501, 0));
                                playerss2.StatusEffect(BitConverter.GetBytes(700501), 1, 180000);
                                playerss2.ADD_DEF_TLC(0.03);
                                playerss2.ADD_ATT_TLC(0.03);
                                error2 = 49;
                                playerss2.UpdateMartialArtsAndStatus();
                                error2 = 50;
                                playerss2.UpdateCharacterData(playerss2);
                                error2 = 51;
                                playerss2.UpdateBroadcastCharacterData();
                                error2 = 52;
                            }
                        }
                    }
                }

                if (FLD_PID == 15492)
                {
                    error2 = 53;
                    World.TheLucChien_TaPhai_DiemBOSS += 100;
                    foreach (var playerss in World.allConnectedChars.Values)
                    {
                        error2 = 54;
                        if (playerss.NhanVatToaDo_BanDo != 801) continue;
                        error2 = 55;
                        if (playerss.Player_Zx == 2)
                        {
                            error2 = 56;
                            if (!playerss.AppendStatusList.ContainsKey(700500))
                            {
                                error2 = 57;
                                if (playerss.AppendStatusList.ContainsKey(700500))
                                {
                                    error2 = 58;
                                    playerss.AppendStatusList[700500].ThoiGianKetThucSuKien();
                                }

                                playerss.AppendStatusList.Add(700500,
                                    new X_Them_Vao_Trang_Thai_Loai(playerss, 120000, 700500, 0));
                                playerss.StatusEffect(BitConverter.GetBytes(700500), 1, 120000);
                                playerss.ADD_DEF_TLC(0.06);
                                playerss.ADD_ATT_TLC(0.06);
                                error2 = 59;
                                playerss.UpdateMartialArtsAndStatus();
                                error2 = 60;
                                playerss.UpdateCharacterData(playerss);
                                error2 = 61;
                                playerss.UpdateBroadcastCharacterData();
                                error2 = 62;
                            }
                        }
                        else
                        {
                            if (playerss.Player_Zx != 1) continue;
                            error2 = 63;
                            if (!playerss.AppendStatusList.ContainsKey(700501))
                            {
                                error2 = 64;
                                if (playerss.AppendStatusList.ContainsKey(700501))
                                {
                                    error2 = 65;
                                    playerss.AppendStatusList[700501].ThoiGianKetThucSuKien();
                                    error2 = 66;
                                }

                                playerss.AppendStatusList.Add(700501,
                                    new X_Them_Vao_Trang_Thai_Loai(playerss, 180000, 700501, 0));
                                playerss.StatusEffect(BitConverter.GetBytes(700501), 1, 180000);
                                playerss.ADD_DEF_TLC(0.03);
                                playerss.ADD_ATT_TLC(0.03);
                                error2 = 67;
                                playerss.UpdateMartialArtsAndStatus();
                                error2 = 68;
                                playerss.UpdateCharacterData(playerss);
                                error2 = 69;
                                playerss.UpdateBroadcastCharacterData();
                                error2 = 70;
                            }
                        }
                    }
                }
            }

            error2 = 71;
            if (TuDongHoiSinh != null)
            {
                error2 = 72;
                TuDongHoiSinh.Interval = FLD_NEWTIME * 1000;
                TuDongHoiSinh.Enabled = true;
                error2 = 73;
            }
            else
            {
                error2 = 74;
                TuDongHoiSinh = new System.Timers.Timer(FLD_NEWTIME * 1000);
                error2 = 75;
                TuDongHoiSinh.Elapsed += TuDongHoiSinhEvent;
                error2 = 76;
                TuDongHoiSinh.Enabled = true;
                error2 = 77;
            }

            if (PlayCw != null)
            {
                error2 = 78;
                PlayCw = null;
                error2 = 79;
            }

            error2 = 80;
            Play_null();
            error2 = 81;
            QuangBa_NPCDeathSoLieu();
            error2 = 82;
        }
        catch (Exception ex2)
        {
            var text4 = $"{FLD_PID}-{Name} [{error2}] Error : {ex2}";
            logo.LogGuiDiTuVongSoLieu(text4);
            Form1.WriteLine(1, "GuiDiTuVongSoLieu   error " + FLD_PID + "|" + Name + "   " + ex2.Message);
            var text = $"{value.Userid}-{value.UserName} | {FLD_PID}-{Name} [{error2}] Error : {ex2}";
            logo.LogGuiDiTuVongSoLieu(text);
        }
    }

    public void GuiDi_PhanSatThuong_CongKichSoLieu(int CongKichLuc, int NhanVat_ID)
    {
        var array = Converter.HexStringToByte("AA551200A42789000C002C0100000F0000000100000055AA");
        System.Buffer.BlockCopy(Buffer.GetBytes(FLD_INDEX), 0, array, 4, 2);
        System.Buffer.BlockCopy(Buffer.GetBytes(NhanVat_ID), 0, array, 10, 2);
        System.Buffer.BlockCopy(Buffer.GetBytes(CongKichLuc), 0, array, 18, 2);
        QuangBaSoLieu(array, array.Length);
    }

    public byte[] RoiRaVatPham_Custom(int FLD_PID, Players yxqname)
    {
        try
        {
            var dBItmeId = RxjhClass.GetDBItmeId();
            var array = new byte[World.Item_Db_Byte_Length];
            var bytes = Buffer.GetBytes(dBItmeId);
            var array2 = new byte[56];
            if (!World.Itme.TryGetValue(FLD_PID, out var value)) return null;
            if (value.FLD_QUESTITEM != 1)
            {
                try
                {
                    if (World.Droplog)
                        Form1.WriteLine(4,
                            "Drop--Custom:" + value.ItmeNAME + "   ThuocTinh1[" + 0 + "]ThuocTinh2[" + 0 +
                            "]ThuocTinh3[" + 0 + "]ThuocTinh4[" + 0 + "]ThuocTinh5[" + 0 + "]");
                    System.Buffer.BlockCopy(Buffer.GetBytes(0), 0, array2, 0, 4);
                    System.Buffer.BlockCopy(Buffer.GetBytes(0), 0, array2, 4, 4);
                    System.Buffer.BlockCopy(Buffer.GetBytes(0), 0, array2, 8, 4);
                    System.Buffer.BlockCopy(Buffer.GetBytes(0), 0, array2, 12, 4);
                    System.Buffer.BlockCopy(Buffer.GetBytes(0), 0, array2, 16, 4);
                    System.Buffer.BlockCopy(bytes, 0, array, 0, 4);
                    System.Buffer.BlockCopy(array2, 0, array, 16, 20);
                    System.Buffer.BlockCopy(Buffer.GetBytes(FLD_PID), 0, array, 8, 4);
                    System.Buffer.BlockCopy(Buffer.GetBytes(1), 0, array, 12, 4);
                    if (value.FLD_NJ > 0 && (value.FLD_RESIDE2 == 1 || value.FLD_RESIDE2 == 2 ||
                                             value.FLD_RESIDE2 == 5 || value.FLD_RESIDE2 == 4 ||
                                             value.FLD_RESIDE2 == 6))
                        System.Buffer.BlockCopy(Buffer.GetBytes(1000), 0, array, 60, 2);
                }
                catch (Exception ex)
                {
                    Form1.WriteLine(1, "Drop Custom  error   " + FLD_PID + "|" + Name + "   " + ex.Message);
                    return null;
                }

                X_Mat_Dat_Vat_Pham_Loai MatDat_VatPhamCLass;
                X_Mat_Dat_Vat_Pham_Loai value2;
                try
                {
                    if (FLD_BOSS == 0)
                    {
                        MatDat_VatPhamCLass =
                            new X_Mat_Dat_Vat_Pham_Loai(array, Rxjh_X, Rxjh_Y, Rxjh_Z, Rxjh_Map, yxqname, 0);
                    }
                    else
                    {
                        var random = new Random(World.GetRandomSeed());
                        random.Next(0, 2);
                        var num2 = random.NextDouble() * 25.0;
                        var num3 = random.NextDouble() * 25.0;
                        MatDat_VatPhamCLass = num3 != 0.0
                            ? new X_Mat_Dat_Vat_Pham_Loai(array, Rxjh_X - (float)num2, Rxjh_Y - (float)num3, Rxjh_Z,
                                Rxjh_Map, yxqname, 0)
                            : new X_Mat_Dat_Vat_Pham_Loai(array, Rxjh_X + (float)num2, Rxjh_Y + (float)num3, Rxjh_Z,
                                Rxjh_Map, yxqname, 0);
                    }

                    if (MatDat_VatPhamCLass == null)
                    {
                        Form1.WriteLine(1, "Drop Custom 2   error   " + FLD_PID + "|" + Name);
                        return null;
                    }

                    if (!World.ItmeTeM.TryGetValue(dBItmeId, out value2))
                        World.ItmeTeM.Add(dBItmeId, MatDat_VatPhamCLass);
                }
                catch (Exception ex2)
                {
                    Form1.WriteLine(1, "Drop Custom 3   error   " + FLD_PID + "|" + Name + "   " + ex2.Message);
                    return null;
                }

                try
                {
                    if (World.ItmeTeM.TryGetValue(dBItmeId, out value2))
                        MatDat_VatPhamCLass.GetARangeOfPlayersSendGroundIncreaseItemSoLieuPackage();
                    return array;
                }
                catch (Exception ex3)
                {
                    Form1.WriteLine(1, "Drop Custom 4   error   " + FLD_PID + "|" + Name + "   " + ex3.Message);
                    return null;
                }
            }

            if (yxqname != null)
            {
                var num4 = yxqname.GetParcelVacancy(yxqname);
                if (num4 != -1)
                    yxqname.AddItems(bytes, Buffer.GetBytes(FLD_PID), num4, Buffer.GetBytes(1), new byte[56]);
            }

            return null;
        }
        catch (Exception ex4)
        {
            Form1.WriteLine(1, "Drop Custom 5   error   " + FLD_PID + "|" + Name + "   " + ex4.Message);
            return null;
        }
    }

    public byte[] RoiRaVatPham(DropClass drop, Players yxqname)
    {
        try
        {
            var dBItmeId = RxjhClass.GetDBItmeId();
            var array = new byte[World.Item_Db_Byte_Length];
            var bytes = Buffer.GetBytes(dBItmeId);
            var array2 = new byte[56];
            if (!World.Itme.TryGetValue(drop.FLD_PID, out var value)) return null;
            if (value.FLD_QUESTITEM != 1)
            {
                try
                {
                    if (World.Droplog)
                        Form1.WriteLine(4,
                            "Drop--VatPhamTen:" + drop.FLD_NAME + "   ThuocTinh1[" + drop.FLD_MAGIC0 + "]ThuocTinh2[" +
                            drop.FLD_MAGIC1 + "]ThuocTinh3[" + drop.FLD_MAGIC2 + "]ThuocTinh4[" + drop.FLD_MAGIC3 +
                            "]ThuocTinh5[" + drop.FLD_MAGIC4 + "]");
                    System.Buffer.BlockCopy(Buffer.GetBytes(drop.FLD_MAGIC0), 0, array2, 0, 4);
                    System.Buffer.BlockCopy(Buffer.GetBytes(drop.FLD_MAGIC1), 0, array2, 4, 4);
                    System.Buffer.BlockCopy(Buffer.GetBytes(drop.FLD_MAGIC2), 0, array2, 8, 4);
                    System.Buffer.BlockCopy(Buffer.GetBytes(drop.FLD_MAGIC3), 0, array2, 12, 4);
                    System.Buffer.BlockCopy(Buffer.GetBytes(drop.FLD_MAGIC4), 0, array2, 16, 4);
                    System.Buffer.BlockCopy(bytes, 0, array, 0, 4);
                    System.Buffer.BlockCopy(array2, 0, array, 16, 20);
                    System.Buffer.BlockCopy(Buffer.GetBytes(drop.FLD_PID), 0, array, 8, 4);
                    System.Buffer.BlockCopy(Buffer.GetBytes(1), 0, array, 12, 4);
                    if (value.FLD_NJ > 0 && (value.FLD_RESIDE2 == 1 || value.FLD_RESIDE2 == 2 ||
                                             value.FLD_RESIDE2 == 5 || value.FLD_RESIDE2 == 4 ||
                                             value.FLD_RESIDE2 == 6))
                        System.Buffer.BlockCopy(Buffer.GetBytes(1000), 0, array, 60, 2);
                }
                catch (Exception ex)
                {
                    Form1.WriteLine(1, "RoiRaVatPham1   error   " + FLD_PID + "|" + Name + "   " + ex.Message);
                    return null;
                }

                X_Mat_Dat_Vat_Pham_Loai MatDat_VatPhamCLass;
                X_Mat_Dat_Vat_Pham_Loai value2;
                try
                {
                    if (FLD_BOSS == 0)
                    {
                        MatDat_VatPhamCLass =
                            new X_Mat_Dat_Vat_Pham_Loai(array, Rxjh_X, Rxjh_Y, Rxjh_Z, Rxjh_Map, yxqname, 0);
                    }
                    else
                    {
                        var random = new Random(World.GetRandomSeed());
                        random.Next(0, 2);
                        var num2 = random.NextDouble() * 25.0;
                        var num3 = random.NextDouble() * 25.0;
                        MatDat_VatPhamCLass = num3 != 0.0
                            ? new X_Mat_Dat_Vat_Pham_Loai(array, Rxjh_X - (float)num2, Rxjh_Y - (float)num3, Rxjh_Z,
                                Rxjh_Map, yxqname, 0)
                            : new X_Mat_Dat_Vat_Pham_Loai(array, Rxjh_X + (float)num2, Rxjh_Y + (float)num3, Rxjh_Z,
                                Rxjh_Map, yxqname, 0);
                    }

                    if (MatDat_VatPhamCLass == null)
                    {
                        Form1.WriteLine(1, "RoiRaVatPham2   error   " + FLD_PID + "|" + Name);
                        return null;
                    }

                    if (!World.ItmeTeM.TryGetValue(dBItmeId, out value2))
                        World.ItmeTeM.Add(dBItmeId, MatDat_VatPhamCLass);
                }
                catch (Exception ex2)
                {
                    Form1.WriteLine(1, "RoiRaVatPham3   error   " + FLD_PID + "|" + Name + "   " + ex2.Message);
                    return null;
                }

                try
                {
                    if (World.ItmeTeM.TryGetValue(dBItmeId, out value2))
                        MatDat_VatPhamCLass.GetARangeOfPlayersSendGroundIncreaseItemSoLieuPackage();
                    return array;
                }
                catch (Exception ex3)
                {
                    Form1.WriteLine(1, "RoiRaVatPham4   error   " + FLD_PID + "|" + Name + "   " + ex3.Message);
                    return null;
                }
            }

            if (yxqname != null)
            {
                var num4 = yxqname.GetParcelVacancy(yxqname);
                if (num4 != -1)
                    yxqname.AddItems(bytes, Buffer.GetBytes(drop.FLD_PID), num4, Buffer.GetBytes(1), new byte[56]);
            }

            return null;
        }
        catch (Exception ex4)
        {
            Form1.WriteLine(1, "RoiRaVatPham5   error   " + FLD_PID + "|" + Name + "   " + ex4.Message);
            return null;
        }
        finally
        {
            drop.FLD_PID = drop.FLD_PIDNew;
            drop.FLD_MAGIC0 = drop.FLD_MAGICNew0;
            drop.FLD_MAGIC1 = drop.FLD_MAGICNew1;
            drop.FLD_MAGIC2 = drop.FLD_MAGICNew2;
            drop.FLD_MAGIC3 = drop.FLD_MAGICNew3;
            drop.FLD_MAGIC4 = drop.FLD_MAGICNew4;
        }
    }

    public void BaoSuat_VatPham(Players yxqname, int pid = 0)
    {
        try
        {
            if (Rxjh_Map == 801) yxqname = null;
            if (FLD_BOSS == 0)
                BaoSuat_VatPham2(yxqname);
            else if (FLD_BOSS == 1)
                Boss_BaoSuat_VatPham(yxqname);
            else if (FLD_BOSS == 2) GSBaoSuat_VatPham(1, 3, yxqname);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Bạo vật phẩm Phạm sai lầm 111: " + ex);
        }
    }

    public void GSBaoSuat_VatPham(int int_0, int maxsl, Players yxqname)
    {
        try
        {
            List<DropClass> list = null;
            var num12 = 0;
            var num18 = 0;
            var num19 = 0;
            var num21 = 0;
            var num22 = 0;
            var num23 = 0;
            var num24 = 0;
            var num20 = 0;
            var num25 = 0;
            var num26 = 0;
            var num27 = 0;
            var num28 = 0;
            var num29 = 0;
            var num30 = 0;
            var num31 = 0;
            var num10 = 0;
            var num11 = 0;
            var num13 = 0;
            var num14 = 0;
            var num15 = 0;
            var num16 = 0;
            var num17 = 0;
            if (Rxjh_Exp <= 0) return;
            list = DropClass.GetGSDrop(Level, int_0, maxsl);
            if (list == null) return;
            foreach (var item in list)
            {
                if (item == null) continue;
                switch (item.FLD_PID)
                {
                    case 800000002:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            num30 = 0;
                            num29 = RNG.Next(1, 100);
                            foreach (var item8 in item.DropShuX)
                                if (num29 <= item8.Max)
                                {
                                    num30 = RNG.Next(item8.ShuXMin, item8.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = num30;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
                        }

                        break;
                    case 800000001:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            num24 = 0;
                            num20 = RNG.Next(1, 100);
                            foreach (var item9 in item.DropShuX)
                                if (num20 <= item9.Max)
                                {
                                    num24 = RNG.Next(item9.ShuXMin, item9.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = num24;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
                        }

                        break;
                    case 800000023:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            num25 = 0;
                            num26 = RNG.Next(1, 100);
                            foreach (var item10 in item.DropShuX)
                                if (num26 <= item10.Max)
                                {
                                    num25 = RNG.Next(item10.ShuXMin, item10.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = num25;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
                        }

                        break;
                    case 800000013:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            num23 = 0;
                            num22 = RNG.Next(1, 100);
                            foreach (var item11 in item.DropShuX)
                                if (num22 <= item11.Max)
                                {
                                    num23 = RNG.Next(item11.ShuXMin, item11.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = num23;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = MagicQigongClass.GetMaGic0("drop");
                        }

                        break;
                    case 800000047:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            num11 = 0;
                            num13 = RNG.Next(1, 100);
                            foreach (var item12 in item.DropShuX)
                                if (num13 <= item12.Max)
                                {
                                    num11 = RNG.Next(item12.ShuXMin, item12.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = num11;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = RNG.Next(23, 51);
                        }

                        break;
                    case 800000046:
                        if (item.FLD_MAGIC0 == 0) item.FLD_MAGIC0 = RNG.Next(1, 22);
                        break;
                    case 1000000321:
                        item.FLD_MAGIC0 = RNG.Next(1001, 2999);
                        item.FLD_MAGIC1 = RNG.Next(10, 50);
                        break;
                    case 800000062:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            num19 = 0;
                            num21 = RNG.Next(1, 100);
                            foreach (var item2 in item.DropShuX)
                                if (num21 <= item2.Max)
                                {
                                    num19 = RNG.Next(item2.ShuXMin, item2.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = num19;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
                        }

                        break;
                    case 800000061:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            num27 = 0;
                            num28 = RNG.Next(1, 100);
                            foreach (var item3 in item.DropShuX)
                                if (num28 <= item3.Max)
                                {
                                    num27 = RNG.Next(item3.ShuXMin, item3.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = num27;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
                        }

                        break;
                    case 800000025:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            num18 = 0;
                            num12 = RNG.Next(1, 100);
                            foreach (var item4 in item.DropShuX)
                                if (num12 <= item4.Max)
                                {
                                    num18 = RNG.Next(item4.ShuXMin, item4.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = num18;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
                        }

                        break;
                    case 1000000325:
                        item.FLD_MAGIC0 = RNG.Next(1001, 2999);
                        item.FLD_MAGIC1 = RNG.Next(400, 699);
                        break;
                    case 800000032:
                        item.FLD_MAGIC0 = World.GetValue(800000032, 5);
                        break;
                    case 800000033:
                        item.FLD_MAGIC0 = World.GetValue(800000033, 5);
                        break;
                    case 800000031:
                        item.FLD_MAGIC0 = World.GetValue(800000031, 5);
                        break;
                    case 800000028:
                        item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
                        break;
                    case 800000026:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            num31 = 0;
                            num10 = RNG.Next(1, 100);
                            foreach (var item5 in item.DropShuX)
                                if (num10 <= item5.Max)
                                {
                                    num31 = RNG.Next(item5.ShuXMin, item5.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = num31;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
                        }

                        break;
                    case 1000000327:
                        item.FLD_MAGIC0 = RNG.Next(1001, 2999);
                        item.FLD_MAGIC1 = RNG.Next(2000, 2499);
                        break;
                    case 800000024:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            num16 = 0;
                            num17 = RNG.Next(1, 100);
                            foreach (var item6 in item.DropShuX)
                                if (num17 <= item6.Max)
                                {
                                    num16 = RNG.Next(item6.ShuXMin, item6.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = num16;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 5);
                        }

                        break;
                    default:
                        if (item.FLD_MAGIC0 != 10) break;
                        num14 = 0;
                        num15 = RNG.Next(1, 100);
                        foreach (var item7 in item.DropShuX)
                            if (num15 <= item7.Max)
                            {
                                num14 = RNG.Next(item7.ShuXMin, item7.ShuXMax - 1);
                                break;
                            }

                        item.FLD_MAGIC0 = num14;
                        break;
                    case 800000030:
                        item.FLD_MAGIC0 = World.GetValue(800000030, 5);
                        break;
                    case 1000000323:
                        item.FLD_MAGIC0 = RNG.Next(1001, 2999);
                        item.FLD_MAGIC1 = RNG.Next(100, 150);
                        break;
                }

                RoiRaVatPham(item, yxqname);
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Bạo vật phẩm Phạm sai lầm 222: " + ex);
        }
    }

    public void Boss_BaoSuat_VatPham(Players yxqname)
    {
        try
        {
            if (Rxjh_Exp <= 0)
            {
                Form1.WriteLine(1, "Boss_BaoSuat_VatPham   error：this.Rxjh_Exp   <=   0");
                return;
            }

            var TiLe_Rot = World.TiLe_Rot;
            if (yxqname != null)
            {
                if (yxqname.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram > 0.0)
                    TiLe_Rot += (int)(TiLe_Rot * yxqname.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram);
                if (yxqname.QueryThienQuanDiaDoMap(yxqname.NhanVatToaDo_BanDo))
                    yxqname.GetTianguanBenefitBonus(1, yxqname.NhanVatToaDo_BanDo);
            }

            var bossDrop = DropClass.GetBossDrop(Level);
            if (bossDrop == null)
            {
                Form1.WriteLine(1, "Boss_BaoSuat_VatPham   error：bossdrop   ==   null");
                return;
            }

            foreach (var item in bossDrop)
            {
                if (item == null) continue;
                switch (item.FLD_PID)
                {
                    case 800000002:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            var fLD_MAGIC5 = 0;
                            var num5 = RNG.Next(1, 100);
                            foreach (var item4 in item.DropShuX)
                                if (num5 <= item4.Max)
                                {
                                    fLD_MAGIC5 = RNG.Next(item4.ShuXMin, item4.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = fLD_MAGIC5;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
                        }

                        break;
                    case 800000001:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            var fLD_MAGIC2 = 0;
                            var num2 = RNG.Next(1, 100);
                            foreach (var item5 in item.DropShuX)
                                if (num2 <= item5.Max)
                                {
                                    fLD_MAGIC2 = RNG.Next(item5.ShuXMin, item5.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = fLD_MAGIC2;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
                        }

                        break;
                    case 800000023:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            var fLD_MAGIC3 = 0;
                            var num3 = RNG.Next(1, 100);
                            foreach (var item6 in item.DropShuX)
                                if (num3 <= item6.Max)
                                {
                                    fLD_MAGIC3 = RNG.Next(item6.ShuXMin, item6.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = fLD_MAGIC3;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
                        }

                        break;
                    case 800000024:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            var fLD_MAGIC9 = 0;
                            var num9 = RNG.Next(1, 100);
                            foreach (var item7 in item.DropShuX)
                                if (num9 <= item7.Max)
                                {
                                    fLD_MAGIC9 = RNG.Next(item7.ShuXMin, item7.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = fLD_MAGIC9;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
                        }

                        break;
                    case 800000025:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            var fLD_MAGIC4 = 0;
                            var num4 = RNG.Next(1, 100);
                            foreach (var item8 in item.DropShuX)
                                if (num4 <= item8.Max)
                                {
                                    fLD_MAGIC4 = RNG.Next(item8.ShuXMin, item8.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = fLD_MAGIC4;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
                        }

                        break;
                    case 800000026:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            var fLD_MAGIC8 = 0;
                            var num8 = RNG.Next(1, 100);
                            foreach (var item9 in item.DropShuX)
                                if (num8 <= item9.Max)
                                {
                                    fLD_MAGIC8 = RNG.Next(item9.ShuXMin, item9.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = fLD_MAGIC8;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
                        }

                        break;
                    case 800000028:
                        item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
                        break;
                    case 800000030:
                        item.FLD_MAGIC0 = World.GetValue(800000030, 4);
                        break;
                    case 800000031:
                        item.FLD_MAGIC0 = World.GetValue(800000031, 4);
                        break;
                    case 800000032:
                        item.FLD_MAGIC0 = World.GetValue(800000032, 4);
                        break;
                    case 800000033:
                        item.FLD_MAGIC0 = World.GetValue(800000033, 4);
                        break;
                    case 800000013:
                        item.FLD_MAGIC0 = MagicQigongClass.GetMaGic0("drop");
                        break;
                    case 800000047:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            var fLD_MAGIC10 = 0;
                            var num10 = RNG.Next(1, 100);
                            foreach (var item10 in item.DropShuX)
                                if (num10 <= item10.Max)
                                {
                                    fLD_MAGIC10 = RNG.Next(item10.ShuXMin, item10.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = fLD_MAGIC10;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = RNG.Next(23, 51);
                        }

                        break;
                    case 800000046:
                        if (item.FLD_MAGIC0 == 0) item.FLD_MAGIC0 = RNG.Next(1, 22);
                        break;
                    case 1000000321:
                        item.FLD_MAGIC0 = RNG.Next(1001, 2999);
                        item.FLD_MAGIC1 = RNG.Next(10, 50);
                        break;
                    case 1000000323:
                        item.FLD_MAGIC0 = RNG.Next(1001, 2999);
                        item.FLD_MAGIC1 = RNG.Next(100, 150);
                        break;
                    case 1000000325:
                        item.FLD_MAGIC0 = RNG.Next(1001, 2999);
                        item.FLD_MAGIC1 = RNG.Next(400, 699);
                        break;
                    default:
                    {
                        if (item.FLD_MAGIC0 != 10) break;
                        var fLD_MAGIC7 = 0;
                        var num7 = RNG.Next(1, 100);
                        foreach (var item11 in item.DropShuX)
                            if (num7 <= item11.Max)
                            {
                                fLD_MAGIC7 = RNG.Next(item11.ShuXMin, item11.ShuXMax - 1);
                                break;
                            }

                        item.FLD_MAGIC0 = fLD_MAGIC7;
                        break;
                    }
                    case 1000000327:
                        item.FLD_MAGIC0 = RNG.Next(1001, 2999);
                        item.FLD_MAGIC1 = RNG.Next(2000, 2499);
                        break;
                    case 800000062:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            var fLD_MAGIC6 = 0;
                            var num6 = RNG.Next(1, 100);
                            foreach (var item2 in item.DropShuX)
                                if (num6 <= item2.Max)
                                {
                                    fLD_MAGIC6 = RNG.Next(item2.ShuXMin, item2.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = fLD_MAGIC6;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
                        }

                        break;
                    case 800000061:
                        if (item.FLD_MAGIC0 == 10)
                        {
                            var fLD_MAGIC = 0;
                            var num = RNG.Next(1, 100);
                            foreach (var item3 in item.DropShuX)
                                if (num <= item3.Max)
                                {
                                    fLD_MAGIC = RNG.Next(item3.ShuXMin, item3.ShuXMax - 1);
                                    break;
                                }

                            item.FLD_MAGIC0 = fLD_MAGIC;
                        }
                        else if (item.FLD_MAGIC0 == 0)
                        {
                            item.FLD_MAGIC0 = World.GetValue(item.FLD_PID, 4);
                        }

                        break;
                }

                if (yxqname != null)
                {
                    if (FLD_PID == World.WorldBoss_BossID) yxqname = null;
                    RoiRaVatPham(item, yxqname);
                }
                else
                {
                    RoiRaVatPham(item, null);
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "BaoSuat_VatPham   error：" + ex);
        }
    }

    public void BaoSuat_VatPham2(Players yxqname)
    {
        try
        {
            if (Rxjh_Exp <= 0) return;
            var num = RNG.Next(1, 80000);
            var num5 = World.TiLe_Rot;
            if (yxqname != null)
            {
                if (yxqname.Player_Level <= World.X2BaoSuat_CapDo_GioiHanCaoNhat)
                    num5 *= (int)World.X2CapDo_GioiHanCaoNhat_BoiSo;
                if (yxqname.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram > 0.0)
                    num5 += (int)(num5 * yxqname.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram);
                if (yxqname.QueryThienQuanDiaDoMap(yxqname.NhanVatToaDo_BanDo))
                    num5 += (int)yxqname.GetTianguanBenefitBonus(1, yxqname.NhanVatToaDo_BanDo);
            }

            if (num > num5) return;
            DropClass dropClass;
            if (World.allConnectedChars.TryGetValue(yxqname.CharacterFullServerID, out var value))
            {
                if (value.FLD_VIP == 0)
                {
                    dropClass = DropClass.GetDrop(Level);
                    if (dropClass == null) return;
                }
                else
                {
                    dropClass = DropClass.GetVipDrop(Level);
                    if (dropClass == null) return;
                }
            }
            else
            {
                dropClass = DropClass.GetDrop(Level);
                if (dropClass == null) return;
            }

            switch (dropClass.FLD_PID)
            {
                case 800000002:
                    if (dropClass.FLD_MAGIC0 == 10)
                    {
                        var fLD_MAGIC5 = 0;
                        var num9 = RNG.Next(1, 100);
                        foreach (var item in dropClass.DropShuX)
                            if (num9 <= item.Max)
                            {
                                fLD_MAGIC5 = RNG.Next(item.ShuXMin, item.ShuXMax - 1);
                                break;
                            }

                        dropClass.FLD_MAGIC0 = fLD_MAGIC5;
                    }
                    else if (dropClass.FLD_MAGIC0 == 0)
                    {
                        dropClass.FLD_MAGIC0 = World.GetValue(dropClass.FLD_PID, 3);
                    }

                    break;
                case 800000001:
                    if (dropClass.FLD_MAGIC0 == 10)
                    {
                        var fLD_MAGIC2 = 0;
                        var num4 = RNG.Next(1, 100);
                        foreach (var item3 in dropClass.DropShuX)
                            if (num4 <= item3.Max)
                            {
                                fLD_MAGIC2 = RNG.Next(item3.ShuXMin, item3.ShuXMax - 1);
                                break;
                            }

                        dropClass.FLD_MAGIC0 = fLD_MAGIC2;
                    }
                    else if (dropClass.FLD_MAGIC0 == 0)
                    {
                        dropClass.FLD_MAGIC0 = World.GetValue(dropClass.FLD_PID, 3);
                    }

                    break;
                case 800000023:
                    if (dropClass.FLD_MAGIC0 == 10)
                    {
                        var fLD_MAGIC3 = 0;
                        var num7 = RNG.Next(1, 100);
                        foreach (var item4 in dropClass.DropShuX)
                            if (num7 <= item4.Max)
                            {
                                fLD_MAGIC3 = RNG.Next(item4.ShuXMin, item4.ShuXMax - 1);
                                break;
                            }

                        dropClass.FLD_MAGIC0 = fLD_MAGIC3;
                    }
                    else if (dropClass.FLD_MAGIC0 == 0)
                    {
                        dropClass.FLD_MAGIC0 = World.GetValue(dropClass.FLD_PID, 3);
                    }

                    break;
                case 800000024:
                    if (dropClass.FLD_MAGIC0 == 10)
                    {
                        var fLD_MAGIC9 = 0;
                        var num2 = RNG.Next(1, 100);
                        foreach (var item5 in dropClass.DropShuX)
                            if (num2 <= item5.Max)
                            {
                                fLD_MAGIC9 = RNG.Next(item5.ShuXMin, item5.ShuXMax - 1);
                                break;
                            }

                        dropClass.FLD_MAGIC0 = fLD_MAGIC9;
                    }
                    else if (dropClass.FLD_MAGIC0 == 0)
                    {
                        dropClass.FLD_MAGIC0 = World.GetValue(dropClass.FLD_PID, 3);
                    }

                    break;
                case 800000025:
                    if (dropClass.FLD_MAGIC0 == 10)
                    {
                        var fLD_MAGIC4 = 0;
                        var num8 = RNG.Next(1, 100);
                        foreach (var item6 in dropClass.DropShuX)
                            if (num8 <= item6.Max)
                            {
                                fLD_MAGIC4 = RNG.Next(item6.ShuXMin, item6.ShuXMax - 1);
                                break;
                            }

                        dropClass.FLD_MAGIC0 = fLD_MAGIC4;
                    }
                    else if (dropClass.FLD_MAGIC0 == 0)
                    {
                        dropClass.FLD_MAGIC0 = World.GetValue(dropClass.FLD_PID, 3);
                    }

                    break;
                case 800000026:
                    if (dropClass.FLD_MAGIC0 == 10)
                    {
                        var fLD_MAGIC8 = 0;
                        var num12 = RNG.Next(1, 100);
                        foreach (var item7 in dropClass.DropShuX)
                            if (num12 <= item7.Max)
                            {
                                fLD_MAGIC8 = RNG.Next(item7.ShuXMin, item7.ShuXMax - 1);
                                break;
                            }

                        dropClass.FLD_MAGIC0 = fLD_MAGIC8;
                    }
                    else if (dropClass.FLD_MAGIC0 == 0)
                    {
                        dropClass.FLD_MAGIC0 = World.GetValue(dropClass.FLD_PID, 3);
                    }

                    break;
                case 800000028:
                    dropClass.FLD_MAGIC0 = World.GetValue(dropClass.FLD_PID, 3);
                    break;
                case 800000030:
                    dropClass.FLD_MAGIC0 = World.GetValue(800000030, 3);
                    break;
                case 800000031:
                    dropClass.FLD_MAGIC0 = World.GetValue(800000031, 3);
                    break;
                case 800000032:
                    dropClass.FLD_MAGIC0 = World.GetValue(800000032, 3);
                    break;
                case 800000033:
                    dropClass.FLD_MAGIC0 = World.GetValue(800000033, 3);
                    break;
                case 800000013:
                    dropClass.FLD_MAGIC0 = MagicQigongClass.GetMaGic0("drop");
                    break;
                case 800000047:
                    if (dropClass.FLD_MAGIC0 == 10)
                    {
                        var fLD_MAGIC10 = 0;
                        var num3 = RNG.Next(1, 100);
                        foreach (var item8 in dropClass.DropShuX)
                            if (num3 <= item8.Max)
                            {
                                fLD_MAGIC10 = RNG.Next(item8.ShuXMin, item8.ShuXMax - 1);
                                break;
                            }

                        dropClass.FLD_MAGIC0 = fLD_MAGIC10;
                    }
                    else if (dropClass.FLD_MAGIC0 == 0)
                    {
                        dropClass.FLD_MAGIC0 = RNG.Next(23, 51);
                    }

                    break;
                case 800000046:
                    if (dropClass.FLD_MAGIC0 == 0) dropClass.FLD_MAGIC0 = RNG.Next(1, 22);
                    break;
                case 1000000321:
                    dropClass.FLD_MAGIC0 = RNG.Next(1001, 2999);
                    dropClass.FLD_MAGIC1 = RNG.Next(10, 50);
                    break;
                case 1000000323:
                    dropClass.FLD_MAGIC0 = RNG.Next(1001, 2999);
                    dropClass.FLD_MAGIC1 = RNG.Next(100, 150);
                    break;
                case 1000000325:
                    dropClass.FLD_MAGIC0 = RNG.Next(1001, 2999);
                    dropClass.FLD_MAGIC1 = RNG.Next(400, 699);
                    break;
                default:
                {
                    if (dropClass.FLD_MAGIC0 != 10) break;
                    var fLD_MAGIC7 = 0;
                    var num11 = RNG.Next(1, 100);
                    foreach (var item9 in dropClass.DropShuX)
                        if (num11 <= item9.Max)
                        {
                            fLD_MAGIC7 = RNG.Next(item9.ShuXMin, item9.ShuXMax - 1);
                            break;
                        }

                    dropClass.FLD_MAGIC0 = fLD_MAGIC7;
                    break;
                }
                case 1000000327:
                    dropClass.FLD_MAGIC0 = RNG.Next(1001, 2999);
                    dropClass.FLD_MAGIC1 = RNG.Next(2000, 2499);
                    break;
                case 800000062:
                    if (dropClass.FLD_MAGIC0 == 10)
                    {
                        var fLD_MAGIC6 = 0;
                        var num10 = RNG.Next(1, 100);
                        foreach (var item10 in dropClass.DropShuX)
                            if (num10 <= item10.Max)
                            {
                                fLD_MAGIC6 = RNG.Next(item10.ShuXMin, item10.ShuXMax - 1);
                                break;
                            }

                        dropClass.FLD_MAGIC0 = fLD_MAGIC6;
                    }
                    else if (dropClass.FLD_MAGIC0 == 0)
                    {
                        dropClass.FLD_MAGIC0 = World.GetValue(dropClass.FLD_PID, 3);
                    }

                    break;
                case 800000061:
                    if (dropClass.FLD_MAGIC0 == 10)
                    {
                        var fLD_MAGIC = 0;
                        var num6 = RNG.Next(1, 100);
                        foreach (var item2 in dropClass.DropShuX)
                            if (num6 <= item2.Max)
                            {
                                fLD_MAGIC = RNG.Next(item2.ShuXMin, item2.ShuXMax - 1);
                                break;
                            }

                        dropClass.FLD_MAGIC0 = fLD_MAGIC;
                    }
                    else if (dropClass.FLD_MAGIC0 == 0)
                    {
                        dropClass.FLD_MAGIC0 = World.GetValue(dropClass.FLD_PID, 3);
                    }

                    break;
            }

            RoiRaVatPham(dropClass, yxqname);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "BaoSuat_VatPham   error：" + ex);
        }
    }

    public void AbnormalStatusList()
    {
        if (TrangThai_BatThuong == null || TrangThai_BatThuong.Count == 0) return;
        var queue = Queue.Synchronized(new Queue());
        try
        {
            foreach (var value in TrangThai_BatThuong.Values) queue.Enqueue(value);
            while (queue.Count > 0)
            {
                var TrangThai_BatThuongClass = (X_Di_Thuong_Trang_Thai_Loai)queue.Dequeue();
                TrangThai_BatThuongClass.ThoiGianKetThucSuKien();
                TrangThai_BatThuong?.Remove(TrangThai_BatThuongClass.FLD_PID);
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "MPCTrangThai_BatThuong列表error![" + FLD_INDEX + "]-[" + Name + "]" + ex.Message);
        }
    }

    public void EndAbnormalBloodDropStatusList()
    {
        if (TrangThai_MatMau_BatThuong == null) return;
        var queue = Queue.Synchronized(new Queue());
        try
        {
            foreach (var value in TrangThai_MatMau_BatThuong.Values) queue.Enqueue(value);
            while (queue.Count > 0)
            {
                if (World.jlMsg == 1) Form1.WriteLine(0, "TrangThai_MatMau_BatThuong列表");
                var TrangThai_MatMau_BatThuong类 = (X_Di_Thuong_Mat_Mau_Trang_Thai_Loai)queue.Dequeue();
                TrangThai_MatMau_BatThuong类.ThoiGianKetThucSuKien();
                TrangThai_MatMau_BatThuong?.Remove(TrangThai_MatMau_BatThuong类.FLD_PID);
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1,
                "MPCTrangThai_MatMau_BatThuong列表列表error![" + FLD_INDEX + "]-[" + Name + "]" + ex.Message);
        }
        finally
        {
            queue = null;
        }
    }

    public bool LookInNpc(int far_, NpcClass Npc)
    {
        if (Npc.Rxjh_Map != Rxjh_Map) return false;
        var num = Npc.Rxjh_X - Rxjh_X;
        var num2 = Npc.Rxjh_Y - Rxjh_Y;
        return (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far_;
    }

    public bool FindPlayers(int far_, Players Playe)
    {
        if (Playe.NhanVatToaDo_BanDo != Rxjh_Map) return false;
        if (Playe.NhanVatToaDo_BanDo == 7101) far_ = 1000;
        var num = Playe.NhanVatToaDo_X - Rxjh_X;
        var num2 = Playe.NhanVatToaDo_Y - Rxjh_Y;
        return (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far_;
    }

    public bool FindPlayers(int far_, X_Linh_Thu_Loai Playe)
    {
        if (Playe.NhanVatToaDo_MAP != Rxjh_Map) return false;
        var num = Playe.NhanVatToaDo_X - Rxjh_X;
        var num2 = Playe.NhanVatToaDo_Y - Rxjh_Y;
        return (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far_;
    }

    public bool GetRangePlayers()
    {
        try
        {
            foreach (var value in _PlayList.Values)
                if (value.NhanVat_HP > 0 && FindPlayers(100, value))
                {
                    Play_Add(value, 0);
                    return true;
                }
        }
        catch (Exception)
        {
            return false;
        }

        return false;
    }

    public void QuangBaSoLieu(byte[] data, int length)
    {
        try
        {
            foreach (var value2 in _PlayList.Values)
            {
                if (value2.Client != null)
                {
                    if (value2.Client.Running)
                    {
                        value2.Client.Send_Map_Data(data, length);
                    }
                    else
                    {
                        value2.Client.Dispose();
                        _PlayList.Remove(value2.CharacterFullServerID);
                    }
                }

                if (!World.allConnectedChars.TryGetValue(value2.Client.WorldId, out var _))
                {
                    Form1.WriteLine(2,
                        "NPC   QuangBaSoLieu   删除卡号人物：[" + value2.Userid + "]   [" + value2.UserName + "]");
                    value2.Client?.Dispose();
                    _PlayList.Remove(value2.CharacterFullServerID);
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "NPCQuangBaSoLieu2   error2：" + ex);
        }
    }

    public void DiemVuongBao_ThucThi()
    {
        var players = World.KiemTra_PlayerWorld_ID(MeegoDebuff_DVB_CasterSessionID);
        if (players == null || !FindPlayers(400, players)) return;
        var num = (int)(MeegoDebuff_DVB_DamageCount * (1.0 + players.ThanNu_ThiDocBaoPhat));
        if (num < 1) return;
        var distance = 40 + (int)players.ThanNu_HacHoaManKhai;
        var list = DiemVuongBao_CheckRadius_NPC2(this, distance);
        list.Add(this);
        foreach (var npcClass in list)
        {
            if (num <= 0) num = 1;
            if (npcClass.Rxjh_HP > num)
            {
                npcClass.Play_Add(players, num);
                npcClass.Rxjh_HP -= num;
            }
            else
            {
                npcClass.Play_Add(players, npcClass.Rxjh_HP);
                npcClass.Rxjh_HP = 0;
            }

            npcClass.NPCSend_FightsBack_Packet(num, 0);
            if (npcClass.Rxjh_HP <= 0 && !npcClass.NPCDeath)
            {
                MeegoDebuff_DVB_DamageCount = 0;
                MeegoDebuff_DVB_CasterSessionID = 0;
                double num4 = (uint)npcClass.ThuDuocTien();
                double num2 = npcClass.ThuDuocKinhNghiem();
                double num3 = npcClass.ThuDuocLichLuyen();
                double ThangThienLichLuyen4 = npcClass.ThuDuocThangThienLichLuyen();
                if (players.TrungCapPhuHon_KyDuyen != 0 && RNG.Next(1, 100) <= players.TrungCapPhuHon_KyDuyen)
                {
                    num2 *= 2.0;
                    players.ShowBigPrint(players.CharacterFullServerID, 403);
                }

                players.PhanPhoiKinhNghiemLichLuyenTienTai(npcClass, num2, num3, num4, ThangThienLichLuyen4);
                npcClass.GuiDiTuVongSoLieu(players.CharacterFullServerID);
            }
        }
    }

    public void NPCSend_FightsBack_Packet(int damage, int playerid)
    {
        var array = Converter.HexStringToByte("AA551B00A42789000C002C0100000F0000000100000000000000000000000055AA");
        System.Buffer.BlockCopy(Buffer.GetBytes(FLD_INDEX), 0, array, 4, 2);
        System.Buffer.BlockCopy(Buffer.GetBytes(playerid), 0, array, 10, 2);
        System.Buffer.BlockCopy(Buffer.GetBytes(damage), 0, array, 18, 2);
        SendMultiplePacketsOfCurrentRangeBroadcastData(array, array.Length);
    }

    public List<NpcClass> DiemVuongBao_CheckRadius_NPC2(NpcClass Npc, int distance = 25)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "NpcClass_AtkAoe_CheckRadius_NPC2");
        try
        {
            var list = new List<NpcClass>();
            var num = 1;
            foreach (var class2 in MapClass.GetnpcTemplate(Rxjh_Map).Values)
            {
                if (num >= 5) break;
                if (!class2.NPCDeath && class2.IsNpc == 0 && LookInNpc(distance, class2) &&
                    class2.FLD_INDEX != FLD_INDEX)
                {
                    list.Add(class2);
                    num++;
                }
            }

            return list;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "DiemVuongBao_CheckRadius_NPC2 Error：" + ex);
            return null;
        }
    }
}