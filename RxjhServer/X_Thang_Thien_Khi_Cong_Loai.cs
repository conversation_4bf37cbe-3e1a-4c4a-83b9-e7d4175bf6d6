namespace RxjhServer;

public class X_Thang_Thien_<PERSON>hi_Cong_Loai
{
    public X_Thang_Thien_Khi_Cong_Loai()
    {
        气功_byte = new byte[4];
    }

    public X_Thang_Thien_Khi_Cong_Loai(byte[] byte_0)
    {
        气功_byte = byte_0;
    }

    public byte[] 气功_byte { get; set; }

    public int KhiCongID
    {
        get => Buffer.ToInt16(气功_byte, 0);
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, 气功_byte, 0, 2);
    }

    public int KhiCong_SoLuong
    {
        get => Buffer.ToInt16(气功_byte, 2);
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, 气功_byte, 2, 2);
    }
}