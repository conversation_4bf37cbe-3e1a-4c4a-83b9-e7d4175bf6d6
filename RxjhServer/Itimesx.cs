namespace RxjhServer;

public class Itimesx
{
    public Itimesx(byte[] byte_0)
    {
        ThuocTinhGiaiDoan(byte_0);
    }

    public int SoLuong { get; set; }

    public int ThuocTinhLoaiHinh { get; set; }

    public int KhiCongThuocTinhLoaiHinh { get; set; }

    public int ThuocTinhSoLuong { get; set; }

    public void ThuocTinhGiaiDoan(byte[] byte_0)
    {
        var text = Buffer.ToInt32(byte_0, 0).ToString();
        switch (text.Length)
        {
            case 8:
                SoLuong = 1;
                ThuocTinhLoaiHinh = int.Parse(text.Substring(0, 1));
                if (ThuocTinhLoaiHinh == 8) KhiCongThuocTinhLoaiHinh = int.Parse(text.Substring(4, 2));
                if (World.CoHoTro_MoRongChuSo_VatPham_ThuocTinhHayKhong == 0)
                    ThuocTinhSoLuong = int.Parse(text.Substring(6, 2));
                else
                    ThuocTinhSoLuong = int.Parse(text) - int.Parse(text.Substring(0, 1)) * 10000000;
                break;
            case 9:
                SoLuong = 1;
                ThuocTinhLoaiHinh = int.Parse(text.Substring(0, 2));
                if (World.CoHoTro_MoRongChuSo_VatPham_ThuocTinhHayKhong == 0)
                    ThuocTinhSoLuong = int.Parse(text.Substring(7, 2));
                else
                    ThuocTinhSoLuong = int.Parse(text) - int.Parse(text.Substring(0, 2)) * 10000000;
                break;
            case 10:
                SoLuong = 1;
                ThuocTinhLoaiHinh = int.Parse(text.Substring(0, 2));
                if (World.CoHoTro_MoRongChuSo_VatPham_ThuocTinhHayKhong == 0)
                    ThuocTinhSoLuong = int.Parse(text.Substring(7, 2));
                else
                    ThuocTinhSoLuong = int.Parse(text) - int.Parse(text.Substring(0, 3)) * 10000000;
                break;
        }
    }
}