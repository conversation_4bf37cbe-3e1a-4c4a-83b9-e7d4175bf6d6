using System;
using System.Timers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class X_<PERSON>_Thuong_Trang_Thai_Cong_Kich_Loai : IDisposable
{
    public Players Play;

    public DateTime time;
    public System.Timers.Timer 人物状态;

    public X_Di_Thuong_Trang_Thai_Cong_Kich_Loai(Players Play_, int 时间, int 异常ID, int 异常数量)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường trạng thái loại -NEW 111");
        FLD_PID = 异常ID;
        time = DateTime.Now;
        time = time.AddMilliseconds(时间);
        Play = Play_;
        人物状态 = new System.Timers.Timer(时间);
        人物状态.Elapsed += 时间结束事件1;
        人物状态.Enabled = true;
        人物状态.AutoReset = false;
        Trang_thai_hieu_qua(FLD_PID, 1, 异常数量, 时间 / 1000);
    }

    public int FLD_PID { get; set; }

    public void Dispose()
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường trạng thái loại -Dispose 333");
        if (人物状态 != null)
        {
            人物状态.Enabled = false;
            人物状态.Close();
            人物状态.Dispose();
            人物状态 = null;
        }

        Play = null;
    }

    public void 时间结束事件1(object sender, ElapsedEventArgs e)
    {
        var num2 = 2;
        while (true)
        {
            switch (num2)
            {
                default:
                    if (World.jlMsg == 1)
                    {
                        num2 = 1;
                        continue;
                    }

                    break;
                case 1:
                    Form1.WriteLine(0, "Thời gian kết thúc sự kiện 1 333");
                    num2 = 0;
                    continue;
                case 0:
                    break;
            }

            break;
        }

        时间结束事件();
    }

    public void 时间结束事件()
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường trạng thái loại - Thời gian kết thúc sự kiện 333");
        try
        {
            if (Play != null)
            {
                if (FLD_PID == 1)
                {
                    Play.FLD_DuocPham_GiamBotCongKich += 0.2;
                    Play.UpdateMartialArtsAndStatus();
                }

                if (FLD_PID == 13)
                {
                    Play.FLD_DuocPham_GiamBotCongKich += 0.1;
                    Play.UpdateMartialArtsAndStatus();
                }

                Play.TrangThai_TanCong_BatThuong?.Remove(FLD_PID);
                Trang_thai_hieu_qua(FLD_PID, 0, 0, 0);
                Dispose();
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1,
                "Dị thường trạng thái loại Thời gian kết thúc sự kiệnPhạm sai lầm: 111 [" + FLD_PID + "]" + ex);
        }
        finally
        {
            Dispose();
        }
    }

    public void Trang_thai_hieu_qua(int 异常ID, int 开关, int 异常数量, int 时间)
    {
        var array = Converter.HexStringToByte(
            "AA5546003527401538008C0300002C0100000900000001000000000000006016A2496016A2492600000014000000000000008C030000E80300000900000001000000000000000000000055AA");
        System.Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 18, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(异常ID), 0, array, 58, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(开关), 0, array, 22, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(开关), 0, array, 62, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(时间), 0, array, 38, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(异常数量), 0, array, 42, 4);
        if (Play != null)
        {
            System.Buffer.BlockCopy(BitConverter.GetBytes(Play.CharacterFullServerID), 0, array, 14, 2);
            System.Buffer.BlockCopy(BitConverter.GetBytes(Play.CharacterFullServerID), 0, array, 4, 2);
            Play.Client?.SendMultiplePackage(array, array.Length);
            Play.SendMultiplePacketsOfCurrentRangeBroadcastData(array, array.Length);
        }
    }
}