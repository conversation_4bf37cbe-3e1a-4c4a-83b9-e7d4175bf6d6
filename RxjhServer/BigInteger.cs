using System;

namespace RxjhServer;

public class BigInteger
{
    private const int maxLength = 70;

    public static readonly int[] primesBelow2000 = new int[303]
    {
        2, 3, 5, 7, 11, 13, 17, 19, 23, 29,
        31, 37, 41, 43, 47, 53, 59, 61, 67, 71,
        73, 79, 83, 89, 97, 101, 103, 107, 109, 113,
        127, 131, 137, 139, 149, 151, 157, 163, 167, 173,
        179, 181, 191, 193, 197, 199, 211, 223, 227, 229,
        233, 239, 241, 251, 257, 263, 269, 271, 277, 281,
        283, 293, 307, 311, 313, 317, 331, 337, 347, 349,
        353, 359, 367, 373, 379, 383, 389, 397, 401, 409,
        419, 421, 431, 433, 439, 443, 449, 457, 461, 463,
        467, 479, 487, 491, 499, 503, 509, 521, 523, 541,
        547, 557, 563, 569, 571, 577, 587, 593, 599, 601,
        607, 613, 617, 619, 631, 641, 643, 647, 653, 659,
        661, 673, 677, 683, 691, 701, 709, 719, 727, 733,
        739, 743, 751, 757, 761, 769, 773, 787, 797, 809,
        811, 821, 823, 827, 829, 839, 853, 857, 859, 863,
        877, 881, 883, 887, 907, 911, 919, 929, 937, 941,
        947, 953, 967, 971, 977, 983, 991, 997, 1009, 1013,
        1019, 1021, 1031, 1033, 1039, 1049, 1051, 1061, 1063, 1069,
        1087, 1091, 1093, 1097, 1103, 1109, 1117, 1123, 1129, 1151,
        1153, 1163, 1171, 1181, 1187, 1193, 1201, 1213, 1217, 1223,
        1229, 1231, 1237, 1249, 1259, 1277, 1279, 1283, 1289, 1291,
        1297, 1301, 1303, 1307, 1319, 1321, 1327, 1361, 1367, 1373,
        1381, 1399, 1409, 1423, 1427, 1429, 1433, 1439, 1447, 1451,
        1453, 1459, 1471, 1481, 1483, 1487, 1489, 1493, 1499, 1511,
        1523, 1531, 1543, 1549, 1553, 1559, 1567, 1571, 1579, 1583,
        1597, 1601, 1607, 1609, 1613, 1619, 1621, 1627, 1637, 1657,
        1663, 1667, 1669, 1693, 1697, 1699, 1709, 1721, 1723, 1733,
        1741, 1747, 1753, 1759, 1777, 1783, 1787, 1789, 1801, 1811,
        1823, 1831, 1847, 1861, 1867, 1871, 1873, 1877, 1879, 1889,
        1901, 1907, 1913, 1931, 1933, 1949, 1951, 1973, 1979, 1987,
        1993, 1997, 1999
    };

    private readonly uint[] data;

    public int dataLength;

    public BigInteger()
    {
        data = new uint[70];
        dataLength = 1;
    }

    public BigInteger(long long_0)
    {
        data = new uint[70];
        var num2 = long_0;
        dataLength = 0;
        while (long_0 != 0L && dataLength < 70)
        {
            data[dataLength] = (uint)(long_0 & 0xFFFFFFFFu);
            long_0 >>= 32;
            dataLength++;
        }

        if (num2 > 0)
        {
            if (long_0 != 0L || (data[69] & 0x80000000u) != 0)
                throw new ArithmeticException("Positive overflow in constructor.");
        }
        else if (num2 < 0 && (long_0 != -1 || (data[dataLength - 1] & 0x80000000u) == 0))
        {
            throw new ArithmeticException("Negative underflow in constructor.");
        }

        if (dataLength == 0) dataLength = 1;
    }

    public BigInteger(ulong ulong_0)
    {
        data = new uint[70];
        dataLength = 0;
        while (ulong_0 != 0L && dataLength < 70)
        {
            data[dataLength] = (uint)(ulong_0 & 0xFFFFFFFFu);
            ulong_0 >>= 32;
            dataLength++;
        }

        if (ulong_0 == 0L && (data[69] & 0x80000000u) == 0)
        {
            if (dataLength == 0) dataLength = 1;
            return;
        }

        throw new ArithmeticException("Positive overflow in constructor.");
    }

    public BigInteger(BigInteger bigInteger_0)
    {
        data = new uint[70];
        dataLength = bigInteger_0.dataLength;
        for (var i = 0; i < dataLength; i++) data[i] = bigInteger_0.data[i];
    }

    public BigInteger(string string_0, int int_0)
    {
        var bigInteger_ = new BigInteger(1L);
        var bigInteger = new BigInteger();
        string_0 = string_0.ToUpper().Trim();
        var num2 = 0;
        if (string_0[0] == '-') num2 = 1;
        var num3 = string_0.Length - 1;
        while (num3 >= num2)
        {
            int num4 = string_0[num3];
            num4 = num4 >= 48 && num4 <= 57 ? num4 - 48 : num4 < 65 || num4 > 90 ? 9999999 : num4 - 65 + 10;
            if (num4 < int_0)
            {
                if (string_0[0] == '-') num4 = -num4;
                bigInteger += bigInteger_ * num4;
                if (num3 - 1 >= num2) bigInteger_ *= int_0;
                num3--;
                continue;
            }

            throw new ArithmeticException("Invalid string in constructor.");
        }

        if (string_0[0] == '-')
        {
            if ((bigInteger.data[69] & 0x80000000u) == 0)
                throw new ArithmeticException("Negative underflow in constructor.");
        }
        else if ((bigInteger.data[69] & 0x80000000u) != 0)
        {
            throw new ArithmeticException("Positive overflow in constructor.");
        }

        data = new uint[70];
        for (var i = 0; i < bigInteger.dataLength; i++) data[i] = bigInteger.data[i];
        dataLength = bigInteger.dataLength;
    }

    public BigInteger(byte[] byte_0)
    {
        dataLength = byte_0.Length >> 2;
        var num2 = byte_0.Length & 3;
        if (num2 != 0) dataLength++;
        if (dataLength > 70) throw new ArithmeticException("Byte overflow in constructor.");
        data = new uint[70];
        var num3 = byte_0.Length - 1;
        var num4 = 0;
        while (num3 >= 3)
        {
            data[num4] = (uint)((byte_0[num3 - 3] << 24) + (byte_0[num3 - 2] << 16) + (byte_0[num3 - 1] << 8) +
                                byte_0[num3]);
            num3 -= 4;
            num4++;
        }

        switch (num2)
        {
            case 1:
                data[dataLength - 1] = byte_0[0];
                break;
            case 2:
                data[dataLength - 1] = (uint)((byte_0[0] << 8) + byte_0[1]);
                break;
            case 3:
                data[dataLength - 1] = (uint)((byte_0[0] << 16) + (byte_0[1] << 8) + byte_0[2]);
                break;
        }

        while (dataLength > 1 && data[dataLength - 1] == 0) dataLength--;
    }

    public BigInteger(byte[] byte_0, int int_0)
    {
        dataLength = int_0 >> 2;
        var num2 = int_0 & 3;
        if (num2 != 0) dataLength++;
        if (dataLength <= 70 && int_0 <= byte_0.Length)
        {
            data = new uint[70];
            var num3 = int_0 - 1;
            var num4 = 0;
            while (num3 >= 3)
            {
                data[num4] = (uint)((byte_0[num3 - 3] << 24) + (byte_0[num3 - 2] << 16) + (byte_0[num3 - 1] << 8) +
                                    byte_0[num3]);
                num3 -= 4;
                num4++;
            }

            switch (num2)
            {
                case 1:
                    data[dataLength - 1] = byte_0[0];
                    break;
                case 2:
                    data[dataLength - 1] = (uint)((byte_0[0] << 8) + byte_0[1]);
                    break;
                case 3:
                    data[dataLength - 1] = (uint)((byte_0[0] << 16) + (byte_0[1] << 8) + byte_0[2]);
                    break;
            }

            if (dataLength == 0) dataLength = 1;
            while (dataLength > 1 && data[dataLength - 1] == 0) dataLength--;
            return;
        }

        throw new ArithmeticException("Byte overflow in constructor.");
    }

    public BigInteger(uint[] uint_0)
    {
        dataLength = uint_0.Length;
        if (dataLength > 70) throw new ArithmeticException("Byte overflow in constructor.");
        data = new uint[70];
        var num2 = dataLength - 1;
        var num3 = 0;
        while (num2 >= 0)
        {
            data[num3] = uint_0[num2];
            num2--;
            num3++;
        }

        while (dataLength > 1 && data[dataLength - 1] == 0) dataLength--;
    }

    public static implicit operator BigInteger(long long_0)
    {
        return new BigInteger(long_0);
    }

    public static implicit operator BigInteger(ulong ulong_0)
    {
        return new BigInteger(ulong_0);
    }

    public static implicit operator BigInteger(int int_0)
    {
        return new BigInteger(int_0);
    }

    public static implicit operator BigInteger(uint uint_0)
    {
        return new BigInteger((ulong)uint_0);
    }

    public static BigInteger operator +(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        var num = 0L;
        var num2 = 0;
        var num3 = 0;
        var bigInteger = new BigInteger();
        bigInteger.dataLength = bigInteger_0.dataLength > bigInteger_1.dataLength
            ? bigInteger_0.dataLength
            : bigInteger_1.dataLength;
        num = 0L;
        for (num3 = 0; num3 < bigInteger.dataLength; num3++)
        {
            var num4 = bigInteger_0.data[num3] + (long)bigInteger_1.data[num3] + num;
            num = num4 >> 32;
            bigInteger.data[num3] = (uint)(num4 & 0xFFFFFFFFu);
        }

        if (num != 0L && bigInteger.dataLength < 70)
        {
            bigInteger.data[bigInteger.dataLength] = (uint)num;
            bigInteger.dataLength++;
        }

        while (bigInteger.dataLength > 1 && bigInteger.data[bigInteger.dataLength - 1] == 0) bigInteger.dataLength--;
        num2 = 69;
        if ((bigInteger_0.data[69] & 0x80000000u) == (bigInteger_1.data[69] & 0x80000000u) &&
            (bigInteger.data[num2] & 0x80000000u) != (bigInteger_0.data[num2] & 0x80000000u))
            throw new ArithmeticException();
        return bigInteger;
    }

    public static BigInteger operator ++(BigInteger bigInteger_0)
    {
        var num2 = 0;
        var bigInteger = new BigInteger(bigInteger_0);
        var num3 = 1L;
        var num4 = 0;
        while (num3 != 0L && num4 < 70)
        {
            long num5 = bigInteger.data[num4];
            num5++;
            bigInteger.data[num4] = (uint)(num5 & 0xFFFFFFFFu);
            num3 = num5 >> 32;
            num4++;
        }

        if (num4 > bigInteger.dataLength)
            bigInteger.dataLength = num4;
        else
            while (bigInteger.dataLength > 1 && bigInteger.data[bigInteger.dataLength - 1] == 0)
                bigInteger.dataLength--;
        num2 = 69;
        if ((bigInteger_0.data[69] & 0x80000000u) == 0 &&
            (bigInteger.data[num2] & 0x80000000u) != (bigInteger_0.data[num2] & 0x80000000u))
            throw new ArithmeticException("Overflow in ++.");
        return bigInteger;
    }

    public static BigInteger operator -(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        var num2 = 0;
        var num3 = 0L;
        var num4 = 0L;
        var num5 = 0;
        var num6 = 0;
        var bigInteger = new BigInteger();
        while (true)
        {
            bigInteger.dataLength = bigInteger_0.dataLength <= bigInteger_1.dataLength
                ? bigInteger_1.dataLength
                : bigInteger_0.dataLength;
            num4 = 0L;
            num2 = 0;
            var flag = true;
            while (true)
            {
                var flag2 = true;
                while (true)
                {
                    if (num2 < bigInteger.dataLength)
                    {
                        num3 = bigInteger_0.data[num2] - (long)bigInteger_1.data[num2] - num4;
                        bigInteger.data[num2] = (uint)(num3 & 0xFFFFFFFFu);
                        while (true)
                        {
                            int num7;
                            switch (num3 < 0 ? 7 : 0)
                            {
                                case 19:
                                    break;
                                case 0:
                                    num7 = 0;
                                    goto IL_0176;
                                case 7:
                                case 14:
                                    num7 = 1;
                                    goto IL_0176;
                                default:
                                    bigInteger = new BigInteger();
                                    goto end_IL_02b6;
                                case 3:
                                    continue;
                                case 9:
                                case 21:
                                    goto IL_0195;
                                case 2:
                                case 23:
                                    goto IL_01d1;
                                case 5:
                                case 8:
                                    goto IL_0204;
                                case 17:
                                    goto IL_0215;
                                case 16:
                                    goto IL_021c;
                                case 11:
                                case 18:
                                case 24:
                                    goto IL_022a;
                                case 15:
                                    goto IL_0249;
                                case 22:
                                    goto IL_027b;
                                case 13:
                                    goto IL_0282;
                                case 4:
                                case 6:
                                case 10:
                                    goto IL_0291;
                                case 1:
                                case 20:
                                    goto end_IL_0050;
                                case 12:
                                    goto end_IL_02b6;
                                    IL_0176:
                                    num4 = num7;
                                    num2++;
                                    goto end_IL_0050;
                            }

                            break;
                        }

                        continue;
                    }

                    goto IL_0204;
                    IL_01d1:
                    if ((bigInteger.data[num6] & 0x80000000u) != (bigInteger_0.data[num6] & 0x80000000u)) goto IL_027b;
                    goto IL_01c7;
                    IL_027b:
                    throw new ArithmeticException();
                    IL_0204:
                    if (num4 != 0) goto IL_021c;
                    goto IL_0291;
                    IL_021c:
                    num5 = bigInteger.dataLength;
                    IL_022a:
                    for (; num5 < 70; num5++) bigInteger.data[num5] = uint.MaxValue;
                    IL_0282:
                    bigInteger.dataLength = 70;
                    IL_0291:
                    if (bigInteger.dataLength > 1) goto IL_0195;
                    goto IL_0215;
                    IL_0195:
                    if (bigInteger.data[bigInteger.dataLength - 1] == 0)
                    {
                        bigInteger.dataLength--;
                        goto IL_0291;
                    }

                    goto IL_0215;
                    IL_01c7:
                    return bigInteger;
                    IL_0215:
                    num6 = 69;
                    IL_0249:
                    if ((bigInteger_0.data[69] & 0x80000000u) != (bigInteger_1.data[69] & 0x80000000u)) goto IL_01d1;
                    goto IL_01c7;
                    continue;
                    end_IL_0050:
                    break;
                }

                continue;
                end_IL_02b6:
                break;
            }
        }
    }

    public static BigInteger operator --(BigInteger bigInteger_0)
    {
        var num2 = 0;
        var num3 = 0L;
        var bigInteger = new BigInteger(bigInteger_0);
        var flag = true;
        var num4 = 0;
        while (flag && num4 < 70)
        {
            num3 = bigInteger.data[num4];
            num3--;
            bigInteger.data[num4] = (uint)(num3 & 0xFFFFFFFFu);
            if (num3 >= 0) flag = false;
            num4++;
        }

        if (num4 > bigInteger.dataLength) bigInteger.dataLength = num4;
        while (bigInteger.dataLength > 1 && bigInteger.data[bigInteger.dataLength - 1] == 0) bigInteger.dataLength--;
        num2 = 69;
        if ((bigInteger_0.data[69] & 0x80000000u) != 0 &&
            (bigInteger.data[num2] & 0x80000000u) != (bigInteger_0.data[num2] & 0x80000000u))
            throw new ArithmeticException("Underflow in --.");
        return bigInteger;
    }

    public static BigInteger operator *(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        BigInteger bigInteger = null;
        var num3 = 0uL;
        var num4 = 0;
        var num5 = 0;
        var flag = false;
        var num6 = 0;
        var num7 = 69;
        var flag2 = false;
        var flag3 = false;
        try
        {
            if ((bigInteger_0.data[num7] & 0x80000000u) != 0)
            {
                flag2 = true;
                bigInteger_0 = -bigInteger_0;
            }

            if ((bigInteger_1.data[num7] & 0x80000000u) != 0)
            {
                flag3 = true;
                bigInteger_1 = -bigInteger_1;
            }
        }
        catch (Exception)
        {
        }

        bigInteger = new BigInteger();
        try
        {
            var flag4 = true;
            while (true)
            {
                IL_007a:
                var num8 = 0;
                var flag5 = true;
                while (true)
                {
                    switch (num8 >= bigInteger_0.dataLength ? 13 : 6)
                    {
                        case 9:
                            goto end_IL_0199;
                        case 6:
                            if (bigInteger_0.data[num8] != 0) goto case 5;
                            goto case 7;
                        case 5:
                            num3 = 0uL;
                            num4 = 0;
                            num5 = num8;
                            goto case 2;
                        case 2:
                        case 4:
                        case 11:
                            while (num4 < bigInteger_1.dataLength)
                            {
                                var num2 = (ulong)(bigInteger_0.data[num8] * (long)bigInteger_1.data[num4] +
                                                   bigInteger.data[num5]) + num3;
                                bigInteger.data[num5] = (uint)(num2 & 0xFFFFFFFFu);
                                num3 = num2 >> 32;
                                num4++;
                                num5++;
                            }

                            goto case 0;
                        case 0:
                        case 12:
                            if (num3 != 0) goto case 1;
                            goto case 7;
                        case 1:
                            bigInteger.data[num8 + bigInteger_1.dataLength] = (uint)num3;
                            goto case 7;
                        case 7:
                            num8++;
                            continue;
                        case 13:
                            goto end_IL_0199;
                        case 3:
                        case 8:
                        case 10:
                            continue;
                    }

                    goto IL_007a;
                    continue;
                    end_IL_0199:
                    break;
                }

                break;
            }
        }
        catch (Exception)
        {
            throw new ArithmeticException("Multiplication overflow.");
        }

        bigInteger.dataLength = bigInteger_0.dataLength + bigInteger_1.dataLength;
        if (bigInteger.dataLength > 70) bigInteger.dataLength = 70;
        while (bigInteger.dataLength > 1 && bigInteger.data[bigInteger.dataLength - 1] == 0) bigInteger.dataLength--;
        if ((bigInteger.data[num7] & 0x80000000u) != 0)
        {
            if (flag2 != flag3 && bigInteger.data[num7] == 2147483648u)
            {
                if (bigInteger.dataLength == 1) return bigInteger;
                flag = true;
                for (num6 = 0; num6 < bigInteger.dataLength - 1 && flag; num6++)
                    if (bigInteger.data[num6] != 0)
                        flag = false;
                if (flag) return bigInteger;
            }

            throw new ArithmeticException("Multiplication overflow.");
        }

        if (flag2 != flag3) return -bigInteger;
        return bigInteger;
    }

    public static BigInteger operator <<(BigInteger bigInteger_0, int int_0)
    {
        var bigInteger = new BigInteger(bigInteger_0);
        bigInteger.dataLength = smethod_0(bigInteger.data, int_0);
        return bigInteger;
    }

    private static int smethod_0(uint[] uint_0, int int_0)
    {
        var num2 = 0;
        var num3 = 0uL;
        var num4 = 0;
        var num5 = 32;
        var num6 = uint_0.Length;
        while (true)
        {
            var flag = true;
            while (true)
            {
                IL_01c0:
                if (num6 > 1) goto IL_0036;
                IL_01b0:
                while (true)
                {
                    IL_01b0_2:
                    num2 = int_0;
                    var flag2 = true;
                    while (true)
                    {
                        switch (num2 <= 0 ? 17 : 9)
                        {
                            case 1:
                            case 13:
                                break;
                            case 9:
                                if (num2 < num5) goto case 15;
                                goto case 3;
                            case 15:
                                num5 = num2;
                                goto case 3;
                            case 3:
                                num3 = 0uL;
                                num4 = 0;
                                goto case 2;
                            case 2:
                            case 4:
                            case 18:
                                for (; num4 < num6; num4++)
                                {
                                    var num7 = (ulong)uint_0[num4] << num5;
                                    num7 |= num3;
                                    uint_0[num4] = (uint)(num7 & 0xFFFFFFFFu);
                                    num3 = num7 >> 32;
                                }

                                goto case 10;
                            case 10:
                            case 14:
                                if (num3 != 0) goto case 0;
                                goto case 20;
                            case 0:
                            case 5:
                                if (num6 + 1 <= uint_0.Length) goto case 7;
                                goto case 20;
                            case 7:
                                uint_0[num6] = (uint)num3;
                                num6++;
                                goto case 20;
                            case 20:
                                num2 -= num5;
                                continue;
                            default:
                                num5 = 32;
                                num6 = uint_0.Length;
                                goto IL_01c0;
                            case 17:
                                return num6;
                            case 8:
                            case 11:
                            case 16:
                                continue;
                            case 19:
                                goto IL_01b0_2;
                            case 6:
                            case 12:
                            case 21:
                                goto IL_01c0;
                        }

                        break;
                    }

                    break;
                }

                IL_0036:
                if (uint_0[num6 - 1] == 0) break;
                goto IL_01b0;
            }

            num6--;
        }
    }

    public static BigInteger operator >> (BigInteger bigInteger_0, int int_0)
    {
        var num2 = 0u;
        var num3 = 0;
        var num4 = 0;
        var flag = true;
        while (true)
        {
            var bigInteger = new BigInteger(bigInteger_0);
            bigInteger.dataLength = smethod_1(bigInteger.data, int_0);
            var flag2 = true;
            while (true)
            {
                IL_002d:
                if ((bigInteger_0.data[69] & 0x80000000u) != 0)
                {
                    var flag3 = true;
                    while (true)
                    {
                        IL_0050:
                        num4 = 69;
                        while (true)
                        {
                            var flag4 = true;
                            while (num4 < bigInteger.dataLength)
                            {
                                var flag5 = true;
                                while (true)
                                {
                                    IL_006d:
                                    num2 = 2147483648u;
                                    num3 = 0;
                                    var flag6 = true;
                                    while (true)
                                    {
                                        switch (num3 >= 32 ? 4 : 11)
                                        {
                                            case 3:
                                                goto IL_002d;
                                            case 9:
                                                goto IL_0050;
                                            case 12:
                                                goto IL_006d;
                                            case 11:
                                                if ((bigInteger.data[bigInteger.dataLength - 1] & num2) == 0)
                                                    goto case 5;
                                                goto case 4;
                                            case 5:
                                                bigInteger.data[bigInteger.dataLength - 1] |= num2;
                                                num2 >>= 1;
                                                num3++;
                                                continue;
                                            case 4:
                                                bigInteger.dataLength = 70;
                                                goto IL_0196;
                                            case 6:
                                            case 7:
                                            case 10:
                                                continue;
                                            case 0:
                                            case 1:
                                            case 2:
                                                goto IL_015a;
                                            case 8:
                                                goto IL_0196;
                                        }

                                        break;
                                    }

                                    break;
                                }

                                goto end_IL_0184;
                                IL_015a: ;
                            }

                            bigInteger.data[num4] = uint.MaxValue;
                            num4--;
                            continue;
                            end_IL_0184:
                            break;
                        }

                        break;
                    }

                    break;
                }

                IL_0196:
                return bigInteger;
            }
        }
    }

    private static int smethod_1(uint[] uint_0, int int_0)
    {
        var num2 = 0uL;
        var num3 = 0;
        var num4 = 0;
        var num5 = 32;
        var num6 = 0;
        var num7 = uint_0.Length;
        while (true)
        {
            var flag = true;
            while (true)
            {
                IL_01ca:
                if (num7 > 1) goto IL_0039;
                IL_01ba:
                while (true)
                {
                    IL_01ba_2:
                    num4 = int_0;
                    var flag2 = true;
                    while (true)
                    {
                        switch (num4 <= 0 ? 7 : 12)
                        {
                            case 2:
                            case 20:
                                break;
                            case 12:
                                if (num4 < num5) goto case 10;
                                goto case 22;
                            case 10:
                                num5 = num4;
                                num6 = 32 - num5;
                                goto case 22;
                            case 22:
                                num2 = 0uL;
                                num3 = num7 - 1;
                                goto case 4;
                            case 4:
                            case 15:
                            case 19:
                                while (num3 >= 0)
                                {
                                    var num8 = (ulong)uint_0[num3] >> num5;
                                    num8 |= num2;
                                    num2 = (ulong)uint_0[num3] << num6;
                                    uint_0[num3] = (uint)num8;
                                    num3--;
                                }

                                goto case 14;
                            case 14:
                                num4 -= num5;
                                continue;
                            default:
                                num5 = 32;
                                num6 = 0;
                                num7 = uint_0.Length;
                                goto IL_01ca;
                            case 3:
                            case 6:
                            case 7:
                            case 21:
                                if (num7 > 1) goto case 8;
                                goto case 18;
                            case 8:
                            case 9:
                                if (uint_0[num7 - 1] == 0)
                                {
                                    num7--;
                                    goto case 3;
                                }

                                goto case 18;
                            case 18:
                                return num7;
                            case 0:
                            case 11:
                            case 17:
                                continue;
                            case 16:
                                goto IL_01ba_2;
                            case 1:
                            case 5:
                            case 13:
                                goto IL_01ca;
                        }

                        break;
                    }

                    break;
                }

                IL_0039:
                if (uint_0[num7 - 1] == 0) break;
                goto IL_01ba;
            }

            num7--;
        }
    }

    public static BigInteger operator ~(BigInteger bigInteger_0)
    {
        var bigInteger = new BigInteger(bigInteger_0);
        for (var i = 0; i < 70; i++) bigInteger.data[i] = ~bigInteger_0.data[i];
        bigInteger.dataLength = 70;
        while (bigInteger.dataLength > 1 && bigInteger.data[bigInteger.dataLength - 1] == 0) bigInteger.dataLength--;
        return bigInteger;
    }

    public static BigInteger operator -(BigInteger bigInteger_0)
    {
        BigInteger bigInteger = null;
        var num2 = 0L;
        var num3 = 0;
        var num4 = 0;
        if (bigInteger_0.dataLength == 1 && bigInteger_0.data[0] == 0) return new BigInteger();
        bigInteger = new BigInteger(bigInteger_0);
        for (num4 = 0; num4 < 70; num4++) bigInteger.data[num4] = ~bigInteger_0.data[num4];
        num2 = 1L;
        num3 = 0;
        while (num2 != 0L && num3 < 70)
        {
            long num5 = bigInteger.data[num3];
            num5++;
            bigInteger.data[num3] = (uint)(num5 & 0xFFFFFFFFu);
            num2 = num5 >> 32;
            num3++;
        }

        if ((bigInteger_0.data[69] & 0x80000000u) == (bigInteger.data[69] & 0x80000000u))
            throw new ArithmeticException("Overflow in negation.\n");
        bigInteger.dataLength = 70;
        while (bigInteger.dataLength > 1 && bigInteger.data[bigInteger.dataLength - 1] == 0) bigInteger.dataLength--;
        return bigInteger;
    }

    public static bool operator ==(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        return bigInteger_0.Equals(bigInteger_1);
    }

    public static bool operator !=(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        return !bigInteger_0.Equals(bigInteger_1);
    }

    public override bool Equals(object object_0)
    {
        var num = 0;
        var bigInteger = (BigInteger)object_0;
        if (dataLength != bigInteger.dataLength) return false;
        for (num = 0; num < dataLength; num++)
            if (data[num] != bigInteger.data[num])
                return false;
        return true;
    }

    public override int GetHashCode()
    {
        return ToString().GetHashCode();
    }

    public static bool operator >(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        var flag = true;
        while (true)
        {
            var num = 69;
            while (true)
            {
                IL_0200:
                if ((bigInteger_0.data[69] & 0x80000000u) != 0) goto IL_002f;
                IL_01ea:
                var flag2 = true;
                while (true)
                {
                    IL_0054:
                    if ((bigInteger_0.data[num] & 0x80000000u) == 0) goto IL_0072;
                    IL_01d4:
                    while (true)
                    {
                        int num2;
                        switch (bigInteger_0.dataLength <= bigInteger_1.dataLength ? 14 : 2)
                        {
                            case 3:
                            case 9:
                                goto IL_002f;
                            case 1:
                                goto IL_0054;
                            case 8:
                            case 20:
                                goto IL_0072;
                            case 2:
                                num2 = bigInteger_0.dataLength;
                                goto IL_01ca;
                            case 12:
                            case 14:
                                num2 = bigInteger_1.dataLength;
                                goto IL_01ca;
                            case 5:
                            case 13:
                            case 18:
                                if (num >= 0) goto case 6;
                                goto case 7;
                            case 6:
                            case 16:
                                if (bigInteger_0.data[num] == bigInteger_1.data[num])
                                {
                                    num--;
                                    goto case 5;
                                }

                                goto case 7;
                            case 7:
                            case 21:
                                if (num >= 0) goto case 10;
                                return false;
                            case 10:
                            case 17:
                                if (bigInteger_0.data[num] > bigInteger_1.data[num]) goto case 0;
                                return false;
                            case 0:
                                return true;
                            case 15:
                                continue;
                            case 11:
                                goto IL_01de;
                            case 19:
                                goto IL_01f4;
                            case 4:
                                goto IL_0200;
                                IL_01ca:
                                num = num2 - 1;
                                goto case 5;
                        }

                        break;
                    }

                    break;
                    IL_0072:
                    if ((bigInteger_1.data[num] & 0x80000000u) == 0) goto IL_01d4;
                    IL_01de:
                    return true;
                }

                break;
                IL_01f4:
                return false;
                IL_002f:
                if ((bigInteger_1.data[num] & 0x80000000u) != 0) goto IL_01ea;
                goto IL_01f4;
            }
        }
    }

    public static bool operator <(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        var flag = true;
        while (true)
        {
            var num = 69;
            while (true)
            {
                IL_0200:
                if ((bigInteger_0.data[69] & 0x80000000u) != 0) goto IL_002f;
                IL_01ea:
                var flag2 = true;
                while (true)
                {
                    IL_0054:
                    if ((bigInteger_0.data[num] & 0x80000000u) == 0) goto IL_0072;
                    IL_01d4:
                    while (true)
                    {
                        int num2;
                        switch (bigInteger_0.dataLength <= bigInteger_1.dataLength ? 14 : 2)
                        {
                            case 3:
                            case 9:
                                goto IL_002f;
                            case 1:
                                goto IL_0054;
                            case 8:
                            case 20:
                                goto IL_0072;
                            case 2:
                                num2 = bigInteger_0.dataLength;
                                goto IL_01ca;
                            case 12:
                            case 14:
                                num2 = bigInteger_1.dataLength;
                                goto IL_01ca;
                            case 5:
                            case 13:
                            case 18:
                                if (num >= 0) goto case 6;
                                goto case 7;
                            case 6:
                            case 16:
                                if (bigInteger_0.data[num] == bigInteger_1.data[num])
                                {
                                    num--;
                                    goto case 5;
                                }

                                goto case 7;
                            case 7:
                            case 21:
                                if (num >= 0) goto case 10;
                                return false;
                            case 10:
                            case 17:
                                if (bigInteger_0.data[num] < bigInteger_1.data[num]) goto case 0;
                                return false;
                            case 0:
                                return true;
                            case 15:
                                continue;
                            case 11:
                                goto IL_01de;
                            case 19:
                                goto IL_01f4;
                            case 4:
                                goto IL_0200;
                                IL_01ca:
                                num = num2 - 1;
                                goto case 5;
                        }

                        break;
                    }

                    break;
                    IL_0072:
                    if ((bigInteger_1.data[num] & 0x80000000u) == 0) goto IL_01d4;
                    IL_01de:
                    return false;
                }

                break;
                IL_01f4:
                return true;
                IL_002f:
                if ((bigInteger_1.data[num] & 0x80000000u) != 0) goto IL_01ea;
                goto IL_01f4;
            }
        }
    }

    public static bool operator >=(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        if (!(bigInteger_0 == bigInteger_1)) return bigInteger_0 > bigInteger_1;
        return true;
    }

    public static bool operator <=(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        if (!(bigInteger_0 == bigInteger_1)) return bigInteger_0 < bigInteger_1;
        return true;
    }

    private static void smethod_2(BigInteger bigInteger_0, BigInteger bigInteger_1, BigInteger bigInteger_2,
        BigInteger bigInteger_3)
    {
        BigInteger bigInteger = null;
        BigInteger bigInteger2 = null;
        var num = 0uL;
        var num3 = 0;
        BigInteger bigInteger3 = null;
        var num4 = 0;
        var num5 = 0;
        var flag = false;
        var num6 = 0;
        var num7 = 0;
        var num8 = 0uL;
        var num9 = 0uL;
        var num10 = 0;
        var num11 = 0;
        var num12 = 0;
        uint[] array = null;
        var num13 = 0uL;
        var array2 = new uint[70];
        var num14 = bigInteger_0.dataLength + 1;
        var array3 = new uint[num14];
        var num15 = 2147483648u;
        var num16 = bigInteger_1.data[bigInteger_1.dataLength - 1];
        var num17 = 0;
        var num18 = 0;
        while (num15 != 0 && (num16 & num15) == 0)
        {
            num17++;
            num15 >>= 1;
        }

        for (num10 = 0; num10 < bigInteger_0.dataLength; num10++) array3[num10] = bigInteger_0.data[num10];
        smethod_0(array3, num17);
        bigInteger_1 <<= num17;
        num7 = num14 - bigInteger_1.dataLength;
        num6 = num14 - 1;
        num8 = bigInteger_1.data[bigInteger_1.dataLength - 1];
        num13 = bigInteger_1.data[bigInteger_1.dataLength - 2];
        num12 = bigInteger_1.dataLength + 1;
        array = new uint[num12];
        while (num7 > 0)
        {
            var num2 = ((ulong)array3[num6] << 32) + array3[num6 - 1];
            num = num2 / num8;
            num9 = num2 % num8;
            flag = false;
            while (!flag)
            {
                flag = true;
                if (num == 4294967296L || num * num13 > (num9 << 32) + array3[num6 - 2])
                {
                    num--;
                    num9 += num8;
                    if (num9 < 4294967296L) flag = false;
                }
            }

            for (num11 = 0; num11 < num12; num11++) array[num11] = array3[num6 - num11];
            bigInteger2 = new BigInteger(array);
            for (bigInteger = bigInteger_1 * (long)num; bigInteger > bigInteger2; bigInteger -= bigInteger_1) num--;
            bigInteger3 = bigInteger2 - bigInteger;
            for (num4 = 0; num4 < num12; num4++) array3[num6 - num4] = bigInteger3.data[bigInteger_1.dataLength - num4];
            array2[num18++] = (uint)num;
            num6--;
            num7--;
        }

        bigInteger_2.dataLength = num18;
        num3 = 0;
        num5 = bigInteger_2.dataLength - 1;
        while (num5 >= 0)
        {
            bigInteger_2.data[num3] = array2[num5];
            num5--;
            num3++;
        }

        for (; num3 < 70; num3++) bigInteger_2.data[num3] = 0u;
        while (bigInteger_2.dataLength > 1 && bigInteger_2.data[bigInteger_2.dataLength - 1] == 0)
            bigInteger_2.dataLength--;
        if (bigInteger_2.dataLength == 0) bigInteger_2.dataLength = 1;
        bigInteger_3.dataLength = smethod_1(array3, num17);
        for (num3 = 0; num3 < bigInteger_3.dataLength; num3++) bigInteger_3.data[num3] = array3[num3];
        for (; num3 < 70; num3++) bigInteger_3.data[num3] = 0u;
    }

    private static void smethod_3(BigInteger bigInteger_0, BigInteger bigInteger_1, BigInteger bigInteger_2,
        BigInteger bigInteger_3)
    {
        var num = 0uL;
        var num2 = 0;
        var num3 = 0uL;
        var num4 = 0;
        var num5 = 0;
        var array = new uint[70];
        var num6 = 0;
        for (var i = 0; i < 70; i++) bigInteger_3.data[i] = bigInteger_0.data[i];
        bigInteger_3.dataLength = bigInteger_0.dataLength;
        while (bigInteger_3.dataLength > 1 && bigInteger_3.data[bigInteger_3.dataLength - 1] == 0)
            bigInteger_3.dataLength--;
        num = bigInteger_1.data[0];
        num2 = bigInteger_3.dataLength - 1;
        num3 = bigInteger_3.data[num2];
        if (num3 >= num)
        {
            var num7 = num3 / num;
            array[num6++] = (uint)num7;
            bigInteger_3.data[num2] = (uint)(num3 % num);
        }

        num2--;
        while (num2 >= 0)
        {
            num3 = ((ulong)bigInteger_3.data[num2 + 1] << 32) + bigInteger_3.data[num2];
            var num8 = num3 / num;
            array[num6++] = (uint)num8;
            bigInteger_3.data[num2 + 1] = 0u;
            bigInteger_3.data[num2--] = (uint)(num3 % num);
        }

        bigInteger_2.dataLength = num6;
        num4 = 0;
        num5 = bigInteger_2.dataLength - 1;
        while (num5 >= 0)
        {
            bigInteger_2.data[num4] = array[num5];
            num5--;
            num4++;
        }

        for (; num4 < 70; num4++) bigInteger_2.data[num4] = 0u;
        while (bigInteger_2.dataLength > 1 && bigInteger_2.data[bigInteger_2.dataLength - 1] == 0)
            bigInteger_2.dataLength--;
        if (bigInteger_2.dataLength == 0) bigInteger_2.dataLength = 1;
        while (bigInteger_3.dataLength > 1 && bigInteger_3.data[bigInteger_3.dataLength - 1] == 0)
            bigInteger_3.dataLength--;
    }

    public static BigInteger operator /(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        var bigInteger = new BigInteger();
        var bigInteger_2 = new BigInteger();
        var num2 = 69;
        var flag = false;
        var flag2 = false;
        var flag3 = true;
        while (true)
        {
            if ((bigInteger_0.data[69] & 0x80000000u) != 0) goto IL_0039;
            IL_0187:
            while (true)
            {
                IL_0187_2:
                var flag4 = true;
                while (true)
                {
                    IL_0054:
                    if ((bigInteger_1.data[num2] & 0x80000000u) != 0) goto IL_0071;
                    IL_0173:
                    while (true)
                    {
                        IL_0173_2:
                        var flag5 = true;
                        while (true)
                        {
                            switch (bigInteger_0 < bigInteger_1 ? 13 : 6)
                            {
                                case 8:
                                    break;
                                case 7:
                                    goto IL_0039;
                                case 11:
                                    goto IL_0054;
                                case 0:
                                    goto IL_0071;
                                default:
                                    bigInteger = new BigInteger();
                                    bigInteger_2 = new BigInteger();
                                    num2 = 69;
                                    flag = false;
                                    flag2 = false;
                                    break;
                                case 6:
                                    if (bigInteger_1.dataLength == 1) goto case 12;
                                    smethod_2(bigInteger_0, bigInteger_1, bigInteger, bigInteger_2);
                                    goto case 1;
                                case 12:
                                    smethod_3(bigInteger_0, bigInteger_1, bigInteger, bigInteger_2);
                                    goto case 1;
                                case 1:
                                case 4:
                                case 5:
                                    if (flag2 != flag) goto case 9;
                                    return bigInteger;
                                case 9:
                                    return -bigInteger;
                                case 13:
                                    return bigInteger;
                                case 3:
                                    continue;
                                case 10:
                                    goto IL_0173_2;
                                case 2:
                                    goto IL_0187_2;
                            }

                            break;
                        }

                        break;
                    }

                    break;
                    IL_0071:
                    bigInteger_1 = -bigInteger_1;
                    flag = true;
                    goto IL_0173;
                }

                break;
            }

            continue;
            IL_0039:
            bigInteger_0 = -bigInteger_0;
            flag2 = true;
            goto IL_0187;
        }
    }

    public static BigInteger operator %(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        var bigInteger_2 = new BigInteger();
        var bigInteger = new BigInteger(bigInteger_0);
        var num2 = 69;
        var flag = false;
        var flag2 = true;
        while (true)
        {
            if ((bigInteger_0.data[69] & 0x80000000u) != 0) goto IL_0037;
            IL_0176:
            while (true)
            {
                IL_0176_2:
                var flag3 = true;
                while (true)
                {
                    IL_0051:
                    if ((bigInteger_1.data[num2] & 0x80000000u) != 0) goto IL_006e;
                    IL_0162:
                    while (true)
                    {
                        IL_0162_2:
                        var flag4 = true;
                        while (true)
                        {
                            switch (bigInteger_0 < bigInteger_1 ? 13 : 6)
                            {
                                case 8:
                                    break;
                                case 7:
                                    goto IL_0037;
                                case 11:
                                    goto IL_0051;
                                case 0:
                                    goto IL_006e;
                                default:
                                    bigInteger_2 = new BigInteger();
                                    bigInteger = new BigInteger(bigInteger_0);
                                    num2 = 69;
                                    flag = false;
                                    break;
                                case 6:
                                    if (bigInteger_1.dataLength == 1) goto case 12;
                                    smethod_2(bigInteger_0, bigInteger_1, bigInteger_2, bigInteger);
                                    goto case 1;
                                case 12:
                                    smethod_3(bigInteger_0, bigInteger_1, bigInteger_2, bigInteger);
                                    goto case 1;
                                case 1:
                                case 4:
                                case 5:
                                    if (flag) goto case 9;
                                    return bigInteger;
                                case 9:
                                    return -bigInteger;
                                case 13:
                                    return bigInteger;
                                case 3:
                                    continue;
                                case 10:
                                    goto IL_0162_2;
                                case 2:
                                    goto IL_0176_2;
                            }

                            break;
                        }

                        break;
                    }

                    break;
                    IL_006e:
                    bigInteger_1 = -bigInteger_1;
                    goto IL_0162;
                }

                break;
            }

            continue;
            IL_0037:
            bigInteger_0 = -bigInteger_0;
            flag = true;
            goto IL_0176;
        }
    }

    public static BigInteger operator &(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        var num2 = 0;
        var num3 = 0;
        var bigInteger = new BigInteger();
        while (true)
        {
            int num4;
            switch (bigInteger_0.dataLength <= bigInteger_1.dataLength ? 9 : 3)
            {
                default:
                    bigInteger = new BigInteger();
                    continue;
                case 3:
                    num4 = bigInteger_0.dataLength;
                    goto IL_0116;
                case 4:
                case 9:
                    num4 = bigInteger_1.dataLength;
                    goto IL_0116;
                case 6:
                case 8:
                case 13:
                    for (; num2 < num3; num2++)
                    {
                        var num5 = bigInteger_0.data[num2] & bigInteger_1.data[num2];
                        bigInteger.data[num2] = num5;
                    }

                    goto case 5;
                case 5:
                    bigInteger.dataLength = 70;
                    goto case 1;
                case 1:
                case 7:
                case 10:
                    if (bigInteger.dataLength <= 1) break;
                    goto case 0;
                case 0:
                case 11:
                    if (bigInteger.data[bigInteger.dataLength - 1] == 0)
                    {
                        bigInteger.dataLength--;
                        goto case 1;
                    }

                    break;
                case 12:
                    continue;
                case 2:
                    break;
                    IL_0116:
                    num3 = num4;
                    num2 = 0;
                    goto case 6;
            }

            break;
        }

        return bigInteger;
    }

    public static BigInteger operator |(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        var num2 = 0;
        var num3 = 0;
        var bigInteger = new BigInteger();
        while (true)
        {
            int num4;
            switch (bigInteger_0.dataLength <= bigInteger_1.dataLength ? 9 : 3)
            {
                default:
                    bigInteger = new BigInteger();
                    continue;
                case 3:
                    num4 = bigInteger_0.dataLength;
                    goto IL_0116;
                case 4:
                case 9:
                    num4 = bigInteger_1.dataLength;
                    goto IL_0116;
                case 6:
                case 8:
                case 13:
                    for (; num2 < num3; num2++)
                    {
                        var num5 = bigInteger_0.data[num2] | bigInteger_1.data[num2];
                        bigInteger.data[num2] = num5;
                    }

                    goto case 5;
                case 5:
                    bigInteger.dataLength = 70;
                    goto case 1;
                case 1:
                case 7:
                case 10:
                    if (bigInteger.dataLength <= 1) break;
                    goto case 0;
                case 0:
                case 11:
                    if (bigInteger.data[bigInteger.dataLength - 1] == 0)
                    {
                        bigInteger.dataLength--;
                        goto case 1;
                    }

                    break;
                case 12:
                    continue;
                case 2:
                    break;
                    IL_0116:
                    num3 = num4;
                    num2 = 0;
                    goto case 6;
            }

            break;
        }

        return bigInteger;
    }

    public static BigInteger operator ^(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        var num2 = 0;
        var num3 = 0;
        var bigInteger = new BigInteger();
        while (true)
        {
            int num4;
            switch (bigInteger_0.dataLength <= bigInteger_1.dataLength ? 9 : 3)
            {
                default:
                    bigInteger = new BigInteger();
                    continue;
                case 3:
                    num4 = bigInteger_0.dataLength;
                    goto IL_0116;
                case 4:
                case 9:
                    num4 = bigInteger_1.dataLength;
                    goto IL_0116;
                case 6:
                case 8:
                case 13:
                    for (; num2 < num3; num2++)
                    {
                        var num5 = bigInteger_0.data[num2] ^ bigInteger_1.data[num2];
                        bigInteger.data[num2] = num5;
                    }

                    goto case 5;
                case 5:
                    bigInteger.dataLength = 70;
                    goto case 1;
                case 1:
                case 7:
                case 10:
                    if (bigInteger.dataLength <= 1) break;
                    goto case 0;
                case 0:
                case 11:
                    if (bigInteger.data[bigInteger.dataLength - 1] == 0)
                    {
                        bigInteger.dataLength--;
                        goto case 1;
                    }

                    break;
                case 12:
                    continue;
                case 2:
                    break;
                    IL_0116:
                    num3 = num4;
                    num2 = 0;
                    goto case 6;
            }

            break;
        }

        return bigInteger;
    }

    public BigInteger method_2(BigInteger bigInteger_0)
    {
        if (this > bigInteger_0) return new BigInteger(this);
        return new BigInteger(bigInteger_0);
    }

    public BigInteger method_3(BigInteger bigInteger_0)
    {
        if (this < bigInteger_0) return new BigInteger(this);
        return new BigInteger(bigInteger_0);
    }

    public BigInteger method_4()
    {
        if ((data[69] & 0x80000000u) != 0) return -this;
        return new BigInteger(this);
    }

    public override string ToString()
    {
        return ToString(10);
    }

    public string ToString(int int_0)
    {
        BigInteger bigInteger = null;
        string text = null;
        BigInteger bigInteger2 = null;
        string text2 = null;
        var flag = false;
        BigInteger bigInteger3 = null;
        BigInteger bigInteger4 = null;
        if (int_0 >= 2 && int_0 <= 36)
        {
            text2 = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            text = "";
            bigInteger = this;
            flag = false;
            if ((bigInteger.data[69] & 0x80000000u) != 0)
            {
                flag = true;
                try
                {
                    bigInteger = -bigInteger;
                }
                catch (Exception)
                {
                }
            }

            bigInteger3 = new BigInteger();
            bigInteger2 = new BigInteger();
            bigInteger4 = new BigInteger(int_0);
            if (bigInteger.dataLength == 1 && bigInteger.data[0] == 0)
            {
                text = "0";
            }
            else
            {
                while (bigInteger.dataLength > 1 || (bigInteger.dataLength == 1 && bigInteger.data[0] != 0))
                {
                    smethod_3(bigInteger, bigInteger4, bigInteger3, bigInteger2);
                    var text3 = bigInteger2.data[0] >= 10
                        ? text2[(int)(bigInteger2.data[0] - 10)] + text
                        : bigInteger2.data[0] + text;
                    text = text3;
                    bigInteger = bigInteger3;
                }

                if (flag) text = "-" + text;
            }

            return text;
        }

        throw new ArgumentException("Radix must be >= 2 and <= 36");
    }

    public string ToHexString()
    {
        var text = data[dataLength - 1].ToString("X");
        for (var num2 = dataLength - 2; num2 >= 0; num2--) text += data[num2].ToString("X8");
        return text;
    }

    public BigInteger modPow(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        if ((bigInteger_0.data[69] & 0x80000000u) != 0) throw new ArithmeticException("Positive exponents only.");
        BigInteger bigInteger = 1;
        var flag = false;
        BigInteger bigInteger2;
        if ((data[69] & 0x80000000u) != 0)
        {
            bigInteger2 = -this % bigInteger_1;
            flag = true;
        }
        else
        {
            bigInteger2 = this % bigInteger_1;
        }

        if ((bigInteger_1.data[69] & 0x80000000u) != 0) bigInteger_1 = -bigInteger_1;
        var bigInteger3 = new BigInteger();
        var num = bigInteger_1.dataLength << 1;
        bigInteger3.data[num] = 1u;
        bigInteger3.dataLength = num + 1;
        bigInteger3 /= bigInteger_1;
        var num2 = bigInteger_0.bitCount();
        var num3 = 0;
        for (var i = 0; i < bigInteger_0.dataLength; i++)
        {
            var num4 = 1u;
            for (var j = 0; j < 32; j++)
            {
                if ((bigInteger_0.data[i] & num4) != 0)
                    bigInteger = method_0(bigInteger * bigInteger2, bigInteger_1, bigInteger3);
                num4 <<= 1;
                bigInteger2 = method_0(bigInteger2 * bigInteger2, bigInteger_1, bigInteger3);
                if (bigInteger2.dataLength != 1 || bigInteger2.data[0] != 1)
                {
                    num3++;
                    if (num3 == num2) break;
                    continue;
                }

                if (flag && (bigInteger_0.data[0] & (true ? 1u : 0u)) != 0) return -bigInteger;
                return bigInteger;
            }
        }

        if (flag && (bigInteger_0.data[0] & (true ? 1u : 0u)) != 0) return -bigInteger;
        return bigInteger;
    }

    private BigInteger method_0(BigInteger bigInteger_0, BigInteger bigInteger_1, BigInteger bigInteger_2)
    {
        BigInteger bigInteger = null;
        var num = 0;
        BigInteger bigInteger2 = null;
        BigInteger bigInteger3 = null;
        BigInteger bigInteger4 = null;
        var num14 = 0;
        var num15 = 0;
        var num2 = 0uL;
        var num3 = 0;
        var num4 = 0;
        var num5 = 0;
        var num6 = 0;
        var num7 = bigInteger_1.dataLength;
        var num8 = num7 + 1;
        var num9 = num7 - 1;
        var bigInteger5 = new BigInteger();
        var num10 = num9;
        var num11 = 0;
        while (num10 < bigInteger_0.dataLength)
        {
            bigInteger5.data[num11] = bigInteger_0.data[num10];
            num10++;
            num11++;
        }

        bigInteger5.dataLength = bigInteger_0.dataLength - num9;
        if (bigInteger5.dataLength <= 0) bigInteger5.dataLength = 1;
        bigInteger4 = bigInteger5 * bigInteger_2;
        bigInteger3 = new BigInteger();
        num14 = num8;
        num15 = 0;
        while (num14 < bigInteger4.dataLength)
        {
            bigInteger3.data[num15] = bigInteger4.data[num14];
            num14++;
            num15++;
        }

        bigInteger3.dataLength = bigInteger4.dataLength - num8;
        if (bigInteger3.dataLength <= 0) bigInteger3.dataLength = 1;
        bigInteger = new BigInteger();
        var num12 = bigInteger_0.dataLength > num8 ? num8 : bigInteger_0.dataLength;
        num5 = num12;
        for (num6 = 0; num6 < num5; num6++) bigInteger.data[num6] = bigInteger_0.data[num6];
        bigInteger.dataLength = num5;
        bigInteger2 = new BigInteger();
        for (num4 = 0; num4 < bigInteger3.dataLength; num4++)
            if (bigInteger3.data[num4] != 0)
            {
                num2 = 0uL;
                num3 = num4;
                num = 0;
                while (num < bigInteger_1.dataLength && num3 < num8)
                {
                    var num13 =
                        (ulong)(bigInteger3.data[num4] * (long)bigInteger_1.data[num] + bigInteger2.data[num3]) + num2;
                    bigInteger2.data[num3] = (uint)(num13 & 0xFFFFFFFFu);
                    num2 = num13 >> 32;
                    num++;
                    num3++;
                }

                if (num3 < num8) bigInteger2.data[num3] = (uint)num2;
            }

        bigInteger2.dataLength = num8;
        while (bigInteger2.dataLength > 1 && bigInteger2.data[bigInteger2.dataLength - 1] == 0)
            bigInteger2.dataLength--;
        bigInteger -= bigInteger2;
        if ((bigInteger.data[69] & 0x80000000u) != 0)
        {
            var bigInteger6 = new BigInteger();
            bigInteger6.data[num8] = 1u;
            bigInteger6.dataLength = num8 + 1;
            bigInteger += bigInteger6;
        }

        for (; bigInteger >= bigInteger_1; bigInteger -= bigInteger_1)
        {
        }

        return bigInteger;
    }

    public BigInteger method_5(BigInteger bigInteger_0)
    {
        BigInteger bigInteger = null;
        BigInteger bigInteger2 = null;
        BigInteger bigInteger3 = null;
        var bigInteger4 = (data[69] & 0x80000000u) == 0 ? this : -this;
        bigInteger = bigInteger4;
        var bigInteger5 = (bigInteger_0.data[69] & 0x80000000u) == 0 ? bigInteger_0 : -bigInteger_0;
        bigInteger3 = bigInteger5;
        bigInteger2 = bigInteger3;
        while (bigInteger.dataLength > 1 || (bigInteger.dataLength == 1 && bigInteger.data[0] != 0))
        {
            bigInteger2 = bigInteger;
            bigInteger = bigInteger3 % bigInteger;
            bigInteger3 = bigInteger2;
        }

        return bigInteger2;
    }

    public void genRandomBits(int int_0, Random random_0)
    {
        var num2 = 0;
        var num3 = 0;
        var num4 = int_0 >> 5;
        var num5 = int_0 & 0x1F;
        if (num5 != 0) num4++;
        if (num4 > 70) throw new ArithmeticException("Number of required bits > maxLength.");
        for (num2 = 0; num2 < num4; num2++) data[num2] = (uint)(random_0.NextDouble() * 4294967296.0);
        for (num3 = num4; num3 < 70; num3++) data[num3] = 0u;
        if (num5 != 0)
        {
            var num6 = (uint)(1 << (num5 - 1));
            data[num4 - 1] |= num6;
            num6 = uint.MaxValue >> (32 - num5);
            data[num4 - 1] &= num6;
        }
        else
        {
            data[num4 - 1] |= 2147483648u;
        }

        dataLength = num4;
        if (dataLength == 0) dataLength = 1;
    }

    public int bitCount()
    {
        var num = 0u;
        var num2 = 0u;
        var num3 = 0;
        while (dataLength > 1 && data[dataLength - 1] == 0) dataLength--;
        num = data[dataLength - 1];
        num2 = 2147483648u;
        num3 = 32;
        while (num3 > 0 && (num & num2) == 0)
        {
            num3--;
            num2 >>= 1;
        }

        return num3 + ((dataLength - 1) << 5);
    }

    public bool FermatLittleTest(int int_0)
    {
        BigInteger bigInteger = null;
        BigInteger bigInteger2 = null;
        BigInteger bigInteger3 = null;
        var flag = false;
        BigInteger bigInteger4 = null;
        BigInteger bigInteger5 = null;
        var num = 0;
        var num2 = 0;
        var num3 = 0;
        Random random = null;
        var num4 = 0;
        var num5 = 0;
        var bigInteger6 = (data[69] & 0x80000000u) == 0 ? this : -this;
        bigInteger = bigInteger6;
        if (bigInteger.dataLength == 1)
        {
            if (bigInteger.data[0] == 0 || bigInteger.data[0] == 1) return false;
            if (bigInteger.data[0] == 2 || bigInteger.data[0] == 3) return true;
        }

        if ((bigInteger.data[0] & 1) == 0) return false;
        num3 = bigInteger.bitCount();
        bigInteger3 = new BigInteger();
        bigInteger5 = bigInteger - new BigInteger(1L);
        random = new Random();
        for (num2 = 0; num2 < int_0; num2++)
        {
            flag = false;
            while (!flag)
            {
                for (num4 = 0; num4 < 2; num4 = (int)(random.NextDouble() * num3))
                {
                }

                bigInteger3.genRandomBits(num4, random);
                num5 = bigInteger3.dataLength;
                if (num5 > 1 || (num5 == 1 && bigInteger3.data[0] != 1)) flag = true;
            }

            bigInteger4 = bigInteger3.method_5(bigInteger);
            if (bigInteger4.dataLength != 1 || bigInteger4.data[0] == 1)
            {
                bigInteger2 = bigInteger3.modPow(bigInteger5, bigInteger);
                num = bigInteger2.dataLength;
                if (num > 1 || (num == 1 && bigInteger2.data[0] != 1)) return false;
                continue;
            }

            return false;
        }

        return true;
    }

    public bool RabinMillerTest(int int_0)
    {
        BigInteger bigInteger = null;
        var num = 0;
        var num2 = 0;
        var flag = false;
        BigInteger bigInteger2 = null;
        var num3 = 0;
        BigInteger bigInteger3 = null;
        BigInteger bigInteger4 = null;
        var num4 = 0u;
        var num5 = 0;
        var num6 = 0;
        BigInteger bigInteger5 = null;
        var flag2 = false;
        var num7 = 0;
        Random random = null;
        var num8 = 0;
        BigInteger bigInteger6 = null;
        var num9 = 0;
        var bigInteger7 = (data[69] & 0x80000000u) == 0 ? this : -this;
        bigInteger = bigInteger7;
        if (bigInteger.dataLength == 1)
        {
            if (bigInteger.data[0] == 0 || bigInteger.data[0] == 1) return false;
            if (bigInteger.data[0] == 2 || bigInteger.data[0] == 3) return true;
        }

        if ((bigInteger.data[0] & 1) == 0) return false;
        bigInteger2 = bigInteger - new BigInteger(1L);
        num3 = 0;
        for (num = 0; num < bigInteger2.dataLength; num++)
        {
            num4 = 1u;
            for (num5 = 0; num5 < 32; num5++)
            {
                if ((bigInteger2.data[num] & num4) == 0)
                {
                    num4 <<= 1;
                    num3++;
                    continue;
                }

                num = bigInteger2.dataLength;
                break;
            }
        }

        bigInteger6 = bigInteger2 >> num3;
        num8 = bigInteger.bitCount();
        bigInteger3 = new BigInteger();
        random = new Random();
        for (num2 = 0; num2 < int_0; num2++)
        {
            flag2 = false;
            while (!flag2)
            {
                for (num7 = 0; num7 < 2; num7 = (int)(random.NextDouble() * num8))
                {
                }

                bigInteger3.genRandomBits(num7, random);
                num9 = bigInteger3.dataLength;
                if (num9 > 1 || (num9 == 1 && bigInteger3.data[0] != 1)) flag2 = true;
            }

            bigInteger4 = bigInteger3.method_5(bigInteger);
            if (bigInteger4.dataLength != 1 || bigInteger4.data[0] == 1)
            {
                bigInteger5 = bigInteger3.modPow(bigInteger6, bigInteger);
                flag = false;
                if (bigInteger5.dataLength == 1 && bigInteger5.data[0] == 1) flag = true;
                num6 = 0;
                while (!flag && num6 < num3)
                {
                    if (!(bigInteger5 == bigInteger2))
                    {
                        bigInteger5 = bigInteger5 * bigInteger5 % bigInteger;
                        num6++;
                        continue;
                    }

                    flag = true;
                    break;
                }

                if (!flag) return false;
                continue;
            }

            return false;
        }

        return true;
    }

    public bool SolovayStrassenTest(int int_0)
    {
        BigInteger bigInteger = null;
        var num = 0;
        BigInteger bigInteger2 = null;
        BigInteger bigInteger3 = null;
        BigInteger bigInteger4 = null;
        Random random = null;
        var num2 = 0;
        BigInteger bigInteger5 = null;
        BigInteger bigInteger6 = null;
        BigInteger bigInteger7 = null;
        var num3 = 0;
        var num4 = 0;
        var flag = false;
        var bigInteger8 = (data[69] & 0x80000000u) == 0 ? this : -this;
        bigInteger = bigInteger8;
        if (bigInteger.dataLength == 1)
        {
            if (bigInteger.data[0] == 0 || bigInteger.data[0] == 1) return false;
            if (bigInteger.data[0] == 2 || bigInteger.data[0] == 3) return true;
        }

        if ((bigInteger.data[0] & 1) == 0) return false;
        num = bigInteger.bitCount();
        bigInteger2 = new BigInteger();
        bigInteger3 = bigInteger - 1;
        bigInteger4 = bigInteger3 >> 1;
        random = new Random();
        for (num2 = 0; num2 < int_0; num2++)
        {
            flag = false;
            while (!flag)
            {
                for (num3 = 0; num3 < 2; num3 = (int)(random.NextDouble() * num))
                {
                }

                bigInteger2.genRandomBits(num3, random);
                num4 = bigInteger2.dataLength;
                if (num4 > 1 || (num4 == 1 && bigInteger2.data[0] != 1)) flag = true;
            }

            bigInteger7 = bigInteger2.method_5(bigInteger);
            if (bigInteger7.dataLength != 1 || bigInteger7.data[0] == 1)
            {
                bigInteger5 = bigInteger2.modPow(bigInteger4, bigInteger);
                if (bigInteger5 == bigInteger3) bigInteger5 = -1;
                bigInteger6 = Jacobi(bigInteger2, bigInteger);
                if (bigInteger5 != bigInteger6) return false;
                continue;
            }

            return false;
        }

        return true;
    }

    public bool LucasStrongTest()
    {
        BigInteger bigInteger = null;
        var bigInteger2 = (data[69] & 0x80000000u) == 0 ? this : -this;
        bigInteger = bigInteger2;
        if (bigInteger.dataLength == 1)
        {
            if (bigInteger.data[0] == 0 || bigInteger.data[0] == 1) return false;
            if (bigInteger.data[0] == 2 || bigInteger.data[0] == 3) return true;
        }

        if ((bigInteger.data[0] & 1) == 0) return false;
        return method_1(bigInteger);
    }

    private bool method_1(BigInteger bigInteger_0)
    {
        var num = 0;
        BigInteger bigInteger = null;
        BigInteger bigInteger2 = null;
        var num9 = 0;
        var num10 = 0;
        BigInteger[] array = null;
        var flag = false;
        var num11 = 0;
        BigInteger bigInteger3 = null;
        var num12 = 0;
        var num2 = 0L;
        var num3 = 0;
        BigInteger bigInteger4 = null;
        var num4 = 0u;
        var num5 = 5L;
        var num6 = -1L;
        var num7 = 0L;
        var flag2 = false;
        num = 38;
        BigInteger bigInteger5 = null;
        while (true)
        {
            var flag3 = true;
            while (true)
            {
                num = 58;
                bigInteger5 = null;
                while (true)
                {
                    if (!flag2)
                    {
                        num10 = Jacobi(num5, bigInteger_0);
                        num = 23;
                        bigInteger5 = null;
                        goto IL_0717;
                    }

                    num = 36;
                    bigInteger5 = null;
                    goto IL_00b0;
                    IL_073d:
                    num = 3;
                    bigInteger5 = null;
                    IL_0745:
                    if (array[1].dataLength != 1) goto IL_0153;
                    num = 29;
                    bigInteger5 = null;
                    goto IL_0084;
                    IL_00b0:
                    num2 = (1 - num5) >> 2;
                    bigInteger4 = bigInteger_0 + 1;
                    num12 = 0;
                    num3 = 0;
                    num = 20;
                    bigInteger5 = null;
                    IL_00d6:
                    num = 25;
                    bigInteger5 = null;
                    IL_00df:
                    if (num3 >= bigInteger4.dataLength)
                    {
                        num = 43;
                        bigInteger5 = null;
                        goto IL_065b;
                    }

                    num4 = 1u;
                    num9 = 0;
                    num = 41;
                    bigInteger5 = null;
                    goto IL_0124;
                    IL_0144:
                    num = num11 < num12 ? 45 : 7;
                    goto IL_015e;
                    IL_076c:
                    num7++;
                    num = 30;
                    bigInteger5 = null;
                    goto end_IL_0049;
                    IL_0124:
                    num = 61;
                    bigInteger5 = null;
                    IL_012d:
                    num = num9 < 32 ? 8 : 49;
                    goto IL_015e;
                    IL_06db:
                    num = num10 != 0 ? 60 : 16;
                    goto IL_015e;
                    IL_0153:
                    num11 = 1;
                    num = 1;
                    bigInteger5 = null;
                    goto IL_013b;
                    IL_010d:
                    num = 2;
                    bigInteger5 = null;
                    goto IL_06db;
                    IL_015e:
                    bigInteger5 = null;
                    switch (num)
                    {
                        case 30:
                        case 38:
                            break;
                        case 29:
                            goto IL_0084;
                        case 54:
                            goto IL_008d;
                        case 36:
                            goto IL_00b0;
                        case 20:
                        case 42:
                            goto IL_00d6;
                        case 25:
                            goto IL_00df;
                        case 59:
                            goto IL_010d;
                        case 56:
                            goto IL_0118;
                        case 24:
                        case 41:
                            goto IL_0124;
                        case 61:
                            goto IL_012d;
                        case 1:
                        case 44:
                            goto IL_013b;
                        case 51:
                            goto IL_0144;
                        case 17:
                            goto IL_0153;
                        case 57:
                            array[1] = bigInteger_0.method_0(array[1] * array[1], bigInteger_0, bigInteger5);
                            array[1] = (array[1] - (array[2] << 1)) % bigInteger_0;
                            num = 27;
                            bigInteger5 = null;
                            goto case 27;
                        case 27:
                            if (array[1].dataLength == 1)
                            {
                                num = 46;
                                bigInteger5 = null;
                                goto case 46;
                            }

                            goto case 15;
                        case 46:
                            num = 52;
                            bigInteger5 = null;
                            goto case 52;
                        case 52:
                            if (array[1].data[0] == 0)
                            {
                                num = 47;
                                bigInteger5 = null;
                                goto case 47;
                            }

                            goto case 15;
                        case 47:
                            flag = true;
                            num = 15;
                            bigInteger5 = null;
                            goto case 15;
                        case 15:
                            array[2] = bigInteger_0.method_0(array[2] * array[2], bigInteger_0, bigInteger5);
                            num11++;
                            num = 44;
                            bigInteger5 = null;
                            goto IL_013b;
                        case 19:
                        case 49:
                            num3++;
                            num = 42;
                            bigInteger5 = null;
                            goto IL_00d6;
                        case 45:
                            if (!flag)
                            {
                                num = 57;
                                bigInteger5 = null;
                                goto case 57;
                            }

                            goto case 15;
                        case 37:
                            num9++;
                            num = 24;
                            bigInteger5 = null;
                            goto IL_0124;
                        case 12:
                            num4 <<= 1;
                            num12++;
                            num = 37;
                            bigInteger5 = null;
                            goto case 37;
                        case 8:
                            if ((bigInteger4.data[num3] & num4) == 0)
                            {
                                num = 12;
                                bigInteger5 = null;
                                goto case 12;
                            }

                            num3 = bigInteger4.dataLength;
                            num = 19;
                            bigInteger5 = null;
                            goto case 19;
                        default:
                            num5 = 5L;
                            num6 = -1L;
                            num7 = 0L;
                            flag2 = false;
                            num = 38;
                            bigInteger5 = null;
                            break;
                        case 16:
                            if (!(Math.Abs(num5) < bigInteger_0)) goto case 34;
                            num = 10;
                            bigInteger5 = null;
                            goto case 10;
                        case 60:
                            num = 34;
                            bigInteger5 = null;
                            goto case 34;
                        case 34:
                            num = 40;
                            bigInteger5 = null;
                            goto case 40;
                        case 40:
                            if (num7 == 20)
                            {
                                num = 63;
                                bigInteger5 = null;
                                goto case 63;
                            }

                            goto IL_0636;
                        case 63:
                            bigInteger = bigInteger_0.sqrt();
                            num = 35;
                            bigInteger5 = null;
                            goto case 35;
                        case 35:
                            if (!(bigInteger * bigInteger == bigInteger_0)) goto IL_0636;
                            num = 0;
                            bigInteger5 = null;
                            goto case 0;
                        case 7:
                            num = 22;
                            bigInteger5 = null;
                            goto case 22;
                        case 10:
                            return false;
                        case 22:
                            if (flag)
                            {
                                num = 48;
                                bigInteger5 = null;
                                goto case 48;
                            }

                            goto case 6;
                        case 48:
                            bigInteger2 = bigInteger_0.method_5(num2);
                            num = 62;
                            bigInteger5 = null;
                            goto case 62;
                        case 62:
                            if (bigInteger2.dataLength == 1)
                            {
                                num = 55;
                                bigInteger5 = null;
                                goto case 55;
                            }

                            goto case 6;
                        case 55:
                            num = 18;
                            bigInteger5 = null;
                            goto case 18;
                        case 18:
                            if (bigInteger2.data[0] == 1)
                            {
                                num = 13;
                                bigInteger5 = null;
                                goto case 13;
                            }

                            goto case 6;
                        case 13:
                            num = 33;
                            bigInteger5 = null;
                            goto case 33;
                        case 33:
                            if ((array[2].data[69] & 0x80000000u) != 0)
                            {
                                num = 39;
                                bigInteger5 = null;
                                goto case 39;
                            }

                            goto case 50;
                        case 39:
                        {
                            var array2 = array;
                            var array3 = array2;
                            var array4 = array3;
                            var array5 = array4;
                            var array6 = array5;
                            var array7 = array6;
                            var array8 = array7;
                            array8[2] += bigInteger_0;
                            num = 50;
                            bigInteger5 = null;
                            goto case 50;
                        }
                        case 50:
                            bigInteger3 = num2 * Jacobi(num2, bigInteger_0) % bigInteger_0;
                            num = 26;
                            bigInteger5 = null;
                            goto case 26;
                        case 26:
                            if ((bigInteger3.data[69] & 0x80000000u) != 0)
                            {
                                num = 53;
                                bigInteger5 = null;
                                goto case 53;
                            }

                            goto case 9;
                        case 53:
                            bigInteger3 += bigInteger_0;
                            num = 9;
                            bigInteger5 = null;
                            goto case 9;
                        case 9:
                            num = 4;
                            bigInteger5 = null;
                            goto case 4;
                        case 4:
                            if (array[2] != bigInteger3)
                            {
                                num = 21;
                                bigInteger5 = null;
                                goto case 21;
                            }

                            goto case 6;
                        case 21:
                            flag = false;
                            num = 6;
                            bigInteger5 = null;
                            goto case 6;
                        case 6:
                            return flag;
                        case 0:
                            return false;
                        case 43:
                            goto IL_065b;
                        case 14:
                            goto IL_06bd;
                        case 2:
                            goto IL_06db;
                        case 28:
                            goto IL_06ec;
                        case 5:
                            goto IL_06f4;
                        case 23:
                            goto IL_0717;
                        case 32:
                            goto IL_073d;
                        case 3:
                            goto IL_0745;
                        case 11:
                        case 31:
                            goto IL_076c;
                        case 58:
                            continue;
                            IL_0636:
                            num5 = (Math.Abs(num5) + 2) * num6;
                            num6 = -num6;
                            num = 31;
                            bigInteger5 = null;
                            goto IL_076c;
                    }

                    break;
                    IL_0717:
                    if (num10 != -1)
                    {
                        num = 59;
                        bigInteger5 = null;
                        goto IL_010d;
                    }

                    flag2 = true;
                    num = 11;
                    bigInteger5 = null;
                    goto IL_076c;
                    IL_013b:
                    num = 51;
                    bigInteger5 = null;
                    goto IL_0144;
                    IL_0118:
                    flag = true;
                    num = 17;
                    bigInteger5 = null;
                    goto IL_0153;
                    IL_065b:
                    var bigInteger_ = bigInteger4 >> num12;
                    bigInteger5 = new BigInteger();
                    var num8 = bigInteger_0.dataLength << 1;
                    bigInteger5.data[num8] = 1u;
                    bigInteger5.dataLength = num8 + 1;
                    bigInteger5 /= bigInteger_0;
                    array = smethod_4(1, num2, bigInteger_, bigInteger_0, bigInteger5, 0);
                    flag = false;
                    num = 14;
                    bigInteger5 = null;
                    IL_06bd:
                    if (array[0].dataLength == 1)
                    {
                        num = 28;
                        bigInteger5 = null;
                        goto IL_06ec;
                    }

                    goto IL_073d;
                    IL_0084:
                    num = 54;
                    bigInteger5 = null;
                    goto IL_008d;
                    IL_06ec:
                    num = 5;
                    bigInteger5 = null;
                    IL_06f4:
                    if (array[0].data[0] == 0) goto IL_0118;
                    num = 32;
                    bigInteger5 = null;
                    goto IL_073d;
                    IL_008d:
                    if (array[1].data[0] == 0)
                    {
                        num = 56;
                        bigInteger5 = null;
                        goto IL_0118;
                    }

                    goto IL_0153;
                }

                continue;
                end_IL_0049:
                break;
            }
        }
    }

    public bool isProbablePrime(int int_0)
    {
        BigInteger bigInteger = null;
        var num2 = 0;
        BigInteger bigInteger2 = null;
        while (true)
        {
            BigInteger bigInteger3;
            switch ((data[69] & 0x80000000u) != 0 ? 10 : 3)
            {
                case 3:
                    bigInteger3 = this;
                    goto IL_00f8;
                case 1:
                case 10:
                    bigInteger3 = -this;
                    goto IL_00f8;
                case 2:
                case 13:
                    if ((bigInteger % bigInteger2).IntValue() != 0)
                    {
                        num2++;
                        goto case 4;
                    }

                    goto case 12;
                case 4:
                case 7:
                case 11:
                    if (num2 < primesBelow2000.Length)
                    {
                        bigInteger2 = primesBelow2000[num2];
                        goto case 6;
                    }

                    goto case 8;
                case 6:
                    if (!(bigInteger2 >= bigInteger)) goto case 2;
                    goto case 8;
                case 12:
                    return false;
                case 8:
                case 9:
                    if (bigInteger.RabinMillerTest(int_0)) break;
                    return false;
                default:
                    continue;
                case 0:
                    break;
                    IL_00f8:
                    bigInteger = bigInteger3;
                    num2 = 0;
                    goto case 4;
            }

            break;
        }

        return true;
    }

    public bool isProbablePrime()
    {
        BigInteger bigInteger = null;
        BigInteger bigInteger2 = null;
        var flag = false;
        BigInteger bigInteger3 = null;
        var num = 0;
        var num2 = 0;
        var num3 = 0;
        BigInteger bigInteger4 = null;
        var num4 = 0;
        var num5 = 0u;
        var num6 = 0;
        var bigInteger5 = (data[69] & 0x80000000u) == 0 ? this : -this;
        bigInteger3 = bigInteger5;
        if (bigInteger3.dataLength == 1)
        {
            if (bigInteger3.data[0] == 0 || bigInteger3.data[0] == 1) return false;
            if (bigInteger3.data[0] == 2 || bigInteger3.data[0] == 3) return true;
        }

        if ((bigInteger3.data[0] & 1) == 0) return false;
        for (num4 = 0; num4 < primesBelow2000.Length; num4++)
        {
            bigInteger4 = primesBelow2000[num4];
            if (bigInteger4 >= bigInteger3) break;
            if ((bigInteger3 % bigInteger4).IntValue() == 0) return false;
        }

        bigInteger2 = bigInteger3 - new BigInteger(1L);
        num = 0;
        for (num2 = 0; num2 < bigInteger2.dataLength; num2++)
        {
            num5 = 1u;
            for (num3 = 0; num3 < 32; num3++)
            {
                if ((bigInteger2.data[num2] & num5) == 0)
                {
                    num5 <<= 1;
                    num++;
                    continue;
                }

                num2 = bigInteger2.dataLength;
                break;
            }
        }

        var bigInteger_ = bigInteger2 >> num;
        bigInteger3.bitCount();
        bigInteger = ((BigInteger)2).modPow(bigInteger_, bigInteger3);
        flag = false;
        if (bigInteger.dataLength == 1 && bigInteger.data[0] == 1) flag = true;
        num6 = 0;
        while (!flag && num6 < num)
        {
            if (!(bigInteger == bigInteger2))
            {
                bigInteger = bigInteger * bigInteger % bigInteger3;
                num6++;
                continue;
            }

            flag = true;
            break;
        }

        if (flag) flag = method_1(bigInteger3);
        return flag;
    }

    public int IntValue()
    {
        return (int)data[0];
    }

    public long LongValue()
    {
        var num = 0L;
        num = data[0];
        try
        {
            num |= (long)((ulong)data[1] << 32);
            return num;
        }
        catch (Exception)
        {
            if ((data[0] & 0x80000000u) != 0) return (int)data[0];
            return num;
        }
    }

    public static int Jacobi(BigInteger bigInteger_0, BigInteger bigInteger_1)
    {
        BigInteger bigInteger = null;
        var num2 = 0;
        var num3 = 0;
        var num4 = 0;
        var num5 = 0;
        var num6 = 0u;
        if ((bigInteger_1.data[0] & 1) == 0) throw new ArgumentException("Jacobi defined only for odd integers.");
        if (bigInteger_0 >= bigInteger_1) bigInteger_0 %= bigInteger_1;
        if (bigInteger_0.dataLength == 1 && bigInteger_0.data[0] == 0) return 0;
        if (bigInteger_0.dataLength == 1 && bigInteger_0.data[0] == 1) return 1;
        if (bigInteger_0 < 0)
        {
            if (((bigInteger_1 - 1).data[0] & 2) == 0) return Jacobi(-bigInteger_0, bigInteger_1);
            return -Jacobi(-bigInteger_0, bigInteger_1);
        }

        num2 = 0;
        for (num4 = 0; num4 < bigInteger_0.dataLength; num4++)
        {
            num6 = 1u;
            for (num5 = 0; num5 < 32; num5++)
            {
                if ((bigInteger_0.data[num4] & num6) == 0)
                {
                    num6 <<= 1;
                    num2++;
                    continue;
                }

                num4 = bigInteger_0.dataLength;
                break;
            }
        }

        bigInteger = bigInteger_0 >> num2;
        num3 = 1;
        if (((uint)num2 & (true ? 1u : 0u)) != 0 &&
            ((bigInteger_1.data[0] & 7) == 3 || (bigInteger_1.data[0] & 7) == 5)) num3 = -1;
        if ((bigInteger_1.data[0] & 3) == 3 && (bigInteger.data[0] & 3) == 3) num3 = -num3;
        if (bigInteger.dataLength == 1 && bigInteger.data[0] == 1) return num3;
        return num3 * Jacobi(bigInteger_1 % bigInteger, bigInteger);
    }

    public static BigInteger genPseudoPrime(int int_0, int int_1, Random random_0)
    {
        var bigInteger = new BigInteger();
        var flag = false;
        while (!flag)
        {
            bigInteger.genRandomBits(int_0, random_0);
            bigInteger.data[0] |= 1u;
            flag = bigInteger.isProbablePrime(int_1);
        }

        return bigInteger;
    }

    public BigInteger genCoPrime(int int_0, Random random_0)
    {
        BigInteger bigInteger = null;
        var flag = false;
        var bigInteger2 = new BigInteger();
        while (!flag)
        {
            bigInteger2.genRandomBits(int_0, random_0);
            bigInteger = bigInteger2.method_5(this);
            if (bigInteger.dataLength == 1 && bigInteger.data[0] == 1) flag = true;
        }

        return bigInteger2;
    }

    public BigInteger modInverse(BigInteger bigInteger_0)
    {
        BigInteger bigInteger = null;
        BigInteger bigInteger2 = null;
        BigInteger bigInteger3 = null;
        var array = new BigInteger[2] { 0, 1 };
        var array2 = new BigInteger[2];
        var array3 = new BigInteger[2] { 0, 0 };
        var num2 = 0;
        var bigInteger_ = bigInteger_0;
        var bigInteger4 = this;
        while (bigInteger4.dataLength > 1 || (bigInteger4.dataLength == 1 && bigInteger4.data[0] != 0))
        {
            bigInteger2 = new BigInteger();
            bigInteger3 = new BigInteger();
            if (num2 > 1)
            {
                var bigInteger5 = (array[0] - array[1] * array2[0]) % bigInteger_0;
                array[0] = array[1];
                array[1] = bigInteger5;
            }

            if (bigInteger4.dataLength == 1)
                smethod_3(bigInteger_, bigInteger4, bigInteger2, bigInteger3);
            else
                smethod_2(bigInteger_, bigInteger4, bigInteger2, bigInteger3);
            array2[0] = array2[1];
            array3[0] = array3[1];
            array2[1] = bigInteger2;
            array3[1] = bigInteger3;
            bigInteger_ = bigInteger4;
            bigInteger4 = bigInteger3;
            num2++;
        }

        if (array3[0].dataLength <= 1 && (array3[0].dataLength != 1 || array3[0].data[0] == 1))
        {
            bigInteger = (array[0] - array[1] * array2[0]) % bigInteger_0;
            if ((bigInteger.data[69] & 0x80000000u) != 0) bigInteger += bigInteger_0;
            return bigInteger;
        }

        throw new ArithmeticException("No inverse!");
    }

    public byte[] getBytes()
    {
        byte[] array = null;
        var num = 0;
        var num2 = 0u;
        var num3 = 0;
        var num4 = 0u;
        var num5 = bitCount();
        var num6 = num5 >> 3;
        if (((uint)num5 & 7u) != 0) num6++;
        array = new byte[num6];
        num = 0;
        num4 = data[dataLength - 1];
        if ((num2 = (num4 >> 24) & 0xFFu) != 0) array[num++] = (byte)num2;
        if ((num2 = (num4 >> 16) & 0xFFu) != 0) array[num++] = (byte)num2;
        if ((num2 = (num4 >> 8) & 0xFFu) != 0) array[num++] = (byte)num2;
        if ((num2 = num4 & 0xFFu) != 0) array[num++] = (byte)num2;
        num3 = dataLength - 2;
        while (num3 >= 0)
        {
            num4 = data[num3];
            array[num + 3] = (byte)(num4 & 0xFFu);
            num4 >>= 8;
            array[num + 2] = (byte)(num4 & 0xFFu);
            num4 >>= 8;
            array[num + 1] = (byte)(num4 & 0xFFu);
            num4 >>= 8;
            array[num] = (byte)(num4 & 0xFFu);
            num3--;
            num += 4;
        }

        return array;
    }

    public void setBit(uint uint_0)
    {
        var num = uint_0 >> 5;
        var b = (byte)(uint_0 & 0x1Fu);
        var num2 = (uint)(1 << b);
        data[num] |= num2;
        if (num >= dataLength) dataLength = (int)(num + 1);
    }

    public void unsetBit(uint uint_0)
    {
        var num = uint_0 >> 5;
        if (num < dataLength)
        {
            var b = (byte)(uint_0 & 0x1Fu);
            var num2 = (uint)(1 << b);
            var num3 = 0xFFFFFFFFu ^ num2;
            data[num] &= num3;
            if (dataLength > 1 && data[dataLength - 1] == 0) dataLength--;
        }
    }

    public BigInteger sqrt()
    {
        BigInteger bigInteger = null;
        var num2 = 0u;
        var num3 = 0;
        var num4 = 0u;
        byte b = 0;
        var num5 = (uint)bitCount();
        while (true)
        {
            int num6;
            switch ((num5 & (true ? 1u : 0u)) != 0 ? 7 : 17)
            {
                default:
                    num5 = (uint)bitCount();
                    continue;
                case 3:
                case 7:
                    num6 = (int)((num5 >> 1) + 1);
                    goto IL_014b;
                case 17:
                    num6 = (int)(num5 >> 1);
                    goto IL_014b;
                case 11:
                    if (b == 0) goto case 15;
                    num4 = (uint)(1 << b);
                    num2++;
                    goto case 14;
                case 15:
                    num4 = 2147483648u;
                    goto case 14;
                case 14:
                case 16:
                    bigInteger.dataLength = (int)num2;
                    num3 = (int)(num2 - 1);
                    goto case 1;
                case 1:
                case 12:
                case 13:
                    if (num3 < 0) break;
                    goto case 2;
                case 2:
                case 8:
                    if (num4 != 0)
                    {
                        bigInteger.data[num3] ^= num4;
                        goto case 0;
                    }

                    goto case 10;
                case 0:
                    if (bigInteger * bigInteger > this) goto case 9;
                    goto case 6;
                case 9:
                    bigInteger.data[num3] ^= num4;
                    goto case 6;
                case 6:
                    num4 >>= 1;
                    goto case 2;
                case 10:
                    num4 = 2147483648u;
                    num3--;
                    goto case 1;
                case 5:
                    continue;
                case 4:
                    break;
                    IL_014b:
                    num5 = (uint)num6;
                    num2 = num5 >> 5;
                    b = (byte)(num5 & 0x1Fu);
                    bigInteger = new BigInteger();
                    goto case 11;
            }

            break;
        }

        return bigInteger;
    }

    public static BigInteger[] LucasSequence(BigInteger P, BigInteger Q, BigInteger bigInteger_0,
        BigInteger bigInteger_1)
    {
        if (bigInteger_0.dataLength == 1 && bigInteger_0.data[0] == 0)
            return new BigInteger[3]
            {
                0,
                2 % bigInteger_1,
                1 % bigInteger_1
            };
        var bigInteger = new BigInteger();
        var num = bigInteger_1.dataLength << 1;
        bigInteger.data[num] = 1u;
        bigInteger.dataLength = num + 1;
        bigInteger /= bigInteger_1;
        var num2 = 0;
        for (var i = 0; i < bigInteger_0.dataLength; i++)
        {
            var num3 = 1u;
            for (var j = 0; j < 32; j++)
            {
                if ((bigInteger_0.data[i] & num3) == 0)
                {
                    num3 <<= 1;
                    num2++;
                    continue;
                }

                i = bigInteger_0.dataLength;
                break;
            }
        }

        var bigInteger_2 = bigInteger_0 >> num2;
        return smethod_4(P, Q, bigInteger_2, bigInteger_1, bigInteger, num2);
    }

    private static BigInteger[] smethod_4(BigInteger bigInteger_0, BigInteger bigInteger_1, BigInteger bigInteger_2,
        BigInteger bigInteger_3, BigInteger bigInteger_4, int int_0)
    {
        var num2 = 0;
        var num3 = 0;
        BigInteger bigInteger = null;
        var num4 = 0u;
        var flag = false;
        BigInteger bigInteger2 = null;
        BigInteger bigInteger3 = null;
        BigInteger bigInteger4 = null;
        var array = new BigInteger[3];
        if ((bigInteger_2.data[0] & 1) == 0) throw new ArgumentException("Argument k must be odd.");
        var num5 = bigInteger_2.bitCount();
        num4 = (uint)(1 << ((num5 & 0x1F) - 1));
        bigInteger4 = 2 % bigInteger_3;
        bigInteger = 1 % bigInteger_3;
        bigInteger3 = bigInteger_0 % bigInteger_3;
        bigInteger2 = bigInteger;
        flag = true;
        for (num3 = bigInteger_2.dataLength - 1; num3 >= 0; num3--)
        {
            while (num4 != 0 && (num3 != 0 || num4 != 1))
            {
                if ((bigInteger_2.data[num3] & num4) != 0)
                {
                    bigInteger2 = bigInteger2 * bigInteger3 % bigInteger_3;
                    bigInteger4 = (bigInteger4 * bigInteger3 - bigInteger_0 * bigInteger) % bigInteger_3;
                    bigInteger3 = bigInteger_3.method_0(bigInteger3 * bigInteger3, bigInteger_3, bigInteger_4);
                    bigInteger3 = (bigInteger3 - ((bigInteger * bigInteger_1) << 1)) % bigInteger_3;
                    if (flag)
                        flag = false;
                    else
                        bigInteger = bigInteger_3.method_0(bigInteger * bigInteger, bigInteger_3, bigInteger_4);
                    bigInteger = bigInteger * bigInteger_1 % bigInteger_3;
                }
                else
                {
                    bigInteger2 = (bigInteger2 * bigInteger4 - bigInteger) % bigInteger_3;
                    bigInteger3 = (bigInteger4 * bigInteger3 - bigInteger_0 * bigInteger) % bigInteger_3;
                    bigInteger4 = bigInteger_3.method_0(bigInteger4 * bigInteger4, bigInteger_3, bigInteger_4);
                    bigInteger4 = (bigInteger4 - (bigInteger << 1)) % bigInteger_3;
                    if (flag)
                    {
                        bigInteger = bigInteger_1 % bigInteger_3;
                        flag = false;
                    }
                    else
                    {
                        bigInteger = bigInteger_3.method_0(bigInteger * bigInteger, bigInteger_3, bigInteger_4);
                    }
                }

                num4 >>= 1;
            }

            num4 = 2147483648u;
        }

        bigInteger2 = (bigInteger2 * bigInteger4 - bigInteger) % bigInteger_3;
        bigInteger4 = (bigInteger4 * bigInteger3 - bigInteger_0 * bigInteger) % bigInteger_3;
        if (flag)
            flag = false;
        else
            bigInteger = bigInteger_3.method_0(bigInteger * bigInteger, bigInteger_3, bigInteger_4);
        bigInteger = bigInteger * bigInteger_1 % bigInteger_3;
        for (num2 = 0; num2 < int_0; num2++)
        {
            bigInteger2 = bigInteger2 * bigInteger4 % bigInteger_3;
            bigInteger4 = (bigInteger4 * bigInteger4 - (bigInteger << 1)) % bigInteger_3;
            if (flag)
            {
                bigInteger = bigInteger_1 % bigInteger_3;
                flag = false;
            }
            else
            {
                bigInteger = bigInteger_3.method_0(bigInteger * bigInteger, bigInteger_3, bigInteger_4);
            }
        }

        array[0] = bigInteger2;
        array[1] = bigInteger4;
        array[2] = bigInteger;
        return array;
    }

    public static void MulDivTest(int int_0)
    {
        var flag = false;
        BigInteger bigInteger = null;
        BigInteger bigInteger2 = null;
        BigInteger bigInteger3 = null;
        var num2 = 0;
        BigInteger bigInteger4 = null;
        BigInteger bigInteger5 = null;
        var num3 = 0;
        var num4 = 0;
        var num5 = 0;
        var random = new Random();
        var array = new byte[64];
        var array2 = new byte[64];
        var num6 = 0;
        while (true)
        {
            if (num6 >= int_0) return;
            for (num3 = 0; num3 == 0; num3 = (int)(random.NextDouble() * 65.0))
            {
            }

            for (num2 = 0; num2 == 0; num2 = (int)(random.NextDouble() * 65.0))
            {
            }

            flag = false;
            while (!flag)
                for (num5 = 0; num5 < 64; num5++)
                {
                    if (num5 < num3)
                        array[num5] = (byte)(random.NextDouble() * 256.0);
                    else
                        array[num5] = 0;
                    if (array[num5] != 0) flag = true;
                }

            flag = false;
            while (!flag)
                for (num4 = 0; num4 < 64; num4++)
                {
                    if (num4 < num2)
                        array2[num4] = (byte)(random.NextDouble() * 256.0);
                    else
                        array2[num4] = 0;
                    if (array2[num4] != 0) flag = true;
                }

            while (array[0] == 0) array[0] = (byte)(random.NextDouble() * 256.0);
            while (array2[0] == 0) array2[0] = (byte)(random.NextDouble() * 256.0);
            Console.WriteLine(num6);
            bigInteger2 = new BigInteger(array, num3);
            bigInteger3 = new BigInteger(array2, num2);
            bigInteger5 = bigInteger2 / bigInteger3;
            bigInteger4 = bigInteger2 % bigInteger3;
            bigInteger = bigInteger5 * bigInteger3 + bigInteger4;
            if (bigInteger != bigInteger2) break;
            num6++;
        }

        Console.WriteLine("Error at " + num6);
        object obj = bigInteger2?.ToString();
        Console.WriteLine((string)obj + "\n");
        object obj2 = bigInteger3?.ToString();
        Console.WriteLine((string)obj2 + "\n");
        object obj3 = bigInteger5?.ToString();
        Console.WriteLine((string)obj3 + "\n");
        object obj4 = bigInteger4?.ToString();
        Console.WriteLine((string)obj4 + "\n");
        object obj5 = bigInteger?.ToString();
        Console.WriteLine((string)obj5 + "\n");
    }

    public static void RSATest(int int_0)
    {
        BigInteger bigInteger = null;
        var flag = false;
        var num2 = 0;
        var num3 = 0;
        var random = new Random(1);
        var array = new byte[64];
        var bigInteger2 =
            new BigInteger(
                "a932b948feed4fb2b692609bd22164fc9edb59fae7880cc1eaff7b3c9626b7e5b241c27a974833b2622ebe09beb451917663d47232488f23a117fc97720f1e7",
                16);
        var bigInteger3 = new BigInteger(
            "4adf2f7a89da93248509347d2ae506d683dd3a16357e859a980c4f77a4e2f7a01fae289f13a851df6e9db5adaa60bfd2b162bbbe31f7c8f828261a6839311929d2cef4f864dde65e556ce43c89bbbf9f1ac5511315847ce9cc8dc92470a747b8792d6a83b0092d2e5ebaf852c85cacf34278efa99160f2f8aa7ee7214de07b7",
            16);
        var bigInteger4 = new BigInteger(
            "e8e77781f36a7b3188d711c2190b560f205a52391b3479cdb99fa010745cbeba5f2adc08e1de6bf38398a0487c4a73610d94ec36f17f3f46ad75e17bc1adfec99839589f45f95ccc94cb2a5c500b477eb3323d8cfab0c8458c96f0147a45d27e45a4d11d54d77684f65d48f15fafcc1ba208e71e921b9bd9017c16a5231af7f",
            16);
        Console.WriteLine("e =\n" + bigInteger2.ToString(10));
        Console.WriteLine("\nd =\n" + bigInteger3.ToString(10));
        Console.WriteLine("\nn =\n" + bigInteger4.ToString(10) + "\n");
        var num4 = 0;
        while (true)
        {
            if (num4 >= int_0) return;
            for (num3 = 0; num3 == 0; num3 = (int)(random.NextDouble() * 65.0))
            {
            }

            flag = false;
            while (!flag)
                for (num2 = 0; num2 < 64; num2++)
                {
                    if (num2 < num3)
                        array[num2] = (byte)(random.NextDouble() * 256.0);
                    else
                        array[num2] = 0;
                    if (array[num2] != 0) flag = true;
                }

            while (array[0] == 0) array[0] = (byte)(random.NextDouble() * 256.0);
            Console.Write("Round = " + num4);
            bigInteger = new BigInteger(array, num3);
            if (bigInteger.modPow(bigInteger2, bigInteger4).modPow(bigInteger3, bigInteger4) != bigInteger) break;
            Console.WriteLine(" <PASSED>.");
            num4++;
        }

        Console.WriteLine("\nError at round " + num4);
        object obj = bigInteger?.ToString();
        Console.WriteLine((string)obj + "\n");
    }

    public static void RSATest2(int int_0)
    {
        var num2 = 0;
        var num3 = 0;
        BigInteger bigInteger = null;
        BigInteger bigInteger2 = null;
        BigInteger bigInteger3 = null;
        var flag = false;
        var random = new Random();
        var array = new byte[64];
        var byte_ = new byte[64]
        {
            133, 132, 100, 253, 112, 106, 159, 240, 148, 12,
            62, 44, 116, 52, 5, 201, 85, 179, 133, 50,
            152, 113, 249, 65, 33, 95, 2, 158, 234, 86,
            141, 140, 68, 204, 238, 238, 61, 44, 157, 44,
            18, 65, 30, 241, 197, 50, 195, 170, 49, 74,
            82, 216, 232, 175, 66, 244, 114, 161, 42, 13,
            151, 177, 49, 179
        };
        var byte_2 = new byte[64]
        {
            153, 152, 202, 184, 94, 215, 229, 220, 40, 92,
            111, 14, 21, 9, 89, 110, 132, 243, 129, 205,
            222, 66, 220, 147, 194, 122, 98, 172, 108, 175,
            222, 116, 227, 203, 96, 32, 56, 156, 33, 195,
            220, 200, 162, 77, 198, 42, 53, 127, 243, 169,
            232, 29, 123, 44, 120, 250, 184, 2, 85, 128,
            155, 194, 165, 203
        };
        var bigInteger_ = new BigInteger(byte_);
        var bigInteger4 = new BigInteger(byte_2);
        var bigInteger5 = (bigInteger_ - 1) * (bigInteger4 - 1);
        var bigInteger6 = bigInteger_ * bigInteger4;
        var num4 = 0;
        while (true)
        {
            if (num4 >= int_0) return;
            bigInteger = bigInteger5.genCoPrime(512, random);
            bigInteger2 = bigInteger.modInverse(bigInteger5);
            Console.WriteLine("\ne =\n" + bigInteger.ToString(10));
            Console.WriteLine("\nd =\n" + bigInteger2.ToString(10));
            Console.WriteLine("\nn =\n" + bigInteger6.ToString(10) + "\n");
            for (num3 = 0; num3 == 0; num3 = (int)(random.NextDouble() * 65.0))
            {
            }

            flag = false;
            while (!flag)
                for (num2 = 0; num2 < 64; num2++)
                {
                    if (num2 < num3)
                        array[num2] = (byte)(random.NextDouble() * 256.0);
                    else
                        array[num2] = 0;
                    if (array[num2] != 0) flag = true;
                }

            while (array[0] == 0) array[0] = (byte)(random.NextDouble() * 256.0);
            Console.Write("Round = " + num4);
            bigInteger3 = new BigInteger(array, num3);
            if (bigInteger3.modPow(bigInteger, bigInteger6).modPow(bigInteger2, bigInteger6) != bigInteger3) break;
            Console.WriteLine(" <PASSED>.");
            num4++;
        }

        Console.WriteLine("\nError at round " + num4);
        object obj = bigInteger3?.ToString();
        Console.WriteLine((string)obj + "\n");
    }

    public static void SqrtTest(int int_0)
    {
        var num3 = 0;
        BigInteger bigInteger = null;
        BigInteger bigInteger2 = null;
        var random = new Random();
        var num4 = 0;
        var flag = true;
        while (num4 < int_0)
        {
            var flag2 = true;
            while (true)
            {
                num3 = 0;
                while (true)
                {
                    var flag3 = true;
                    while (num3 != 0)
                    {
                        var flag4 = true;
                        while (true)
                        {
                            IL_003f:
                            Console.Write("Round = " + num4);
                            bigInteger = new BigInteger();
                            bigInteger.genRandomBits(num3, random);
                            bigInteger2 = bigInteger.sqrt();
                            var flag5 = true;
                            while (true)
                            {
                                IL_0073:
                                if ((bigInteger2 + 1) * (bigInteger2 + 1) <= bigInteger)
                                {
                                    Console.WriteLine("\nError at round " + num4);
                                    while (true)
                                    {
                                        object obj;
                                        switch ((object)bigInteger != null ? 12 : 0)
                                        {
                                            case 8:
                                                break;
                                            case 7:
                                                goto IL_003f;
                                            case 1:
                                                goto IL_0073;
                                            default:
                                                random = new Random();
                                                num4 = 0;
                                                goto end_IL_0023;
                                            case 12:
                                                obj = bigInteger.ToString();
                                                goto IL_014a;
                                            case 0:
                                            case 13:
                                                obj = null;
                                                goto IL_014a;
                                            case 5:
                                                continue;
                                            case 2:
                                                goto IL_0175;
                                            case 6:
                                            case 9:
                                            case 11:
                                                goto IL_01a3;
                                            case 3:
                                            case 4:
                                            case 10:
                                                goto end_IL_0023;
                                                IL_014a:
                                                Console.WriteLine((string)obj + "\n");
                                                return;
                                        }

                                        break;
                                    }

                                    break;
                                }

                                IL_0175:
                                Console.WriteLine(" <PASSED>.");
                                num4++;
                                goto end_IL_0023;
                            }

                            break;
                        }

                        goto end_IL_01cd;
                        IL_01a3: ;
                    }

                    num3 = (int)(random.NextDouble() * 1024.0);
                    continue;
                    end_IL_01cd:
                    break;
                }

                continue;
                end_IL_0023:
                break;
            }
        }
    }
}