using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class NpcList : Form
{
    private static Dictionary<int, NpcClass> List = new();

    private static List<int> mapLis = new();

    private Button button1;

    private ComboBox comboBox1;

    private ComboBox comboBox2;

    private GroupBox groupBox1;

    private IContainer icontainer_0;

    private Label label1;

    private Label label10;

    private Label label11;

    private Label label12;

    private Label label13;

    private Label label14;

    private Label label15;

    private Label label16;

    private Label label17;

    private Label label18;

    private Label label19;

    private Label label2;

    private Label label20;

    private Label label3;

    private Label label4;

    private Label label5;

    private Label label6;

    private Label label7;

    private Label label8;

    private Label label9;

    private TextBox textBox1;

    private TextBox textBox10;

    private TextBox textBox11;

    private TextBox textBox12;

    private TextBox textBox13;

    private TextBox textBox14;

    private TextBox textBox15;

    private TextBox textBox16;

    private TextBox textBox17;

    private TextBox textBox18;

    private TextBox textBox2;

    private TextBox textBox3;

    private TextBox textBox4;

    private TextBox textBox5;

    private TextBox textBox6;

    private TextBox textBox7;

    private TextBox textBox8;

    private TextBox textBox9;

    public NpcList()
    {
        InitializeComponent();
    }

    private void NpcList_Load(object sender, EventArgs e)
    {
        try
        {
            mapLis = new List<int>();
            List = new Dictionary<int, NpcClass>();
            comboBox1.Items.Clear();
            comboBox2.Items.Clear();
            comboBox1.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox2.DropDownStyle = ComboBoxStyle.DropDownList;
            foreach (var value in World.Maplist.Values) comboBox2.Items.Add(value);
            foreach (var value2 in World.NpcList.Values)
                try
                {
                    List.Add(value2.FLD_PID, value2);
                    comboBox1.Items.Add(value2.Name);
                }
                catch
                {
                    MessageBox.Show(value2.FLD_INDEX + "|" + value2.Name);
                }
        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message);
        }
    }

    private NpcClass method_0(string string_0)
    {
        using (var enumerator = List.Values.GetEnumerator())
        {
            NpcClass npcClass = null;
            while (enumerator.MoveNext())
            {
                npcClass = enumerator.Current;
                if (npcClass.Name == string_0) return npcClass;
            }
        }

        return null;
    }

    private void comboBox2_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            var num2 = 0;
            if (comboBox2.SelectedItem.ToString().Contains("九泉之下"))
            {
                comboBox1.Items.Clear();
                foreach (var value in World.NpcList.Values)
                    try
                    {
                        if (value.Rxjh_Map >= 23001 && value.Rxjh_Map <= 24000) comboBox1.Items.Add(value.Name);
                    }
                    catch
                    {
                        MessageBox.Show(value.FLD_INDEX + "|" + value.Name);
                    }

                if (comboBox1.Items.Count <= 0)
                {
                    comboBox1.Text = string.Empty;
                    comboBox1.Items.Clear();
                    MessageBox.Show("此地图没有怪物");
                }
                else
                {
                    comboBox1.SelectedItem = comboBox1.Items[0];
                }

                return;
            }

            num2 = 0;
            foreach (var item in World.Maplist)
                if (item.Value == comboBox2.SelectedItem.ToString())
                    num2 = item.Key;
            if (num2 != 0)
            {
                comboBox1.Items.Clear();
                foreach (var value2 in World.NpcList.Values)
                    try
                    {
                        if (value2.Rxjh_Map == num2) comboBox1.Items.Add(value2.Name);
                    }
                    catch
                    {
                        MessageBox.Show(value2.FLD_INDEX + "|" + value2.Name);
                    }

                if (comboBox1.Items.Count <= 0)
                {
                    comboBox1.Text = string.Empty;
                    comboBox1.Items.Clear();
                    MessageBox.Show("此地图没有怪物");
                }
                else
                {
                    comboBox1.SelectedItem = comboBox1.Items[0];
                }
            }
            else
            {
                MessageBox.Show("无此地图");
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show("错误:" + ex.Message);
        }
    }

    private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            var npcClass = method_0(comboBox1.SelectedItem.ToString());
            if (npcClass != null)
            {
                textBox1.Text = npcClass.FLD_INDEX.ToString();
                textBox2.Text = npcClass.FLD_PID.ToString();
                textBox3.Text = npcClass.Rxjh_X.ToString();
                textBox4.Text = npcClass.Rxjh_Z.ToString();
                textBox5.Text = npcClass.Rxjh_Y.ToString();
                textBox6.Text = npcClass.Name;
                textBox7.Text = npcClass.FLD_FACE1.ToString();
                textBox8.Text = npcClass.FLD_FACE2.ToString();
                textBox9.Text = npcClass.Rxjh_Map.ToString();
                textBox10.Text = npcClass.Level.ToString();
                textBox11.Text = !World.MonSter.TryGetValue(npcClass.FLD_PID, out var value)
                    ? npcClass.Rxjh_HP.ToString()
                    : value.Rxjh_HP.ToString();
                textBox12.Text = npcClass.FLD_AT.ToString();
                textBox13.Text = npcClass.FLD_DF.ToString();
                textBox14.Text = npcClass.IsNpc.ToString();
                textBox15.Text = npcClass.FLD_NEWTIME.ToString();
                textBox16.Text = npcClass.Rxjh_Exp.ToString();
                textBox17.Text = npcClass.FLD_AUTO.ToString();
                textBox18.Text = npcClass.FLD_BOSS.ToString();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message);
        }
    }

    private void button1_Click(object sender, EventArgs e)
    {
        try
        {
            if (comboBox1.Text.Length != 0 && textBox1.Text.Length != 0 && textBox9.Text.Length != 0 &&
                textBox2.Text.Length != 0 && textBox10.Text.Length != 0 && textBox11.Text.Length != 0 &&
                textBox12.Text.Length != 0 && textBox13.Text.Length != 0 && textBox16.Text.Length != 0 &&
                textBox17.Text.Length != 0 && textBox18.Text.Length != 0)
                method_1(int.Parse(textBox9.Text), int.Parse(textBox2.Text), int.Parse(textBox10.Text),
                    int.Parse(textBox11.Text), int.Parse(textBox12.Text), int.Parse(textBox13.Text),
                    int.Parse(textBox16.Text), int.Parse(textBox17.Text), int.Parse(textBox18.Text));
        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message);
        }
    }

    private void method_1(int MapID, int PID, int Level, int FLD_HP, int FLD_ATK, int FLD_DF, int FLD_EXP, int FLD_AUTO,
        int FLD_BOSS)
    {
        try
        {
            NpcClass npcClass = null;
            var enumerator = default(Dictionary<int, NpcClass>.ValueCollection.Enumerator);
            MapClass value = null;
            if (!World.Map.TryGetValue(MapID, out value)) return;
            using var enumerator2 = value.npcTemplate.Values.GetEnumerator();
            if (DBA.ExeSqlCommand(
                    string.Format(
                        "UPDATE TBL_XWWL_NPC SET FLD_LEVEL={1},FLD_HP={2},FLD_AT={3},FLD_DF={4},FLD_EXP={5},FLD_AUTO={6},FLD_BOSS={7} WHERE FLD_PID={0}",
                        PID, Level, FLD_HP, FLD_ATK, FLD_DF, FLD_EXP, FLD_AUTO, FLD_BOSS), "PublicDb") == -1) return;
            while (enumerator2.MoveNext())
            {
                npcClass = enumerator2.Current;
                if (npcClass.FLD_PID == PID)
                {
                    npcClass.Level = Level;
                    npcClass.Max_Rxjh_HP = FLD_HP;
                    npcClass.Rxjh_HP = FLD_HP;
                    npcClass.FLD_AT = FLD_ATK;
                    npcClass.FLD_DF = FLD_DF;
                    npcClass.Rxjh_Exp = FLD_EXP;
                    npcClass.FLD_AUTO = FLD_AUTO;
                    npcClass.FLD_BOSS = FLD_BOSS;
                }
            }

            MessageBox.Show("Cập nhật thành công!");
        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message);
        }
    }

    private void NpcList_FormClosing(object sender, FormClosingEventArgs e)
    {
        if (mapLis != null)
        {
            mapLis.Clear();
            mapLis = null;
        }

        if (List != null)
        {
            List.Clear();
            List = null;
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && icontainer_0 != null) icontainer_0.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        comboBox1 = new ComboBox();
        label1 = new Label();
        button1 = new Button();
        groupBox1 = new GroupBox();
        textBox18 = new TextBox();
        label19 = new Label();
        textBox17 = new TextBox();
        label18 = new Label();
        textBox16 = new TextBox();
        label17 = new Label();
        textBox15 = new TextBox();
        label16 = new Label();
        textBox14 = new TextBox();
        label15 = new Label();
        textBox13 = new TextBox();
        label14 = new Label();
        textBox12 = new TextBox();
        label13 = new Label();
        textBox11 = new TextBox();
        label12 = new Label();
        textBox10 = new TextBox();
        label11 = new Label();
        textBox9 = new TextBox();
        label10 = new Label();
        textBox8 = new TextBox();
        label9 = new Label();
        textBox7 = new TextBox();
        label8 = new Label();
        textBox6 = new TextBox();
        label7 = new Label();
        textBox5 = new TextBox();
        label6 = new Label();
        textBox4 = new TextBox();
        label5 = new Label();
        textBox3 = new TextBox();
        label4 = new Label();
        textBox2 = new TextBox();
        label3 = new Label();
        textBox1 = new TextBox();
        label2 = new Label();
        label20 = new Label();
        comboBox2 = new ComboBox();
        groupBox1.SuspendLayout();
        SuspendLayout();
        comboBox1.FormattingEnabled = true;
        comboBox1.Location = new Point(172, 12);
        comboBox1.Name = "comboBox1";
        comboBox1.Size = new Size(114, 21);
        comboBox1.TabIndex = 0;
        comboBox1.SelectedIndexChanged += comboBox1_SelectedIndexChanged;
        label1.AutoSize = true;
        label1.Location = new Point(135, 15);
        label1.Name = "label1";
        label1.Size = new Size(36, 13);
        label1.TabIndex = 1;
        label1.Text = "name:";
        button1.Location = new Point(292, 10);
        button1.Name = "button1";
        button1.Size = new Size(59, 25);
        button1.TabIndex = 2;
        button1.Text = "Update";
        button1.UseVisualStyleBackColor = true;
        button1.Click += button1_Click;
        groupBox1.Controls.Add(textBox18);
        groupBox1.Controls.Add(label19);
        groupBox1.Controls.Add(textBox17);
        groupBox1.Controls.Add(label18);
        groupBox1.Controls.Add(textBox16);
        groupBox1.Controls.Add(label17);
        groupBox1.Controls.Add(textBox15);
        groupBox1.Controls.Add(label16);
        groupBox1.Controls.Add(textBox14);
        groupBox1.Controls.Add(label15);
        groupBox1.Controls.Add(textBox13);
        groupBox1.Controls.Add(label14);
        groupBox1.Controls.Add(textBox12);
        groupBox1.Controls.Add(label13);
        groupBox1.Controls.Add(textBox11);
        groupBox1.Controls.Add(label12);
        groupBox1.Controls.Add(textBox10);
        groupBox1.Controls.Add(label11);
        groupBox1.Controls.Add(textBox9);
        groupBox1.Controls.Add(label10);
        groupBox1.Controls.Add(textBox8);
        groupBox1.Controls.Add(label9);
        groupBox1.Controls.Add(textBox7);
        groupBox1.Controls.Add(label8);
        groupBox1.Controls.Add(textBox6);
        groupBox1.Controls.Add(label7);
        groupBox1.Controls.Add(textBox5);
        groupBox1.Controls.Add(label6);
        groupBox1.Controls.Add(textBox4);
        groupBox1.Controls.Add(label5);
        groupBox1.Controls.Add(textBox3);
        groupBox1.Controls.Add(label4);
        groupBox1.Controls.Add(textBox2);
        groupBox1.Controls.Add(label3);
        groupBox1.Controls.Add(textBox1);
        groupBox1.Controls.Add(label2);
        groupBox1.Location = new Point(7, 43);
        groupBox1.Name = "groupBox1";
        groupBox1.Size = new Size(328, 313);
        groupBox1.TabIndex = 4;
        groupBox1.TabStop = false;
        groupBox1.Text = "Chi tiết";
        textBox18.Location = new Point(213, 266);
        textBox18.Name = "textBox18";
        textBox18.Size = new Size(90, 20);
        textBox18.TabIndex = 35;
        label19.AutoSize = true;
        label19.Location = new Point(178, 269);
        label19.Name = "label19";
        label19.Size = new Size(39, 13);
        label19.TabIndex = 34;
        label19.Text = "BOSS:";
        textBox17.Location = new Point(213, 236);
        textBox17.Name = "textBox17";
        textBox17.Size = new Size(90, 20);
        textBox17.TabIndex = 33;
        label18.AutoSize = true;
        label18.Location = new Point(178, 240);
        label18.Name = "label18";
        label18.Size = new Size(40, 13);
        label18.TabIndex = 32;
        label18.Text = "AUTO:";
        textBox16.Location = new Point(213, 207);
        textBox16.Name = "textBox16";
        textBox16.Size = new Size(90, 20);
        textBox16.TabIndex = 31;
        label17.AutoSize = true;
        label17.Location = new Point(184, 210);
        label17.Name = "label17";
        label17.Size = new Size(31, 13);
        label17.TabIndex = 30;
        label17.Text = "EXP:";
        textBox15.Location = new Point(213, 178);
        textBox15.Name = "textBox15";
        textBox15.Size = new Size(90, 20);
        textBox15.TabIndex = 29;
        label16.AutoSize = true;
        label16.Location = new Point(178, 181);
        label16.Name = "label16";
        label16.Size = new Size(36, 13);
        label16.TabIndex = 28;
        label16.Text = "TIME:";
        textBox14.Location = new Point(213, 149);
        textBox14.Name = "textBox14";
        textBox14.Size = new Size(90, 20);
        textBox14.TabIndex = 27;
        label15.AutoSize = true;
        label15.Location = new Point(184, 152);
        label15.Name = "label15";
        label15.Size = new Size(32, 13);
        label15.TabIndex = 26;
        label15.Text = "NPC:";
        textBox13.Location = new Point(213, 119);
        textBox13.Name = "textBox13";
        textBox13.Size = new Size(90, 20);
        textBox13.TabIndex = 25;
        label14.AutoSize = true;
        label14.Location = new Point(190, 123);
        label14.Name = "label14";
        label14.Size = new Size(24, 13);
        label14.TabIndex = 24;
        label14.Text = "DF:";
        textBox12.Location = new Point(213, 90);
        textBox12.Name = "textBox12";
        textBox12.Size = new Size(90, 20);
        textBox12.TabIndex = 23;
        label13.AutoSize = true;
        label13.Location = new Point(190, 93);
        label13.Name = "label13";
        label13.Size = new Size(24, 13);
        label13.TabIndex = 22;
        label13.Text = "AT:";
        textBox11.Location = new Point(213, 61);
        textBox11.Name = "textBox11";
        textBox11.Size = new Size(90, 20);
        textBox11.TabIndex = 21;
        label12.AutoSize = true;
        label12.Location = new Point(190, 64);
        label12.Name = "label12";
        label12.Size = new Size(25, 13);
        label12.TabIndex = 20;
        label12.Text = "HP:";
        textBox10.Location = new Point(213, 32);
        textBox10.Name = "textBox10";
        textBox10.Size = new Size(90, 20);
        textBox10.TabIndex = 19;
        label11.AutoSize = true;
        label11.Location = new Point(172, 35);
        label11.Name = "label11";
        label11.Size = new Size(43, 13);
        label11.TabIndex = 18;
        label11.Text = "LEVEL:";
        textBox9.Location = new Point(57, 266);
        textBox9.Name = "textBox9";
        textBox9.Size = new Size(90, 20);
        textBox9.TabIndex = 17;
        label10.AutoSize = true;
        label10.Location = new Point(22, 269);
        label10.Name = "label10";
        label10.Size = new Size(30, 13);
        label10.TabIndex = 16;
        label10.Text = "MID:";
        textBox8.Location = new Point(57, 236);
        textBox8.Name = "textBox8";
        textBox8.Size = new Size(90, 20);
        textBox8.TabIndex = 15;
        label9.AutoSize = true;
        label9.Location = new Point(10, 240);
        label9.Name = "label9";
        label9.Size = new Size(43, 13);
        label9.TabIndex = 14;
        label9.Text = "FACE2:";
        textBox7.Location = new Point(57, 207);
        textBox7.Name = "textBox7";
        textBox7.Size = new Size(90, 20);
        textBox7.TabIndex = 13;
        label8.AutoSize = true;
        label8.Location = new Point(10, 210);
        label8.Name = "label8";
        label8.Size = new Size(43, 13);
        label8.TabIndex = 12;
        label8.Text = "FACE1:";
        textBox6.Location = new Point(57, 178);
        textBox6.Name = "textBox6";
        textBox6.Size = new Size(90, 20);
        textBox6.TabIndex = 11;
        label7.AutoSize = true;
        label7.Location = new Point(16, 181);
        label7.Name = "label7";
        label7.Size = new Size(41, 13);
        label7.TabIndex = 10;
        label7.Text = "NAME:";
        textBox5.Location = new Point(57, 149);
        textBox5.Name = "textBox5";
        textBox5.Size = new Size(90, 20);
        textBox5.TabIndex = 9;
        label6.AutoSize = true;
        label6.Location = new Point(34, 152);
        label6.Name = "label6";
        label6.Size = new Size(17, 13);
        label6.TabIndex = 8;
        label6.Text = "Y:";
        textBox4.Location = new Point(57, 119);
        textBox4.Name = "textBox4";
        textBox4.Size = new Size(90, 20);
        textBox4.TabIndex = 7;
        label5.AutoSize = true;
        label5.Location = new Point(34, 123);
        label5.Name = "label5";
        label5.Size = new Size(17, 13);
        label5.TabIndex = 6;
        label5.Text = "Z:";
        textBox3.Location = new Point(57, 90);
        textBox3.Name = "textBox3";
        textBox3.Size = new Size(90, 20);
        textBox3.TabIndex = 5;
        label4.AutoSize = true;
        label4.Location = new Point(34, 90);
        label4.Name = "label4";
        label4.Size = new Size(17, 13);
        label4.TabIndex = 4;
        label4.Text = "X:";
        textBox2.Location = new Point(57, 61);
        textBox2.Name = "textBox2";
        textBox2.Size = new Size(90, 20);
        textBox2.TabIndex = 3;
        label3.AutoSize = true;
        label3.Location = new Point(22, 64);
        label3.Name = "label3";
        label3.Size = new Size(28, 13);
        label3.TabIndex = 2;
        label3.Text = "PID:";
        textBox1.Location = new Point(57, 32);
        textBox1.Name = "textBox1";
        textBox1.Size = new Size(90, 20);
        textBox1.TabIndex = 1;
        label2.AutoSize = true;
        label2.Location = new Point(10, 35);
        label2.Name = "label2";
        label2.Size = new Size(43, 13);
        label2.TabIndex = 0;
        label2.Text = "INDEX:";
        label20.AutoSize = true;
        label20.Location = new Point(5, 15);
        label20.Name = "label20";
        label20.Size = new Size(30, 13);
        label20.TabIndex = 36;
        label20.Text = "map:";
        comboBox2.FormattingEnabled = true;
        comboBox2.Location = new Point(35, 12);
        comboBox2.Name = "comboBox2";
        comboBox2.Size = new Size(101, 21);
        comboBox2.TabIndex = 36;
        comboBox2.SelectedIndexChanged += comboBox2_SelectedIndexChanged;
        AutoScaleDimensions = new SizeF(6f, 13f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(363, 370);
        Controls.Add(comboBox2);
        Controls.Add(label20);
        Controls.Add(groupBox1);
        Controls.Add(button1);
        Controls.Add(label1);
        Controls.Add(comboBox1);
        Name = "NpcList";
        StartPosition = FormStartPosition.CenterScreen;
        Text = "NpcList";
        FormClosing += NpcList_FormClosing;
        Load += NpcList_Load;
        groupBox1.ResumeLayout(false);
        groupBox1.PerformLayout();
        ResumeLayout(false);
        PerformLayout();
    }
}