using System.Collections.Generic;

namespace RxjhServer;

public class X_Che_Tac_Vat_Pham_Loai
{
    public List<X_Che_Tac_Can_Vat_Pham_Loai> CanVatPham = new();

    public int CheTaoDangCap;

    public int CheTaoLoaiHinh;

    public int VatPham_ID;

    public int VatPhamSoLuong;

    public string VatPhamTen;

    public static List<int> Get制作物品类列表(int int_0, int int_1)
    {
        var list = new List<int>();
        using var enumerator = World.CheTacVatPhamDanhSach.Values.GetEnumerator();
        X_Che_Tac_Vat_Pham_Loai 制作物品类 = null;
        while (enumerator.MoveNext())
        {
            制作物品类 = enumerator.Current;
            if (制作物品类.CheTaoLoaiHinh == int_0 && int_1 >= 制作物品类.CheTaoDangCap) list.Add(制作物品类.VatPham_ID);
        }

        return list;
    }
}