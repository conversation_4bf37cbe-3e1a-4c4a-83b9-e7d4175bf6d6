using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading;

namespace RxjhServer;

public class Timer
{
    private static readonly Queue<Timer> m_Queue = new();

    private static int int_3;

    private bool bool_0;

    private bool bool_1;

    private int int_0;

    private readonly int int_1;

    private List<Timer> m_List;

    private TimerPriority timerPriority_0;

    public Timer(TimeSpan timeSpan_2)
        : this(timeSpan_2, TimeSpan.Zero, 1)
    {
    }

    public Timer(TimeSpan timeSpan_2, TimeSpan timeSpan_3)
        : this(timeSpan_2, timeSpan_3, 0)
    {
    }

    public Timer(TimeSpan timeSpan_2, TimeSpan timeSpan_3, int int_4)
    {
        Delay = timeSpan_2;
        Interval = timeSpan_3;
        int_1 = int_4;
        if (DefRegCreation) RegCreation();
    }

    public TimerPriority Priority
    {
        get => timerPriority_0;
        set
        {
            if (timerPriority_0 != value)
            {
                timerPriority_0 = value;
                if (bool_0) TimerThread.PriorityChange(this, (int)timerPriority_0);
            }
        }
    }

    public DateTime Next { get; private set; }

    public TimeSpan Delay { get; set; }

    public TimeSpan Interval { get; set; }

    public bool Running
    {
        get => bool_0;
        set
        {
            if (value)
                Start();
            else
                Stop();
        }
    }

    public static Dictionary<string, TimerProfile> Profiles { get; } = new();

    public static int BreakCount { get; set; } = 20000;

    public virtual bool DefRegCreation => true;

    private static string smethod_0(Delegate delegate_0)
    {
        if ((object)delegate_0 == null) return "null";
        return delegate_0.Method.DeclaringType.FullName + "." + delegate_0.Method.Name;
    }

    public static void DumpInfo(TextWriter textWriter_0)
    {
        TimerThread.DumpInfo(textWriter_0);
    }

    public TimerProfile GetProfile()
    {
        TimerProfile timerProfile = null;
        var text = ToString();
        if (text == null) text = "null";
        timerProfile = null;
        Profiles.TryGetValue(text, out timerProfile);
        if (timerProfile == null)
        {
            var timerProfile8 = Profiles[text] = new TimerProfile();
            var timerProfile6 = timerProfile8;
            var timerProfile2 = timerProfile6;
            var timerProfile3 = timerProfile2;
            var timerProfile4 = timerProfile3;
            var timerProfile5 = timerProfile4;
            timerProfile = timerProfile5;
        }

        return timerProfile;
    }

    public static void Slice()
    {
        lock (m_Queue)
        {
            Timer timer = null;
            TimerProfile timerProfile = null;
            int_3 = m_Queue.Count;
            var num = 0;
            Stopwatch stopwatch = null;
            while (num < BreakCount && m_Queue.Count != 0)
            {
                timer = m_Queue.Dequeue();
                timerProfile = timer.GetProfile();
                if (timerProfile != null)
                {
                    if (stopwatch == null)
                        stopwatch = Stopwatch.StartNew();
                    else
                        stopwatch.Start();
                }

                timer.OnTick();
                timer.bool_1 = false;
                num++;
                if (timerProfile != null)
                {
                    timerProfile.RegTicked(stopwatch.Elapsed);
                    stopwatch.Reset();
                }
            }
        }
    }

    public virtual void RegCreation()
    {
        GetProfile()?.RegCreation();
    }

    public override string ToString()
    {
        return GetType().FullName;
    }

    public static TimerPriority ComputePriority(TimeSpan timeSpan_2)
    {
        if (timeSpan_2 >= TimeSpan.FromMinutes(1.0)) return TimerPriority.FiveSeconds;
        if (timeSpan_2 >= TimeSpan.FromSeconds(10.0)) return TimerPriority.OneSecond;
        if (timeSpan_2 >= TimeSpan.FromSeconds(5.0)) return TimerPriority.TwoFiftyMS;
        if (timeSpan_2 >= TimeSpan.FromSeconds(2.5)) return TimerPriority.FiftyMS;
        if (timeSpan_2 >= TimeSpan.FromSeconds(1.0)) return TimerPriority.TwentyFiveMS;
        if (timeSpan_2 >= TimeSpan.FromSeconds(0.5)) return TimerPriority.TenMS;
        return TimerPriority.EveryTick;
    }

    public static Timer DelayCall(TimeSpan timeSpan_2, TimerCallback timerCallback_0)
    {
        return DelayCall(timeSpan_2, TimeSpan.Zero, 1, timerCallback_0);
    }

    public static Timer DelayCall(TimeSpan timeSpan_2, TimeSpan timeSpan_3, TimerCallback timerCallback_0)
    {
        return DelayCall(timeSpan_2, timeSpan_3, 0, timerCallback_0);
    }

    public static Timer DelayCall(TimeSpan timeSpan_2, TimeSpan timeSpan_3, int int_4, TimerCallback timerCallback_0)
    {
        Timer timer = new DelayCallTimer(timeSpan_2, timeSpan_3, int_4, timerCallback_0);
        if (int_4 == 1)
            timer.Priority = ComputePriority(timeSpan_2);
        else
            timer.Priority = ComputePriority(timeSpan_3);
        timer.Start();
        return timer;
    }

    public static Timer DelayCall(TimeSpan timeSpan_2, TimerStateCallback timerStateCallback_0, object object_0)
    {
        return DelayCall(timeSpan_2, TimeSpan.Zero, 1, timerStateCallback_0, object_0);
    }

    public static Timer DelayCall(TimeSpan timeSpan_2, TimeSpan timeSpan_3, TimerStateCallback timerStateCallback_0,
        object object_0)
    {
        return DelayCall(timeSpan_2, timeSpan_3, 0, timerStateCallback_0, object_0);
    }

    public static Timer DelayCall(TimeSpan timeSpan_2, TimeSpan timeSpan_3, int int_4,
        TimerStateCallback timerStateCallback_0, object object_0)
    {
        Timer timer = new DelayStateCallTimer(timeSpan_2, timeSpan_3, int_4, timerStateCallback_0, object_0);
        if (int_4 == 1)
            timer.Priority = ComputePriority(timeSpan_2);
        else
            timer.Priority = ComputePriority(timeSpan_3);
        timer.Start();
        return timer;
    }

    public void Start()
    {
        if (!bool_0)
        {
            bool_0 = true;
            TimerThread.AddTimer(this);
            GetProfile()?.RegStart();
        }
    }

    public void Stop()
    {
        if (bool_0)
        {
            bool_0 = false;
            TimerThread.RemoveTimer(this);
            GetProfile()?.RegStopped();
        }
    }

    protected virtual void OnTick()
    {
    }

    public class TimerThread
    {
        private static readonly Queue queue_0 = Queue.Synchronized(new Queue());

        private static readonly DateTime[] m_NextPriorities = new DateTime[8];

        private static readonly TimeSpan[] m_PriorityDelays = new TimeSpan[8]
        {
            TimeSpan.Zero,
            TimeSpan.FromMilliseconds(10.0),
            TimeSpan.FromMilliseconds(25.0),
            TimeSpan.FromMilliseconds(50.0),
            TimeSpan.FromMilliseconds(250.0),
            TimeSpan.FromSeconds(1.0),
            TimeSpan.FromSeconds(5.0),
            TimeSpan.FromMinutes(1.0)
        };

        private static readonly List<Timer>[] m_Timers = new List<Timer>[8]
        {
            new(),
            new(),
            new(),
            new(),
            new(),
            new(),
            new(),
            new()
        };

        public static void DumpInfo(TextWriter textWriter_0)
        {
            Dictionary<string, List<Timer>> dictionary = null;
            List<Timer> value = null;
            Timer timer = null;
            var num2 = 0;
            string text = null;
            for (var i = 0; i < 8; i++)
            {
                textWriter_0.WriteLine("Priority: {0}", (TimerPriority)i);
                textWriter_0.WriteLine();
                dictionary = new Dictionary<string, List<Timer>>();
                for (num2 = 0; num2 < m_Timers[i].Count; num2++)
                {
                    timer = m_Timers[i][num2];
                    text = timer.ToString();
                    dictionary.TryGetValue(text, out value);
                    if (value == null)
                    {
                        var list8 = dictionary[text] = new List<Timer>();
                        var list6 = list8;
                        var list2 = list6;
                        var list3 = list2;
                        var list4 = list3;
                        var list5 = list4;
                        value = list5;
                    }

                    value.Add(timer);
                }

                foreach (var item in dictionary)
                {
                    var key = item.Key;
                    var value2 = item.Value;
                    textWriter_0.WriteLine("Type: {0}; Count: {1}; Percent: {2}%", key, value2.Count,
                        (int)(100.0 * (value2.Count / (double)m_Timers[i].Count)));
                }

                textWriter_0.WriteLine();
                textWriter_0.WriteLine();
            }
        }

        public static void Change(Timer timer_0, int int_0, bool bool_0)
        {
            queue_0.Enqueue(TimerChangeEntry.GetInstance(timer_0, int_0, bool_0));
        }

        public static void AddTimer(Timer timer_0)
        {
            Change(timer_0, (int)timer_0.Priority, true);
        }

        public static void PriorityChange(Timer timer_0, int int_0)
        {
            Change(timer_0, int_0, false);
        }

        public static void RemoveTimer(Timer timer_0)
        {
            Change(timer_0, -1, false);
        }

        private static void smethod_0()
        {
            Timer timer = null;
            var num = 0;
            TimerChangeEntry timerChangeEntry = null;
            while (queue_0.Count > 0)
            {
                timerChangeEntry = (TimerChangeEntry)queue_0.Dequeue();
                timer = timerChangeEntry.m_Timer;
                num = timerChangeEntry.m_NewIndex;
                timer.m_List?.Remove(timer);
                if (timerChangeEntry.m_IsAdd)
                {
                    timer.Next = DateTime.Now + timer.Delay;
                    timer.int_0 = 0;
                }

                if (num >= 0)
                {
                    timer.m_List = m_Timers[num];
                    timer.m_List.Add(timer);
                }
                else
                {
                    timer.m_List = null;
                }

                timerChangeEntry.Free();
            }
        }

        public void TimerMain()
        {
            var num = 0;
            var dateTime = default(DateTime);
            Timer timer = null;
            while (true)
            {
                Thread.Sleep(10);
                smethod_0();
                for (var i = 0; i < m_Timers.Length; i++)
                {
                    dateTime = DateTime.Now;
                    if (dateTime < m_NextPriorities[i]) break;
                    m_NextPriorities[i] = dateTime + m_PriorityDelays[i];
                    for (num = 0; num < m_Timers[i].Count; num++)
                    {
                        timer = m_Timers[i][num];
                        if (!timer.bool_1 && dateTime > timer.Next)
                        {
                            timer.bool_1 = true;
                            lock (m_Queue)
                            {
                                m_Queue.Enqueue(timer);
                            }

                            if (timer.int_1 != 0 && ++timer.int_0 >= timer.int_1)
                                timer.Stop();
                            else
                                timer.Next = dateTime + timer.Interval;
                        }
                    }
                }
            }
        }

        private class TimerChangeEntry
        {
            private static readonly Queue<TimerChangeEntry> m_InstancePool = new();

            public bool m_IsAdd;

            public int m_NewIndex;
            public Timer m_Timer;

            private TimerChangeEntry(Timer timer_0, int int_0, bool bool_0)
            {
                m_Timer = timer_0;
                m_NewIndex = int_0;
                m_IsAdd = bool_0;
            }

            public void Free()
            {
            }

            public static TimerChangeEntry GetInstance(Timer timer_0, int int_0, bool bool_0)
            {
                TimerChangeEntry timerChangeEntry = null;
                if (m_InstancePool.Count > 0)
                {
                    timerChangeEntry = m_InstancePool.Dequeue();
                    if (timerChangeEntry == null)
                    {
                        timerChangeEntry = new TimerChangeEntry(timer_0, int_0, bool_0);
                    }
                    else
                    {
                        timerChangeEntry.m_Timer = timer_0;
                        timerChangeEntry.m_NewIndex = int_0;
                        timerChangeEntry.m_IsAdd = bool_0;
                    }
                }
                else
                {
                    timerChangeEntry = new TimerChangeEntry(timer_0, int_0, bool_0);
                }

                return timerChangeEntry;
            }
        }
    }

    private class DelayCallTimer : Timer
    {
        public DelayCallTimer(TimeSpan timeSpan_2, TimeSpan timeSpan_3, int int_4, TimerCallback timerCallback_1)
            : base(timeSpan_2, timeSpan_3, int_4)
        {
            Callback = timerCallback_1;
            RegCreation();
        }

        public TimerCallback Callback { get; }

        public override bool DefRegCreation => false;

        protected override void OnTick()
        {
            var num = 2;
            while (true)
            {
                switch (num)
                {
                    case 1:
                        return;
                    case 0:
                        Callback();
                        num = 1;
                        continue;
                }

                if (Callback != null)
                {
                    num = 0;
                    continue;
                }

                return;
            }
        }

        public override string ToString()
        {
            return "DelayCallTimer[" + smethod_0(Callback) + "]";
        }
    }

    private class DelayStateCallTimer : Timer
    {
        private readonly object m_State;

        public DelayStateCallTimer(TimeSpan timeSpan_2, TimeSpan timeSpan_3, int int_4,
            TimerStateCallback timerStateCallback_1, object object_0)
            : base(timeSpan_2, timeSpan_3, int_4)
        {
            Callback = timerStateCallback_1;
            m_State = object_0;
            RegCreation();
        }

        public TimerStateCallback Callback { get; }

        public override bool DefRegCreation => false;

        protected override void OnTick()
        {
            var num = 2;
            while (true)
            {
                switch (num)
                {
                    case 1:
                        return;
                    case 0:
                        Callback(m_State);
                        num = 1;
                        continue;
                }

                if (Callback != null)
                {
                    num = 0;
                    continue;
                }

                return;
            }
        }

        public override string ToString()
        {
            return "DelayStateCall[" + smethod_0(Callback) + "]";
        }
    }
}