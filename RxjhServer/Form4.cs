using System;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class Form4 : Form
{
    private Button button1;

    private Button button7;
    private IContainer components;

    private GroupBox groupBox1;

    private Label label1;

    private Label label2;

    private Label label3;

    private Label label4;

    private ListBox listBox2;

    private StatusStrip statusStrip1;

    private TextBox textBox1;

    private TextBox textBox2;

    private TextBox textBox3;

    private ToolStripStatusLabel tishi;

    private ToolStripStatusLabel toolStripStatusLabel1;

    public Form4()
    {
        InitializeComponent();
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        groupBox1 = new GroupBox();
        button7 = new Button();
        label4 = new Label();
        listBox2 = new ListBox();
        label3 = new Label();
        textBox3 = new TextBox();
        label2 = new Label();
        textBox2 = new TextBox();
        label1 = new Label();
        textBox1 = new TextBox();
        button1 = new Button();
        statusStrip1 = new StatusStrip();
        toolStripStatusLabel1 = new ToolStripStatusLabel();
        tishi = new ToolStripStatusLabel();
        groupBox1.SuspendLayout();
        statusStrip1.SuspendLayout();
        SuspendLayout();
        groupBox1.Controls.Add(button7);
        groupBox1.Controls.Add(label4);
        groupBox1.Controls.Add(listBox2);
        groupBox1.Controls.Add(label3);
        groupBox1.Controls.Add(textBox3);
        groupBox1.Controls.Add(label2);
        groupBox1.Controls.Add(textBox2);
        groupBox1.Controls.Add(label1);
        groupBox1.Controls.Add(textBox1);
        groupBox1.Controls.Add(button1);
        groupBox1.Location = new Point(16, 16);
        groupBox1.Margin = new Padding(4, 4, 4, 4);
        groupBox1.Name = "groupBox1";
        groupBox1.Padding = new Padding(4, 4, 4, 4);
        groupBox1.Size = new Size(613, 295);
        groupBox1.TabIndex = 0;
        groupBox1.TabStop = false;
        groupBox1.Text = "角色换名";
        button7.Location = new Point(292, 29);
        button7.Margin = new Padding(4, 4, 4, 4);
        button7.Name = "button7";
        button7.Size = new Size(100, 31);
        button7.TabIndex = 9;
        button7.Text = "查找";
        button7.UseVisualStyleBackColor = true;
        button7.Click += button7_Click;
        label4.AutoSize = true;
        label4.Location = new Point(403, 23);
        label4.Margin = new Padding(4, 0, 4, 0);
        label4.Name = "label4";
        label4.Size = new Size(64, 17);
        label4.TabIndex = 8;
        label4.Text = "角色列表";
        listBox2.FormattingEnabled = true;
        listBox2.ItemHeight = 16;
        listBox2.Location = new Point(405, 51);
        listBox2.Margin = new Padding(4, 4, 4, 4);
        listBox2.Name = "listBox2";
        listBox2.Size = new Size(168, 84);
        listBox2.TabIndex = 7;
        listBox2.Click += listBox2_Click;
        label3.AutoSize = true;
        label3.Location = new Point(8, 36);
        label3.Margin = new Padding(4, 0, 4, 0);
        label3.Name = "label3";
        label3.Size = new Size(105, 17);
        label3.TabIndex = 6;
        label3.Text = "player account ";
        textBox3.Location = new Point(151, 32);
        textBox3.Margin = new Padding(4, 4, 4, 4);
        textBox3.Name = "textBox3";
        textBox3.Size = new Size(132, 22);
        textBox3.TabIndex = 5;
        label2.AutoSize = true;
        label2.Location = new Point(8, 112);
        label2.Margin = new Padding(4, 0, 4, 0);
        label2.Name = "label2";
        label2.Size = new Size(111, 17);
        label2.TabIndex = 4;
        label2.Text = "character name ";
        textBox2.Location = new Point(151, 108);
        textBox2.Margin = new Padding(4, 4, 4, 4);
        textBox2.Name = "textBox2";
        textBox2.Size = new Size(132, 22);
        textBox2.TabIndex = 3;
        label1.AutoSize = true;
        label1.Location = new Point(8, 76);
        label1.Margin = new Padding(4, 0, 4, 0);
        label1.Name = "label1";
        label1.Size = new Size(107, 17);
        label1.TabIndex = 2;
        label1.Text = "character name";
        textBox1.Location = new Point(151, 72);
        textBox1.Margin = new Padding(4, 4, 4, 4);
        textBox1.Name = "textBox1";
        textBox1.Size = new Size(132, 22);
        textBox1.TabIndex = 1;
        button1.Location = new Point(151, 161);
        button1.Margin = new Padding(4, 4, 4, 4);
        button1.Name = "button1";
        button1.Size = new Size(133, 39);
        button1.TabIndex = 0;
        button1.Text = "确认更改";
        button1.UseVisualStyleBackColor = true;
        button1.Click += button1_Click;
        statusStrip1.ImageScalingSize = new Size(20, 20);
        statusStrip1.Items.AddRange(new ToolStripItem[2] { toolStripStatusLabel1, tishi });
        statusStrip1.Location = new Point(0, 337);
        statusStrip1.Name = "statusStrip1";
        statusStrip1.Padding = new Padding(1, 0, 19, 0);
        statusStrip1.Size = new Size(651, 26);
        statusStrip1.TabIndex = 65;
        statusStrip1.Text = "statusStrip1";
        toolStripStatusLabel1.Name = "toolStripStatusLabel1";
        toolStripStatusLabel1.Size = new Size(89, 20);
        toolStripStatusLabel1.Text = "信息提示：";
        tishi.ForeColor = Color.Red;
        tishi.Name = "tishi";
        tishi.Size = new Size(0, 20);
        AutoScaleDimensions = new SizeF(8f, 16f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(651, 363);
        Controls.Add(statusStrip1);
        Controls.Add(groupBox1);
        Margin = new Padding(4, 4, 4, 4);
        Name = "Form4";
        Text = "角色换名工具";
        groupBox1.ResumeLayout(false);
        groupBox1.PerformLayout();
        statusStrip1.ResumeLayout(false);
        statusStrip1.PerformLayout();
        ResumeLayout(false);
        PerformLayout();
    }

    private void button1_Click(object sender, EventArgs e)
    {
        var num2 = 0;
        DataTable dataTable = null;
        Players players = null;
        DataTable dataTable2 = null;
        DataTable dataTable3 = null;
        if (!(textBox1.Text.Trim() == string.Empty) && !(textBox2.Text.Trim() == string.Empty) &&
            !(textBox3.Text.Trim() == string.Empty))
        {
            players = World.KiemTra_Ten_NguoiChoi(textBox1.Text);
            if (players != null)
            {
                tishi.Text = "当前玩家在线无法更改,请离线后在更改";
                return;
            }

            var string_ = "SELECT * FROM TBL_XWWL_GuildMember WHERE name ='" + textBox1.Text + "'";
            dataTable2 = DBA.GetDBToDataTable(string_);
            var string_2 = "SELECT * FROM TBL_XWWL_Guild WHERE G_Master ='" + textBox1.Text + "'";
            dataTable3 = DBA.GetDBToDataTable(string_2);
            if (dataTable2.Rows.Count <= 0 && dataTable3.Rows.Count <= 0)
            {
                var string_3 =
                    "UPDATE TBL_XWWL_Char SET FLD_NAME = @sTemp WHERE FLD_ID = @Userid AND FLD_NAME = @Username";
                var sqlParameter_ = new SqlParameter[3]
                {
                    SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, textBox3.Text),
                    SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, textBox1.Text),
                    SqlDBA.MakeInParam("@sTemp", SqlDbType.VarChar, 30, textBox2.Text)
                };
                DBA.ExeSqlCommand(string_3, sqlParameter_);
                string_3 =
                    "UPDATE TBL_XWWL_Warehouse SET FLD_NAME = @sTemp WHERE FLD_ID = @Userid AND FLD_NAME = @Username";
                var sqlParameter_2 = new SqlParameter[3]
                {
                    SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, textBox3.Text),
                    SqlDBA.MakeInParam("@Username", SqlDbType.VarChar, 30, textBox1.Text),
                    SqlDBA.MakeInParam("@sTemp", SqlDbType.VarChar, 30, textBox2.Text)
                };
                DBA.ExeSqlCommand(string_3, sqlParameter_2);
                tishi.Text = "换名成功请进入游戏查看";
                listBox2.Items.Clear();
                var string_4 = "select * from tbl_xwwl_char  where fld_id='" + textBox3.Text + "'";
                dataTable = DBA.GetDBToDataTable(string_4);
                for (num2 = 0; num2 < dataTable.Rows.Count; num2++)
                    listBox2.Items.Add(dataTable.Rows[num2]["FLD_NAME"].ToString());
            }
            else
            {
                tishi.Text = "请通知玩家退出门派后在更名";
            }
        }
        else
        {
            tishi.Text = "请认真填写每项，每项不能为空";
        }
    }

    private void button7_Click(object sender, EventArgs e)
    {
        try
        {
            listBox2.Items.Clear();
            var string_ = "select * from tbl_xwwl_char  where fld_id='" + textBox3.Text + "'";
            var dBToDataTable = DBA.GetDBToDataTable(string_);
            for (var i = 0; i < dBToDataTable.Rows.Count; i++)
                listBox2.Items.Add(dBToDataTable.Rows[i]["FLD_NAME"].ToString());
            tishi.Text = "查询完毕";
        }
        catch
        {
            tishi.Text = "出现未知错误";
        }
    }

    private void listBox2_Click(object sender, EventArgs e)
    {
        textBox1.Text = listBox2.Text;
    }
}