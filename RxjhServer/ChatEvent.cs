using System;
using System.Timers;

namespace RxjhServer;

public class ChatEvent : IDisposable
{
    private DateTime dateTime_0;

    private DateTime dateTime_1;

    private int int_0;

    private readonly System.Timers.Timer timer_0;

    private System.Timers.Timer timer_1;

    public ChatEvent()
    {
        try
        {
            if (World.jlMsg == 1) Form1.WriteLine(0, "ChataEvent-");
            dateTime_0 = DateTime.Now.AddMinutes(5.0);
            timer_0 = new System.Timers.Timer(60000.0);
            timer_0.Elapsed += TimeEventEnd1;
            timer_0.Enabled = true;
            timer_0.AutoReset = true;
            TimeEventEnd1(null, null);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Trăm miệng một lời Trăm miệng một lờiPhạm sai lầm: " + ex);
        }
    }

    public void Dispose()
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Trăm miệng một lời -Dispose");
        if (timer_0 != null)
        {
            timer_0.Enabled = false;
            timer_0.Close();
            timer_0.Dispose();
        }

        if (timer_1 != null)
        {
            timer_1.Enabled = false;
            timer_1.Close();
            timer_1.Dispose();
        }

        World.ykts = null;
        World.Chat_Event_ON_OFF = 0;
    }

    public void TimeEventEnd1(object sender, ElapsedEventArgs e)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Trăm miệng một lời thời gian kết thúc sự kiện 1");
        try
        {
            var int_0 = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
            foreach (var current in World.allConnectedChars.Values)
            {
                current.SystemCountdown(0, int_0);
                current.HeThongNhacNho("thời gian còn lại:" + int_0 / 60 + "Phút", 10, "ChatEvent");
                current.SystemNotification("Khi thời gian kết thúc nhập vào khung chat nội dung:[" +
                                           World.UnanimousContent + "] để nhận buff thần bí");
            }

            if (int_0 <= 0)
            {
                World.Chat_Event_ON_OFF = 1;
                timer_0.Enabled = false;
                timer_0.Close();
                timer_0.Dispose();
                dateTime_1 = DateTime.Now.AddMinutes(World.UnanimousEndTime);
                timer_1 = new System.Timers.Timer(World.UnanimousEndTime * 60000);
                timer_1.Elapsed += TimeEventEnd2;
                timer_1.Enabled = true;
                timer_1.AutoReset = true;
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Trăm miệng một lời Thời gian kết thúc sự kiện 1Phạm sai lầm: " + ex);
        }
    }

    public void TimeEventEnd2(object sender, ElapsedEventArgs e)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Trăm miệng một lời thời gian kết thúc sự kiện 2");
        try
        {
            var num3 = (int)dateTime_1.Subtract(DateTime.Now).TotalSeconds;
            if (num3 > 0) return;
            timer_1.Enabled = false;
            timer_1.Close();
            timer_1.Dispose();
            foreach (var current in World.allConnectedChars.Values)
            {
                current.SystemCountdown(0, 0);
                current.SystemNotification("Sự kiện đã kết thúc, lần sau hãy tham gia sớm hơn！！");
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Trăm miệng một lời Thời gian kết thúc sự kiện 2Phạm sai lầm: " + ex);
        }
    }
}