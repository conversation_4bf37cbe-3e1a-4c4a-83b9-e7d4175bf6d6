using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class Form10 : Form
{
    private Button button1;

    private Button button2;

    private Button button3;

    private Button button4;

    private Button button5;

    private ComboBox comboBox11;

    private ComboBox comboBox12;
    private IContainer components;

    private GroupBox groupBox1;

    private GroupBox groupBox2;

    private GroupBox groupBox3;

    private Label label1;

    private Label label10;

    private Label label11;

    private Label label12;

    private Label label13;

    private Label label14;

    private Label label15;

    private Label label16;

    private Label label2;

    private Label label20;

    private Label label25;

    private Label label27;

    private Label label28;

    private Label label3;

    private Label label4;

    private Label label40;

    private Label label5;

    private Label label6;

    private Label label7;

    private Label label8;

    private Label label9;

    private ListBox listBox1;

    private ListBox listBox3;

    private RadioButton radioButton1;

    private RadioButton radioButton2;

    private TextBox textBox1;

    private TextBox textBox10;

    private TextBox textBox11;

    private TextBox textBox12;

    private TextBox textBox13;

    private TextBox textBox14;

    private TextBox textBox15;

    private TextBox textBox16;

    private TextBox textBox17;

    private TextBox textBox18;

    private TextBox textBox2;

    private TextBox textBox3;

    private TextBox textBox4;

    private TextBox textBox5;

    private TextBox textBox6;

    private TextBox textBox7;

    private TextBox textBox8;

    private TextBox textBox9;

    public Form10()
    {
        InitializeComponent();
        label28.Text = "0";
    }

    private void 刷新()
    {
        listBox1.Items.Clear();
        var string_ = "select FLD_NAME from ITEMSELL";
        var dBToDataTable = DBA.GetDBToDataTable(string_, "BBG");
        for (var i = 0; i < dBToDataTable.Rows.Count; i++)
            listBox1.Items.Add(dBToDataTable.Rows[i]["FLD_NAME"].ToString());
        label28.Text = dBToDataTable.Rows.Count.ToString();
        dBToDataTable.Dispose();
    }

    private void button3_Click(object sender, EventArgs e)
    {
        listBox1.Items.Clear();
        var string_ = "select FLD_NAME from ITEMSELL";
        var dBToDataTable = DBA.GetDBToDataTable(string_, "BBG");
        for (var i = 0; i < dBToDataTable.Rows.Count; i++)
            listBox1.Items.Add(dBToDataTable.Rows[i]["FLD_NAME"].ToString());
        label28.Text = dBToDataTable.Rows.Count.ToString();
        dBToDataTable.Dispose();
    }

    private void listBox1_SelectedValueChanged(object sender, EventArgs e)
    {
        try
        {
            var string_ = "select * from ITEMSELL where FLD_NAME='" + listBox1.Text + "'";
            var dBToDataTable = DBA.GetDBToDataTable(string_, "BBG");
            if (dBToDataTable != null)
            {
                textBox1.Text = dBToDataTable.Rows[0]["FLD_PID"].ToString();
                textBox3.Text = dBToDataTable.Rows[0]["FLD_Name"].ToString();
                textBox4.Text = dBToDataTable.Rows[0]["FLD_PRICE"].ToString();
                textBox5.Text = dBToDataTable.Rows[0]["FLD_TYPE"].ToString();
                textBox6.Text = dBToDataTable.Rows[0]["FLD_RETURN"].ToString();
                textBox7.Text = dBToDataTable.Rows[0]["FLD_NUMBER"].ToString();
                textBox8.Text = dBToDataTable.Rows[0]["FLD_MAGIC1"].ToString();
                textBox9.Text = dBToDataTable.Rows[0]["FLD_MAGIC2"].ToString();
                textBox10.Text = dBToDataTable.Rows[0]["FLD_MAGIC3"].ToString();
                textBox11.Text = dBToDataTable.Rows[0]["FLD_MAGIC4"].ToString();
                textBox12.Text = dBToDataTable.Rows[0]["FLD_MAGIC5"].ToString();
                textBox2.Text = dBToDataTable.Rows[0]["FLD_SoCapPhuHon"].ToString();
                textBox13.Text = dBToDataTable.Rows[0]["FLD_TrungCapPhuHon"].ToString();
                textBox16.Text = dBToDataTable.Rows[0]["FLD_TienHoa"].ToString();
                textBox17.Text = dBToDataTable.Rows[0]["FLD_PhaiChangKhoaLai"].ToString();
                textBox18.Text = dBToDataTable.Rows[0]["FLD_DAYS"].ToString();
                textBox14.Text = dBToDataTable.Rows[0]["FLD_DESC"].ToString();
            }

            dBToDataTable.Dispose();
        }
        catch (Exception)
        {
            MessageBox.Show("未知错误!", "Msg");
        }
    }

    internal static uint ComputeStringHash(string string_0)
    {
        var num = 0;
        var num2 = 0u;
        if (string_0 != null)
        {
            num2 = 2166136261u;
            for (num = 0; num < string_0.Length; num++) num2 = (string_0[num] ^ num2) * 16777619;
        }

        return num2;
    }

    private void comboBox11_SelectedIndexChanged(object sender, EventArgs e)
    {
        DataTable dataTable = null;
        var num2 = 0;
        var arg = "";
        var text = comboBox11.Text;
        switch (ComputeStringHash(text))
        {
            case 963180387u:
                if (text == "武器") arg = "4";
                break;
            case 867671693u:
                if (text == "衣服") arg = "1";
                break;
            case 477010303u:
                if (text == "门甲") arg = "14";
                break;
            case 1895148392u:
                if (text == "护手") arg = "2";
                break;
            case 1618293597u:
                if (text == "弓箭") arg = "13";
                break;
            case 1502406078u:
                if (text == "项链") arg = "7";
                break;
            case 3393768302u:
                if (text == "鞋子") arg = "5";
                break;
            case 3322318871u:
                if (text == "耳环") arg = "8";
                break;
            case 2913798006u:
                if (text == "戒指") arg = "10";
                break;
            case 4183737334u:
                if (text == "内甲") arg = "6";
                break;
            case 3578152463u:
                if (text == "宝宝") arg = "15";
                break;
            case 3558870252u:
                if (text == "披风") arg = "12";
                break;
        }

        listBox3.Items.Clear();
        comboBox12.Items.Clear();
        var string_ = "select * from TBL_XWWL_ITEM where FLD_RESIDE2='" + arg + "'";
        dataTable = DBA.GetDBToDataTable(string_, "PublicDb");
        for (num2 = 0; num2 < dataTable.Rows.Count; num2++)
        {
            listBox3.Items.Add(dataTable.Rows[num2]["FLD_NAME"].ToString());
            comboBox12.Items.Add(dataTable.Rows[num2]["FLD_PID"].ToString());
        }

        dataTable.Dispose();
    }

    private void listBox3_SelectedIndexChanged(object sender, EventArgs e)
    {
        comboBox12.SelectedIndex = listBox3.SelectedIndex;
        textBox1.Text = comboBox12.Text;
        textBox3.Text = listBox3.Text;
    }

    private void button2_Click(object sender, EventArgs e)
    {
        DataTable dataTable = null;
        var num3 = 0;
        var num4 = 0;
        if (listBox1.Items.Count == 0)
        {
            MessageBox.Show("请先查询数据!", "Msg");
            return;
        }

        if (textBox15.Text == "")
        {
            MessageBox.Show("请先输入要查询的内容!", "Msg");
            return;
        }

        if (radioButton1.Checked)
            try
            {
                if (!int.TryParse(textBox15.Text, NumberStyles.Integer, NumberFormatInfo.CurrentInfo, out var _))
                {
                    MessageBox.Show("请输入正确的物品ID!", "Msg");
                }
                else
                {
                    try
                    {
                        while (true)
                        {
                            switch (int.Parse(textBox15.Text) > 0 ? 1 : 3)
                            {
                                case 1:
                                    break;
                                case 2:
                                    return;
                                case 3:
                                    MessageBox.Show("必须是正整数");
                                    return;
                                default:
                                    continue;
                            }

                            break;
                        }
                    }
                    catch (FormatException)
                    {
                        MessageBox.Show("必须是正整数");
                        return;
                    }

                    var num5 = Convert.ToInt32(textBox15.Text);
                    var string_ = $"select * from ITEMSELL where FLD_PID={num5}";
                    dataTable = DBA.GetDBToDataTable(string_, "BBG");
                    if (dataTable != null)
                    {
                        textBox1.Text = dataTable.Rows[0]["FLD_PID"].ToString();
                        textBox3.Text = dataTable.Rows[0]["FLD_Name"].ToString();
                        textBox4.Text = dataTable.Rows[0]["FLD_PRICE"].ToString();
                        textBox5.Text = dataTable.Rows[0]["FLD_TYPE"].ToString();
                        textBox6.Text = dataTable.Rows[0]["FLD_RETURN"].ToString();
                        textBox7.Text = dataTable.Rows[0]["FLD_NUMBER"].ToString();
                        textBox8.Text = dataTable.Rows[0]["FLD_MAGIC1"].ToString();
                        textBox9.Text = dataTable.Rows[0]["FLD_MAGIC2"].ToString();
                        textBox10.Text = dataTable.Rows[0]["FLD_MAGIC3"].ToString();
                        textBox11.Text = dataTable.Rows[0]["FLD_MAGIC4"].ToString();
                        textBox12.Text = dataTable.Rows[0]["FLD_MAGIC5"].ToString();
                        textBox2.Text = dataTable.Rows[0]["FLD_SoCapPhuHon"].ToString();
                        textBox13.Text = dataTable.Rows[0]["FLD_TrungCapPhuHon"].ToString();
                        textBox16.Text = dataTable.Rows[0]["FLD_TienHoa"].ToString();
                        textBox17.Text = dataTable.Rows[0]["FLD_PhaiChangKhoaLai"].ToString();
                        textBox18.Text = dataTable.Rows[0]["FLD_DAYS"].ToString();
                        textBox14.Text = dataTable.Rows[0]["FLD_DESC"].ToString();
                        for (num3 = 0; num3 < listBox1.Items.Count; num3++)
                            if (listBox1.Items[num3].Equals(textBox3.Text))
                            {
                                listBox1.SetSelected(num3, true);
                                return;
                            }
                    }
                    else
                    {
                        MessageBox.Show("无此物品,请检查PID是否正确！", "Msg");
                    }

                    dataTable.Dispose();
                }

                return;
            }
            catch (Exception ex2)
            {
                MessageBox.Show(ex2.ToString(), "错误");
                return;
            }

        if (radioButton2.Checked)
            try
            {
                var flag = true;
                while (true)
                {
                    var string_2 = "select * from ITEMSELL where FLD_NAME='" + textBox15.Text + "'";
                    var dBToDataTable = DBA.GetDBToDataTable(string_2, "BBG");
                    while (true)
                    {
                        IL_08af:
                        if (dBToDataTable != null)
                        {
                            var flag2 = true;
                            while (true)
                            {
                                IL_0504:
                                textBox1.Text = dBToDataTable.Rows[0]["FLD_PID"].ToString();
                                textBox3.Text = dBToDataTable.Rows[0]["FLD_Name"].ToString();
                                textBox4.Text = dBToDataTable.Rows[0]["FLD_PRICE"].ToString();
                                textBox5.Text = dBToDataTable.Rows[0]["FLD_TYPE"].ToString();
                                textBox6.Text = dBToDataTable.Rows[0]["FLD_RETURN"].ToString();
                                textBox7.Text = dBToDataTable.Rows[0]["FLD_NUMBER"].ToString();
                                textBox8.Text = dBToDataTable.Rows[0]["FLD_MAGIC1"].ToString();
                                textBox9.Text = dBToDataTable.Rows[0]["FLD_MAGIC2"].ToString();
                                textBox10.Text = dBToDataTable.Rows[0]["FLD_MAGIC3"].ToString();
                                textBox11.Text = dBToDataTable.Rows[0]["FLD_MAGIC4"].ToString();
                                textBox12.Text = dBToDataTable.Rows[0]["FLD_MAGIC5"].ToString();
                                textBox2.Text = dBToDataTable.Rows[0]["FLD_SoCapPhuHon"].ToString();
                                textBox13.Text = dBToDataTable.Rows[0]["FLD_TrungCapPhuHon"].ToString();
                                textBox16.Text = dBToDataTable.Rows[0]["FLD_TienHoa"].ToString();
                                textBox17.Text = dBToDataTable.Rows[0]["FLD_PhaiChangKhoaLai"].ToString();
                                textBox18.Text = dBToDataTable.Rows[0]["FLD_DAYS"].ToString();
                                textBox14.Text = dBToDataTable.Rows[0]["FLD_DESC"].ToString();
                                num4 = 0;
                                var flag3 = true;
                                while (true)
                                {
                                    switch (num4 >= listBox1.Items.Count ? 4 : 9)
                                    {
                                        case 5:
                                            goto IL_0504;
                                        case 7:
                                            return;
                                        case 8:
                                            return;
                                        case 9:
                                            if (listBox1.Items[num4].Equals(textBox3.Text))
                                            {
                                                listBox1.SetSelected(num4, true);
                                                return;
                                            }

                                            goto case 2;
                                        case 2:
                                            num4++;
                                            continue;
                                        case 4:
                                            return;
                                        case 3:
                                        case 6:
                                            return;
                                        case 0:
                                        case 10:
                                        case 11:
                                            continue;
                                        case 1:
                                            goto IL_08af;
                                    }

                                    break;
                                }

                                break;
                            }

                            break;
                        }

                        MessageBox.Show("无此物品,请检查物品名是否正确！", "Msg");
                        return;
                    }
                }
            }
            catch (Exception ex3)
            {
                MessageBox.Show(ex3.ToString(), "错误");
                return;
            }

        MessageBox.Show("请选择查询的类型", "Msg");
    }

    public void 加载百宝阁()
    {
        var num3 = 0;
        var string_ = "SELECT * FROM ITEMSELL";
        var dBToDataTable = DBA.GetDBToDataTable(string_, "BBG");
        while (true)
        {
            switch (dBToDataTable == null ? 3 : 8)
            {
                case 3:
                    return;
                default:
                    string_ = "SELECT * FROM ITEMSELL";
                    dBToDataTable = DBA.GetDBToDataTable(string_, "BBG");
                    continue;
                case 8:
                    if (dBToDataTable.Rows.Count == 0) goto case 2;
                    World.BachBaoCat_ThuocTinhVatPhamClassList.Clear();
                    num3 = 0;
                    goto case 1;
                case 2:
                    Form1.WriteLine(1, "Tăng thêm Bách Bảo các vật phẩm ---- Không có Bách Bảo các số liệu 111");
                    break;
                case 1:
                case 6:
                case 7:

                    break;
                case 0:
                    continue;
                case 4:
                case 5:
                    break;
            }

            break;
        }

        dBToDataTable.Dispose();
    }

    private void button1_Click(object sender, EventArgs e)
    {
        if (listBox1.Items.Count == 0)
        {
            MessageBox.Show("请先查询数据库!", "Msg");
            return;
        }

        if (textBox1.Text == "")
        {
            MessageBox.Show("请先选择要修改的物品!", "Msg");
            return;
        }

        if (textBox14.Text.Length > 120)
        {
            MessageBox.Show("装备说明不能超过120个文字!");
            return;
        }

        if (textBox3.Text.Length > 15)
        {
            MessageBox.Show("装备名称不能超过15个文字!");
            return;
        }

        try
        {
            var string_ = string.Format(
                "UPDATE ITEMSELL  SET FLD_Name='{1}',FLD_PRICE={2}, FLD_TYPE={3},FLD_RETURN={4},FLD_NUMBER={5},FLD_MAGIC1={6},FLD_MAGIC2={7},FLD_MAGIC3={8},FLD_MAGIC4={9},FLD_MAGIC5={10},FLD_DESC='{11}',FLD_初级附魂={12},FLD_中级附魂={13},FLD_进化={14},FLD_是否绑定={15},FLD_DAYS={16} WHERE FLD_PID={0}",
                int.Parse(textBox1.Text), textBox3.Text, int.Parse(textBox4.Text), int.Parse(textBox5.Text),
                int.Parse(textBox6.Text), int.Parse(textBox7.Text), int.Parse(textBox8.Text), int.Parse(textBox9.Text),
                int.Parse(textBox10.Text), int.Parse(textBox11.Text), int.Parse(textBox12.Text), textBox14.Text,
                int.Parse(textBox2.Text), int.Parse(textBox13.Text), int.Parse(textBox16.Text),
                int.Parse(textBox17.Text), int.Parse(textBox18.Text));
            DBA.ExeSqlCommand(string_, "BBG");
            MessageBox.Show("修改成功!");
            加载百宝阁();
        }
        catch (Exception)
        {
            MessageBox.Show("修改出错!");
        }
    }

    private void button4_Click(object sender, EventArgs e)
    {
        if (listBox1.Items.Count == 0)
        {
            MessageBox.Show("请先查询数据库!", "Msg");
            return;
        }

        if (textBox1.Text == "")
        {
            MessageBox.Show("请先选择要修改的物品!", "Msg");
            return;
        }

        if (textBox14.Text.Length > 120)
        {
            MessageBox.Show("装备说明不能超过120个文字!");
            return;
        }

        if (textBox3.Text.Length > 15)
        {
            MessageBox.Show("装备名称不能超过15个文字!");
            return;
        }

        try
        {
            var flag = true;
            while (true)
            {
                var num3 = 0;
                while (true)
                {
                    IL_0343:
                    var flag2 = true;
                    while (true)
                    {
                        switch (num3 >= listBox1.Items.Count ? 5 : 7)
                        {
                            case 1:
                                return;
                            case 2:
                                return;
                            case 7:
                                if (!listBox1.Items[num3].Equals(textBox3.Text))
                                {
                                    num3++;
                                    goto IL_0343;
                                }

                                goto case 0;
                            case 5:
                            {
                                var string_ =
                                    $"insert INTO ITEMSELL ( FLD_PID,FLD_Name, FLD_PRICE,FLD_TYPE,FLD_RETURN,FLD_NUMBER,FLD_MAGIC1,FLD_MAGIC2,FLD_MAGIC3,FLD_MAGIC4,FLD_MAGIC5,FLD_DESC,FLD_初级附魂,FLD_中级附魂,FLD_进化,FLD_是否绑定,FLD_DAYS)VALUES({int.Parse(textBox1.Text)},'{textBox3.Text}',{int.Parse(textBox4.Text)},{int.Parse(textBox5.Text)},{int.Parse(textBox6.Text)},{int.Parse(textBox7.Text)},{int.Parse(textBox8.Text)},{int.Parse(textBox9.Text)},{int.Parse(textBox10.Text)},{int.Parse(textBox11.Text)},{int.Parse(textBox12.Text)},'{textBox14.Text}',{int.Parse(textBox2.Text)},{int.Parse(textBox13.Text)},{int.Parse(textBox16.Text)},{int.Parse(textBox17.Text)},{int.Parse(textBox18.Text)})";
                                DBA.ExeSqlCommand(string_, "BBG");
                                MessageBox.Show("添加成功!");
                                加载百宝阁();
                                刷新();
                                return;
                            }
                            case 0:
                                MessageBox.Show("物品重复请重新添加!");
                                return;
                            case 3:
                                continue;
                            case 4:
                            case 6:
                                goto IL_0343;
                        }

                        break;
                    }

                    break;
                }
            }
        }
        catch (Exception)
        {
            MessageBox.Show("添加出错!");
        }
    }

    private void button5_Click(object sender, EventArgs e)
    {
        string text = null;
        if (!string.IsNullOrEmpty(listBox1.Text) && listBox1.Items.Count > 0)
        {
            text = "delete  from ITEMSELL where FLD_NAME='" + listBox1.Text + "'";
            if (DBA.ExeSqlCommand(text, "BBG") == -1)
            {
                MessageBox.Show("删除错误!");
                return;
            }

            加载百宝阁();
            刷新();
            MessageBox.Show("删除成功!");
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        groupBox1 = new GroupBox();
        button3 = new Button();
        label28 = new Label();
        label27 = new Label();
        listBox1 = new ListBox();
        groupBox3 = new GroupBox();
        button2 = new Button();
        textBox15 = new TextBox();
        radioButton2 = new RadioButton();
        radioButton1 = new RadioButton();
        groupBox2 = new GroupBox();
        textBox18 = new TextBox();
        label16 = new Label();
        textBox17 = new TextBox();
        label15 = new Label();
        textBox16 = new TextBox();
        label14 = new Label();
        textBox13 = new TextBox();
        label9 = new Label();
        textBox2 = new TextBox();
        label3 = new Label();
        button5 = new Button();
        comboBox12 = new ComboBox();
        comboBox11 = new ComboBox();
        label40 = new Label();
        listBox3 = new ListBox();
        button4 = new Button();
        label20 = new Label();
        button1 = new Button();
        textBox14 = new TextBox();
        textBox12 = new TextBox();
        textBox11 = new TextBox();
        textBox10 = new TextBox();
        textBox9 = new TextBox();
        textBox8 = new TextBox();
        textBox7 = new TextBox();
        textBox6 = new TextBox();
        textBox5 = new TextBox();
        textBox4 = new TextBox();
        textBox3 = new TextBox();
        textBox1 = new TextBox();
        label25 = new Label();
        label13 = new Label();
        label12 = new Label();
        label11 = new Label();
        label10 = new Label();
        label8 = new Label();
        label7 = new Label();
        label6 = new Label();
        label5 = new Label();
        label4 = new Label();
        label2 = new Label();
        label1 = new Label();
        groupBox1.SuspendLayout();
        groupBox3.SuspendLayout();
        groupBox2.SuspendLayout();
        SuspendLayout();
        groupBox1.Controls.Add(button3);
        groupBox1.Controls.Add(label28);
        groupBox1.Controls.Add(label27);
        groupBox1.Controls.Add(listBox1);
        groupBox1.Location = new Point(12, 78);
        groupBox1.Name = "groupBox1";
        groupBox1.Size = new Size(229, 516);
        groupBox1.TabIndex = 19;
        groupBox1.TabStop = false;
        groupBox1.Text = "物品列表";
        button3.Location = new Point(120, 476);
        button3.Name = "button3";
        button3.Size = new Size(75, 23);
        button3.TabIndex = 53;
        button3.Text = "查询物品";
        button3.UseVisualStyleBackColor = true;
        button3.Click += button3_Click;
        label28.AutoSize = true;
        label28.ForeColor = Color.Red;
        label28.Location = new Point(65, 483);
        label28.Name = "label28";
        label28.Size = new Size(47, 12);
        label28.TabIndex = 14;
        label28.Text = "label28";
        label27.AutoSize = true;
        label27.Location = new Point(0, 483);
        label27.Name = "label27";
        label27.Size = new Size(59, 12);
        label27.TabIndex = 13;
        label27.Text = "物品总数:";
        listBox1.FormattingEnabled = true;
        listBox1.ItemHeight = 12;
        listBox1.Location = new Point(17, 32);
        listBox1.Name = "listBox1";
        listBox1.Size = new Size(192, 436);
        listBox1.TabIndex = 1;
        listBox1.Click += listBox1_SelectedValueChanged;
        groupBox3.Controls.Add(button2);
        groupBox3.Controls.Add(textBox15);
        groupBox3.Controls.Add(radioButton2);
        groupBox3.Controls.Add(radioButton1);
        groupBox3.Location = new Point(12, 12);
        groupBox3.Name = "groupBox3";
        groupBox3.Size = new Size(760, 48);
        groupBox3.TabIndex = 21;
        groupBox3.TabStop = false;
        groupBox3.Text = "查找";
        button2.Location = new Point(286, 19);
        button2.Name = "button2";
        button2.Size = new Size(67, 23);
        button2.TabIndex = 9;
        button2.Text = "确定";
        button2.UseVisualStyleBackColor = true;
        button2.Click += button2_Click;
        textBox15.Location = new Point(16, 21);
        textBox15.Name = "textBox15";
        textBox15.Size = new Size(132, 21);
        textBox15.TabIndex = 6;
        radioButton2.AutoSize = true;
        radioButton2.Location = new Point(216, 22);
        radioButton2.Name = "radioButton2";
        radioButton2.Size = new Size(59, 16);
        radioButton2.TabIndex = 8;
        radioButton2.TabStop = true;
        radioButton2.Text = "物品名";
        radioButton2.UseVisualStyleBackColor = true;
        radioButton1.AutoSize = true;
        radioButton1.Location = new Point(169, 22);
        radioButton1.Name = "radioButton1";
        radioButton1.Size = new Size(41, 16);
        radioButton1.TabIndex = 7;
        radioButton1.TabStop = true;
        radioButton1.Text = "PID";
        radioButton1.UseVisualStyleBackColor = true;
        groupBox2.Controls.Add(textBox18);
        groupBox2.Controls.Add(label16);
        groupBox2.Controls.Add(textBox17);
        groupBox2.Controls.Add(label15);
        groupBox2.Controls.Add(textBox16);
        groupBox2.Controls.Add(label14);
        groupBox2.Controls.Add(textBox13);
        groupBox2.Controls.Add(label9);
        groupBox2.Controls.Add(textBox2);
        groupBox2.Controls.Add(label3);
        groupBox2.Controls.Add(button5);
        groupBox2.Controls.Add(comboBox12);
        groupBox2.Controls.Add(comboBox11);
        groupBox2.Controls.Add(label40);
        groupBox2.Controls.Add(listBox3);
        groupBox2.Controls.Add(button4);
        groupBox2.Controls.Add(label20);
        groupBox2.Controls.Add(button1);
        groupBox2.Controls.Add(textBox14);
        groupBox2.Controls.Add(textBox12);
        groupBox2.Controls.Add(textBox11);
        groupBox2.Controls.Add(textBox10);
        groupBox2.Controls.Add(textBox9);
        groupBox2.Controls.Add(textBox8);
        groupBox2.Controls.Add(textBox7);
        groupBox2.Controls.Add(textBox6);
        groupBox2.Controls.Add(textBox5);
        groupBox2.Controls.Add(textBox4);
        groupBox2.Controls.Add(textBox3);
        groupBox2.Controls.Add(textBox1);
        groupBox2.Controls.Add(label25);
        groupBox2.Controls.Add(label13);
        groupBox2.Controls.Add(label12);
        groupBox2.Controls.Add(label11);
        groupBox2.Controls.Add(label10);
        groupBox2.Controls.Add(label8);
        groupBox2.Controls.Add(label7);
        groupBox2.Controls.Add(label6);
        groupBox2.Controls.Add(label5);
        groupBox2.Controls.Add(label4);
        groupBox2.Controls.Add(label2);
        groupBox2.Controls.Add(label1);
        groupBox2.Location = new Point(269, 78);
        groupBox2.Name = "groupBox2";
        groupBox2.Size = new Size(503, 516);
        groupBox2.TabIndex = 20;
        groupBox2.TabStop = false;
        groupBox2.Text = "物品属性";
        textBox18.Location = new Point(109, 437);
        textBox18.Name = "textBox18";
        textBox18.Size = new Size(136, 21);
        textBox18.TabIndex = 69;
        label16.AutoSize = true;
        label16.Location = new Point(13, 440);
        label16.Name = "label16";
        label16.Size = new Size(59, 12);
        label16.TabIndex = 68;
        label16.Text = "FLD_DAYS:";
        textBox17.Location = new Point(109, 407);
        textBox17.Name = "textBox17";
        textBox17.Size = new Size(136, 21);
        textBox17.TabIndex = 67;
        label15.AutoSize = true;
        label15.Location = new Point(13, 410);
        label15.Name = "label15";
        label15.Size = new Size(83, 12);
        label15.TabIndex = 66;
        label15.Text = "FLD_是否绑定:";
        textBox16.Location = new Point(109, 379);
        textBox16.Name = "textBox16";
        textBox16.Size = new Size(136, 21);
        textBox16.TabIndex = 65;
        label14.AutoSize = true;
        label14.Location = new Point(13, 382);
        label14.Name = "label14";
        label14.Size = new Size(59, 12);
        label14.TabIndex = 64;
        label14.Text = "FLD_进化:";
        textBox13.Location = new Point(108, 350);
        textBox13.Name = "textBox13";
        textBox13.Size = new Size(136, 21);
        textBox13.TabIndex = 63;
        label9.AutoSize = true;
        label9.Location = new Point(12, 353);
        label9.Name = "label9";
        label9.Size = new Size(83, 12);
        label9.TabIndex = 62;
        label9.Text = "FLD_中级附魂:";
        textBox2.Location = new Point(108, 323);
        textBox2.Name = "textBox2";
        textBox2.Size = new Size(136, 21);
        textBox2.TabIndex = 61;
        label3.AutoSize = true;
        label3.Location = new Point(12, 326);
        label3.Name = "label3";
        label3.Size = new Size(83, 12);
        label3.TabIndex = 60;
        label3.Text = "FLD_初级附魂:";
        button5.Location = new Point(108, 478);
        button5.Name = "button5";
        button5.Size = new Size(75, 23);
        button5.TabIndex = 59;
        button5.Text = "删除";
        button5.UseVisualStyleBackColor = true;
        button5.Click += button5_Click;
        comboBox12.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox12.FormattingEnabled = true;
        comboBox12.Location = new Point(339, 337);
        comboBox12.Name = "comboBox12";
        comboBox12.Size = new Size(129, 20);
        comboBox12.TabIndex = 58;
        comboBox11.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox11.FormattingEnabled = true;
        comboBox11.Items.AddRange(new object[13]
        {
            "衣服", "护手", "鞋子", "武器", "内甲", "耳环", "项链", "戒指", "披风", "门甲",
            "宝宝", "弓箭", "其他"
        });
        comboBox11.Location = new Point(339, 154);
        comboBox11.MaxDropDownItems = 20;
        comboBox11.Name = "comboBox11";
        comboBox11.Size = new Size(129, 20);
        comboBox11.TabIndex = 57;
        comboBox11.SelectedIndexChanged += comboBox11_SelectedIndexChanged;
        label40.AutoSize = true;
        label40.Location = new Point(257, 157);
        label40.Name = "label40";
        label40.Size = new Size(53, 12);
        label40.TabIndex = 56;
        label40.Text = "物品列表";
        listBox3.FormattingEnabled = true;
        listBox3.ItemHeight = 12;
        listBox3.Location = new Point(339, 180);
        listBox3.Name = "listBox3";
        listBox3.Size = new Size(129, 136);
        listBox3.TabIndex = 55;
        listBox3.SelectedIndexChanged += listBox3_SelectedIndexChanged;
        button4.Location = new Point(211, 476);
        button4.Name = "button4";
        button4.Size = new Size(75, 23);
        button4.TabIndex = 54;
        button4.Text = "添加";
        button4.UseVisualStyleBackColor = true;
        button4.Click += button4_Click;
        label20.AutoSize = true;
        label20.Location = new Point(257, 53);
        label20.Name = "label20";
        label20.Size = new Size(77, 12);
        label20.TabIndex = 53;
        label20.Text = "注:120字以内";
        button1.Location = new Point(14, 476);
        button1.Name = "button1";
        button1.Size = new Size(75, 23);
        button1.TabIndex = 52;
        button1.Text = "修改";
        button1.UseVisualStyleBackColor = true;
        button1.Click += button1_Click;
        textBox14.Location = new Point(339, 32);
        textBox14.Multiline = true;
        textBox14.Name = "textBox14";
        textBox14.Size = new Size(136, 116);
        textBox14.TabIndex = 51;
        textBox12.Location = new Point(108, 296);
        textBox12.Name = "textBox12";
        textBox12.Size = new Size(136, 21);
        textBox12.TabIndex = 37;
        textBox11.Location = new Point(108, 269);
        textBox11.Name = "textBox11";
        textBox11.Size = new Size(136, 21);
        textBox11.TabIndex = 36;
        textBox10.Location = new Point(108, 242);
        textBox10.Name = "textBox10";
        textBox10.Size = new Size(136, 21);
        textBox10.TabIndex = 35;
        textBox9.Location = new Point(108, 215);
        textBox9.Name = "textBox9";
        textBox9.Size = new Size(136, 21);
        textBox9.TabIndex = 34;
        textBox8.Location = new Point(108, 188);
        textBox8.Name = "textBox8";
        textBox8.Size = new Size(136, 21);
        textBox8.TabIndex = 33;
        textBox7.Location = new Point(108, 161);
        textBox7.Name = "textBox7";
        textBox7.Size = new Size(136, 21);
        textBox7.TabIndex = 32;
        textBox6.Location = new Point(108, 134);
        textBox6.Name = "textBox6";
        textBox6.Size = new Size(136, 21);
        textBox6.TabIndex = 31;
        textBox5.Location = new Point(108, 107);
        textBox5.Name = "textBox5";
        textBox5.Size = new Size(136, 21);
        textBox5.TabIndex = 30;
        textBox4.Location = new Point(108, 80);
        textBox4.Name = "textBox4";
        textBox4.Size = new Size(136, 21);
        textBox4.TabIndex = 29;
        textBox3.Location = new Point(108, 53);
        textBox3.Name = "textBox3";
        textBox3.Size = new Size(136, 21);
        textBox3.TabIndex = 28;
        textBox1.Location = new Point(108, 26);
        textBox1.Name = "textBox1";
        textBox1.Size = new Size(136, 21);
        textBox1.TabIndex = 26;
        label25.AutoSize = true;
        label25.Location = new Point(257, 26);
        label25.Name = "label25";
        label25.Size = new Size(53, 12);
        label25.TabIndex = 24;
        label25.Text = "FLD_DES:";
        label13.AutoSize = true;
        label13.Location = new Point(12, 83);
        label13.Name = "label13";
        label13.Size = new Size(65, 12);
        label13.TabIndex = 12;
        label13.Text = "FLD_PRICE:";
        label12.AutoSize = true;
        label12.Location = new Point(12, 191);
        label12.Name = "label12";
        label12.Size = new Size(71, 12);
        label12.TabIndex = 11;
        label12.Text = "FLD_MAGIC1:";
        label11.AutoSize = true;
        label11.Location = new Point(12, 245);
        label11.Name = "label11";
        label11.Size = new Size(71, 12);
        label11.TabIndex = 10;
        label11.Text = "FLD_MAGIC3:";
        label10.AutoSize = true;
        label10.Location = new Point(12, 218);
        label10.Name = "label10";
        label10.Size = new Size(71, 12);
        label10.TabIndex = 9;
        label10.Text = "FLD_MAGIC2:";
        label8.AutoSize = true;
        label8.Location = new Point(12, 137);
        label8.Name = "label8";
        label8.Size = new Size(71, 12);
        label8.TabIndex = 7;
        label8.Text = "FLD_RETURN:";
        label7.AutoSize = true;
        label7.Location = new Point(12, 164);
        label7.Name = "label7";
        label7.Size = new Size(71, 12);
        label7.TabIndex = 6;
        label7.Text = "FLD_NUMBER:";
        label6.AutoSize = true;
        label6.Location = new Point(12, 299);
        label6.Name = "label6";
        label6.Size = new Size(71, 12);
        label6.TabIndex = 5;
        label6.Text = "FLD_MAGIC5:";
        label5.AutoSize = true;
        label5.Location = new Point(12, 272);
        label5.Name = "label5";
        label5.Size = new Size(71, 12);
        label5.TabIndex = 4;
        label5.Text = "FLD_MAGIC4:";
        label4.AutoSize = true;
        label4.Location = new Point(12, 110);
        label4.Name = "label4";
        label4.Size = new Size(59, 12);
        label4.TabIndex = 3;
        label4.Text = "FLD_TYPE:";
        label2.AutoSize = true;
        label2.Location = new Point(12, 56);
        label2.Name = "label2";
        label2.Size = new Size(59, 12);
        label2.TabIndex = 1;
        label2.Text = "FLD_NAME:";
        label1.AutoSize = true;
        label1.Location = new Point(12, 29);
        label1.Name = "label1";
        label1.Size = new Size(53, 12);
        label1.TabIndex = 0;
        label1.Text = "FLD_PID:";
        AutoScaleDimensions = new SizeF(6f, 12f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(793, 606);
        Controls.Add(groupBox1);
        Controls.Add(groupBox3);
        Controls.Add(groupBox2);
        Name = "Form10";
        Text = "百宝阁修改";
        groupBox1.ResumeLayout(false);
        groupBox1.PerformLayout();
        groupBox3.ResumeLayout(false);
        groupBox3.PerformLayout();
        groupBox2.ResumeLayout(false);
        groupBox2.PerformLayout();
        ResumeLayout(false);
    }
}