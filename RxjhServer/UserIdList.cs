using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace RxjhServer;

public class UserIdList : Form
{
    private ColumnHeader columnHeader1;

    private ColumnHeader columnHeader5;

    private IContainer components;

    private ListView listView1;

    private PropertyGrid propertyGridNPC;

    public UserIdList()
    {
        InitializeComponent();
    }

    public int worldid { get; set; }

    public string username { get; set; }

    private void UserIdList_Load(object sender, EventArgs e)
    {
        try
        {
            var num2 = 0;
            num2 = 3;
            X_Them_Vao_Trang_Thai_Loai 追加状态类 = null;
            string text = null;
            var num3 = 0;
            ItmeClass value = null;
            X_Vo_Cong_Loai value2 = null;
            IEnumerator<X_Them_Vao_Trang_Thai_Loai> enumerator = null;
            IEnumerator<X_Them_Vao_Trang_Thai_New_Loai> enumerator2 = null;
            Players value3 = null;
            X_Them_Vao_Trang_Thai_New_Loai 追加状态New类 = null;
            string text2 = null;
            string text3 = null;
            var num4 = 0;
            if (World.allConnectedChars.TryGetValue(worldid, out value3))
            {
                num2 = 0;
                additmes("帐号", value3.Userid);
                additmes("角色名", value3.UserName);
                additmes("元宝", value3.FLD_RXPIONT);
                additmes("泡点/赠品元宝", value3.FLD_Coin);
                additmes("人物职业", value3.Player_Job);
                additmes("人物职业等级", value3.Player_Job_level);
                additmes("CharacterPKMode", value3.CharacterPKMode);
                additmes("人物武勋", value3.Player_WuXun);
                additmes("人物善恶", value3.NhanVatThienVaAc);
                additmes("人物等级", value3.Player_Level);
                additmes("人物经验", value3.CharacterExperience);
                additmes("人物最大经验", value3.CharacterGreatestExperience);
                additmes("人物钱数", value3.Player_Money);
                additmes("人物历练", value3.Player_ExpErience);
                additmes("升天历练", value3.ThangThienLichLuyen_KinhNghiem);
                additmes("每日获得武勋", value3.NhanVoHuan_MoiNgay);
                additmes("丢失武勋", value3.MatDi_VoHuan);
                additmes("修炼地图剩余时间", value3.RemainingTimeOfTrainingMap);
                additmes("活动地图剩余时间", value3.ActivityMapRemainingTime);
                additmes("PVP_Piont", value3.FLD_PVP_Piont);
                additmes("防御--------------------------------", "-------------------");
                additmes("FLD_人物基本_防御", value3.FLD_NhanVatCoBan_PhongNguNew);
                additmes("FLD_防御", value3.FLD_PhongNgu);
                additmes("FLD_装备_追加_防御", value3.FLD_TrangBi_ThemVao_PhongNgu);
                additmes("FLD_装备_追加_防御New", value3.FLD_TrangBi_ThemVao_PhongNguNew);
                additmes("FLD_人物_追加_防御", value3.FLD_NhanVat_ThemVao_PhongNgu);
                additmes("FLD_人物_气功_防御", value3.FLD_NhanVat_KhiCong_PhongNgu);
                additmes("FLD_追加百分比_防御", value3.FLD_ThemVaoTiLePhanTram_PhongNgu);
                additmes("FLD_人物_武功防御力增加百分比", value3.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram);
                additmes("FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram",
                    value3.FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram);
                additmes("FLD_人物_气功_武功防御力增加百分比", value3.FLD_NhanVat_KhiCong_VoCong_LucPhongNgu_GiaTangTiLePhanTram);
                additmes("FLD_装备_追加_降低百分比防御", value3.FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramPhongNgu);
                additmes("攻击--------------------------------", "-------------------");
                additmes("FLD_人物基本_攻击", value3.FLD_NhanVatCoBan_CongKich);
                additmes("FLD_攻击", value3.FLD_CongKich);
                additmes("FLD_装备_追加_攻击", value3.FLD_TrangBi_ThemVao_CongKich);
                additmes("FLD_装备_追加_攻击New", value3.FLD_TrangBi_ThemVao_CongKich);
                additmes("FLD_人物_追加_攻击", value3.FLD_NhanVat_ThemVao_CongKich);
                additmes("FLD_人物_气功_攻击", value3.FLD_NhanVat_KhiCong_CongKich);
                additmes("FLD_追加百分比_攻击", value3.FLD_ThemVaoTiLePhanTram_CongKich);
                additmes("FLD_人物_武功攻击力增加百分比", value3.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram);
                additmes("FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram",
                    value3.FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram);
                additmes("FLD_人物_气功_武功攻击力增加百分比", value3.FLD_NhanVat_KhiCong_LucCongKichVoCongGiaTang_TiLePhanTram);
                additmes("FLD_装备_追加_降低百分比攻击", value3.FLD_TrangBi_ThemVao_GiamXuongTiLePhanTramCongKich);
                additmes("其他--------------------------------", "-------------------");
                additmes("FLD_人物_气功_命中", value3.FLD_NhanVat_KhiCong_TrungDich);
                additmes("FLD_人物_气功_回避", value3.FLD_NhanVat_KhiCong_NeTranh);
                additmes("FLD_人物_追加_命中", value3.FLD_NhanVat_ThemVao_TrungDich);
                additmes("FLD_人物_追加_回避", value3.FLD_NhanVat_ThemVao_NeTranh);
                additmes("FLD_人物基本_命中", value3.FLD_NhanVatCoBan_TrungDich);
                additmes("FLD_人物基本_回避", value3.FLD_NhanVatCoBan_NeTranh);
                additmes("FLD_体", value3.FLD_The);
                additmes("FLD_力", value3.FLD_Luc);
                additmes("FLD_命中", value3.FLD_TrungDich);
                additmes("FLD_回避", value3.FLD_NeTranh);
                additmes("FLD_心", value3.FLD_Tam);
                additmes("FLD_身", value3.FLD_Than);
                additmes("FLD_装备_追加_HP", value3.FLD_TrangBi_ThemVao_HP);
                additmes("FLD_装备_追加_MP", value3.FLD_TrangBi_ThemVao_MP);
                additmes("FLD_装备_追加_命中", value3.FLD_TrangBi_ThemVao_TrungDich);
                additmes("FLD_装备_追加_回避", value3.FLD_TrangBi_ThemVao_NeTranh);
                additmes("FLD_装备_追加_武器_强化", value3.CuongHoaVK);
                additmes("FLD_装备_追加_气功", value3.FLD_TrangBi_ThemVao_KhiCong);
                additmes("FLD_装备_追加_防具_强化", value3.CuongHoaTB);
                additmes("FLD_装备_追加_命中百分比", value3.FLD_TrangBi_ThemVao_TrungDichTiLePhanTram);
                additmes("FLD_装备_追加_回避百分比", value3.FLD_TrangBi_ThemVao_NeTranhTiLePhanTram);
                additmes("FLD_装备_追加_愤怒", value3.FLD_TrangBi_ThemVao_Phan_NoKhi);
                additmes("FLD_装备_追加_觉醒", value3.FLD_TrangBi_ThemVao_ThucTinh);
                additmes("FLD_装备_追加_初始化愤怒概率百分比", value3.FLD_TrangBi_ThemVao_KhoiTao_XacSuat_PhanNo_BanDauTiLePhanTram);
                additmes("FLD_装备_追加_中毒概率百分比", value3.FLD_TrangBi_ThemVao_TrungDocXacSuatTiLePhanTram);
                additmes("FLD_装备_降低_伤害值", value3.FLD_TrangBi_GiamXuong_MucThuongTon);
                additmes("FLD_装备_追加_伤害值", value3.FLD_TrangBi_ThemVao_MucThuongTon);
                additmes("FLD_装备_追加_死亡损失经验减少", value3.FLD_TrangBi_ThemVao_TuVong_TonThat_KinhNghiem_GiamBot);
                additmes("FLD_追加百分比_HP上限", value3.FLD_ThemVaoTiLePhanTram_HPCaoNhat);
                additmes("FLD_追加百分比_MP上限", value3.FLD_ThemVaoTiLePhanTram_MPCaoNhat);
                additmes("FLD_追加百分比_命中", value3.FLD_ThemVaoTiLePhanTram_TrungDich);
                additmes("FLD_追加百分比_回避", value3.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh);
                additmes("人物_HP", value3.NhanVat_HP);
                additmes("人物_MP", value3.NhanVat_MP);
                additmes("人物_SP", value3.NhanVat_SP);
                additmes("人物_气功_追加_HP", value3.NhanVat_KhiCong_ThemVao_HP);
                additmes("人物_气功_追加_MP", value3.NhanVat_KhiCong_ThemVao_MP);
                additmes("人物_追加_贩卖价格百分比", value3.FLD_NhanVat_ThemVao_GiaBanTiLePhanTram);
                additmes("人物_追加_武勋获得量百分比", value3.FLD_NhanVat_ThemVao_VoHuanThuHoach_SoLuongTiLePhanTram);
                additmes("人物_追加_吸魂几率百分比", value3.FLD_NhanVat_ThemVao_HapHonTiLe_TiLePhanTram);
                additmes("人物_追加_合成成功率百分比", value3.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram);
                additmes("人物_追加_获得游戏币百分比", value3.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram);
                additmes("人物_追加_经验百分比", value3.FLD_NhanVat_ThemVao_PhanTramKinhNghiem);
                additmes("宠物_追加_经验百分比", value3.FLD_Pet_ThemVao_PhanTramKinhNghiem);
                additmes("人物_追加_物品掉落概率百分比", value3.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram);
                additmes("人物_追加_历练百分比", value3.FLD_NhanVat_ThemVao_PhanTramTraiNghiem);
                additmes("所在位置----------------------------", "-------------------");
                additmes("地图ID", value3.NhanVatToaDo_BanDo);
                additmes("X坐标", value3.NhanVatToaDo_X);
                additmes("Y坐标", value3.NhanVatToaDo_Y);
                additmes("帮派信息----------------------------", "-------------------");
                additmes("帮派名字", value3.GangName);
                additmes("帮派Id", value3.GangId);
                additmes("帮派等级", value3.GangLevel);
                additmes("帮派人物等级", value3.GangCharacterLevel);
                additmes("情侣信息----------------------------", "-------------------");
                additmes("情侣名字", value3.FLD_Couple);
                additmes("爱情度", value3.FLD_Couple_Love);
                additmes("爱情度等级", value3.FLD_loveDegreeLevel);
                additmes("FLD_PhuThe_HoTro_ThemVao_DoPhongNguThuocTinh",
                    value3.FLD_PhuThe_HoTro_ThemVao_DoPhongNguThuocTinh);
                additmes("FLD_PhuThe_HoTro_ThemVao_VuKhiThuocTinh", value3.FLD_PhuThe_HoTro_ThemVao_VuKhiThuocTinh);
                additmes("KyNangKetHon", value3.KyNangKetHon);
                additmes("KyNangKetHonMP", value3.KyNangKetHonMP);
                additmes("追加状态----------------------------", "-------------------");
                enumerator = value3.AppendStatusList.Values.GetEnumerator();
                num2 = 4;
                try
                {
                    num2 = 18;
                    while (true)
                    {
                        num2 = 5;
                        if (!enumerator.MoveNext()) break;
                        追加状态类 = enumerator.Current;
                        text = string.Empty;
                        num2 = 19;
                        if (World.Itme.TryGetValue(追加状态类.FLD_PID, out value))
                        {
                            num2 = 0;
                            text = value.ItmeNAME;
                            num2 = 12;
                        }

                        num2 = 9;
                        if (text.Length == 0)
                        {
                            num2 = 22;
                            num2 = 17;
                            if (World.TBL_KONGFU.TryGetValue(追加状态类.FLD_PID, out value2))
                            {
                                num2 = 1;
                                text = value2.FLD_NAME;
                                num2 = 6;
                            }
                        }

                        num2 = 23;
                        if (追加状态类.FLD_PID == 700014)
                        {
                            num2 = 15;
                            text = "狂风万破";
                            num2 = 14;
                        }

                        num2 = 10;
                        if (text.Length == 0)
                        {
                            num2 = 11;
                            num3 = 追加状态类.FLD_PID;
                            num2 = 16;
                            if (num3 != 310)
                            {
                                num2 = 13;
                                num2 = 20;
                                if (num3 == 350)
                                {
                                    num2 = 7;
                                    text = "狂意护体";
                                    num2 = 8;
                                }
                            }
                            else
                            {
                                text = "遁出逆境";
                                num2 = 21;
                            }
                        }

                        additmes(追加状态类.FLD_PID + "[" + text + "]", 追加状态类.npcyd.Interval / 1000.0);
                        num2 = 3;
                    }

                    num2 = 2;
                    num2 = 4;
                }
                finally
                {
                    num2 = 0;
                    while (true)
                    {
                        switch (num2)
                        {
                            case 1:
                                break;
                            default:
                                if (enumerator != null)
                                {
                                    num2 = 2;
                                    continue;
                                }

                                break;
                            case 2:
                                enumerator.Dispose();
                                num2 = 1;
                                continue;
                        }

                        break;
                    }
                }

                additmes("追加状态new-------------------------", "-------------------");
                enumerator2 = value3.AppendStatusNewList.Values.GetEnumerator();
                num2 = 1;
                try
                {
                    num2 = 20;
                    while (true)
                    {
                        num2 = 16;
                        if (!enumerator2.MoveNext()) break;
                        追加状态New类 = enumerator2.Current;
                        text2 = string.Empty;
                        text3 = string.Empty;
                        num4 = 追加状态New类.FLD_PID;
                        num2 = 10;
                        switch (num4)
                        {
                            default:
                                num2 = 17;
                                num2 = 7;
                                break;
                            case 1:
                                text2 = "攻击力增加";
                                num2 = 0;
                                break;
                            case 2:
                                text2 = "防御力增加";
                                num2 = 4;
                                break;
                            case 3:
                                text2 = "生命值上限";
                                num2 = 11;
                                break;
                            case 4:
                                text2 = "内功值上限";
                                num2 = 3;
                                break;
                            case 5:
                                text2 = "命中率增加";
                                num2 = 5;
                                break;
                            case 6:
                                text2 = "回避率增加";
                                num2 = 18;
                                break;
                            case 7:
                                text2 = "武功攻击力增加";
                                num2 = 25;
                                break;
                            case 8:
                                text2 = "武功防御力增加";
                                num2 = 2;
                                break;
                            case 9:
                                text2 = "经验值获得增加";
                                num2 = 6;
                                break;
                            case 10:
                                text2 = "强化合成成功率增加";
                                num2 = 13;
                                break;
                            case 11:
                                text2 = "属性增加";
                                num2 = 8;
                                break;
                            case 12:
                                text2 = "游戏币获得增加";
                                num2 = 19;
                                break;
                            case 13:
                                text2 = "物品掉率增加";
                                num2 = 9;
                                break;
                            case 14:
                                text2 = "所有气功增加";
                                num2 = 14;
                                break;
                            case 15:
                                text2 = "历练值获得增加";
                                num2 = 24;
                                break;
                        }

                        num2 = 21;
                        if (追加状态New类.SoLuongLoaiHinh == 2)
                        {
                            num2 = 1;
                            text3 = "%";
                            num2 = 12;
                        }

                        additmes(追加状态New类.FLD_PID + "[" + text2 + "]",
                            追加状态New类.SoLuong + text3 + "|" + 追加状态New类.npcyd.Interval / 1000.0);
                        num2 = 15;
                    }

                    num2 = 23;
                    num2 = 22;
                }
                finally
                {
                    num2 = 0;
                    while (true)
                    {
                        switch (num2)
                        {
                            case 1:
                                break;
                            default:
                                if (enumerator2 != null)
                                {
                                    num2 = 2;
                                    continue;
                                }

                                break;
                            case 2:
                                enumerator2.Dispose();
                                num2 = 1;
                                continue;
                        }

                        break;
                    }
                }
            }

            num2 = 2;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Nhân vật liệt biểu phạm sai lầm" + ex);
        }
    }

    public void additmes(string string_0, object object_0)
    {
        var array = new string[2];
        try
        {
            array[0] = string_0;
            array[1] = object_0.ToString();
            listView1.Items.Insert(listView1.Items.Count, new ListViewItem(array));
        }
        catch
        {
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        propertyGridNPC = new PropertyGrid();
        listView1 = new ListView();
        columnHeader5 = new ColumnHeader();
        columnHeader1 = new ColumnHeader();
        SuspendLayout();
        propertyGridNPC.Location = new Point(313, 127);
        propertyGridNPC.Margin = new Padding(2);
        propertyGridNPC.Name = "propertyGridNPC";
        propertyGridNPC.PropertySort = PropertySort.Categorized;
        propertyGridNPC.Size = new Size(174, 59);
        propertyGridNPC.TabIndex = 7;
        listView1.Columns.AddRange(new ColumnHeader[2] { columnHeader5, columnHeader1 });
        listView1.Dock = DockStyle.Fill;
        listView1.ForeColor = SystemColors.WindowText;
        listView1.FullRowSelect = true;
        listView1.GridLines = true;
        listView1.Location = new Point(0, 0);
        listView1.Name = "listView1";
        listView1.Size = new Size(378, 627);
        listView1.TabIndex = 8;
        listView1.UseCompatibleStateImageBehavior = false;
        listView1.View = View.Details;
        columnHeader5.Text = "名称";
        columnHeader5.Width = 228;
        columnHeader1.Text = "数据";
        columnHeader1.Width = 127;
        AutoScaleDimensions = new SizeF(6f, 12f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(378, 627);
        Controls.Add(listView1);
        Controls.Add(propertyGridNPC);
        Name = "UserIdList";
        StartPosition = FormStartPosition.CenterScreen;
        Text = "UserIdList";
        Load += UserIdList_Load;
        ResumeLayout(false);
    }
}