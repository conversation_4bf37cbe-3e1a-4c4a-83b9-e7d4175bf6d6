using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;

namespace RxjhServer;

public class Reverser<T> : IComparer<T>
{
    private readonly ReverserInfo info;
    private readonly Type type;

    public Reverser(Type type, string name, ReverserInfo.Direction direction)
    {
        this.type = type;
        info.name = name;
        if (direction != 0) info.direction = direction;
    }

    public Reverser(string className, string name, ReverserInfo.Direction direction)
    {
        try
        {
            type = Type.GetType(className, true);
            info.name = name;
            info.direction = direction;
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
    }

    public Reverser(T gparam_0, string name, ReverserInfo.Direction direction)
    {
        type = gparam_0.GetType();
        info.name = name;
        info.direction = direction;
    }

    int IComparer<T>.Compare(T gparam_0, T gparam_1)
    {
        var object_ = type.InvokeMember(info.name,
            BindingFlags.Instance | BindingFlags.Public | BindingFlags.GetProperty, null, gparam_0, null);
        var object_2 = type.InvokeMember(info.name,
            BindingFlags.Instance | BindingFlags.Public | BindingFlags.GetProperty, null, gparam_1, null);
        if (info.direction != 0) Swap(ref object_, ref object_2);
        return new CaseInsensitiveComparer().Compare(object_, object_2);
    }

    private void Swap(ref object object_0, ref object object_1)
    {
        var obj = object_0;
        object_0 = object_1;
        object_1 = obj;
    }
}