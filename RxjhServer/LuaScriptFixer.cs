using System;
using System.IO;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace RxjhServer
{
    public static class LuaScriptFixer
    {
        public static void FixAllScripts(string scriptDirectory)
        {
            if (!Directory.Exists(scriptDirectory))
            {
                Form1.WriteLine(1, $"LuaScriptFixer: Directory not found: {scriptDirectory}");
                return;
            }
            
            Form1.WriteLine(2, "Starting Lua script fixing process...");
            var luaFiles = Directory.GetFiles(scriptDirectory, "*.lua");
            
            int totalFiles = luaFiles.Length;
            int fixedFiles = 0;
            int errorFiles = 0;
            
            foreach (var file in luaFiles)
            {
                try
                {
                    if (FixScript(file))
                    {
                        fixedFiles++;
                        Form1.WriteLine(2, $"Fixed: {Path.GetFileName(file)}");
                    }
                }
                catch (Exception ex)
                {
                    errorFiles++;
                    Form1.WriteLine(1, $"Error fixing {Path.GetFileName(file)}: {ex.Message}");
                }
            }
            
            Form1.WriteLine(2, $"Lua script fixing completed:");
            Form1.WriteLine(2, $"  Total files: {totalFiles}");
            Form1.WriteLine(2, $"  Fixed files: {fixedFiles}");
            Form1.WriteLine(2, $"  Error files: {errorFiles}");
        }
        
        public static bool FixScript(string filePath)
        {
            try
            {
                var content = File.ReadAllText(filePath);
                var originalContent = content;
                bool hasChanges = false;
                
                // Add safety checks at the beginning of function
                content = AddSafetyChecks(content, ref hasChanges);
                
                // Fix common syntax issues
                content = FixSyntaxIssues(content, ref hasChanges);
                
                // Add error handling for API calls
                content = AddErrorHandling(content, ref hasChanges);
                
                if (hasChanges)
                {
                    // Create backup
                    File.Copy(filePath, filePath + ".backup", true);
                    File.WriteAllText(filePath, content);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"FixScript error for {Path.GetFileName(filePath)}: {ex.Message}");
                return false;
            }
        }
        
        private static string AddSafetyChecks(string content, ref bool hasChanges)
        {
            // Check if function already has safety checks
            if (content.Contains("-- Safety checks for parameters"))
            {
                return content;
            }
            
            // Find function declaration
            var functionPattern = @"function\s+(\w+)\s*\(([^)]*)\)";
            var match = Regex.Match(content, functionPattern);
            
            if (match.Success)
            {
                var functionName = match.Groups[1].Value;
                var parameters = match.Groups[2].Value.Split(',');
                
                var safetyChecks = "  -- Safety checks for parameters\n";
                var paramChecks = new List<string>();
                
                foreach (var param in parameters)
                {
                    var trimmedParam = param.Trim();
                    if (!string.IsNullOrEmpty(trimmedParam))
                    {
                        paramChecks.Add($"{trimmedParam} == nil");
                    }
                }
                
                if (paramChecks.Count > 0)
                {
                    safetyChecks += $"  if {string.Join(" or ", paramChecks)} then\n";
                    safetyChecks += "    return\n";
                    safetyChecks += "  end\n\n";
                    
                    // Add Player safety check
                    safetyChecks += "  local Player = GetPlayer(UserWorldId)\n";
                    safetyChecks += "  if Player == nil then\n";
                    safetyChecks += "    SendSysMsg(UserWorldId, \"Lỗi: Không tìm thấy thông tin người chơi\", 9, \"Lỗi\")\n";
                    safetyChecks += "    return\n";
                    safetyChecks += "  end\n\n";
                    
                    // Insert after function declaration
                    var insertIndex = match.Index + match.Length;
                    content = content.Insert(insertIndex, "\n" + safetyChecks);
                    
                    // Remove old Player declaration if exists
                    content = Regex.Replace(content, @"^\s*local Player = GetPlayer\(UserWorldId\)\s*$", "", RegexOptions.Multiline);
                    
                    hasChanges = true;
                }
            }
            
            return content;
        }
        
        private static string FixSyntaxIssues(string content, ref bool hasChanges)
        {
            var original = content;
            
            // Fix missing 'then' after if statements
            content = Regex.Replace(content, @"if\s+([^then\n]+)\s*\n", "if $1 then\n");
            
            // Fix missing 'do' after for/while statements  
            content = Regex.Replace(content, @"for\s+([^do\n]+)\s*\n", "for $1 do\n");
            content = Regex.Replace(content, @"while\s+([^do\n]+)\s*\n", "while $1 do\n");
            
            // Fix unbalanced if/end statements by adding missing ends
            var lines = content.Split('\n');
            var result = new List<string>();
            var ifStack = new Stack<int>();
            
            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i];
                var trimmed = line.Trim();
                
                // Count if statements
                if (Regex.IsMatch(trimmed, @"^\s*if\s+.*then\s*$") || 
                    Regex.IsMatch(trimmed, @".*\s+if\s+.*then\s*$"))
                {
                    ifStack.Push(i);
                }
                
                // Count elseif statements (don't add to stack, they're part of existing if)
                if (trimmed.StartsWith("elseif "))
                {
                    // elseif doesn't need its own end
                }
                
                // Count end statements
                if (trimmed == "end" || trimmed.StartsWith("end "))
                {
                    if (ifStack.Count > 0)
                    {
                        ifStack.Pop();
                    }
                }
                
                result.Add(line);
            }
            
            // Add missing ends
            while (ifStack.Count > 0)
            {
                result.Add("  end");
                ifStack.Pop();
                hasChanges = true;
            }
            
            if (hasChanges || content != original)
            {
                content = string.Join("\n", result);
                hasChanges = true;
            }
            
            return content;
        }
        
        private static string AddErrorHandling(string content, ref bool hasChanges)
        {
            // Add safety checks for GetPackages calls
            if (content.Contains("GetPackages(") && !content.Contains("-- Safety check for weizs"))
            {
                var pattern = @"local\s+(\w+)\s*=\s*GetPackages\(([^)]+)\)";
                var matches = Regex.Matches(content, pattern);
                
                foreach (Match match in matches)
                {
                    var varName = match.Groups[1].Value;
                    var replacement = match.Value + "\n  \n  -- Safety check for " + varName + "\n";
                    replacement += $"  if {varName} == nil or {varName}[0] == nil then\n";
                    replacement += "    SendSysMsg(UserWorldId, \"Lỗi: Không thể lấy thông tin túi đồ\", 9, \"Lỗi\")\n";
                    replacement += "    return\n";
                    replacement += "  end";
                    
                    content = content.Replace(match.Value, replacement);
                    hasChanges = true;
                }
            }
            
            return content;
        }
    }
}
