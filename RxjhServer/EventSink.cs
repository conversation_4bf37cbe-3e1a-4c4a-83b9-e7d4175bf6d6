using System;
using System.Runtime.CompilerServices;
using System.Threading;

namespace RxjhServer;

public class EventSink
{
    [CompilerGenerated] private static SocketConnectEventHandler socketConnectEventHandler_0;

    public static event SocketConnectEventHandler Event_0
    {
        [CompilerGenerated]
        add
        {
            SocketConnectEventHandler socketConnectEventHandler = null;
            var socketConnectEventHandler2 = socketConnectEventHandler_0;
            do
            {
                socketConnectEventHandler = socketConnectEventHandler2;
                var value2 = (SocketConnectEventHandler)Delegate.Combine(socketConnectEventHandler, value);
                socketConnectEventHandler2 = Interlocked.CompareExchange(ref socketConnectEventHandler_0, value2,
                    socketConnectEventHandler);
            } while ((object)socketConnectEventHandler2 != socketConnectEventHandler);
        }
        [CompilerGenerated]
        remove
        {
            SocketConnectEventHandler socketConnectEventHandler = null;
            var socketConnectEventHandler2 = socketConnectEventHandler_0;
            do
            {
                socketConnectEventHandler = socketConnectEventHandler2;
                var value2 = (SocketConnectEventHandler)Delegate.Remove(socketConnectEventHandler, value);
                socketConnectEventHandler2 = Interlocked.CompareExchange(ref socketConnectEventHandler_0, value2,
                    socketConnectEventHandler);
            } while ((object)socketConnectEventHandler2 != socketConnectEventHandler);
        }
    }

    public static void InvokeSocketConnect(SocketConnectEventArgs socketConnectEventArgs_0)
    {
        var num = 0;
        while (true)
        {
            switch (num)
            {
                case 2:
                    return;
                case 1:
                    socketConnectEventHandler_0(socketConnectEventArgs_0);
                    num = 2;
                    continue;
            }

            if (socketConnectEventHandler_0 != null)
            {
                num = 1;
                continue;
            }

            return;
        }
    }
}