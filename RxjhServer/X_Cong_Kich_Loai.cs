using System;
using System.Collections.Generic;

namespace RxjhServer;

public class X_Cong_Kich_Loai : IDisposable
{
    public List<X_Quan_Cong_Kich_Loai> DanhNhieuMucTieu;

    public X_Cong_Kich_Loai(int NhanVat_ID_, int VoCongID_, int CongKichLuc_, int CongKichLoaiHinh_,
        int DanhNhieuMucTieul, bool TuHao_PhongLang)
    {
        NhanVat_ID = NhanVat_ID_;
        VoCong_ID = VoCongID_;
        CongKichLuc = CongKichLuc_;
        CongKichLoaiHinh = CongKichLoaiHinh_;
        if (DanhNhieuMucTieul == 4 || TuHao_PhongLang) DanhNhieuMucTieu = new List<X_Quan_Cong_Kich_Loai>();
    }

    public X_Cong_Kich_Loai(int 人物ID_, int 武功ID_, int 攻击力_, int 攻击类型_)
    {
        NhanVat_ID = 人物ID_;
        VoCong_ID = 武功ID_;
        CongKichLuc = 攻击力_;
        CongKichLoaiHinh = 攻击类型_;
    }

    public int NhanVat_ID { get; set; }

    public int VoCong_ID { get; set; }

    public int CongKichLuc { get; set; }

    public int CongKichLoaiHinh { get; set; }

    public void Dispose()
    {
        var num = 1;
        while (true)
        {
            switch (num)
            {
                case 2:
                    return;
                case 0:
                    DanhNhieuMucTieu.Clear();
                    DanhNhieuMucTieu = null;
                    num = 2;
                    continue;
            }

            if (DanhNhieuMucTieu != null)
            {
                num = 0;
                continue;
            }

            return;
        }
    }
}