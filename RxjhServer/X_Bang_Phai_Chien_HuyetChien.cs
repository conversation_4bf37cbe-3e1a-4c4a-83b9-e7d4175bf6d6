using System;
using System.Timers;
using RxjhServer.DbClss;

namespace RxjhServer;

public class X_Bang_Phai_Chien_HuyetChien
{
    private object AsyncLock;

    public X_Bang_Chien_Class BangChienChuPhuong;

    public X_Bang_Chien_Class BangChienKhachHang;

    public int ChuPhuong_DiemSo;

    private DateTime dateTime_0;

    private DateTime dateTime_1;

    public int KetThuc;

    public int KetThuc2;

    public int KhachHang_DiemSo;

    private readonly System.Timers.Timer timer_0;

    private System.Timers.Timer timer_1;

    public X_Bang_Phai_Chien_HuyetChien(X_Bang_Chien_Class 帮战Class_0, X_Bang_Chien_Class 帮战Class_1)
    {
        AsyncLock = new object();
        try
        {
            BangChienChuPhuong = 帮战Class_0;
            BangChienKhachHang = 帮战Class_1;
            ChuPhuong_DiemSo = 0;
            KhachHang_DiemSo = 0;
            KetThuc = 0;
            using (new Lock(BangChienChuPhuong.DanhSachUngVien, "帮战客方.申请人物列表"))
            {
                foreach (var value in BangChienChuPhuong.DanhSachUngVien.Values)
                    value.Bang_chien_phoi_doi_thanh_cong_thong_cao_nhac_nho(0);
            }

            using (new Lock(BangChienKhachHang.DanhSachUngVien, "帮战客方.申请人物列表"))
            {
                foreach (var value2 in BangChienKhachHang.DanhSachUngVien.Values)
                    value2.Bang_chien_phoi_doi_thanh_cong_thong_cao_nhac_nho(0);
            }

            dateTime_0 = DateTime.Now.AddMinutes(1.0);
            timer_0 = new System.Timers.Timer(60000.0);
            timer_0.Elapsed += 准备记时器结束事件;
            timer_0.Enabled = true;
            timer_0.AutoReset = true;
            准备记时器结束事件(null, null);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Máu bang chiến Chuẩn bị nhớ lúc khí kết thúc sự kiệnPhạm sai lầm: 111 " + ex);
        }
    }

    public void 准备记时器结束事件(object sender, ElapsedEventArgs e)
    {
        try
        {
            var num2 = 0;
            Lock @lock = null;
            Lock lock2 = null;
            Lock lock3 = null;
            var timeSpan = dateTime_0.Subtract(DateTime.Now);
            var num3 = (int)timeSpan.TotalSeconds;
            var lock4 = new Lock(BangChienChuPhuong.DanhSachUngVien, "帮战客方.申请人物列表");
            num2 = 0;
            try
            {
                using var enumerator = BangChienChuPhuong.DanhSachUngVien.Values.GetEnumerator();
                num2 = 4;
                while (true)
                {
                    num2 = 3;
                    if (!enumerator.MoveNext()) break;
                    var current = enumerator.Current;
                    current.HelpPrepareAnnouncementPrompt(timeSpan.Minutes.ToString());
                    num2 = 1;
                }

                num2 = 2;
                num2 = 0;
            }
            finally
            {
                num2 = 2;
                while (true)
                {
                    switch (num2)
                    {
                        case 0:
                            break;
                        default:
                            if (lock4 != null)
                            {
                                num2 = 1;
                                continue;
                            }

                            break;
                        case 1:
                            ((IDisposable)lock4).Dispose();
                            num2 = 0;
                            continue;
                    }

                    break;
                }
            }

            lock3 = new Lock(BangChienKhachHang.DanhSachUngVien, "帮战客方.申请人物列表");
            num2 = 1;
            try
            {
                using var enumerator2 = BangChienKhachHang.DanhSachUngVien.Values.GetEnumerator();
                num2 = 4;
                while (true)
                {
                    num2 = 3;
                    if (!enumerator2.MoveNext()) break;
                    var current2 = enumerator2.Current;
                    current2.HelpPrepareAnnouncementPrompt(timeSpan.Minutes.ToString());
                    num2 = 1;
                }

                num2 = 2;
                num2 = 0;
            }
            finally
            {
                num2 = 2;
                while (true)
                {
                    switch (num2)
                    {
                        case 0:
                            break;
                        default:
                            if (lock3 != null)
                            {
                                num2 = 1;
                                continue;
                            }

                            break;
                        case 1:
                            ((IDisposable)lock3).Dispose();
                            num2 = 0;
                            continue;
                    }

                    break;
                }
            }

            num2 = 2;
            if (num3 <= 0)
            {
                num2 = 6;
                timer_0.Enabled = false;
                timer_0.Close();
                timer_0.Dispose();
                dateTime_1 = DateTime.Now.AddMinutes(10.0);
                timer_1 = new System.Timers.Timer(1000.0);
                timer_1.Elapsed += 开始对战记时器结束事件;
                timer_1.Enabled = true;
                timer_1.AutoReset = true;
                @lock = new Lock(BangChienChuPhuong.DanhSachUngVien, "帮战客方.申请人物列表");
                num2 = 3;
                try
                {
                    using var enumerator3 = BangChienChuPhuong.DanhSachUngVien.Values.GetEnumerator();
                    num2 = 1;
                    while (true)
                    {
                        num2 = 2;
                        if (!enumerator3.MoveNext()) break;
                        var current3 = enumerator3.Current;
                        current3.Mobile(787f, -787f, 15f, 7101);
                        current3.HelpStartPrompt(0, 0);
                        num2 = 0;
                    }

                    num2 = 4;
                    num2 = 3;
                }
                finally
                {
                    num2 = 2;
                    while (true)
                    {
                        switch (num2)
                        {
                            case 0:
                                break;
                            default:
                                if (@lock != null)
                                {
                                    num2 = 1;
                                    continue;
                                }

                                break;
                            case 1:
                                ((IDisposable)@lock).Dispose();
                                num2 = 0;
                                continue;
                        }

                        break;
                    }
                }

                lock2 = new Lock(BangChienKhachHang.DanhSachUngVien, "帮战客方.申请人物列表");
                num2 = 4;
                try
                {
                    using var enumerator4 = BangChienKhachHang.DanhSachUngVien.Values.GetEnumerator();
                    num2 = 1;
                    while (true)
                    {
                        num2 = 2;
                        if (!enumerator4.MoveNext()) break;
                        var current4 = enumerator4.Current;
                        current4.Mobile(-787f, 787f, 15f, 7101);
                        current4.HelpStartPrompt(0, 0);
                        num2 = 0;
                    }

                    num2 = 4;
                    num2 = 3;
                }
                finally
                {
                    num2 = 0;
                    while (true)
                    {
                        switch (num2)
                        {
                            case 2:
                                break;
                            case 1:
                                ((IDisposable)lock2).Dispose();
                                num2 = 2;
                                continue;
                            default:
                                if (lock2 != null)
                                {
                                    num2 = 1;
                                    continue;
                                }

                                break;
                        }

                        break;
                    }
                }
            }

            num2 = 5;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Máu bang chiến Chuẩn bị nhớ lúc khí kết thúc sự kiệnPhạm sai lầm: 222 " + ex);
        }
    }

    public void 开始对战记时器结束事件(object sender, ElapsedEventArgs e)
    {
        try
        {
            var num2 = 0;
            Players players = null;
            Lock @lock = null;
            Players players2 = null;
            var timeSpan = default(TimeSpan);
            var lock2 = new Lock(BangChienChuPhuong.DanhSachUngVien, "帮战客方.申请人物列表");
            num2 = 4;
            try
            {
                using var enumerator = BangChienChuPhuong.DanhSachUngVien.Values.GetEnumerator();
                num2 = 6;
                while (true)
                {
                    num2 = 0;
                    if (!enumerator.MoveNext()) break;
                    players = enumerator.Current;
                    num2 = 2;
                    if (players.CloseUp == 1)
                    {
                        num2 = 5;
                        num2 = 4;
                    }

                    players.HelpUpdateScore(ChuPhuong_DiemSo, KhachHang_DiemSo);
                    num2 = 3;
                }

                num2 = 1;
                num2 = 7;
            }
            finally
            {
                num2 = 1;
                while (true)
                {
                    switch (num2)
                    {
                        case 2:
                            break;
                        case 0:
                            ((IDisposable)lock2).Dispose();
                            num2 = 2;
                            continue;
                        default:
                            if (lock2 != null)
                            {
                                num2 = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            @lock = new Lock(BangChienKhachHang.DanhSachUngVien, "帮战客方.申请人物列表");
            num2 = 2;
            try
            {
                using var enumerator2 = BangChienKhachHang.DanhSachUngVien.Values.GetEnumerator();
                num2 = 6;
                while (true)
                {
                    num2 = 2;
                    if (!enumerator2.MoveNext()) break;
                    players2 = enumerator2.Current;
                    num2 = 1;
                    if (players2.CloseUp == 1)
                    {
                        num2 = 3;
                        num2 = 4;
                    }

                    players2.HelpUpdateScore(ChuPhuong_DiemSo, KhachHang_DiemSo);
                    num2 = 0;
                }

                num2 = 5;
                num2 = 7;
            }
            finally
            {
                num2 = 1;
                while (true)
                {
                    switch (num2)
                    {
                        case 2:
                            break;
                        case 0:
                            ((IDisposable)@lock).Dispose();
                            num2 = 2;
                            continue;
                        default:
                            if (@lock != null)
                            {
                                num2 = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            timeSpan = dateTime_1.Subtract(DateTime.Now);
            num2 = 0;
            if ((int)timeSpan.TotalSeconds <= 0)
            {
                num2 = 1;
                KetThuc = 1;
                timer_1.Enabled = false;
                timer_1.Close();
                timer_1.Dispose();
                Dispose();
                num2 = 3;
            }

            num2 = 5;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "血帮战 开始对战记时器结束事件 出错：" + ex);
        }
    }

    public void Dispose()
    {
        var num2 = 0;
        if (KetThuc2 == 1) return;
        try
        {
            num2 = 17;
            Lock @lock = null;
            Players players = null;
            Lock lock2 = null;
            Lock lock3 = null;
            Players players2 = null;
            Lock lock4 = null;
            Players players3 = null;
            Players players4 = null;
            Lock lock5 = null;
            Players players5 = null;
            Lock lock6 = null;
            Players players6 = null;
            if (ChuPhuong_DiemSo > KhachHang_DiemSo)
            {
                num2 = 28;
                @lock = new Lock(BangChienChuPhuong.DanhSachUngVien, "帮战客方.申请人物列表");
                num2 = 10;
                try
                {
                    using var enumerator = BangChienChuPhuong.DanhSachUngVien.Values.GetEnumerator();
                    num2 = 1;
                    var flag = true;
                    while (true)
                    {
                        num2 = 9;
                        var flag2 = true;
                        while (true)
                        {
                            IL_009c:
                            if (enumerator.MoveNext())
                            {
                                players4 = enumerator.Current;
                                num2 = 12;
                                var flag3 = true;
                                while (true)
                                {
                                    IL_00c0:
                                    if (players4.GangCharacterLevel == 6)
                                    {
                                        num2 = 2;
                                        goto IL_00d8;
                                    }

                                    goto IL_022e;
                                    IL_00d8:
                                    players4.CheckTheNumberOfIngotsInBaibaoge();
                                    players4.KiemSoatNguyenBao_SoLuong(100, 1);
                                    players4.Player_Money += 90000000L;
                                    RxjhClass.BangChien_TienDatCuoc_XoaBo(players4.Userid, players4.UserName,
                                        BangChienChuPhuong.DangKy_BangPhaiID, 1);
                                    players4.Save_NguyenBaoData();
                                    players4.UpdateMoneyAndWeight();
                                    num2 = 11;
                                    IL_022e:
                                    while (true)
                                    {
                                        IL_022e_2:
                                        num2 = 5;
                                        while (true)
                                        {
                                            switch (KetThuc == 1 ? 8 : 6)
                                            {
                                                case 9:
                                                    goto IL_009c;
                                                case 12:
                                                    goto IL_00c0;
                                                case 2:
                                                    goto IL_00d8;
                                                case 4:
                                                    goto end_IL_0092;
                                                case 6:
                                                    if (KetThuc == 2)
                                                    {
                                                        num2 = 3;
                                                        goto case 3;
                                                    }

                                                    goto case 10;
                                                case 3:
                                                    players4.HeThongNhacNho(
                                                        "Bang Chiến: coì môòt phe Bang ChuÒ ðaÞ rõÌi khoÒi baÒn ðôÌ, cuôòc chiêìn châìm dýìt",
                                                        9, ":");
                                                    num2 = 10;
                                                    goto case 10;
                                                case 8:
                                                    players4.HelpStartPrompt(13, 1);
                                                    num2 = 13;
                                                    goto case 10;
                                                case 10:
                                                case 13:
                                                    players4.HelpStartPrompt(12, 3);
                                                    players4.Mobile(-5f, -145f, 15f, 1201);
                                                    num2 = 0;
                                                    break;
                                                case 5:
                                                    continue;
                                                case 11:
                                                    goto IL_022e_2;
                                                case 7:
                                                    goto IL_0246;
                                            }

                                            break;
                                        }

                                        break;
                                    }

                                    break;
                                }

                                break;
                            }

                            num2 = 7;
                            IL_0246:
                            num2 = 4;
                            goto end_IL_0092;
                        }

                        continue;
                        end_IL_0092:
                        break;
                    }
                }
                finally
                {
                    num2 = 0;
                    while (true)
                    {
                        switch (num2)
                        {
                            case 2:
                                break;
                            case 1:
                                ((IDisposable)@lock).Dispose();
                                num2 = 2;
                                continue;
                            default:
                                if (@lock != null)
                                {
                                    num2 = 1;
                                    continue;
                                }

                                break;
                        }

                        break;
                    }
                }

                lock5 = new Lock(BangChienKhachHang.DanhSachUngVien, "帮战客方.申请人物列表");
                num2 = 0;
                try
                {
                    using var enumerator2 = BangChienKhachHang.DanhSachUngVien.Values.GetEnumerator();
                    num2 = 10;
                    var flag4 = true;
                    while (true)
                    {
                        num2 = 2;
                        var flag5 = true;
                        while (true)
                        {
                            IL_02f8:
                            if (enumerator2.MoveNext())
                            {
                                players6 = enumerator2.Current;
                                num2 = 7;
                                var flag6 = true;
                                while (true)
                                {
                                    IL_031b:
                                    if (players6.GangCharacterLevel == 6)
                                    {
                                        num2 = 4;
                                        goto IL_0333;
                                    }

                                    goto IL_0456;
                                    IL_0333:
                                    RxjhClass.BangChien_TienDatCuoc_XoaBo(players6.Userid, players6.UserName,
                                        BangChienKhachHang.DangKy_BangPhaiID, -1);
                                    num2 = 6;
                                    IL_0456:
                                    while (true)
                                    {
                                        IL_0456_2:
                                        num2 = 13;
                                        while (true)
                                        {
                                            switch (KetThuc == 1 ? 1 : 12)
                                            {
                                                case 2:
                                                    goto IL_02f8;
                                                case 7:
                                                    goto IL_031b;
                                                case 4:
                                                    goto IL_0333;
                                                case 9:
                                                    goto end_IL_02ef;
                                                case 1:
                                                    players6.HelpStartPrompt(13, -1);
                                                    num2 = 3;
                                                    goto case 3;
                                                case 12:
                                                    if (KetThuc == 2)
                                                    {
                                                        num2 = 0;
                                                        goto case 0;
                                                    }

                                                    goto case 3;
                                                case 0:
                                                    players6.HeThongNhacNho(
                                                        "Bang Chiến: coì môòt phe Bang ChuÒ ðaÞ rõÌi khoÒi baÒn ðôÌ, cuôòc chiêìn châìm dýìt",
                                                        9, ":");
                                                    num2 = 8;
                                                    goto case 3;
                                                case 3:
                                                case 8:
                                                    players6.HelpStartPrompt(12, 3);
                                                    players6.Mobile(-5f, -145f, 15f, 1201);
                                                    num2 = 5;
                                                    break;
                                                case 13:
                                                    continue;
                                                case 6:
                                                    goto IL_0456_2;
                                                case 11:
                                                    goto IL_046f;
                                            }

                                            break;
                                        }

                                        break;
                                    }

                                    break;
                                }

                                break;
                            }

                            num2 = 11;
                            IL_046f:
                            num2 = 9;
                            goto end_IL_02ef;
                        }

                        continue;
                        end_IL_02ef:
                        break;
                    }
                }
                finally
                {
                    num2 = 0;
                    while (true)
                    {
                        switch (num2)
                        {
                            case 1:
                                break;
                            default:
                                if (lock5 != null)
                                {
                                    num2 = 2;
                                    continue;
                                }

                                break;
                            case 2:
                                ((IDisposable)lock5).Dispose();
                                num2 = 1;
                                continue;
                        }

                        break;
                    }
                }
            }
            else
            {
                num2 = 16;
                if (KhachHang_DiemSo > ChuPhuong_DiemSo)
                {
                    num2 = 5;
                    lock6 = new Lock(BangChienChuPhuong.DanhSachUngVien, "帮战客方.申请人物列表");
                    num2 = 6;
                    try
                    {
                        using var enumerator3 = BangChienChuPhuong.DanhSachUngVien.Values.GetEnumerator();
                        num2 = 6;
                        var flag7 = true;
                        while (true)
                        {
                            num2 = 5;
                            var flag8 = true;
                            while (true)
                            {
                                IL_0547:
                                if (enumerator3.MoveNext())
                                {
                                    players5 = enumerator3.Current;
                                    num2 = 9;
                                    var flag9 = true;
                                    while (true)
                                    {
                                        IL_056b:
                                        if (players5.GangCharacterLevel == 6)
                                        {
                                            num2 = 12;
                                            goto IL_0584;
                                        }

                                        goto IL_06a8;
                                        IL_0584:
                                        RxjhClass.BangChien_TienDatCuoc_XoaBo(players5.Userid, players5.UserName,
                                            BangChienChuPhuong.DangKy_BangPhaiID, -1);
                                        num2 = 10;
                                        IL_06a8:
                                        while (true)
                                        {
                                            IL_06a8_2:
                                            num2 = 2;
                                            while (true)
                                            {
                                                switch (KetThuc == 1 ? 4 : 0)
                                                {
                                                    case 5:
                                                        goto IL_0547;
                                                    case 9:
                                                        goto IL_056b;
                                                    case 12:
                                                        goto IL_0584;
                                                    case 8:
                                                        goto end_IL_053e;
                                                    case 0:
                                                        if (KetThuc == 2)
                                                        {
                                                            num2 = 13;
                                                            goto case 13;
                                                        }

                                                        goto case 1;
                                                    case 4:
                                                        players5.HelpStartPrompt(13, -1);
                                                        num2 = 7;
                                                        goto case 1;
                                                    case 13:
                                                        players5.HeThongNhacNho(
                                                            "Bang Chiến: coì môòt phe Bang ChuÒ ðaÞ rõÌi khoÒi baÒn ðôÌ, cuôòc chiêìn châìm dýìt",
                                                            9, ":");
                                                        num2 = 1;
                                                        goto case 1;
                                                    case 1:
                                                    case 7:
                                                        players5.HelpStartPrompt(12, 3);
                                                        players5.Mobile(-5f, -145f, 15f, 1201);
                                                        num2 = 11;
                                                        break;
                                                    case 2:
                                                        continue;
                                                    case 10:
                                                        goto IL_06a8_2;
                                                    case 3:
                                                        goto IL_06c0;
                                                }

                                                break;
                                            }

                                            break;
                                        }

                                        break;
                                    }

                                    break;
                                }

                                num2 = 3;
                                IL_06c0:
                                num2 = 8;
                                goto end_IL_053e;
                            }

                            continue;
                            end_IL_053e:
                            break;
                        }
                    }
                    finally
                    {
                        num2 = 2;
                        while (true)
                        {
                            switch (num2)
                            {
                                case 0:
                                    break;
                                default:
                                    if (lock6 != null)
                                    {
                                        num2 = 1;
                                        continue;
                                    }

                                    break;
                                case 1:
                                    ((IDisposable)lock6).Dispose();
                                    num2 = 0;
                                    continue;
                            }

                            break;
                        }
                    }

                    lock2 = new Lock(BangChienKhachHang.DanhSachUngVien, "帮战客方.申请人物列表");
                    num2 = 26;
                    try
                    {
                        using var enumerator4 = BangChienKhachHang.DanhSachUngVien.Values.GetEnumerator();
                        num2 = 12;
                        var flag10 = true;
                        while (true)
                        {
                            num2 = 2;
                            var flag11 = true;
                            while (true)
                            {
                                IL_0774:
                                if (enumerator4.MoveNext())
                                {
                                    players = enumerator4.Current;
                                    num2 = 1;
                                    var flag12 = true;
                                    while (true)
                                    {
                                        IL_0796:
                                        if (players.GangCharacterLevel == 6)
                                        {
                                            num2 = 7;
                                            goto IL_07ad;
                                        }

                                        goto IL_08ff;
                                        IL_07ad:
                                        players.CheckTheNumberOfIngotsInBaibaoge();
                                        players.KiemSoatNguyenBao_SoLuong(100, 1);
                                        players.Player_Money += 90000000L;
                                        RxjhClass.BangChien_TienDatCuoc_XoaBo(players.Userid, players.UserName,
                                            BangChienKhachHang.DangKy_BangPhaiID, 1);
                                        players.Save_NguyenBaoData();
                                        players.UpdateMoneyAndWeight();
                                        num2 = 4;
                                        IL_08ff:
                                        while (true)
                                        {
                                            IL_08ff_2:
                                            num2 = 10;
                                            while (true)
                                            {
                                                switch (KetThuc == 1 ? 6 : 5)
                                                {
                                                    case 2:
                                                        goto IL_0774;
                                                    case 1:
                                                        goto IL_0796;
                                                    case 7:
                                                        goto IL_07ad;
                                                    case 0:
                                                        goto end_IL_076b;
                                                    case 5:
                                                        if (KetThuc == 2)
                                                        {
                                                            num2 = 13;
                                                            goto case 13;
                                                        }

                                                        goto case 9;
                                                    case 6:
                                                        players.HelpStartPrompt(13, 1);
                                                        num2 = 11;
                                                        goto case 9;
                                                    case 13:
                                                        players.HeThongNhacNho(
                                                            "Bang Chiến: coì môòt phe Bang ChuÒ ðaÞ rõÌi khoÒi baÒn ðôÌ, cuôòc chiêìn châìm dýìt",
                                                            9, ":");
                                                        num2 = 9;
                                                        goto case 9;
                                                    case 9:
                                                    case 11:
                                                        players.HelpStartPrompt(12, 3);
                                                        players.Mobile(-5f, -145f, 15f, 1201);
                                                        num2 = 8;
                                                        break;
                                                    case 10:
                                                        continue;
                                                    case 4:
                                                        goto IL_08ff_2;
                                                    case 3:
                                                        goto IL_0917;
                                                }

                                                break;
                                            }

                                            break;
                                        }

                                        break;
                                    }

                                    break;
                                }

                                num2 = 3;
                                IL_0917:
                                num2 = 0;
                                goto end_IL_076b;
                            }

                            continue;
                            end_IL_076b:
                            break;
                        }
                    }
                    finally
                    {
                        num2 = 2;
                        while (true)
                        {
                            switch (num2)
                            {
                                case 0:
                                    break;
                                default:
                                    if (lock2 != null)
                                    {
                                        num2 = 1;
                                        continue;
                                    }

                                    break;
                                case 1:
                                    ((IDisposable)lock2).Dispose();
                                    num2 = 0;
                                    continue;
                            }

                            break;
                        }
                    }
                }
                else
                {
                    num2 = 11;
                    if (KhachHang_DiemSo == ChuPhuong_DiemSo)
                    {
                        num2 = 23;
                        lock3 = new Lock(BangChienChuPhuong.DanhSachUngVien, "帮战客方.申请人物列表");
                        num2 = 19;
                        try
                        {
                            using var enumerator5 = BangChienChuPhuong.DanhSachUngVien.Values.GetEnumerator();
                            num2 = 2;
                            var flag13 = true;
                            while (true)
                            {
                                num2 = 8;
                                var flag14 = true;
                                while (true)
                                {
                                    IL_09ef:
                                    if (enumerator5.MoveNext())
                                    {
                                        players2 = enumerator5.Current;
                                        num2 = 3;
                                        var flag15 = true;
                                        while (true)
                                        {
                                            IL_0a12:
                                            if (players2.GangCharacterLevel == 6)
                                            {
                                                num2 = 13;
                                                goto IL_0a2b;
                                            }

                                            goto IL_0b87;
                                            IL_0a2b:
                                            players2.CheckTheNumberOfIngotsInBaibaoge();
                                            players2.KiemSoatNguyenBao_SoLuong(50, 1);
                                            players2.Player_Money += 45000000L;
                                            RxjhClass.BangChien_TienDatCuoc_XoaBo(players2.Userid, players2.UserName,
                                                BangChienChuPhuong.DangKy_BangPhaiID, 0);
                                            players2.Save_NguyenBaoData();
                                            players2.UpdateMoneyAndWeight();
                                            num2 = 6;
                                            IL_0b87:
                                            while (true)
                                            {
                                                IL_0b87_2:
                                                num2 = 12;
                                                while (true)
                                                {
                                                    switch (KetThuc != 1 ? 4 : 0)
                                                    {
                                                        case 8:
                                                            goto IL_09ef;
                                                        case 3:
                                                            goto IL_0a12;
                                                        case 13:
                                                            goto IL_0a2b;
                                                        case 7:
                                                            goto end_IL_09e6;
                                                        case 0:
                                                            players2.HelpStartPrompt(13, 0);
                                                            num2 = 10;
                                                            goto case 1;
                                                        case 4:
                                                            if (KetThuc == 2)
                                                            {
                                                                num2 = 5;
                                                                goto case 5;
                                                            }

                                                            goto case 1;
                                                        case 5:
                                                            players2.HeThongNhacNho(
                                                                "Bang Chiến: coì môòt phe Bang ChuÒ ðaÞ rõÌi khoÒi baÒn ðôÌ, cuôòc chiêìn châìm dýìt",
                                                                9, ":");
                                                            num2 = 1;
                                                            goto case 1;
                                                        case 1:
                                                        case 10:
                                                            players2.HelpStartPrompt(12, 3);
                                                            players2.Mobile(-5f, -145f, 15f, 1201);
                                                            num2 = 11;
                                                            break;
                                                        case 12:
                                                            continue;
                                                        case 6:
                                                            goto IL_0b87_2;
                                                        case 9:
                                                            goto IL_0ba0;
                                                    }

                                                    break;
                                                }

                                                break;
                                            }

                                            break;
                                        }

                                        break;
                                    }

                                    num2 = 9;
                                    IL_0ba0:
                                    num2 = 7;
                                    goto end_IL_09e6;
                                }

                                continue;
                                end_IL_09e6:
                                break;
                            }
                        }
                        finally
                        {
                            num2 = 1;
                            while (true)
                            {
                                switch (num2)
                                {
                                    case 0:
                                        break;
                                    default:
                                        if (lock3 != null)
                                        {
                                            num2 = 2;
                                            continue;
                                        }

                                        break;
                                    case 2:
                                        ((IDisposable)lock3).Dispose();
                                        num2 = 0;
                                        continue;
                                }

                                break;
                            }
                        }

                        lock4 = new Lock(BangChienKhachHang.DanhSachUngVien, "帮战客方.申请人物列表");
                        num2 = 14;
                        try
                        {
                            using var enumerator6 = BangChienKhachHang.DanhSachUngVien.Values.GetEnumerator();
                            num2 = 5;
                            var flag16 = true;
                            while (true)
                            {
                                num2 = 2;
                                var flag17 = true;
                                while (true)
                                {
                                    IL_0c53:
                                    if (enumerator6.MoveNext())
                                    {
                                        players3 = enumerator6.Current;
                                        num2 = 7;
                                        var flag18 = true;
                                        while (true)
                                        {
                                            IL_0c76:
                                            if (players3.GangCharacterLevel == 6)
                                            {
                                                num2 = 4;
                                                goto IL_0c8e;
                                            }

                                            goto IL_0dea;
                                            IL_0c8e:
                                            players3.CheckTheNumberOfIngotsInBaibaoge();
                                            players3.KiemSoatNguyenBao_SoLuong(50, 1);
                                            players3.Player_Money += 45000000L;
                                            RxjhClass.BangChien_TienDatCuoc_XoaBo(players3.Userid, players3.UserName,
                                                BangChienKhachHang.DangKy_BangPhaiID, 0);
                                            players3.Save_NguyenBaoData();
                                            players3.UpdateMoneyAndWeight();
                                            num2 = 11;
                                            IL_0dea:
                                            while (true)
                                            {
                                                IL_0dea_2:
                                                num2 = 12;
                                                while (true)
                                                {
                                                    switch (KetThuc == 1 ? 3 : 10)
                                                    {
                                                        case 2:
                                                            goto IL_0c53;
                                                        case 7:
                                                            goto IL_0c76;
                                                        case 4:
                                                            goto IL_0c8e;
                                                        case 1:
                                                            goto end_IL_0c4a;
                                                        case 3:
                                                            players3.HelpStartPrompt(13, 0);
                                                            num2 = 9;
                                                            goto case 9;
                                                        case 10:
                                                            if (KetThuc == 2)
                                                            {
                                                                num2 = 8;
                                                                goto case 8;
                                                            }

                                                            goto case 9;
                                                        case 8:
                                                            players3.HeThongNhacNho(
                                                                "Bang Chiến: coì môòt phe Bang ChuÒ ðaÞ rõÌi khoÒi baÒn ðôÌ, cuôòc chiêìn châìm dýìt",
                                                                9, ":");
                                                            num2 = 13;
                                                            goto case 9;
                                                        case 9:
                                                        case 13:
                                                            players3.HelpStartPrompt(12, 3);
                                                            players3.Mobile(-5f, -145f, 15f, 1201);
                                                            num2 = 0;
                                                            break;
                                                        case 12:
                                                            continue;
                                                        case 11:
                                                            goto IL_0dea_2;
                                                        case 6:
                                                            goto IL_0e02;
                                                    }

                                                    break;
                                                }

                                                break;
                                            }

                                            break;
                                        }

                                        break;
                                    }

                                    num2 = 6;
                                    IL_0e02:
                                    num2 = 1;
                                    goto end_IL_0c4a;
                                }

                                continue;
                                end_IL_0c4a:
                                break;
                            }
                        }
                        finally
                        {
                            num2 = 0;
                            while (true)
                            {
                                switch (num2)
                                {
                                    case 2:
                                        break;
                                    case 1:
                                        ((IDisposable)lock4).Dispose();
                                        num2 = 2;
                                        continue;
                                    default:
                                        if (lock4 != null)
                                        {
                                            num2 = 1;
                                            continue;
                                        }

                                        break;
                                }

                                break;
                            }
                        }
                    }
                }
            }

            Form1.WriteLine(88,
                "Bang chiến kết thúc Địa đồ ID:7101Kết thúc ID:" + KetThuc + " Chu\u0309 bang phái ID:" +
                BangChienChuPhuong.DangKy_BangPhaiID + " Chu\u0309 tên bang:" + BangChienChuPhuong.申请帮派名称 +
                " Bang chủ:" + BangChienChuPhuong.帮主名称 + " Nhân số:" + BangChienChuPhuong.DanhSachUngVien.Count +
                " Điểm số:" + ChuPhuong_DiemSo + " ---- Đoàn lái buôn phái ID:" + BangChienKhachHang.DangKy_BangPhaiID +
                " Đoàn lái buôn phái danh tự:" + BangChienKhachHang.申请帮派名称 + " Bang chủ:" + BangChienKhachHang.帮主名称 +
                " Nhân số:" + BangChienKhachHang.DanhSachUngVien.Count + " Điểm số:" + KhachHang_DiemSo);
            KetThuc2 = 1;
            num2 = 25;
            if (timer_0 != null)
            {
                num2 = 1;
                timer_0.Enabled = false;
                timer_0.Close();
                timer_0.Dispose();
                num2 = 29;
            }

            num2 = 8;
            if (timer_1 != null)
            {
                num2 = 13;
                timer_1.Enabled = false;
                timer_1.Close();
                timer_1.Dispose();
                num2 = 27;
            }

            num2 = 7;
            if (BangChienChuPhuong != null)
            {
                num2 = 4;
                num2 = 21;
                if (BangChienChuPhuong.DanhSachUngVien != null)
                {
                    num2 = 12;
                    BangChienChuPhuong.DanhSachUngVien.Clear();
                    BangChienChuPhuong.DanhSachUngVien = null;
                    num2 = 30;
                }

                BangChienChuPhuong = null;
                num2 = 20;
            }

            num2 = 3;
            if (BangChienKhachHang != null)
            {
                num2 = 9;
                num2 = 18;
                if (BangChienKhachHang.DanhSachUngVien != null)
                {
                    num2 = 15;
                    BangChienKhachHang.DanhSachUngVien.Clear();
                    BangChienKhachHang.DanhSachUngVien = null;
                    num2 = 2;
                }

                BangChienKhachHang = null;
                num2 = 24;
            }

            World.HuyetChien = null;
            KetThuc2 = 1;
            num2 = 22;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Máu bang chiến DisposePhạm sai lầm: " + ex);
        }
    }
}