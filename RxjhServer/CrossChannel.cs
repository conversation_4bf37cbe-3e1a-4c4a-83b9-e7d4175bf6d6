using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace RxjhServer;

public class CrossChannel : Form
{
    private Button button1;

    private IContainer components;

    private GroupBox groupBox1;

    private Label label1;

    private Label label2;
    private Listener listener_0;

    private TextBox textBox1;

    private TextBox textBox2;

    public CrossChannel()
    {
        InitializeComponent();
    }

    private void button1_Click(object sender, EventArgs e)
    {
        try
        {
            var num2 = 5;
            IEnumerator<Players> enumerator = null;
            if (!World.停止服务器)
            {
                num2 = 2;
                MessageBox.Show("请先在GS服务里面停止登陆服务开始转移!!!");
                num2 = 0;
            }
            else
            {
                button1.Enabled = false;
                button1.Text = "请稍等....";
                enumerator = World.allConnectedChars.Values.GetEnumerator();
                num2 = 1;
                try
                {
                    num2 = 4;
                    while (true)
                    {
                        num2 = 0;
                        if (!enumerator.MoveNext()) break;
                        var current = enumerator.Current;
                        Thread.Sleep(100);
                        Doi_tuyen(textBox1.Text, int.Parse(textBox2.Text), current);
                        num2 = 1;
                    }

                    num2 = 2;
                    num2 = 3;
                }
                finally
                {
                    num2 = 2;
                    while (true)
                    {
                        switch (num2)
                        {
                            case 1:
                                break;
                            case 0:
                                enumerator.Dispose();
                                num2 = 1;
                                continue;
                            default:
                                if (enumerator != null)
                                {
                                    num2 = 0;
                                    continue;
                                }

                                break;
                        }

                        break;
                    }
                }

                button1.Enabled = true;
                button1.Text = "全体转移";
                MessageBox.Show("所有在线玩家转移完成!!!");
                num2 = 3;
            }

            num2 = 4;
        }
        catch
        {
            MessageBox.Show("换线过程中有错误请重新运行换线");
        }
    }

    public void Doi_tuyen(string IP, int port, Players play)
    {
        try
        {
            byte[] array = null;
            byte[] array2 = null;
            var string_ = "select * from [TBL_XWWL_Char] where FLD_NAME=@Userid";
            var sqlParameter_ = new SqlParameter[1]
                { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, play.UserName) };
            var dBToDataTable = DBA.GetDBToDataTable(string_, sqlParameter_);
            if (dBToDataTable != null && dBToDataTable.Rows.Count != 0)
            {
                var string_2 = "AA550F00000003501500000000000000000AAE55AA";
                array = Converter.HexStringToByte(string_2);
                System.Buffer.BlockCopy(BitConverter.GetBytes(play.CharacterFullServerID), 0, array, 5, 2);
                play.Client?.Send_Map_Data(array, array.Length);
                play.ClearAuxiliaryStatus();
                play.Logout();
                Thread.Sleep(3000);
                play.Client.Online = false;
                play.Exiting = true;
                var value = (int)dBToDataTable.Rows[0]["FLD_INDEX"];
                dBToDataTable.Dispose();
                var string_3 =
                    "AA553700055013D20028000100000000000000000000000000000000000000000000003200000000000000000000000000000000000000000023BC55AA";
                array2 = Converter.HexStringToByte(string_3);
                System.Buffer.BlockCopy(Buffer.GetBytes(World.ServerID), 0, array2, 15, 4);
                System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, array2, 19, 4);
                System.Buffer.BlockCopy(Buffer.GetBytes(port), 0, array2, 31, 4);
                var bytes = Encoding.Default.GetBytes(IP);
                System.Buffer.BlockCopy(bytes, 0, array2, 35, bytes.Length);
                play.Client?.Send_Map_Data(array2, array2.Length);
                World.conn.Transmit("用户换线通知|" + play.Userid);
            }
            else if (play.Client != null)
            {
                Form1.WriteLine(1, "Thu hoạch nhân vật phạm sai lầm, [" + play.Userid + "][" + play.UserName + "]");
                play.OpClient(1);
                play.Client.Dispose();
            }
        }
        catch
        {
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        groupBox1 = new GroupBox();
        textBox2 = new TextBox();
        textBox1 = new TextBox();
        button1 = new Button();
        label2 = new Label();
        label1 = new Label();
        groupBox1.SuspendLayout();
        SuspendLayout();
        groupBox1.Controls.Add(textBox2);
        groupBox1.Controls.Add(textBox1);
        groupBox1.Controls.Add(button1);
        groupBox1.Controls.Add(label2);
        groupBox1.Controls.Add(label1);
        groupBox1.Location = new Point(12, 12);
        groupBox1.Name = "groupBox1";
        groupBox1.Size = new Size(270, 131);
        groupBox1.TabIndex = 0;
        groupBox1.TabStop = false;
        groupBox1.Text = "在线人员集体跨线";
        textBox2.Location = new Point(117, 47);
        textBox2.Name = "textBox2";
        textBox2.Size = new Size(100, 21);
        textBox2.TabIndex = 4;
        textBox1.Location = new Point(117, 20);
        textBox1.Name = "textBox1";
        textBox1.Size = new Size(100, 21);
        textBox1.TabIndex = 3;
        button1.Location = new Point(117, 91);
        button1.Name = "button1";
        button1.Size = new Size(100, 23);
        button1.TabIndex = 2;
        button1.Text = "全体转移";
        button1.UseVisualStyleBackColor = true;
        button1.Click += button1_Click;
        label2.AutoSize = true;
        label2.Location = new Point(21, 50);
        label2.Name = "label2";
        label2.Size = new Size(77, 12);
        label2.TabIndex = 1;
        label2.Text = "其他线路端口";
        label1.AutoSize = true;
        label1.Location = new Point(21, 26);
        label1.Name = "label1";
        label1.Size = new Size(65, 12);
        label1.TabIndex = 0;
        label1.Text = "其他线路IP";
        AutoScaleDimensions = new SizeF(6f, 12f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(294, 159);
        Controls.Add(groupBox1);
        Name = "CrossChannel";
        Text = "CrossChannel";
        groupBox1.ResumeLayout(false);
        groupBox1.PerformLayout();
        ResumeLayout(false);
    }
}