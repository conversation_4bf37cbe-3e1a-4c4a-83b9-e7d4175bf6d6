namespace RxjhServer;

public class X_Khi_Cong_Thuoc_Tinh
{
    public double _Cung_ThangThien6_AcTanThiCung;

    public double _DaiPhu_ThangThien6_VanTamNguyetTinh;

    public double _DHL_ThangThien6_DienQuangTrieuLo;

    public double _DieuYen_ThangThien6_HoaHiemViDi;

    public double _HBQ_ThangThien6_ChanKhiHoanNguyen;

    public double _Kiem_ThangThien6_BachDocBatXam;

    public double _QuyenSu_ThangThien6_VoChuongVoNgai;

    public double _ThanNu_ThangThien6_KhangKichThanPhap;

    public double _Thuong_ThangThien6_BinhNguyenBangGia;

    public double _TuHao_ThangThien6_PhanDonVoHieu;

    public double _卢_转攻为守;

    public double _神女万毒不侵;

    public double _神女太极心法;

    public double _神女妙手回春;

    public double _神女尸毒爆发;

    public double _神女愤怒调节;

    public double _神女杀星义杀;

    public double _神女杀星义气;

    public double _神女杀星义虎;

    public double _神女洗髓易筋;

    public double _神女真武绝击;

    public double _神女神力保护;

    public double _神女神力激发;

    public double _神女蛊毒解除;

    public double _神女运气行心;

    public double _神女长功击力;

    public double _神女黑花漫开;

    public double _神女黑花集中;

    public double TuHao_ChanVu_TuyetKich_;

    public double DAO_LienHoanPhiVu { get; set; }

    public double HanBaoQuan_ThienMaCuongHuyet { get; set; }

    public double HanBaoQuan_ThienMaCuongHuyetx2 { get; set; }

    public double HanBaoQuan_TruyCotHapNguyen { get; set; }

    public double HanBaoQuan_HoaLongVanDinh { get; set; }

    public double DamHoaLien_ChieuThucTanPhap { get; set; }

    public double NhatKich_TriMang_TiLe { get; set; }

    public double DAO_ThangThien_3_KhiCong_HoaLong_ChiHoa { get; set; }

    public double KIEM_PhaThien_NhatKiem { get; set; }

    public double THUONG_ThangThien_3_KhiCong_NoYChiHoa { get; set; }

    public double QuaiVat_PhanSatThuong_TiLe { get; set; }

    public double NguoiChoi_PhanSatThuong_Tile { get; set; }

    public double BaKhiPhaGiap { get; set; }

    public double ChanVu_TuyetKich { get; set; }

    public double DonKhi_DanDien { get; set; }

    public double AmAnh_TuyetSat { get; set; }

    public double ToanPhongNhatDao { get; set; }

    public double KIEM_ThangThien_3_KhiCong_HoThan_CuongKhi { get; set; }

    public double KIEM_DiHoa_TiepMoc { get; set; }

    public double Kiem_BachBienThanHanh { get; set; }

    public double KIEM_HoiLieu_ThanPhap { get; set; }

    public double KIEM_NoHai_CuongLan { get; set; }

    public double KIEM_TrungQuan_NhatNo { get; set; }

    public double KIEM_VoKien_BatToi { get; set; }

    public double THUONG_VanKhi_LieuThuong { get; set; }

    public double THUONG_LinhGiapHoThan { get; set; }

    public double THUONG_CanKhonNaDi { get; set; }

    public double THUONG_CuongThanHangThe { get; set; }

    public double LuuTinhManThien { get; set; }

    public double TuHao_ChuyenCongViThu
    {
        get => _卢_转攻为守;
        set => _卢_转攻为守 = value;
    }

    public double THUONG_ChuyenCongViThu { get; set; }

    public double THUONG_MatNhatCuongVu { get; set; }

    public double CUNG_NhueLoiChiTien { get; set; }

    public double CUNG_LiepUngChiNhan { get; set; }

    public double Cung_VoAnhAmTien { get; set; }

    public double DAIPHU_VanKhiLieuTam { get; set; }

    public double DAIPHU_TruongCongKichLuc { get; set; }

    public double DAIPHU_ThaiCucTamPhap { get; set; }

    public double DAIPHU_DieuThuHoiXuan { get; set; }

    public double DAIPHU_ThanNongTienThuat { get; set; }

    public double DAIPHU_CuuThienChanKhi { get; set; }

    public double DAIPHU_ThangThien_2_KhiCong_VanVatHoiXuan { get; set; }

    public double DAIPHU_HapTinhDaiPhap { get; set; }

    public double NINJA_KinhKhaChiNo { get; set; }

    public double NINJA_TamHoaTuDinh { get; set; }

    public double NINJA_LienHoanPhiVu { get; set; }

    public double CuongPhong_VanPha { get; set; }

    public double KIEM_LienHoanPhiVu { get; set; }

    public double THUONG_LienHoanPhiVu { get; set; }

    public double NINJA_TatSatNhatKich { get; set; }

    public double CUNG_HoiLuuChanKhi { get; set; }

    public double CUNG_TamThanNgungTu { get; set; }

    public double CUNG_LuuTinhTamThi { get; set; }

    public double CUNG_DocBaGiangHo_140 { get; set; }

    public double NINJA_TamThanNgungTu { get; set; }

    public double NINJA_TriThuTuyetMenh { get; set; }

    public double NINJA_TienPhatCheNhan { get; set; }

    public double NINJA_ThienChuVanThu { get; set; }

    public double NINJA_NguKhiXungTieu { get; set; }

    public double NINJA_KhoaiDaoLoanVu { get; set; }

    public double NINJA_NhatChieuTanSat { get; set; }

    public double NINJA_ThangThien_3_KhiCong_VoTinhDaKich { get; set; }

    public double CAMSU_TamHoaHuyen_XacXuat_XuatHien { get; set; }

    public double CAMSU_ChienMaBonDang { get; set; }

    public double CAMSU_ThuGiangDaBac { get; set; }

    public double CAMSU_ThanhTamPhoThien { get; set; }

    public double CAMSU_DuongQuanTamDiep { get; set; }

    public double CAMSU_HanCungThuNguyet { get; set; }

    public double CAMSU_CaoSonLuuThuy { get; set; }

    public double CAMSU_NhacDuongTamTuy { get; set; }

    public double CAMSU_MaiHoaTamLong { get; set; }

    public double CAMSU_LoanPhuongHoaMinh { get; set; }

    public double CAMSU_KichLucCongThanh { get; set; }

    public double CAMSU_DuongMinhXuanHieu { get; set; }

    public double CAMSU_TieuTuongVuDa { get; set; }

    public double DamHoaLien_LienHoanPhiVu { get; set; }

    public double DamHoaLien_HoThan_CuongKhi { get; set; }

    public double DamHoaLien_DiHoa_TiepMoc { get; set; }

    public double DamHoaLien_TungHoanhVoSong { get; set; }

    public double DamHoaLien_HoiLieu_ThanPhap { get; set; }

    public double DamHoaLien_NoHai_CuongLan { get; set; }

    public double DamHoaLien_TrungQuan_NhatNo { get; set; }

    public double QuyenSu_CuongThanHangThe { get; set; }

    public double 拳师_力劈华山 { get; set; }

    public double QuyenSu_KimCuongBatHoai { get; set; }

    public double QuyenSu_ChuyenCongViThu { get; set; }

    public double QuyenSu_ThuyHoaNhatThe { get; set; }

    public double QuyenSu_HoiTamNhatKich { get; set; }

    public double QuyenSu_MaXuThanhCham { get; set; }

    public double QuyenSu_MatNhatCuongVu { get; set; }

    public double 拳师_升天_电光石火 { get; set; }

    public double 拳师_升天_夺命连环 { get; set; }

    public double 拳师_升天_精益求精 { get; set; }

    public double MaiLieuChan_ChuongLucKichHoat { get; set; }

    public double MaiLieuChan_ChuongLucVanDung { get; set; }

    public double 梅_百变神行 { get; set; }

    public double MaiLieuChan_HuyenVuThanCong { get; set; }

    public double MaiLieuChan_HuyenVuDichChiDiem { get; set; }

    public double MaiLieuChan_HuyenVuCuongKich { get; set; }

    public double MaiLieuChan_HuyenVuNguyHoa { get; set; }

    public double MaiLieuChan_ChuongLucKhoiPhuc { get; set; }

    public double MaiLieuChan_TatDoDichHoaThan { get; set; }

    public double MaiLieuChan_PhanNoBaoPhat { get; set; }

    public double MaiLieuChan_HapHuyetTienKich { get; set; }

    public double HanBaoQuan_ThangThien_1_KhiCong_HanhPhongLongVu { get; set; }

    public double HanBaoQuan_ThangThien_2_KhiCong_ThienMaHoThe { get; set; }

    public double HanBaoQuan_ThangThien_2_KhiCong_NoiTucHanhTam { get; set; }

    public double DAO_ThangThien_1_KhiCong_DonXuatNghichCanh { get; set; }

    public double KIEM_ThuaThang_TruyKich { get; set; }

    public double THUONG_ThangThien_1_KhiCong_PhaGiapThuHon { get; set; }

    public double CUNG_ThangThien_1_KhiCong_LuuVanCuongKich { get; set; }

    public double DAIPHU_ThangThien_1_KhiCong_HoThanKhiGiap { get; set; }

    public double DaiPhu_HoanVuLoanHoa { get; set; }

    public double DaiPhu_HoanVuLoanHoaBonus { get; set; }

    public double NINJA_ThangThien_1_KhiCong_ViemDoanDietQuyet { get; set; }

    public double ThangThien_1_KhiCong_LucPhachHoaSon { get; set; }

    public double ThangThien_1_KhiCong_TruongHong_QuanNhat { get; set; }

    public double ThangThien_1_KhiCong_KimChungCuongKhi { get; set; }

    public double ThangThien_1_KhiCong_VanKhiHanhTam { get; set; }

    public double ThangThien_1_KhiCong_ChinhBanBoiNguyen { get; set; }

    public double ThangThien_1_KhiCong_VanKhi_LieuThuong { get; set; }

    public double ThangThien_1_KhiCong_BachBien_ThanHanh { get; set; }

    public double ThangThien_1_KhiCong_CuongPhongThienY { get; set; }

    public int ThangThien_1_KhiCong_CuongPhongThienY_Time { get; set; }

    public double CAMSU_ThangThien_1_SatThanAmCong_390 { get; set; }

    public double DamHoaLien_ThangThien_1_KhiCong_NoHai_CuongLan { get; set; }

    public double Quyen_ThangThien_1_KhiCong_DoatMenhLienHoan { get; set; }

    public double Quyen_ThangThien_2_KhiCong_DienQuangThachHoa { get; set; }

    public double Quyen_ThangThien_3_KhiCong_TinhIchCauTinh { get; set; }

    public double MaiLieuChan_ThangThien_1_KhiCong_HuyenVuLoiDien { get; set; }

    public double MaiLieuChan_ThangThien_2_KhiCong_HuyenVuTroChu { get; set; }

    public double MaiLieuChan_ThangThien_3_KhiCong_QuySatNhan { get; set; }

    public double DAO_ThangThien_2_KhiCong_CungDoMatLo { get; set; }

    public double KIEM_ThangThien_2_KhiCong_ThienDiaDongTho { get; set; }

    public double DamHoaLien_ThangThien_2_KhiCong_ThienDiaDongTho { get; set; }

    public double THUONG_ThangThien_2_KhiCong_DiThoiViTien { get; set; }

    public double CUNG_ThangThien_2_KhiCong_ThienQuanApDa { get; set; }

    public double DAIPHU_BachSuNhuY { get; set; }

    public double NINJA_ThangThien_2_KhiCong_CoLapVoY { get; set; }

    public double CAMSU_ThangThien_2_KhiCong_TamDamAnhNguyet { get; set; }

    public double DamHoaLien_ThangThien_2_KhiCong_TungHoanhVoSong { get; set; }

    public double NINJA_DiNoHoanNo { get; set; }

    public double DAO_ManhLongSatTran { get; set; }

    public double KIEM_ThangThien_3_KhiCong_HoaPhuongLamTrieu { get; set; }

    public double DamHoaLien_ThangThien_3_KhiCong_HoaPhuongLamTrieu { get; set; }

    public double THUONG_NoYChiHong { get; set; }

    public double TuHao_NhatDiemNguHanh { get; set; }

    public double TuHao_ToiCuongHoaDiem { get; set; }

    public double TuHao_ThangThien_1_ToiCuongPhatDiem { get; set; }

    public double TuHao_ThangThien_2_SatTinhCuongPhat { get; set; }

    public double TuHao_ThangThien_3_VanSonCong { get; set; }

    public double CUNG_ThangThien_3_KhiCong_ThienNgoaiTamThi { get; set; }

    public double DAIPHU_ThangThien_3_KhiCong_TapYSinhCong { get; set; }

    public double CAMSU_ThangThien_3_CuuKhucCamMa { get; set; }

    public double DamHoaLien_ThangThien_1_BaVuongQuyDienGiap { get; set; }

    public double ThangThien_4_HongNguyetCuongPhong { get; set; }

    public double ThangThien_4_DocXaXuatDong { get; set; }

    public double ThangThien_4_ManNguyetCuongPhong { get; set; }

    public double ThangThien_4_LietNhatViemViem { get; set; }

    public double ThangThien_4_VongMaiThiemHoa { get; set; }

    public double ThangThien_4_HuyenTiChanMach { get; set; }

    public double ThangThien_4_TruongHongQuanThien { get; set; }

    public double ThangThien_4_AiHongBienDa { get; set; }

    public double ThangThien_1_LangKinhToiLuyen { get; set; }

    public double ThangThien_2_SatTinhQuangPhu { get; set; }

    public double ThangThien_3_KyQuan_QuanHung { get; set; }

    public double ThangThien_5_TriTan { get; set; }

    public double ThangThien_5_HoaLongPhapChieu { get; set; }

    public double ThangThien_5_KinhThienDongDia { get; set; }

    public double ThangThien_5_DietTheCuongVu { get; set; }

    public double ThangThien_5_ThienLyNhatKich { get; set; }

    public double ThangThien_5_HinhDiYeuTuong { get; set; }

    public double ThangThien_5_NhatChieuSatThan { get; set; }

    public double ThangThien_5_LongTraoTiemChiThu { get; set; }

    public double ThangThien_5_ThienMaChiLuc { get; set; }

    public double ThangThien_5_KinhDaoHaiLang { get; set; }

    public double ThangThien_5_BatTu_ChiKhu { get; set; }

    public double NhanVat_KhiCong_DefQuai { get; set; }

    public double ThangThien_5_MaHonChiLuc { get; set; }

    public double ThangThien_5_PhaKhongTruyTinh { get; set; }

    public double ThanNu_VanKhiHanhTam
    {
        get => _神女运气行心;
        set => _神女运气行心 = value;
    }

    public double ThanNu_ThaiCucTamPhap
    {
        get => _神女太极心法;
        set => _神女太极心法 = value;
    }

    public double ThanNu_ThanLucKichPhat
    {
        get => _神女神力激发;
        set => _神女神力激发 = value;
    }

    public double ThanNu_SatTinhNghiaHo
    {
        get => _神女杀星义虎;
        set => _神女杀星义虎 = value;
    }

    public double ThanNu_SatTinhNghiaSat
    {
        get => _神女杀星义杀;
        set => _神女杀星义杀 = value;
    }

    public double ThanNu_SatTinhNghiaKhi
    {
        get => _神女杀星义气;
        set => _神女杀星义气 = value;
    }

    public double ThanNu_TayTuyDichCan
    {
        get => _神女洗髓易筋;
        set => _神女洗髓易筋 = value;
    }

    public double ThanNu_HacHoaManKhai
    {
        get => _神女黑花漫开;
        set => _神女黑花漫开 = value;
    }

    public double ThanNu_DieuThuHoiXuan
    {
        get => _神女妙手回春;
        set => _神女妙手回春 = value;
    }

    public double ThanNu_TruongCongKichLuc
    {
        get => _神女长功击力;
        set => _神女长功击力 = value;
    }

    public double ThanNu_HacHoaTapTrung
    {
        get => _神女黑花集中;
        set => _神女黑花集中 = value;
    }

    public double ThanNu_ChanVu_TuyetKich
    {
        get => _神女真武绝击;
        set => _神女真武绝击 = value;
    }

    public double TuHao_ChanVu_TuyetKich
    {
        get => TuHao_ChanVu_TuyetKich_;
        set => TuHao_ChanVu_TuyetKich_ = value;
    }

    public double ThanNu_VanDocBatXam
    {
        get => _神女万毒不侵;
        set => _神女万毒不侵 = value;
    }

    public double ThanNu_PhanNoDieuTiet
    {
        get => _神女愤怒调节;
        set => _神女愤怒调节 = value;
    }

    public double ThanNu_CoDocGiaiTru
    {
        get => _神女蛊毒解除;
        set => _神女蛊毒解除 = value;
    }

    public double ThanNu_ThanLucBaoHo
    {
        get => _神女神力保护;
        set => _神女神力保护 = value;
    }

    public double ThanNu_ThiDocBaoPhat
    {
        get => _神女尸毒爆发;
        set => _神女尸毒爆发 = value;
    }

    public double Kiem_ThangThien6_BachDocBatXam
    {
        get => _Kiem_ThangThien6_BachDocBatXam;
        set => _Kiem_ThangThien6_BachDocBatXam = value;
    }

    public double Thuong_ThangThien6_BinhNguyenBangGia
    {
        get => _Thuong_ThangThien6_BinhNguyenBangGia;
        set => _Thuong_ThangThien6_BinhNguyenBangGia = value;
    }

    public double Cung_ThangThien6_AcTanThiCung
    {
        get => _Cung_ThangThien6_AcTanThiCung;
        set => _Cung_ThangThien6_AcTanThiCung = value;
    }

    public double DaiPhu_ThangThien6_VanTamNguyetTinh
    {
        get => _DaiPhu_ThangThien6_VanTamNguyetTinh;
        set => _DaiPhu_ThangThien6_VanTamNguyetTinh = value;
    }

    public double HBQ_ThangThien6_ChanKhiHoanNguyen
    {
        get => _HBQ_ThangThien6_ChanKhiHoanNguyen;
        set => _HBQ_ThangThien6_ChanKhiHoanNguyen = value;
    }

    public double DHL_ThangThien6_DienQuangTrieuLo
    {
        get => _DHL_ThangThien6_DienQuangTrieuLo;
        set => _DHL_ThangThien6_DienQuangTrieuLo = value;
    }

    public double QuyenSu_ThangThien6_VoChuongVoNgai
    {
        get => _QuyenSu_ThangThien6_VoChuongVoNgai;
        set => _QuyenSu_ThangThien6_VoChuongVoNgai = value;
    }

    public double DieuYen_ThangThien6_HoaHiemViDi
    {
        get => _DieuYen_ThangThien6_HoaHiemViDi;
        set => _DieuYen_ThangThien6_HoaHiemViDi = value;
    }

    public double TuHao_ThangThien6_PhanDonVoHieu
    {
        get => _TuHao_ThangThien6_PhanDonVoHieu;
        set => _TuHao_ThangThien6_PhanDonVoHieu = value;
    }

    public double ThanNu_ThangThien6_KhangKichThanPhap
    {
        get => _ThanNu_ThangThien6_KhangKichThanPhap;
        set => _ThanNu_ThangThien6_KhangKichThanPhap = value;
    }

    public double KhiCong_ThanCoMinhChau { get; set; }
}