using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class Move : Form
{
    private Button button1;

    private CheckBox checkBox1;
    private IContainer components;

    private GroupBox groupBox1;

    private GroupBox groupBox2;

    private GroupBox groupBox3;

    private GroupBox groupBox4;

    private GroupBox groupBox5;

    private Label label1;

    private Label label2;

    private Label label3;

    private Label label4;

    private Label label5;

    private Label label6;

    private Label label7;

    private Label label8;

    private Label label9;

    private NumericUpDown numericUpDown1;

    private NumericUpDown numericUpDown10;

    private NumericUpDown numericUpDown11;

    private NumericUpDown numericUpDown2;

    private NumericUpDown numericUpDown3;

    private NumericUpDown numericUpDown4;

    private NumericUpDown numericUpDown5;

    private NumericUpDown numericUpDown6;

    private NumericUpDown numericUpDown7;

    private NumericUpDown numericUpDown8;

    private NumericUpDown numericUpDown9;

    private StatusStrip statusStrip1;

    private ToolStripStatusLabel tishi;

    private ToolStripStatusLabel toolStripStatusLabel1;

    public Move()
    {
        InitializeComponent();
    }

    private void numericUpDown1_ValueChanged(object sender, EventArgs e)
    {
        var num = 0;
        while (true)
        {
            switch (num)
            {
                case 2:
                    return;
                case 1:
                    button1.Enabled = true;
                    num = 2;
                    continue;
            }

            if (!button1.Enabled)
            {
                num = 1;
                continue;
            }

            return;
        }
    }

    private void Move_Load(object sender, EventArgs e)
    {
        numericUpDown1.Value = decimal.Parse(World.普通走.ToString());
        numericUpDown2.Value = decimal.Parse(World.轻功一.ToString());
        numericUpDown3.Value = decimal.Parse(World.轻功二.ToString());
        if (World.是否开启实时坐标显示 == 1)
            checkBox1.Checked = true;
        else
            checkBox1.Checked = false;
        numericUpDown6.Value = decimal.Parse(World.宠物普通走.ToString());
        numericUpDown5.Value = decimal.Parse(World.韩轻功一.ToString());
        numericUpDown4.Value = decimal.Parse(World.韩轻功二.ToString());
        numericUpDown7.Value = decimal.Parse(World.韩轻功三.ToString());
        numericUpDown11.Value = decimal.Parse(World.韩轻功四.ToString());
        numericUpDown8.Value = decimal.Parse(World.ThoiGianThucKiemSoatKhoangCach.ToString());
        numericUpDown9.Value = decimal.Parse(World.轻功三.ToString());
        numericUpDown10.Value = decimal.Parse(World.ThoiGianThucDidongThoiGian.ToString());
    }

    private void button1_Click(object sender, EventArgs e)
    {
        World.普通走 = float.Parse(numericUpDown1.Value.ToString());
        World.轻功一 = float.Parse(numericUpDown2.Value.ToString());
        World.轻功二 = float.Parse(numericUpDown3.Value.ToString());
        World.轻功三 = float.Parse(numericUpDown9.Value.ToString());
        World.ThoiGianThucDidongThoiGian = int.Parse(numericUpDown10.Value.ToString());
        World.宠物普通走 = float.Parse(numericUpDown6.Value.ToString());
        World.韩轻功一 = float.Parse(numericUpDown5.Value.ToString());
        World.韩轻功二 = float.Parse(numericUpDown4.Value.ToString());
        World.韩轻功三 = float.Parse(numericUpDown7.Value.ToString());
        World.韩轻功四 = float.Parse(numericUpDown11.Value.ToString());
        World.ThoiGianThucKiemSoatKhoangCach = int.Parse(numericUpDown8.Value.ToString());
        if (checkBox1.Checked)
            World.是否开启实时坐标显示 = 1;
        else
            World.是否开启实时坐标显示 = 0;
        Config.IniWriteValue("GameServer", "普通走", numericUpDown1.Value.ToString());
        Config.IniWriteValue("GameServer", "轻功一", numericUpDown2.Value.ToString());
        Config.IniWriteValue("GameServer", "轻功二", numericUpDown3.Value.ToString());
        Config.IniWriteValue("GameServer", "轻功三", numericUpDown9.Value.ToString());
        Config.IniWriteValue("GameServer", "实时移动时间", numericUpDown10.Value.ToString());
        Config.IniWriteValue("GameServer", "实时检测距离", numericUpDown8.Value.ToString());
        if (checkBox1.Checked)
            Config.IniWriteValue("GameServer", "是否开启实时坐标显示", "1");
        else
            Config.IniWriteValue("GameServer", "是否开启实时坐标显示", "0");
        Config.IniWriteValue("GameServer", "宠物普通走", numericUpDown6.Value.ToString());
        Config.IniWriteValue("GameServer", "韩轻功一", numericUpDown5.Value.ToString());
        Config.IniWriteValue("GameServer", "韩轻功二", numericUpDown4.Value.ToString());
        Config.IniWriteValue("GameServer", "韩轻功三", numericUpDown7.Value.ToString());
        Config.IniWriteValue("GameServer", "韩轻功四", numericUpDown11.Value.ToString());
        tishi.Text = "保存加载成功！";
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        groupBox1 = new GroupBox();
        numericUpDown9 = new NumericUpDown();
        label8 = new Label();
        numericUpDown3 = new NumericUpDown();
        numericUpDown2 = new NumericUpDown();
        numericUpDown1 = new NumericUpDown();
        label3 = new Label();
        label2 = new Label();
        label1 = new Label();
        button1 = new Button();
        groupBox2 = new GroupBox();
        numericUpDown6 = new NumericUpDown();
        label4 = new Label();
        numericUpDown7 = new NumericUpDown();
        label7 = new Label();
        numericUpDown4 = new NumericUpDown();
        numericUpDown5 = new NumericUpDown();
        label5 = new Label();
        label6 = new Label();
        checkBox1 = new CheckBox();
        groupBox3 = new GroupBox();
        numericUpDown8 = new NumericUpDown();
        statusStrip1 = new StatusStrip();
        toolStripStatusLabel1 = new ToolStripStatusLabel();
        tishi = new ToolStripStatusLabel();
        groupBox4 = new GroupBox();
        numericUpDown10 = new NumericUpDown();
        groupBox5 = new GroupBox();
        numericUpDown11 = new NumericUpDown();
        label9 = new Label();
        groupBox1.SuspendLayout();
        ((ISupportInitialize)numericUpDown9).BeginInit();
        ((ISupportInitialize)numericUpDown3).BeginInit();
        ((ISupportInitialize)numericUpDown2).BeginInit();
        ((ISupportInitialize)numericUpDown1).BeginInit();
        groupBox2.SuspendLayout();
        ((ISupportInitialize)numericUpDown6).BeginInit();
        ((ISupportInitialize)numericUpDown7).BeginInit();
        ((ISupportInitialize)numericUpDown4).BeginInit();
        ((ISupportInitialize)numericUpDown5).BeginInit();
        groupBox3.SuspendLayout();
        ((ISupportInitialize)numericUpDown8).BeginInit();
        statusStrip1.SuspendLayout();
        groupBox4.SuspendLayout();
        ((ISupportInitialize)numericUpDown10).BeginInit();
        groupBox5.SuspendLayout();
        ((ISupportInitialize)numericUpDown11).BeginInit();
        SuspendLayout();
        groupBox1.Controls.Add(numericUpDown9);
        groupBox1.Controls.Add(label8);
        groupBox1.Controls.Add(numericUpDown3);
        groupBox1.Controls.Add(numericUpDown2);
        groupBox1.Controls.Add(numericUpDown1);
        groupBox1.Controls.Add(label3);
        groupBox1.Controls.Add(label2);
        groupBox1.Controls.Add(label1);
        groupBox1.Location = new Point(12, 39);
        groupBox1.Name = "groupBox1";
        groupBox1.Size = new Size(170, 164);
        groupBox1.TabIndex = 0;
        groupBox1.TabStop = false;
        groupBox1.Text = "人物移动参数";
        numericUpDown9.DecimalPlaces = 2;
        numericUpDown9.Increment = new decimal(new int[4] { 1, 0, 0, 65536 });
        numericUpDown9.Location = new Point(64, 118);
        numericUpDown9.Name = "numericUpDown9";
        numericUpDown9.Size = new Size(58, 21);
        numericUpDown9.TabIndex = 8;
        label8.AutoSize = true;
        label8.Location = new Point(16, 123);
        label8.Name = "label8";
        label8.Size = new Size(41, 12);
        label8.TabIndex = 7;
        label8.Text = "草上飞";
        numericUpDown3.DecimalPlaces = 2;
        numericUpDown3.Increment = new decimal(new int[4] { 1, 0, 0, 65536 });
        numericUpDown3.Location = new Point(63, 88);
        numericUpDown3.Name = "numericUpDown3";
        numericUpDown3.Size = new Size(58, 21);
        numericUpDown3.TabIndex = 6;
        numericUpDown3.ValueChanged += numericUpDown1_ValueChanged;
        numericUpDown2.DecimalPlaces = 2;
        numericUpDown2.Increment = new decimal(new int[4] { 1, 0, 0, 65536 });
        numericUpDown2.Location = new Point(64, 56);
        numericUpDown2.Name = "numericUpDown2";
        numericUpDown2.Size = new Size(58, 21);
        numericUpDown2.TabIndex = 5;
        numericUpDown2.ValueChanged += numericUpDown1_ValueChanged;
        numericUpDown1.DecimalPlaces = 2;
        numericUpDown1.Increment = new decimal(new int[4] { 1, 0, 0, 65536 });
        numericUpDown1.Location = new Point(64, 22);
        numericUpDown1.Name = "numericUpDown1";
        numericUpDown1.Size = new Size(58, 21);
        numericUpDown1.TabIndex = 4;
        numericUpDown1.ValueChanged += numericUpDown1_ValueChanged;
        label3.AutoSize = true;
        label3.Location = new Point(16, 25);
        label3.Name = "label3";
        label3.Size = new Size(41, 12);
        label3.TabIndex = 2;
        label3.Text = "普通跑";
        label2.AutoSize = true;
        label2.Location = new Point(16, 62);
        label2.Name = "label2";
        label2.Size = new Size(41, 12);
        label2.TabIndex = 1;
        label2.Text = "15轻功";
        label1.AutoSize = true;
        label1.Location = new Point(15, 93);
        label1.Name = "label1";
        label1.Size = new Size(41, 12);
        label1.TabIndex = 0;
        label1.Text = "60轻功";
        button1.Enabled = false;
        button1.Location = new Point(223, 127);
        button1.Name = "button1";
        button1.Size = new Size(88, 35);
        button1.TabIndex = 3;
        button1.Text = "保存并加载";
        button1.UseVisualStyleBackColor = true;
        button1.Click += button1_Click;
        groupBox2.Controls.Add(numericUpDown6);
        groupBox2.Controls.Add(label4);
        groupBox2.Location = new Point(191, 39);
        groupBox2.Name = "groupBox2";
        groupBox2.Size = new Size(152, 70);
        groupBox2.TabIndex = 7;
        groupBox2.TabStop = false;
        groupBox2.Text = "坐骑移动参数";
        numericUpDown6.DecimalPlaces = 2;
        numericUpDown6.Increment = new decimal(new int[4] { 1, 0, 0, 65536 });
        numericUpDown6.Location = new Point(73, 31);
        numericUpDown6.Name = "numericUpDown6";
        numericUpDown6.Size = new Size(58, 21);
        numericUpDown6.TabIndex = 4;
        label4.AutoSize = true;
        label4.Location = new Point(16, 34);
        label4.Name = "label4";
        label4.Size = new Size(53, 12);
        label4.TabIndex = 2;
        label4.Text = "移动速度";
        numericUpDown7.DecimalPlaces = 2;
        numericUpDown7.Increment = new decimal(new int[4] { 1, 0, 0, 65536 });
        numericUpDown7.Location = new Point(53, 75);
        numericUpDown7.Name = "numericUpDown7";
        numericUpDown7.Size = new Size(58, 21);
        numericUpDown7.TabIndex = 8;
        label7.AutoSize = true;
        label7.Location = new Point(5, 80);
        label7.Name = "label7";
        label7.Size = new Size(41, 12);
        label7.TabIndex = 7;
        label7.Text = "轻功三";
        numericUpDown4.DecimalPlaces = 2;
        numericUpDown4.Increment = new decimal(new int[4] { 1, 0, 0, 65536 });
        numericUpDown4.Location = new Point(53, 43);
        numericUpDown4.Name = "numericUpDown4";
        numericUpDown4.Size = new Size(58, 21);
        numericUpDown4.TabIndex = 6;
        numericUpDown5.DecimalPlaces = 2;
        numericUpDown5.Increment = new decimal(new int[4] { 1, 0, 0, 65536 });
        numericUpDown5.Location = new Point(54, 11);
        numericUpDown5.Name = "numericUpDown5";
        numericUpDown5.Size = new Size(58, 21);
        numericUpDown5.TabIndex = 5;
        label5.AutoSize = true;
        label5.Location = new Point(6, 17);
        label5.Name = "label5";
        label5.Size = new Size(41, 12);
        label5.TabIndex = 1;
        label5.Text = "轻功一";
        label6.AutoSize = true;
        label6.Location = new Point(5, 48);
        label6.Name = "label6";
        label6.Size = new Size(41, 12);
        label6.TabIndex = 0;
        label6.Text = "轻功二";
        checkBox1.AutoSize = true;
        checkBox1.Location = new Point(14, 12);
        checkBox1.Name = "checkBox1";
        checkBox1.Size = new Size(120, 16);
        checkBox1.TabIndex = 8;
        checkBox1.Text = "是否开启坐标显示";
        checkBox1.UseVisualStyleBackColor = true;
        groupBox3.Controls.Add(numericUpDown8);
        groupBox3.Location = new Point(223, 182);
        groupBox3.Name = "groupBox3";
        groupBox3.Size = new Size(105, 53);
        groupBox3.TabIndex = 9;
        groupBox3.TabStop = false;
        groupBox3.Text = "实时检测距离";
        numericUpDown8.Increment = new decimal(new int[4] { 10, 0, 0, 0 });
        numericUpDown8.Location = new Point(15, 20);
        numericUpDown8.Maximum = new decimal(new int[4] { 10000, 0, 0, 0 });
        numericUpDown8.Name = "numericUpDown8";
        numericUpDown8.Size = new Size(49, 21);
        numericUpDown8.TabIndex = 0;
        numericUpDown8.ValueChanged += numericUpDown1_ValueChanged;
        statusStrip1.Items.AddRange(new ToolStripItem[2] { toolStripStatusLabel1, tishi });
        statusStrip1.Location = new Point(0, 309);
        statusStrip1.Name = "statusStrip1";
        statusStrip1.Size = new Size(573, 22);
        statusStrip1.TabIndex = 10;
        statusStrip1.Text = "statusStrip1";
        toolStripStatusLabel1.ForeColor = Color.Red;
        toolStripStatusLabel1.Name = "toolStripStatusLabel1";
        toolStripStatusLabel1.Size = new Size(43, 17);
        toolStripStatusLabel1.Text = "提示：";
        tishi.ForeColor = Color.Red;
        tishi.Name = "tishi";
        tishi.Size = new Size(0, 17);
        groupBox4.Controls.Add(numericUpDown10);
        groupBox4.Location = new Point(223, 251);
        groupBox4.Name = "groupBox4";
        groupBox4.Size = new Size(108, 53);
        groupBox4.TabIndex = 10;
        groupBox4.TabStop = false;
        groupBox4.Text = "实时计算时间";
        numericUpDown10.Increment = new decimal(new int[4] { 10, 0, 0, 0 });
        numericUpDown10.Location = new Point(15, 20);
        numericUpDown10.Maximum = new decimal(new int[4] { 1000000, 0, 0, 0 });
        numericUpDown10.Name = "numericUpDown10";
        numericUpDown10.Size = new Size(49, 21);
        numericUpDown10.TabIndex = 0;
        groupBox5.Controls.Add(numericUpDown11);
        groupBox5.Controls.Add(label9);
        groupBox5.Controls.Add(label5);
        groupBox5.Controls.Add(numericUpDown7);
        groupBox5.Controls.Add(label6);
        groupBox5.Controls.Add(numericUpDown5);
        groupBox5.Controls.Add(label7);
        groupBox5.Controls.Add(numericUpDown4);
        groupBox5.Location = new Point(372, 39);
        groupBox5.Name = "groupBox5";
        groupBox5.Size = new Size(175, 164);
        groupBox5.TabIndex = 11;
        groupBox5.TabStop = false;
        groupBox5.Text = "韩移动参数";
        numericUpDown11.DecimalPlaces = 2;
        numericUpDown11.Increment = new decimal(new int[4] { 1, 0, 0, 65536 });
        numericUpDown11.Location = new Point(54, 113);
        numericUpDown11.Maximum = new decimal(new int[4] { 10000, 0, 0, 0 });
        numericUpDown11.Name = "numericUpDown11";
        numericUpDown11.Size = new Size(58, 21);
        numericUpDown11.TabIndex = 10;
        label9.AutoSize = true;
        label9.Location = new Point(6, 118);
        label9.Name = "label9";
        label9.Size = new Size(41, 12);
        label9.TabIndex = 9;
        label9.Text = "轻功四";
        AutoScaleDimensions = new SizeF(6f, 12f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(573, 331);
        Controls.Add(groupBox5);
        Controls.Add(groupBox4);
        Controls.Add(statusStrip1);
        Controls.Add(groupBox3);
        Controls.Add(checkBox1);
        Controls.Add(groupBox2);
        Controls.Add(groupBox1);
        Controls.Add(button1);
        FormBorderStyle = FormBorderStyle.FixedDialog;
        MaximizeBox = false;
        MinimizeBox = false;
        Name = "Move";
        Text = "Move";
        Load += Move_Load;
        groupBox1.ResumeLayout(false);
        groupBox1.PerformLayout();
        ((ISupportInitialize)numericUpDown9).EndInit();
        ((ISupportInitialize)numericUpDown3).EndInit();
        ((ISupportInitialize)numericUpDown2).EndInit();
        ((ISupportInitialize)numericUpDown1).EndInit();
        groupBox2.ResumeLayout(false);
        groupBox2.PerformLayout();
        ((ISupportInitialize)numericUpDown6).EndInit();
        ((ISupportInitialize)numericUpDown7).EndInit();
        ((ISupportInitialize)numericUpDown4).EndInit();
        ((ISupportInitialize)numericUpDown5).EndInit();
        groupBox3.ResumeLayout(false);
        ((ISupportInitialize)numericUpDown8).EndInit();
        statusStrip1.ResumeLayout(false);
        statusStrip1.PerformLayout();
        groupBox4.ResumeLayout(false);
        ((ISupportInitialize)numericUpDown10).EndInit();
        groupBox5.ResumeLayout(false);
        groupBox5.PerformLayout();
        ((ISupportInitialize)numericUpDown11).EndInit();
        ResumeLayout(false);
        PerformLayout();
    }
}