using System;
using System.Collections.Generic;

namespace RxjhServer;

public class SummonBossClass
{
    public int ID { get; set; }

    public int FLD_ITEM { get; set; }

    public string FLD_ITEMNAME { get; set; }

    public int FLD_BossID { get; set; }

    public string FLD_BossName { get; set; }

    public int FLD_PP { get; set; }

    public static int Get_BossID(int itemid)
    {
        var list = new List<SummonBossClass>();
        var random = new Random(World.GetRandomSeed()).Next(0, 1001);
        foreach (var value in World.List_SummonBoss.Values)
            if (value.FLD_ITEM == itemid && value.FLD_PP > random)
                list.Add(value);
        if (list.Count > 0)
        {
            var i = new Random(World.GetRandomSeed()).Next(0, list.Count);
            return list[i].FLD_BossID;
        }

        return 0;
    }
}