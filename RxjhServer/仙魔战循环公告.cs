using System;
using System.Timers;

namespace RxjhServer;

public class 仙魔战循环公告 : IDisposable
{
    private DateTime kssj;

    private int kssjint;
    private System.Timers.Timer 时间1;

    public 仙魔战循环公告()
    {
        try
        {
            kssj = DateTime.Now.AddMinutes(3.0);
            时间1 = new System.Timers.Timer(20000.0);
            时间1.Elapsed += 时间结束事件1;
            时间1.Enabled = true;
            时间1.AutoReset = true;
            时间结束事件1(null, null);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Tiên Ma chiến tuần hoàn thông cáoPhạm sai lầm: " + ex);
        }
    }

    public void Dispose()
    {
        try
        {
            if (时间1 != null)
            {
                时间1.Enabled = false;
                时间1.Close();
                时间1.Dispose();
                时间1 = null;
            }

            if (World.TienMaChienThongBao != null) World.TienMaChienThongBao = null;
        }
        catch
        {
        }
    }

    public void 时间结束事件1(object sender, ElapsedEventArgs e)
    {
        try
        {
            var num = (int)kssj.Subtract(DateTime.Now).TotalSeconds;
            if (num <= 0) num = 0;
            kssjint = num;
            foreach (var value in World.allConnectedChars.Values)
                if (value.NhanVatToaDo_BanDo == 801)
                {
                    value.Gui_di_the_luc_chien_nhanh_bat_dau_tin_tuc(kssjint);
                    value.GuiDi_TheLucChien_DemNguoc(kssjint);
                }
                else if (value.Player_Job_level >= 2 && value.NhanVatToaDo_BanDo != 801)
                {
                    value.Send_TheLucChien_HeThong_Packet(1, 1, 2);
                }

            if (kssjint <= 0) Dispose();
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "TLC Auto thông cáo Thời gian kết thúc sự kiện 1Phạm sai lầm: " + ex);
        }
    }
}