using System;
using System.Collections.Generic;

namespace RxjhServer;

public class MagicQigongClass
{
    public int ID { get; set; }

    public int FLD_MAGIC { get; set; }

    public string FLD_NAME { get; set; }

    public int Shop_PP { get; set; }

    public int Drop_PP { get; set; }

    public string DungChung_QigongID { get; set; }

    public int QigongID { get; set; }

    public int DonVi { get; set; }

    public static int GetMaGic0(string type)
    {
        var error = 0;
        try
        {
            var ListMagic = new List<int>();
            var random = new Random(World.GetRandomSeed()).Next(0, 10000);
            error = 1;
            if (type == "shop")
            {
                error = 2;
                foreach (var GetMagic2 in World.List_MagicQigong.Values)
                    if (GetMagic2.Shop_PP > random)
                        ListMagic.Add(GetMagic2.FLD_MAGIC);
                error = 3;
            }
            else if (type == "drop")
            {
                error = 4;
                foreach (var GetMagic in World.List_MagicQigong.Values)
                    if (GetMagic.Drop_PP > random)
                        ListMagic.Add(GetMagic.FLD_MAGIC);
                error = 5;
            }

            if (ListMagic.Count > 0)
            {
                error = 6;
                var i = new Random(World.GetRandomSeed()).Next(0, ListMagic.Count);
                return ListMagic[i];
            }

            return 0;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "GetMaGic0 Error [" + error + "]: " + ex);
            return 0;
        }
    }

    public static bool Check_KhiCongDungChung(int Pid, int AC_QigongId)
    {
        foreach (var Value in World.List_MagicQigong.Values)
        {
            if (Value.QigongID != Pid || !(Value.DungChung_QigongID != "")) continue;
            var str = Value.DungChung_QigongID.Split(',');
            for (var i = 0; i < str.Length; i++)
                if (int.Parse(str[i]) == AC_QigongId)
                    return true;
        }

        return false;
    }
}