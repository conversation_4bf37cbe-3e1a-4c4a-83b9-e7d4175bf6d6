using System;
using System.Collections.Generic;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace RxjhServer;

public class X_Vat_Pham_Loai : IDisposable
{
    public int _FLD_LEVEL;

    private Itimesx _ThuocTinh1;

    private Itimesx _ThuocTinh2;

    private Itimesx _ThuocTinh3;

    private Itimesx _ThuocTinh4;

    private bool _Vat_Pham_Khoa_Lai;

    private byte[] _VatPham_byte;

    private string _VatPhamstring;

    public bool Khoa_Chat;

    public X_Vat_Pham_Loai()
    {
    }

    public X_Vat_Pham_Loai(byte[] VatPham_byte_)
    {
        VatPham_byte = VatPham_byte_;
    }

    public X_Vat_Pham_Loai(byte[] VatPham_byte_, int Position)
    {
        VatPham_byte = VatPham_byte_;
        VatPhamViTri = Position;
    }

    public bool Vat_Pham_Khoa_Lai
    {
        get
        {
            try
            {
                var array = new byte[2];
                System.Buffer.BlockCopy(VatPham_byte, 72, array, 0, 1);
                return Buffer.ToInt16(array, 0) != 0;
            }
            catch (Exception)
            {
                return false;
            }
        }
        set => _Vat_Pham_Khoa_Lai = value;
    }

    public long GetItemGlobal_ID => BitConverter.ToInt64(VatPham_byte, 0);

    public long GetVatPham_ID => Buffer.ToInt32(VatPham_byte, 8);

    public int FLD_Intrgration
    {
        get
        {
            try
            {
                var result = 0;
                while (true)
                {
                    switch (GetVatPham_ID != 0L ? 1 : 3)
                    {
                        case 3:
                            goto end_IL_0051;
                        case 1:
                            result = World.Itme[(int)GetVatPham_ID].FLD_INTEGRATION;
                            break;
                        case 0:
                            break;
                        default:
                            continue;
                    }

                    return result;
                    continue;
                    end_IL_0051:
                    break;
                }
            }
            catch
            {
            }

            return 0;
        }
    }

    public int FLD_SERIES
    {
        get
        {
            try
            {
                var result = 0;
                while (true)
                {
                    switch (GetVatPham_ID != 0L ? 1 : 3)
                    {
                        case 3:
                            goto end_IL_0051;
                        case 1:
                            result = World.Itme[(int)GetVatPham_ID].FLD_SERIES;
                            break;
                        case 0:
                            break;
                        default:
                            continue;
                    }

                    return result;
                    continue;
                    end_IL_0051:
                    break;
                }
            }
            catch
            {
            }

            return 0;
        }
    }

    public int GetVatPhamSoLuong => Buffer.ToInt32(VatPham_byte, 12);

    public string 物品string
    {
        get => DatDuocVatPhamstring();
        set => _VatPhamstring = value;
    }

    public string FLD_NAME { get; set; }

    public int VatPhamViTri { get; set; }

    public int VatPhamLoaiHinh => DatDuocVatPhamLoaiHinh();

    public int VatPham_TrongLuong1Cai => DatDuocVatPham_TrongLuong1Cai();

    public int VatPham_TongTrongLuong => DatDuocVatPhamTrongLuong();

    public byte[] VatPham_byte
    {
        get => _VatPham_byte;
        set
        {
            Khoa_Chat = false;
            _VatPham_byte = value;
        }
    }

    public byte[] VatPhamSoLuong
    {
        get => DatDuocVatPhamSoLuong();
        set => ThietLap_VatPhamSoLuong(value);
    }

    public byte[] VatPham_ID
    {
        get => DatDuocVatPham_ID();
        set => ThietLapVatPham_ID(value);
    }

    public byte[] ItemGlobal_ID => DatDuocGlobal_ID();

    public byte[] VatPham_ThuocTinh => DatDuocVatPham_ThuocTinh();

    public Itimesx ThuocTinh1
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham_byte, 20, array, 0, 4);
            ThuocTinh1 = new Itimesx(array);
            return _ThuocTinh1;
        }
        set => _ThuocTinh1 = value;
    }

    public Itimesx ThuocTinh2
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham_byte, 24, array, 0, 4);
            ThuocTinh2 = new Itimesx(array);
            return _ThuocTinh2;
        }
        set => _ThuocTinh2 = value;
    }

    public Itimesx ThuocTinh3
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham_byte, 28, array, 0, 4);
            ThuocTinh3 = new Itimesx(array);
            return _ThuocTinh3;
        }
        set => _ThuocTinh3 = value;
    }

    public Itimesx ThuocTinh4
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham_byte, 32, array, 0, 4);
            ThuocTinh4 = new Itimesx(array);
            return _ThuocTinh4;
        }
        set => _ThuocTinh4 = value;
    }

    public int VatPham_ThemVaoCamLang_CamPhucHoi { get; set; }

    public int Vat_Pham_Luc_Phong_Ngu { get; set; }

    public int Vat_Pham_Gia_Tang_Da_Kich { get; set; }

    public int VatPham_ThemVao_PVE_Defense { get; set; }

    public int VatPham_ThuocTinhCoBan_CLVC { get; set; }

    public int Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu { get; set; }

    public int Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich { get; set; }

    public int Vat_Pham_Trung_Cap_Phu_Hon_ThemVao_ThucTinh { get; set; }

    public int Vat_Pham_Luc_Cong_Kich { get; set; }

    public int Vat_Pham_Luc_Cong_KichMAX { get; set; }

    public int VatPham_ThuocTinh_LaChan_GiaTang { get; set; }

    public int VatPham_ThuocTinh_Manh_Loai_Hinh { get; set; }

    public int VatPham_ThuocTinh_Manh { get; set; }

    public int VatPham_ThuocTinh_Giai_Doan_Loai_Hinh { get; set; }

    public int VatPham_ThuocTinh_So_Giai_Doan { get; set; }

    public int VatPham_ThuocTinh_LucCongKich_GiaTang { get; set; }

    public int Vat_Pham_Luc_Cong_KichNew { get; set; }

    public int Vat_Pham_Luc_Cong_KichMaxNew { get; set; }

    public int VatPham_ThuocTinh_LucPhongNgu_GiaTang { get; set; }

    public int Vat_Pham_Luc_Phong_NguNew { get; set; }

    public int VatPham_ThuocTinh_SinhMenhLuc_GiaTang { get; set; }

    public int VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan { get; set; }

    public int VatPham_ThuocTinh_NoiCong_Luc_GiaTang { get; set; }

    public int VatPham_ThuocTinh_TiLeChinhXac_GiaTang { get; set; }

    public int VatPham_ThuocTinh_NeTranh_Suat_GiaTang { get; set; }

    public int VatPham_ThuocTinh_VoCong_LucCongKich { get; set; }

    public int VatPham_ThuocTinh_VoCong_LucCongKichNew { get; set; }

    public double VatPham_ThuocTinh_GiamXuong_TiLePhanTram_CongKich { get; set; }

    public double VatPham_ThuocTinh_GiamXuong_TiLePhanTram_PhongNgu { get; set; }

    public double VatPham_ThuocTinh_GiaTang_TiLe_PhanTram_TrungDich { get; set; }

    public double VatPham_ThuocTinh_Gia_Tang_TiLe_PhanTram_NeTranh { get; set; }

    public double VatPham_ThuocTinh_BanDau_HoaPhanNo_XacSuat_TiLe_PhanTram { get; set; }

    public int VatPham_ThuocTinh_PhanNo_GiaTri_GiaTang { get; set; }

    public int VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang { get; set; }

    public int VatPham_ThuocTinh_ThemVao_DAO_LucPhachHoaSon { get; set; }

    public int VatPham_ThuocTinh_ThemVao_DAO_NhiepHonNhatKich { get; set; }

    public int VatPham_ThuocTinh_ThemVao_DAO_LienHoanPhiVu { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_3火龙之火 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha { get; set; }

    public int VatPham_ThuocTinh_ThemVao_DAO_TuLuong_ThienCan { get; set; }

    public int Item_qigong_6_job1 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_DAO_BaKhi_PhaGiap { get; set; }

    public int VatPham_ThuocTinh_ThemVao_DAO_ChanVu_TuyetKich { get; set; }

    public int VatPham_ThuocTinh_ThemVao_DAO_AmAnh_TuyetSat { get; set; }

    public int VatPham_ThuocTinh_ThemVao_DAO_OnNhu_ThaiSon { get; set; }

    public int VatPham_ThuocTinh_ThemVao_DAO_LuuQuang_LoanVu { get; set; }

    public int VatPham_ThuocTinh_ThemVao_DAO_ManhLongSatTran { get; set; }

    public int VatPham_ThuocTinh_ThemVao_KIEM_TruongHong_QuanNhat { get; set; }

    public int VatPham_ThuocTinh_ThemVao_KIEM_BachBien_ThanHanh { get; set; }

    public int VatPham_ThuocTinh_ThemVao_KIEM_LienHoanPhiVu { get; set; }

    public int VatPham_ThuocTinh_ThemVao_KIEM_PhaThien_NhatKiem { get; set; }

    public int VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1护身罡气 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_KIEM_DiHoa_TiepMoc { get; set; }

    public int VatPham_ThuocTinh_ThemVao_KIEM_HoiLieu_ThanPhap { get; set; }

    public int VatPham_ThuocTinh_ThemVao_KIEM_NoHai_CuongLan { get; set; }

    public int VatPham_ThuocTinh_ThemVao_KIEM_TrungQuan_NhatNo { get; set; }

    public int VatPham_ThuocTinh_ThemVao_KIEM_VoKien_BatToi { get; set; }

    public int VatPham_ThuocTinh_ThemVao_KIEM_ThuaThang_TruyKich { get; set; }

    public int VatPham_ThuocTinh_ThemVao_THUONG_KimChung_TraoKhi { get; set; }

    public int VatPham_ThuocTinh_ThemVao_THUONG_VanKhi_LieuThuong { get; set; }

    public int VatPham_ThuocTinh_ThemVao_THUONG_LienHoanPhiVu { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_3怒意之火 { get; set; }

    public int Item_qigong_3_job3 { get; set; }

    public int Item_qigong_4_job3 { get; set; }

    public int Item_qigong_8_job3 { get; set; }

    public int Item_qigong_11_job3 { get; set; }

    public int Item_qigong_7_job3 { get; set; }

    public int Item_qigong_5_job3 { get; set; }

    public int Item_qigong_9_job3 { get; set; }

    public int Item_qigong_10_job3 { get; set; }

    public int Item_qigong_0_job4 { get; set; }

    public int Item_qigong_1_job4 { get; set; }

    public int Item_qigong_2_job4 { get; set; }

    public int Item_qigong_9_job4 { get; set; }

    public int Item_qigong_3_job4 { get; set; }

    public int Item_qigong_4_job4 { get; set; }

    public int Item_qigong_7_job4 { get; set; }

    public int Item_qigong_8_job4 { get; set; }

    public int Item_qigong_5_job4 { get; set; }

    public int Item_qigong_10_job4 { get; set; }

    public int Item_qigong_11_job4 { get; set; }

    public int Item_qigong_0_job5 { get; set; }

    public int Item_qigong_1_job5 { get; set; }

    public int Item_qigong_2_job5 { get; set; }

    public int Item_qigong_3_job5 { get; set; }

    public int Item_qigong_4_job5 { get; set; }

    public int Item_qigong_5_job5 { get; set; }

    public int Item_qigong_8_job5 { get; set; }

    public int Item_qigong_7_job5 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1护身气甲 { get; set; }

    public int Item_qigong_11_job5 { get; set; }

    public int Item_qigong_9_job5 { get; set; }

    public int Item_qigong_10_job5 { get; set; }

    public int Item_qigong_0_job6 { get; set; }

    public int Item_qigong_1_job6 { get; set; }

    public int Item_qigong_2_job6 { get; set; }

    public int Item_qigong_11_job6 { get; set; }

    public int Item_qigong_3_job6 { get; set; }

    public int Item_qigong_4_job6 { get; set; }

    public int Item_qigong_7_job6 { get; set; }

    public int Item_qigong_8_job6 { get; set; }

    public int Item_qigong_9_job6 { get; set; }

    public int Item_qigong_10_job6 { get; set; }

    public int Item_qigong_5_job6 { get; set; }

    public int Item_qigong_0_job7 { get; set; }

    public int Item_qigong_1_job7 { get; set; }

    public int Item_qigong_2_job7 { get; set; }

    public int Item_qigong_3_job7 { get; set; }

    public int Item_qigong_4_job7 { get; set; }

    public int Item_qigong_5_job7 { get; set; }

    public int Item_qigong_7_job7 { get; set; }

    public int Item_qigong_8_job7 { get; set; }

    public int Item_qigong_9_job7 { get; set; }

    public int Item_qigong_10_job7 { get; set; }

    public int Item_qigong_11_job7 { get; set; }

    public int Item_qigong_0_job8 { get; set; }

    public int Item_qigong_1_job8 { get; set; }

    public int Item_qigong_3_job8 { get; set; }

    public int Item_qigong_2_job8 { get; set; }

    public int Item_qigong_4_job8 { get; set; }

    public int Item_qigong_5_job8 { get; set; }

    public int Item_qigong_7_job8 { get; set; }

    public int Item_qigong_8_job8 { get; set; }

    public int Item_qigong_11_job8 { get; set; }

    public int Item_qigong_9_job8 { get; set; }

    public int Item_qigong_10_job8 { get; set; }

    public int Item_qigong_0_job9 { get; set; }

    public int Item_qigong_1_job9 { get; set; }

    public int Item_qigong_2_job9 { get; set; }

    public int Item_qigong_3_job9 { get; set; }

    public int Item_qigong_4_job_9 { get; set; }

    public int Item_qigong_5_job9 { get; set; }

    public int Item_qigong_7_job9 { get; set; }

    public int Item_qigong_8_job9 { get; set; }

    public int Item_qigong_9_job9 { get; set; }

    public int Item_qigong_6_job11 { get; set; }

    public int Item_qigong_1_job11 { get; set; }

    public int Item_qigong_2_job11 { get; set; }

    public int Item_qigong_3_job11 { get; set; }

    public int Item_qigong_4_job11 { get; set; }

    public int Item_qigong_5_job11 { get; set; }

    public int Item_qigong_7_job11 { get; set; }

    public int Item_qigong_8_job11 { get; set; }

    public int Item_qigong_9_job11 { get; set; }

    public int Item_qigong_11_job11 { get; set; }

    public int Item_qigong_0_job11 { get; set; }

    public int Item_qigong_10_job11 { get; set; }

    public int Item_qigong_10_job9 { get; set; }

    public int Item_qigong_11_job9 { get; set; }

    public int Item_qigong_0_job10 { get; set; }

    public int Item_qigong_1_job10 { get; set; }

    public int Item_qigong_2_job10 { get; set; }

    public int Item_qigong_3_job10 { get; set; }

    public int Item_qigong_4_job10 { get; set; }

    public int Item_qigong_5_job10 { get; set; }

    public int Item_qigong_6_job10 { get; set; }

    public int Item_qigong_7_job10 { get; set; }

    public int Item_qigong_8_job10 { get; set; }

    public int Item_qigong_9_job10 { get; set; }

    public int Item_qigong_10_job10 { get; set; }

    public int Item_qigong_11_job10 { get; set; }

    public int Item_qigong_11_job12 { get; set; }

    public int Item_qigong_10_job12 { get; set; }

    public int Item_qigong_9_job12 { get; set; }

    public int Item_qigong_8_job12 { get; set; }

    public int Item_qigong_7_job12 { get; set; }

    public int Item_qigong_6_job12 { get; set; }

    public int Item_qigong_5_job12 { get; set; }

    public int Item_qigong_4_job12 { get; set; }

    public int Item_qigong_3_job12 { get; set; }

    public int Item_qigong_2_job12 { get; set; }

    public int Item_qigong_1_job12 { get; set; }

    public int Item_qigong_0_job12 { get; set; }

    public int Item_qigong_11_job13 { get; set; }

    public int Item_qigong_10_job13 { get; set; }

    public int Item_qigong_9_job13 { get; set; }

    public int Item_qigong_8_job13 { get; set; }

    public int Item_qigong_7_job13 { get; set; }

    public int Item_qigong_6_job13 { get; set; }

    public int Item_qigong_5_job13 { get; set; }

    public int Item_qigong_4_job13 { get; set; }

    public int Item_qigong_3_job13 { get; set; }

    public int Item_qigong_2_job13 { get; set; }

    public int Item_qigong_1_job13 { get; set; }

    public int Item_qigong_0_job13 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1遁出逆境 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_2_穷途末路 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_4_红月狂风 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_4_毒蛇出洞 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_4_满月狂风 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_4_烈日炎炎 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_4_长虹贯天 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_4_哀鸿遍野 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_夺命连环 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_电光石火 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_精益求精 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_2_天地同寿 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_3火凤临朝 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1破甲刺魂 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_2_以退为进 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1绝影射魂 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_2_千钧压驼 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_2_万物回春 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_3天外三矢 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_3明镜止水 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_4_望梅添花 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1夜魔缠身 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_2_顺水推舟 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_3无情打击 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_3以柔克刚 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_3内息行心 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_2_天魔护体 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1行风弄舞 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_4_悬丝诊脉 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_3子夜秋歌 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_2_三潭映月 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1力劈华山 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1长虹贯日 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1金钟罡气 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1运气行心 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1正本培源 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1运气疗伤 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1百变神行 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1狂风天意 { get; set; }

    public int VatPham_ThuocTinh_ThemVao_ThangThien_1飞花点翠 { get; set; }

    public int VatPham_ThuocTinh_ThangCap_XacSuat_ThanhCong { get; set; }

    public int VatPham_ThuocTinh_ThemVao_MucThuongTon { get; set; }

    public int VatPham_ThuocTinh_GiamXuong_MucThuongTon { get; set; }

    public double VatPham_ThuocTinh_ThemVao_TrungDoc_TiLe_TiLePhanTram { get; set; }

    public double VatPham_ThuocTinh_ThemVao_CuongHoa { get; set; }

    public int VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang { get; set; }

    public int VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew { get; set; }

    public int VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang { get; set; }

    public int VatPham_ThuocTinh_TuVong_TonThat_KinhNghiem_GiamBot { get; set; }

    public int VatPham_ThuocTinh_KinhNghiem_ThuHoach_Duoc_GiaTang { get; set; }

    public int FLD_LEVEL
    {
        get => _FLD_LEVEL;
        set => _FLD_LEVEL = value;
    }

    public int FLD_RESIDE2 { get; set; }
    public int FLD_RESIDE1 { get; set; }

    public int FLD_MAGIC0
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham_byte, 16, array, 0, 4);
            return Buffer.ToInt32(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 16, 4);
    }

    public int FLD_强化类型
    {
        get
        {
            var fLD_MAGIC = FLD_MAGIC0;
            if (fLD_MAGIC <= 0) return 0;
            var text = fLD_MAGIC.ToString();
            return int.Parse(text.Substring(text.Length - 8, 1));
        }
    }

    public int FLD_CuongHoaSoLuong
    {
        get
        {
            var fLD_MAGIC = FLD_MAGIC0;
            if (fLD_MAGIC <= 0) return 0;
            var text = fLD_MAGIC.ToString();
            return int.Parse(text.Substring(text.Length - 2, 2));
        }
    }

    public int FLDThuocTinhLoaiHinh
    {
        get
        {
            var fLD_MAGIC = FLD_MAGIC0;
            if (fLD_MAGIC > 0 && fLD_MAGIC > **********)
            {
                var text = fLD_MAGIC.ToString();
                return int.Parse(text.Substring(text.Length - 4, 1));
            }

            return 0;
        }
    }

    public int FLDThuocTinhSoLuong
    {
        get
        {
            var fLD_MAGIC = FLD_MAGIC0;
            if (fLD_MAGIC > 0 && fLD_MAGIC > **********)
            {
                var text = fLD_MAGIC.ToString();
                return int.Parse(text.Substring(text.Length - 3, 1));
            }

            return 0;
        }
    }

    public int FLD_MAGIC1
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham_byte, 20, array, 0, 4);
            return Buffer.ToInt32(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 20, 4);
    }

    public int FLD_MAGIC2
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham_byte, 24, array, 0, 4);
            return Buffer.ToInt32(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 24, 4);
    }

    public int FLD_MAGIC3
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham_byte, 28, array, 0, 4);
            return Buffer.ToInt32(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 28, 4);
    }

    public int FLD_MAGIC4
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham_byte, 32, array, 0, 4);
            return Buffer.ToInt32(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 32, 4);
    }

    public int FLD_FJ_MAGIC0
    {
        get
        {
            var array = new byte[2];
            System.Buffer.BlockCopy(VatPham_byte, 36, array, 0, 2);
            return Buffer.ToInt16(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 36, 2);
    }

    public int FLD_FJ_MAGIC1
    {
        get
        {
            var array = new byte[2];
            System.Buffer.BlockCopy(VatPham_byte, 38, array, 0, 2);
            return Buffer.ToInt16(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 38, 2);
    }

    public int FLD_FJ_TrungCapPhuHon
    {
        get
        {
            try
            {
                var array = new byte[2];
                System.Buffer.BlockCopy(VatPham_byte, 40, array, 0, 2);
                return Buffer.ToInt16(array, 0);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "FLD_FJ_TrungCapPhuHon   Geterror：   [" + DatDuocVatPhamstring() + "]" + ex);
                return 0;
            }
        }
        set
        {
            try
            {
                if (value > 0)
                    System.Buffer.BlockCopy(Buffer.GetBytes(1), 0, VatPham_byte, 38, 2);
                else if (value == 0) System.Buffer.BlockCopy(Buffer.GetBytes(0), 0, VatPham_byte, 38, 2);
                System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 40, 2);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "FLD_FJ_TrungCapPhuHon   Seterror：   [" + DatDuocVatPhamstring() + "]" + ex);
            }
        }
    }

    public int FLD_FJ_MAGIC2
    {
        get
        {
            var array = new byte[2];
            System.Buffer.BlockCopy(VatPham_byte, 42, array, 0, 2);
            return Buffer.ToInt16(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 42, 2);
    }

    public int FLD_FJ_MAGIC3
    {
        get
        {
            var array = new byte[2];
            System.Buffer.BlockCopy(VatPham_byte, 44, array, 0, 2);
            return Buffer.ToInt16(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 44, 2);
    }

    public int FLD_FJ_MAGIC4
    {
        get
        {
            var array = new byte[2];
            System.Buffer.BlockCopy(VatPham_byte, 46, array, 0, 2);
            return Buffer.ToInt16(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 46, 2);
    }

    public int FLD_FJ_MAGIC5
    {
        get
        {
            var array = new byte[2];
            System.Buffer.BlockCopy(VatPham_byte, 48, array, 0, 2);
            return Buffer.ToInt16(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 48, 2);
    }

    public int FLD_DAY1
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham_byte, 56, array, 0, 4);
            new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(BitConverter.ToInt32(array, 0));
            return BitConverter.ToInt32(array, 0);
        }
        set => System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 56, 4);
    }

    public int FLD_DAY2
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham_byte, 52, array, 0, 4);
            return BitConverter.ToInt32(array, 0);
        }
        set => System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 52, 4);
    }

    public int FLD_FJ_LowSoul
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham_byte, 62, array, 0, 4);
            return Buffer.ToInt32(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 62, 4);
    }

    public int FLD_FJ_NJ
    {
        get
        {
            var array = new byte[2];
            System.Buffer.BlockCopy(VatPham_byte, 60, array, 0, 2);
            return Buffer.ToInt16(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 60, 2);
    }

    public int FLD_FJ_TienHoa
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham_byte, 68, array, 0, 2);
            return Buffer.ToInt32(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 68, 2);
    }

    public int FLD_TuLinh
    {
        get
        {
            var array = new byte[2];
            System.Buffer.BlockCopy(VatPham_byte, 71, array, 0, 1);
            return Buffer.ToInt16(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham_byte, 71, 1);
    }

    public void Dispose()
    {
        VatPham_byte = null;
    }

    public int GetGender()
    {
        if (World.Itme.TryGetValue(BitConverter.ToInt32(VatPham_ID, 0), out var gender))
        {
            if (gender.FLD_SEX != 0) return gender.FLD_SEX;
            return 0;
        }

        return 0;
    }

    public byte[] GetByte()
    {
        var SendingClass = new SendingClass();
        SendingClass.Write(GetItemGlobal_ID);
        if (Vat_Pham_Khoa_Lai)
            SendingClass.Write(GetVatPham_ID + 20000);
        else
            SendingClass.Write(GetVatPham_ID);
        SendingClass.Write4(GetVatPhamSoLuong);
        SendingClass.Write4(FLD_MAGIC0);
        SendingClass.Write4(FLD_MAGIC1);
        SendingClass.Write4(FLD_MAGIC2);
        SendingClass.Write4(FLD_MAGIC3);
        SendingClass.Write4(FLD_MAGIC4);
        SendingClass.WriteInt(FLD_FJ_MAGIC0);
        SendingClass.WriteInt(FLD_FJ_MAGIC1);
        SendingClass.WriteInt(FLD_FJ_TrungCapPhuHon);
        SendingClass.WriteInt(FLD_FJ_MAGIC2);
        SendingClass.WriteInt(FLD_FJ_MAGIC3);
        SendingClass.WriteInt(FLD_FJ_MAGIC4);
        SendingClass.WriteInt(FLD_FJ_MAGIC5);
        SendingClass.WriteInt(0);
        SendingClass.Write4(FLD_DAY2);
        SendingClass.Write4(FLD_DAY1);
        SendingClass.WriteInt(FLD_FJ_NJ);
        SendingClass.Write4(FLD_FJ_LowSoul + Vat_Pham_Trung_Cap_Phu_Hon_ThemVao_ThucTinh);
        SendingClass.WriteInt(0);
        SendingClass.WriteInt(FLD_FJ_TienHoa);
        SendingClass.WriteInt(0);
        SendingClass.Write4(FLD_TuLinh);
        SendingClass.Write4(0);
        SendingClass.Write4(0);
        SendingClass.Write4(0);
        // SendingClass.Write4(0);
        if (World.Item_Byte_Length_92 == 96) SendingClass.Write4(0);
        return SendingClass.ToArray3();
    }

    public string DatDuocVatPhamstring()
    {
        try
        {
            return Converter.ToString(VatPham_byte);
        }
        catch
        {
            return string.Empty;
        }
    }

    public int DatDuocVatPham_TrongLuong1Cai()
    {
        try
        {
            ItmeClass value;
            return World.Itme.TryGetValue(Buffer.ToInt32(VatPham_ID, 0), out value) ? value.FLD_WEIGHT : 0;
        }
        catch
        {
            return 0;
        }
    }

    public int DatDuocVatPhamTrongLuong()
    {
        try
        {
            ItmeClass value;
            return World.Itme.TryGetValue(Buffer.ToInt32(VatPham_ID, 0), out value)
                ? value.FLD_WEIGHT * Buffer.ToInt32(VatPhamSoLuong, 0)
                : 0;
        }
        catch
        {
            return 0;
        }
    }

    public byte[] DatDuocVatPham_ThuocTinh()
    {
        var array = new byte[56];
        System.Buffer.BlockCopy(VatPham_byte, 16, array, 0, 56);
        return array;
    }

    public byte[] DatDuocGlobal_ID()
    {
        var array = new byte[8];
        System.Buffer.BlockCopy(VatPham_byte, 0, array, 0, 8);
        return array;
    }

    public byte[] DatDuocVatPham_ID()
    {
        var array = new byte[4];
        System.Buffer.BlockCopy(VatPham_byte, 8, array, 0, 4);
        return array;
    }

    public void ThietLapVatPham_ID(byte[] PID)
    {
        System.Buffer.BlockCopy(PID, 0, VatPham_byte, 8, 4);
    }

    public byte[] DatDuocVatPhamSoLuong()
    {
        var array = new byte[4];
        System.Buffer.BlockCopy(VatPham_byte, 12, array, 0, 4);
        return array;
    }

    public void ThietLap_VatPhamSoLuong(byte[] SoLuong)
    {
        System.Buffer.BlockCopy(SoLuong, 0, VatPham_byte, 12, 4);
    }

    public int DatDuocVatPhamLoaiHinh()
    {
        var key = Buffer.ToInt32(DatDuocVatPham_ID(), 0);
        return World.Itme[key].FLD_SIDE;
    }

    public int DatDuocVatPhamViTriLoaiHinh()
    {
        var byte_ = DatDuocVatPham_ID();
        if (!World.Itme.TryGetValue(Buffer.ToInt32(byte_, 0), out var value)) return 0;
        return value.FLD_RESIDE2;
    }

    public string DatDuocVatPhamTen_XungHao()
    {
        if (!World.Itme.TryGetValue(Buffer.ToInt32(VatPham_ID, 0), out var value)) return string.Empty;
        return value.ItmeNAME;
    }

    private void TinhToanVatPham_ThuocTinhPhuongPhap()
    {
        VatPham_ThuocTinhCoBan_CLVC = 0;
        VatPham_ThemVao_PVE_Defense = 0;
        Vat_Pham_Gia_Tang_Da_Kich = 0;
        Vat_Pham_Luc_Cong_Kich = 0;
        Vat_Pham_Luc_Cong_KichNew = 0;
        Vat_Pham_Luc_Cong_KichMAX = 0;
        Vat_Pham_Luc_Cong_KichMaxNew = 0;
        VatPham_ThuocTinh_LucCongKich_GiaTang = 0;
        VatPham_ThuocTinh_LaChan_GiaTang = 0;
        Vat_Pham_Luc_Phong_Ngu = 0;
        VatPham_ThuocTinh_LucPhongNgu_GiaTang = 0;
        Vat_Pham_Luc_Phong_NguNew = 0;
        VatPham_ThuocTinh_SinhMenhLuc_GiaTang = 0;
        VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan = 0;
        VatPham_ThuocTinh_NoiCong_Luc_GiaTang = 0;
        VatPham_ThuocTinh_TiLeChinhXac_GiaTang = 0;
        VatPham_ThuocTinh_NeTranh_Suat_GiaTang = 0;
        VatPham_ThuocTinh_VoCong_LucCongKich = 0;
        VatPham_ThuocTinh_VoCong_LucCongKichNew = 0;
        VatPham_ThuocTinh_GiamXuong_TiLePhanTram_PhongNgu = 0.0;
        VatPham_ThuocTinh_GiamXuong_TiLePhanTram_CongKich = 0.0;
        VatPham_ThuocTinh_GiaTang_TiLe_PhanTram_TrungDich = 0.0;
        VatPham_ThuocTinh_Gia_Tang_TiLe_PhanTram_NeTranh = 0.0;
        VatPham_ThuocTinh_BanDau_HoaPhanNo_XacSuat_TiLe_PhanTram = 0.0;
        VatPham_ThuocTinh_PhanNo_GiaTri_GiaTang = 0;
        VatPham_ThuocTinh_ThemVao_TrungDoc_TiLe_TiLePhanTram = 0.0;
        VatPham_ThuocTinh_GiamXuong_MucThuongTon = 0;
        VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang = 0;
        VatPham_ThuocTinh_ThangCap_XacSuat_ThanhCong = 0;
        VatPham_ThuocTinh_ThemVao_MucThuongTon = 0;
        VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang = 0;
        VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew = 0;
        VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang = 0;
        VatPham_ThuocTinh_TuVong_TonThat_KinhNghiem_GiamBot = 0;
        VatPham_ThuocTinh_KinhNghiem_ThuHoach_Duoc_GiaTang = 0;
        Vat_Pham_Trung_Cap_Phu_Hon_ThemVao_ThucTinh = 0;
        FLD_RESIDE2 = 0;
        FLD_LEVEL = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1护身气甲 = 0;
        VatPham_ThuocTinh_ThemVao_THUONG_VanKhi_LieuThuong = 0;
        VatPham_ThuocTinh_ThemVao_THUONG_LienHoanPhiVu = 0;
        VatPham_ThuocTinh_ThemVao_THUONG_KimChung_TraoKhi = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_3怒意之火 = 0;
        VatPham_ThuocTinh_ThemVao_KIEM_TruongHong_QuanNhat = 0;
        VatPham_ThuocTinh_ThemVao_KIEM_DiHoa_TiepMoc = 0;
        VatPham_ThuocTinh_ThemVao_KIEM_NoHai_CuongLan = 0;
        VatPham_ThuocTinh_ThemVao_KIEM_LienHoanPhiVu = 0;
        VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha = 0;
        VatPham_ThuocTinh_ThemVao_KIEM_HoiLieu_ThanPhap = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1护身罡气 = 0;
        VatPham_ThuocTinh_ThemVao_KIEM_TrungQuan_NhatNo = 0;
        VatPham_ThuocTinh_ThemVao_KIEM_PhaThien_NhatKiem = 0;
        VatPham_ThuocTinh_ThemVao_KIEM_BachBien_ThanHanh = 0;
        VatPham_ThuocTinh_ThemVao_KIEM_VoKien_BatToi = 0;
        VatPham_ThuocTinh_ThemVao_KIEM_ThuaThang_TruyKich = 0;
        VatPham_ThuocTinh_ThemVao_DAO_ChanVu_TuyetKich = 0;
        VatPham_ThuocTinh_ThemVao_DAO_OnNhu_ThaiSon = 0;
        VatPham_ThuocTinh_ThemVao_DAO_TuLuong_ThienCan = 0;
        VatPham_ThuocTinh_ThemVao_DAO_NhiepHonNhatKich = 0;
        VatPham_ThuocTinh_ThemVao_DAO_LienHoanPhiVu = 0;
        VatPham_ThuocTinh_ThemVao_DAO_LucPhachHoaSon = 0;
        VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_3火龙之火 = 0;
        VatPham_ThuocTinh_ThemVao_DAO_BaKhi_PhaGiap = 0;
        VatPham_ThuocTinh_ThemVao_DAO_AmAnh_TuyetSat = 0;
        VatPham_ThuocTinh_ThemVao_DAO_LuuQuang_LoanVu = 0;
        VatPham_ThuocTinh_ThemVao_DAO_ManhLongSatTran = 0;
        Item_qigong_5_job3 = 0;
        Item_qigong_8_job3 = 0;
        Item_qigong_11_job3 = 0;
        Item_qigong_7_job3 = 0;
        Item_qigong_3_job3 = 0;
        Item_qigong_4_job3 = 0;
        Item_qigong_9_job3 = 0;
        Item_qigong_10_job3 = 0;
        Item_qigong_4_job4 = 0;
        Item_qigong_7_job4 = 0;
        Item_qigong_10_job4 = 0;
        Item_qigong_5_job4 = 0;
        Item_qigong_2_job4 = 0;
        Item_qigong_8_job4 = 0;
        Item_qigong_1_job4 = 0;
        Item_qigong_3_job4 = 0;
        Item_qigong_9_job4 = 0;
        Item_qigong_0_job4 = 0;
        Item_qigong_11_job4 = 0;
        Item_qigong_2_job5 = 0;
        Item_qigong_1_job5 = 0;
        Item_qigong_7_job5 = 0;
        Item_qigong_4_job5 = 0;
        Item_qigong_11_job5 = 0;
        Item_qigong_9_job5 = 0;
        Item_qigong_10_job5 = 0;
        Item_qigong_5_job5 = 0;
        Item_qigong_0_job5 = 0;
        Item_qigong_3_job5 = 0;
        Item_qigong_8_job5 = 0;
        Item_qigong_5_job6 = 0;
        Item_qigong_4_job6 = 0;
        Item_qigong_3_job6 = 0;
        Item_qigong_7_job6 = 0;
        Item_qigong_1_job6 = 0;
        Item_qigong_8_job6 = 0;
        Item_qigong_9_job6 = 0;
        Item_qigong_2_job6 = 0;
        Item_qigong_0_job6 = 0;
        Item_qigong_10_job6 = 0;
        Item_qigong_11_job6 = 0;
        Item_qigong_0_job7 = 0;
        Item_qigong_7_job7 = 0;
        Item_qigong_10_job7 = 0;
        Item_qigong_3_job7 = 0;
        Item_qigong_1_job7 = 0;
        Item_qigong_2_job7 = 0;
        Item_qigong_8_job7 = 0;
        Item_qigong_9_job7 = 0;
        Item_qigong_4_job7 = 0;
        Item_qigong_5_job7 = 0;
        Item_qigong_11_job7 = 0;
        Item_qigong_11_job8 = 0;
        Item_qigong_7_job8 = 0;
        Item_qigong_2_job8 = 0;
        Item_qigong_9_job8 = 0;
        Item_qigong_4_job8 = 0;
        Item_qigong_0_job8 = 0;
        Item_qigong_10_job8 = 0;
        Item_qigong_1_job8 = 0;
        Item_qigong_3_job8 = 0;
        Item_qigong_8_job8 = 0;
        Item_qigong_5_job8 = 0;
        Item_qigong_0_job9 = 0;
        Item_qigong_1_job9 = 0;
        Item_qigong_2_job9 = 0;
        Item_qigong_3_job9 = 0;
        Item_qigong_4_job_9 = 0;
        Item_qigong_5_job9 = 0;
        Item_qigong_7_job9 = 0;
        Item_qigong_8_job9 = 0;
        Item_qigong_9_job9 = 0;
        Item_qigong_10_job9 = 0;
        Item_qigong_11_job9 = 0;
        Item_qigong_11_job10 = 0;
        Item_qigong_10_job10 = 0;
        Item_qigong_9_job10 = 0;
        Item_qigong_8_job10 = 0;
        Item_qigong_7_job10 = 0;
        Item_qigong_5_job10 = 0;
        Item_qigong_4_job10 = 0;
        Item_qigong_3_job10 = 0;
        Item_qigong_2_job10 = 0;
        Item_qigong_1_job10 = 0;
        Item_qigong_0_job10 = 0;
        Item_qigong_11_job11 = 0;
        Item_qigong_10_job11 = 0;
        Item_qigong_9_job11 = 0;
        Item_qigong_8_job11 = 0;
        Item_qigong_7_job11 = 0;
        Item_qigong_6_job11 = 0;
        Item_qigong_5_job11 = 0;
        Item_qigong_4_job11 = 0;
        Item_qigong_3_job11 = 0;
        Item_qigong_2_job11 = 0;
        Item_qigong_1_job11 = 0;
        Item_qigong_0_job11 = 0;
        Item_qigong_11_job12 = 0;
        Item_qigong_10_job12 = 0;
        Item_qigong_9_job12 = 0;
        Item_qigong_8_job12 = 0;
        Item_qigong_7_job12 = 0;
        Item_qigong_6_job12 = 0;
        Item_qigong_5_job12 = 0;
        Item_qigong_4_job12 = 0;
        Item_qigong_3_job12 = 0;
        Item_qigong_2_job12 = 0;
        Item_qigong_1_job12 = 0;
        Item_qigong_0_job12 = 0;
        Item_qigong_11_job13 = 0;
        Item_qigong_10_job13 = 0;
        Item_qigong_9_job13 = 0;
        Item_qigong_8_job13 = 0;
        Item_qigong_7_job13 = 0;
        Item_qigong_5_job13 = 0;
        Item_qigong_4_job13 = 0;
        Item_qigong_3_job13 = 0;
        Item_qigong_2_job13 = 0;
        Item_qigong_1_job13 = 0;
        Item_qigong_0_job13 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_夺命连环 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_电光石火 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_精益求精 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_4_长虹贯天 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_4_悬丝诊脉 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_4_望梅添花 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_4_哀鸿遍野 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_4_毒蛇出洞 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_4_红月狂风 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_4_烈日炎炎 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_4_满月狂风 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_3天外三矢 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_3以柔克刚 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_3子夜秋歌 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_3火凤临朝 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_3明镜止水 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_3内息行心 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_3无情打击 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_2_天魔护体 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_2_天地同寿 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_2_顺水推舟 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_2_以退为进 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_2_千钧压驼 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_2_穷途末路 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_2_三潭映月 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_2_万物回春 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1夜魔缠身 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1正本培源 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1长虹贯日 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1运气疗伤 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1运气行心 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1力劈华山 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1狂风天意 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1金钟罡气 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1飞花点翠 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1百变神行 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1遁出逆境 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1行风弄舞 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1绝影射魂 = 0;
        VatPham_ThuocTinh_ThemVao_ThangThien_1破甲刺魂 = 0;
        VatPham_ThuocTinh_ThemVao_CuongHoa = 0.0;
        Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu = 0;
        Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich = 0;
    }

    public void DatDuocVatPham_ThuocTinhPhuongThuc()
    {
        try
        {
            var num2 = 0;
            TinhToanVatPham_ThuocTinhPhuongPhap();
            if (BitConverter.ToInt32(VatPham_ID, 0) != 0)
            {
                DatDuocVatPhamCoBanCongKichLuc();
                var array = new byte[4];
                System.Buffer.BlockCopy(VatPham_byte, 16, array, 0, 4);
                GET_CUONG_HOA(BitConverter.ToInt32(array, 0).ToString());
                for (num2 = 0; num2 < 4; num2++)
                {
                    var array2 = new byte[4];
                    System.Buffer.BlockCopy(VatPham_byte, 20 + num2 * 4, array2, 0, 4);
                    DatDuocCoBanThuocTinh(BitConverter.ToInt32(array2, 0).ToString());
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Đạt được vật phẩm thuộc tính phương phápPhạm sai lầm: " + ex);
        }
    }

    private void GET_CUONG_HOA(string 强化数)
    {
        try
        {
            string text = null;
            if (强化数.Length != 9) return;
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham_byte, 20, array, 0, 4);
            text = BitConverter.ToInt32(array, 0).ToString();
            if (text != "0")
            {
            }

            VatPham_ThuocTinh_Manh_Loai_Hinh = int.Parse(强化数.Substring(强化数.Length - 9, 2));
            VatPham_ThuocTinh_Manh = int.Parse(强化数.Substring(强化数.Length - 2, 2));
            if (VatPham_ThuocTinh_Manh_Loai_Hinh != 19 || FLD_RESIDE2 != 16 ||
                !World.Itme.TryGetValue(BitConverter.ToInt32(VatPham_ID, 0), out var _)) return;
            var tile_TC_PT = 1f;
            var tile_CLVC_ULPT = 1f;
            var base_HP = 0;
            var base_cuongkhi = 0;
            var lvl_cuongkhi = 0;
            if (VatPham_ThuocTinh_Manh >= 0 && VatPham_ThuocTinh_Manh <= 99)
            {
                var getVatPham_ID = GetVatPham_ID;
                var num = getVatPham_ID;
                var num2 = num - **********;
                if ((ulong)num2 <= 5uL)
                    switch (ThuocTinh1.ThuocTinhSoLuong)
                    {
                        case 4:
                            base_cuongkhi = 50;
                            lvl_cuongkhi = 10;
                            break;
                        case 5:
                            base_cuongkhi = 100;
                            lvl_cuongkhi = 12;
                            break;
                        case 6:
                            base_cuongkhi = 150;
                            lvl_cuongkhi = 14;
                            break;
                        case 7:
                            base_cuongkhi = 200;
                            lvl_cuongkhi = 18;
                            break;
                        default:
                            base_cuongkhi = 0;
                            lvl_cuongkhi = 0;
                            break;
                    }
                    switch (num2)
                    {
                        case 0L:
                            if (ThuocTinh1.ThuocTinhSoLuong == 0)
                            {
                                tile_TC_PT = 1f;
                                tile_CLVC_ULPT = 1f;
                            }
                            else if (ThuocTinh1.ThuocTinhSoLuong == 1)
                            {
                                tile_TC_PT = 1.2f;
                                tile_CLVC_ULPT = 2f;
                            }
                            else if (ThuocTinh1.ThuocTinhSoLuong == 2)
                            {
                                tile_TC_PT = 1.5f;
                                tile_CLVC_ULPT = 4f;
                            }
                            else 
                            {
                                tile_TC_PT = 2f;
                                tile_CLVC_ULPT = 8f;
                            }

                            base_HP = 10;
                            break;
                        case 1L:
                            if (ThuocTinh1.ThuocTinhSoLuong == 0)
                            {
                                tile_TC_PT = 1f;
                                tile_CLVC_ULPT = 0.9f;
                            }
                            else if (ThuocTinh1.ThuocTinhSoLuong == 1)
                            {
                                tile_TC_PT = 1.2f;
                                tile_CLVC_ULPT = 1.8f;
                            }
                            else if (ThuocTinh1.ThuocTinhSoLuong == 2)
                            {
                                tile_TC_PT = 1.5f;
                                tile_CLVC_ULPT = 3.6f;
                            }
                            else 
                            {
                                tile_TC_PT = 2f;
                                tile_CLVC_ULPT = 7.2f;
                            }

                            base_HP = 8;
                            break;
                        case 2L:
                            if (ThuocTinh1.ThuocTinhSoLuong == 0)
                            {
                                tile_TC_PT = 1f;
                                tile_CLVC_ULPT = 0.55f;
                            }
                            else if (ThuocTinh1.ThuocTinhSoLuong == 1)
                            {
                                tile_TC_PT = 1.2f;
                                tile_CLVC_ULPT = 1.1f;
                            }
                            else if (ThuocTinh1.ThuocTinhSoLuong == 2)
                            {
                                tile_TC_PT = 1.5f;
                                tile_CLVC_ULPT = 2.2f;
                            }
                            else 
                            {
                                tile_TC_PT = 2f;
                                tile_CLVC_ULPT = 4.4f;
                            }

                            base_HP = 1;
                            break;
                        case 3L:
                            if (ThuocTinh1.ThuocTinhSoLuong == 0)
                            {
                                tile_TC_PT = 1f;
                                tile_CLVC_ULPT = 0.6f;
                            }
                            else if (ThuocTinh1.ThuocTinhSoLuong == 1)
                            {
                                tile_TC_PT = 1.2f;
                                tile_CLVC_ULPT = 1.2f;
                            }
                            else if (ThuocTinh1.ThuocTinhSoLuong == 2)
                            {
                                tile_TC_PT = 1.5f;
                                tile_CLVC_ULPT = 2.4f;
                            }
                            else 
                            {
                                tile_TC_PT = 2f;
                                tile_CLVC_ULPT = 4.8f;
                            }

                            base_HP = 2;
                            break;
                        case 4L:
                            if (ThuocTinh1.ThuocTinhSoLuong == 0)
                            {
                                tile_TC_PT = 1f;
                                tile_CLVC_ULPT = 0.5f;
                            }
                            else if (ThuocTinh1.ThuocTinhSoLuong == 1)
                            {
                                tile_TC_PT = 1.2f;
                                tile_CLVC_ULPT = 1f;
                            }
                            else if (ThuocTinh1.ThuocTinhSoLuong == 2)
                            {
                                tile_TC_PT = 1.5f;
                                tile_CLVC_ULPT = 2f;
                            }
                            else 
                            {
                                tile_TC_PT = 2f;
                                tile_CLVC_ULPT = 4f;
                            }

                            base_HP = 0;
                            break;
                        case 5L:
                            if (ThuocTinh1.ThuocTinhSoLuong == 0)
                            {
                                tile_TC_PT = 1f;
                                tile_CLVC_ULPT = 0.8f;
                            }
                            else if (ThuocTinh1.ThuocTinhSoLuong == 1)
                            {
                                tile_TC_PT = 1.2f;
                                tile_CLVC_ULPT = 1.6f;
                            }
                            else if (ThuocTinh1.ThuocTinhSoLuong == 2)
                            {
                                tile_TC_PT = 1.5f;
                                tile_CLVC_ULPT = 3.2f;
                            }
                            else 
                            {
                                tile_TC_PT = 2f;
                                tile_CLVC_ULPT = 6.4f;
                            }

                            base_HP = 6;
                            break;
                    }
            }

            Vat_Pham_Luc_Phong_Ngu += (int)((4.0 + (int)((VatPham_ThuocTinh_Manh + 1) * 0.5)) * tile_TC_PT);
            Vat_Pham_Luc_Cong_Kich += (int)((4.0 + (int)((VatPham_ThuocTinh_Manh + 1) * 0.5)) * tile_TC_PT);
            Vat_Pham_Luc_Cong_KichMAX += (int)((4.0 + (int)((VatPham_ThuocTinh_Manh + 1) * 0.5)) * tile_TC_PT);
            Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich +=
                (int)((8.0 + 2 * (int)((VatPham_ThuocTinh_Manh + 1) * 0.5)) * tile_CLVC_ULPT);
            Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu +=
                (int)((8.0 + 2 * (int)((VatPham_ThuocTinh_Manh + 1) * 0.5)) * tile_CLVC_ULPT);
            int cuongkhi_total = (base_cuongkhi + lvl_cuongkhi*(int)((VatPham_ThuocTinh_Manh)*0.5));
            Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich += cuongkhi_total;
            Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu += cuongkhi_total;
            if (VatPham_ThuocTinh_Manh > 1)
                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += base_HP + (int)(VatPham_ThuocTinh_Manh * 0.5) * 10;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Đạt được cường hóa Phạm sai lầm: " + ex);
        }
    }

    public void DatDuocVatPham_ThuocTinhPhuongThuc(int CuongHoaVK, int CuongHoaTB)
    {
        try
        {
            var num2 = 0;
            TinhToanVatPham_ThuocTinhPhuongPhap();
            if (Buffer.ToInt32(VatPham_ID, 0) != 0)
            {
                DatDuocVatPhamCoBanCongKichLuc();
                var array = new byte[4];
                System.Buffer.BlockCopy(VatPham_byte, 16, array, 0, 4);
                DatDuocCuongHoa(Buffer.ToInt32(array, 0).ToString(), CuongHoaVK, CuongHoaTB);
                for (num2 = 0; num2 < 4; num2++)
                {
                    var array2 = new byte[4];
                    System.Buffer.BlockCopy(VatPham_byte, 20 + num2 * 4, array2, 0, 4);
                    DatDuocCoBanThuocTinh(Buffer.ToInt32(array2, 0).ToString());
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "DatDuocVatPham_ThuocTinhPhuongThu   Error：" + ex);
        }
    }

    public void DatDuocVatPhamCoBanCongKichLuc()
    {
        try
        {
            ItmeClass itmeClass = null;
            if (Buffer.ToInt32(VatPham_ID, 0) == 0) return;
            itmeClass = World.Itme[Buffer.ToInt32(VatPham_ID, 0)];
            FLD_RESIDE2 = itmeClass.FLD_RESIDE2;
            FLD_LEVEL = itmeClass.FLD_LEVEL;
            if (FLD_RESIDE2 == 4)
            {
                if (FLD_FJ_TienHoa == 0)
                {
                    Vat_Pham_Luc_Cong_Kich = itmeClass.FLD_AT;
                    Vat_Pham_Luc_Cong_KichMAX = itmeClass.FLD_AT_Max;
                    Vat_Pham_Luc_Cong_KichNew = itmeClass.FLD_AT;
                    Vat_Pham_Luc_Cong_KichMaxNew = itmeClass.FLD_AT_Max;
                    Vat_Pham_Luc_Phong_Ngu = itmeClass.FLD_DF;
                    Vat_Pham_Luc_Phong_NguNew = itmeClass.FLD_DF;
                }
                else if (FLD_FJ_TienHoa == 1)
                {
                    Vat_Pham_Luc_Cong_Kich = (int)(itmeClass.FLD_AT + itmeClass.FLD_AT * 0.05);
                    Vat_Pham_Luc_Cong_KichMAX = (int)(itmeClass.FLD_AT_Max + itmeClass.FLD_AT_Max * 0.05);
                    Vat_Pham_Luc_Cong_KichNew = (int)(itmeClass.FLD_AT + itmeClass.FLD_AT * 0.05);
                    Vat_Pham_Luc_Cong_KichMaxNew = (int)(itmeClass.FLD_AT_Max + itmeClass.FLD_AT_Max * 0.05);
                    Vat_Pham_Luc_Phong_Ngu = (int)(itmeClass.FLD_DF + itmeClass.FLD_DF * 0.05);
                    Vat_Pham_Luc_Phong_NguNew = (int)(itmeClass.FLD_DF + itmeClass.FLD_DF * 0.05);
                }
                else if (FLD_FJ_TienHoa == 2)
                {
                    Vat_Pham_Luc_Cong_Kich = (int)(itmeClass.FLD_AT + itmeClass.FLD_AT * 0.08);
                    Vat_Pham_Luc_Cong_KichMAX = (int)(itmeClass.FLD_AT_Max + itmeClass.FLD_AT_Max * 0.08);
                    Vat_Pham_Luc_Cong_KichNew = (int)(itmeClass.FLD_AT + itmeClass.FLD_AT * 0.08);
                    Vat_Pham_Luc_Cong_KichMaxNew = (int)(itmeClass.FLD_AT_Max + itmeClass.FLD_AT_Max * 0.08);
                    Vat_Pham_Luc_Phong_Ngu = (int)(itmeClass.FLD_DF + itmeClass.FLD_DF * 0.08);
                    Vat_Pham_Luc_Phong_NguNew = (int)(itmeClass.FLD_DF + itmeClass.FLD_DF * 0.08);
                }
            }
            else if (FLD_RESIDE2 != 13 && FLD_RESIDE2 != 12 && FLD_RESIDE2 != 15 && FLD_RESIDE2 != 16 &&
                     FLD_RESIDE2 != 8 && FLD_RESIDE2 != 9 && FLD_RESIDE2 != 10 && FLD_RESIDE2 != 14)
            {
                VatPham_ThuocTinh_LaChan_GiaTang = itmeClass.FLD_LEVEL;
                if (FLD_FJ_TienHoa == 0)
                {
                    Vat_Pham_Luc_Cong_Kich = itmeClass.FLD_AT;
                    Vat_Pham_Luc_Cong_KichMAX = itmeClass.FLD_AT_Max;
                    Vat_Pham_Luc_Cong_KichNew = itmeClass.FLD_AT;
                    Vat_Pham_Luc_Cong_KichMaxNew = itmeClass.FLD_AT_Max;
                    Vat_Pham_Luc_Phong_Ngu = itmeClass.FLD_DF;
                    Vat_Pham_Luc_Phong_NguNew = itmeClass.FLD_DF;
                }
                else if (FLD_FJ_TienHoa == 1)
                {
                    Vat_Pham_Luc_Cong_Kich = (int)(itmeClass.FLD_AT + itmeClass.FLD_AT * 0.05);
                    Vat_Pham_Luc_Cong_KichMAX = (int)(itmeClass.FLD_AT_Max + itmeClass.FLD_AT_Max * 0.05);
                    Vat_Pham_Luc_Cong_KichNew = (int)(itmeClass.FLD_AT + itmeClass.FLD_AT * 0.05);
                    Vat_Pham_Luc_Cong_KichMaxNew = (int)(itmeClass.FLD_AT_Max + itmeClass.FLD_AT_Max * 0.05);
                    Vat_Pham_Luc_Phong_Ngu = (int)(itmeClass.FLD_DF + itmeClass.FLD_DF * 0.05);
                    Vat_Pham_Luc_Phong_NguNew = (int)(itmeClass.FLD_DF + itmeClass.FLD_DF * 0.05);
                }
                else if (FLD_FJ_TienHoa == 2)
                {
                    Vat_Pham_Luc_Cong_Kich = (int)(itmeClass.FLD_AT + itmeClass.FLD_AT * 0.08);
                    Vat_Pham_Luc_Cong_KichMAX = (int)(itmeClass.FLD_AT_Max + itmeClass.FLD_AT_Max * 0.08);
                    Vat_Pham_Luc_Cong_KichNew = (int)(itmeClass.FLD_AT + itmeClass.FLD_AT * 0.08);
                    Vat_Pham_Luc_Cong_KichMaxNew = (int)(itmeClass.FLD_AT_Max + itmeClass.FLD_AT_Max * 0.08);
                    Vat_Pham_Luc_Phong_Ngu = (int)(itmeClass.FLD_DF + itmeClass.FLD_DF * 0.08);
                    Vat_Pham_Luc_Phong_NguNew = (int)(itmeClass.FLD_DF + itmeClass.FLD_DF * 0.08);
                }
            }
            else
            {
                Vat_Pham_Luc_Cong_Kich = itmeClass.FLD_AT;
                Vat_Pham_Luc_Cong_KichMAX = itmeClass.FLD_AT_Max;
                Vat_Pham_Luc_Cong_KichNew = itmeClass.FLD_AT;
                Vat_Pham_Luc_Cong_KichMaxNew = itmeClass.FLD_AT_Max;
                Vat_Pham_Luc_Phong_Ngu = itmeClass.FLD_DF;
                Vat_Pham_Luc_Phong_NguNew = itmeClass.FLD_DF;
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Đạt được vật phẩm cơ bản lực công kích Phạm sai lầm: " + ex);
        }
    }

    private void DatDuocCoBanThuocTinh(string ysqh)
    {
        try
        {
            string str;
            switch (ysqh.Length)
            {
                default:
                    return;
                case 8:
                    str = ysqh.Substring(0, 1);
                    break;
                case 9:
                    str = ysqh.Substring(0, 2);
                    break;
            }

            var num = 0;
            var num2 = 0;
            if (World.CoHoTro_MoRongChuSo_VatPham_ThuocTinhHayKhong == 0)
            {
                num = !(str == "8")
                    ? int.Parse(ysqh.Substring(ysqh.Length - 3, 3))
                    : int.Parse(ysqh.Substring(ysqh.Length - 2, 2));
            }
            else
            {
                num2 = int.Parse(ysqh.Substring(0, 1));
                num = num2 != 8
                    ? int.Parse(ysqh) - int.Parse(str) * 10000000
                    : int.Parse(ysqh.Substring(ysqh.Length - 2, 2));
            }

            switch (int.Parse(str))
            {
                case 1:
                    VatPham_ThuocTinh_LucCongKich_GiaTang += num;
                    Vat_Pham_Luc_Cong_Kich += num;
                    Vat_Pham_Luc_Cong_KichMAX += num;
                    break;
                case 2:
                    VatPham_ThuocTinh_LucPhongNgu_GiaTang += num;
                    Vat_Pham_Luc_Phong_Ngu += num;
                    break;
                case 3:
                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += num;
                    break;
                case 4:
                    VatPham_ThuocTinh_NoiCong_Luc_GiaTang += num;
                    break;
                case 5:
                    VatPham_ThuocTinh_TiLeChinhXac_GiaTang += num;
                    break;
                case 6:
                    VatPham_ThuocTinh_NeTranh_Suat_GiaTang += num;
                    break;
                case 7:
                    switch (num)
                    {
                        case 40:
                            num += World.wg40;
                            break;
                        case 39:
                            num += World.wg39;
                            break;
                        case 38:
                            num += World.wg38;
                            break;
                        case 37:
                            num += World.wg37;
                            break;
                        case 36:
                            num += World.wg36;
                            break;
                        case 35:
                            num += World.wg35;
                            break;
                        case 34:
                            num += World.wg34;
                            break;
                        case 33:
                            num += World.wg33;
                            break;
                        case 32:
                            num += World.wg32;
                            break;
                        case 31:
                            num += World.wg31;
                            break;
                        case 30:
                            num += World.wg30;
                            break;
                        case 29:
                            num += World.wg29;
                            break;
                        case 28:
                            num += World.wg28;
                            break;
                        case 27:
                            num += World.wg27;
                            break;
                        case 26:
                            num += World.wg26;
                            break;
                        case 25:
                            num += World.wg25;
                            break;
                    }

                    VatPham_ThuocTinh_VoCong_LucCongKich += num;
                    VatPham_ThuocTinh_VoCong_LucCongKichNew += num;
                    VatPham_ThuocTinhCoBan_CLVC += num;
                    break;
                case 8:
                    switch (ysqh.Substring(3, 3))
                    {
                        case "000":
                            VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += num;
                            break;
                        case "010":
                            VatPham_ThuocTinh_ThemVao_DAO_LucPhachHoaSon += num;
                            break;
                        case "011":
                            VatPham_ThuocTinh_ThemVao_DAO_NhiepHonNhatKich += num;
                            break;
                        case "012":
                            VatPham_ThuocTinh_ThemVao_DAO_LienHoanPhiVu += num;
                            break;
                        case "014":
                            VatPham_ThuocTinh_ThemVao_DAO_CuongPhong_VanPha += num;
                            break;
                        case "019":
                            VatPham_ThuocTinh_ThemVao_DAO_OnNhu_ThaiSon += num;
                            break;
                        case "016":
                            VatPham_ThuocTinh_ThemVao_DAO_BaKhi_PhaGiap += num;
                            break;
                        case "017":
                            VatPham_ThuocTinh_ThemVao_DAO_ChanVu_TuyetKich += num;
                            Item_qigong_8_job8 += num;
                            Item_qigong_10_job13 += num;
                            Item_qigong_7_job5 += num;
                            break;
                        case "015":
                            VatPham_ThuocTinh_ThemVao_DAO_TuLuong_ThienCan += num;
                            break;
                        case "018":
                            VatPham_ThuocTinh_ThemVao_DAO_AmAnh_TuyetSat += num;
                            Item_qigong_11_job8 += num;
                            break;
                        case "312":
                            VatPham_ThuocTinh_ThemVao_DAO_ManhLongSatTran += num;
                            break;
                        case "110":
                            VatPham_ThuocTinh_ThemVao_DAO_LuuQuang_LoanVu += num;
                            break;
                        case "020":
                            VatPham_ThuocTinh_ThemVao_KIEM_TruongHong_QuanNhat += num;
                            break;
                        case "021":
                            VatPham_ThuocTinh_ThemVao_KIEM_BachBien_ThanHanh += num;
                            break;
                        case "022":
                            VatPham_ThuocTinh_ThemVao_KIEM_LienHoanPhiVu += num;
                            break;
                        case "023":
                            VatPham_ThuocTinh_ThemVao_KIEM_PhaThien_NhatKiem += num;
                            break;
                        case "024":
                            VatPham_ThuocTinh_ThemVao_KIEM_CuongPhong_VanPha += num;
                            break;
                        case "025":
                            VatPham_ThuocTinh_ThemVao_ThangThien_1护身罡气 += num;
                            break;
                        case "026":
                            VatPham_ThuocTinh_ThemVao_KIEM_DiHoa_TiepMoc += num;
                            Item_qigong_7_job9 += num;
                            break;
                        case "028":
                            VatPham_ThuocTinh_ThemVao_KIEM_NoHai_CuongLan += num;
                            break;
                        case "027":
                            VatPham_ThuocTinh_ThemVao_KIEM_HoiLieu_ThanPhap += num;
                            Item_qigong_9_job9 += num;
                            break;
                        case "120":
                            VatPham_ThuocTinh_ThemVao_KIEM_VoKien_BatToi += num;
                            break;
                        case "320":
                            VatPham_ThuocTinh_ThemVao_KIEM_ThuaThang_TruyKich += num;
                            break;
                        case "029":
                            VatPham_ThuocTinh_ThemVao_KIEM_TrungQuan_NhatNo += num;
                            break;
                        case "030":
                            VatPham_ThuocTinh_ThemVao_THUONG_KimChung_TraoKhi += num;
                            break;
                        case "031":
                            VatPham_ThuocTinh_ThemVao_THUONG_VanKhi_LieuThuong += num;
                            break;
                        case "032":
                            VatPham_ThuocTinh_ThemVao_THUONG_LienHoanPhiVu += num;
                            break;
                        case "034":
                            Item_qigong_3_job3 += num;
                            break;
                        case "035":
                            Item_qigong_4_job3 += num;
                            break;
                        case "039":
                            Item_qigong_5_job3 += num;
                            break;
                        case "038":
                            Item_qigong_7_job3 += num;
                            break;
                        case "036":
                            Item_qigong_8_job3 += num;
                            break;
                        case "130":
                            Item_qigong_9_job3 += num;
                            break;
                        case "332":
                            Item_qigong_10_job3 += num;
                            break;
                        case "037":
                            Item_qigong_11_job3 += num;
                            break;
                        case "040":
                            Item_qigong_0_job4 += num;
                            break;
                        case "041":
                            Item_qigong_1_job4 += num;
                            break;
                        case "042":
                            Item_qigong_2_job4 += num;
                            break;
                        case "044":
                            Item_qigong_3_job4 += num;
                            break;
                        case "045":
                            Item_qigong_4_job4 += num;
                            break;
                        case "048":
                            Item_qigong_5_job4 += num;
                            break;
                        case "046":
                            Item_qigong_7_job4 += num;
                            break;
                        case "047":
                            Item_qigong_8_job4 += num;
                            break;
                        case "043":
                            Item_qigong_9_job4 += num;
                            break;
                        case "049":
                            Item_qigong_10_job4 += num;
                            break;
                        case "140":
                            Item_qigong_11_job4 += num;
                            break;
                        case "050":
                            Item_qigong_0_job5 += num;
                            break;
                        case "051":
                            Item_qigong_1_job5 += num;
                            break;
                        case "052":
                            Item_qigong_3_job5 += num;
                            break;
                        case "053":
                            Item_qigong_2_job5 += num;
                            break;
                        case "054":
                            Item_qigong_4_job5 += num;
                            break;
                        case "055":
                            Item_qigong_5_job5 += num;
                            Item_qigong_8_job13 += num;
                            break;
                        case "057":
                            Item_qigong_7_job5 += num;
                            break;
                        case "056":
                            Item_qigong_8_job5 += num;
                            break;
                        case "350":
                            Item_qigong_9_job5 += num;
                            break;
                        case "351":
                            Item_qigong_10_job5 += num;
                            break;
                        case "059":
                            Item_qigong_11_job5 += num;
                            break;
                        case "070":
                            Item_qigong_0_job6 += num;
                            break;
                        case "071":
                            Item_qigong_1_job6 += num;
                            break;
                        case "072":
                            Item_qigong_2_job6 += num;
                            break;
                        case "074":
                            Item_qigong_3_job6 += num;
                            break;
                        case "075":
                            Item_qigong_4_job6 += num;
                            break;
                        case "372":
                            Item_qigong_5_job6 += num;
                            break;
                        case "076":
                            Item_qigong_7_job6 += num;
                            break;
                        case "077":
                            Item_qigong_8_job6 += num;
                            break;
                        case "078":
                            Item_qigong_9_job6 += num;
                            break;
                        case "079":
                            Item_qigong_10_job6 += num;
                            break;
                        case "073":
                            Item_qigong_11_job6 += num;
                            break;
                        case "080":
                            Item_qigong_0_job7 += num;
                            break;
                        case "081":
                            Item_qigong_1_job7 += num;
                            break;
                        case "082":
                            Item_qigong_2_job7 += num;
                            break;
                        case "083":
                            Item_qigong_3_job7 += num;
                            break;
                        case "084":
                            Item_qigong_4_job7 += num;
                            break;
                        case "085":
                            Item_qigong_5_job7 += num;
                            break;
                        case "086":
                            Item_qigong_7_job7 += num;
                            break;
                        case "087":
                            Item_qigong_8_job7 += num;
                            break;
                        case "088":
                            Item_qigong_9_job7 += num;
                            break;
                        case "089":
                            Item_qigong_10_job7 += num;
                            break;
                        case "180":
                            Item_qigong_11_job7 += num;
                            break;
                        case "250":
                            Item_qigong_0_job8 += num;
                            break;
                        case "251":
                            Item_qigong_1_job8 += num;
                            break;
                        case "253":
                            Item_qigong_2_job8 += num;
                            break;
                        case "254":
                            Item_qigong_3_job8 += num;
                            break;
                        case "252":
                            Item_qigong_4_job8 += num;
                            break;
                        case "255":
                            Item_qigong_5_job8 += num;
                            break;
                        case "256":
                            Item_qigong_7_job8 += num;
                            break;
                        case "257":
                            Item_qigong_8_job8 += num;
                            break;
                        case "259":
                            Item_qigong_9_job8 += num;
                            break;
                        case "260":
                            Item_qigong_10_job8 += num;
                            break;
                        case "258":
                            Item_qigong_11_job8 += num;
                            break;
                        case "270":
                            Item_qigong_0_job9 += num;
                            break;
                        case "271":
                            Item_qigong_1_job9 += num;
                            break;
                        case "272":
                            Item_qigong_2_job9 += num;
                            break;
                        case "273":
                            Item_qigong_3_job9 += num;
                            break;
                        case "274":
                            Item_qigong_4_job_9 += num;
                            break;
                        case "275":
                            Item_qigong_5_job9 += num;
                            break;
                        case "276":
                            Item_qigong_7_job9 += num;
                            break;
                        case "277":
                            Item_qigong_8_job9 += num;
                            break;
                        case "287":
                            Item_qigong_6_job12 += num;
                            break;
                        case "288":
                            Item_qigong_8_job12 += num;
                            break;
                        case "278":
                            Item_qigong_9_job9 += num;
                            break;
                        case "279":
                            Item_qigong_10_job9 += num;
                            break;
                        case "280":
                            Item_qigong_11_job9 += num;
                            break;
                        case "550":
                            Item_qigong_0_job10 += num;
                            break;
                        case "551":
                            Item_qigong_1_job10 += num;
                            break;
                        case "552":
                            Item_qigong_2_job10 += num;
                            break;
                        case "553":
                            Item_qigong_3_job10 += num;
                            break;
                        case "558":
                            Item_qigong_4_job10 += num;
                            break;
                        case "559":
                            Item_qigong_6_job10 += num;
                            break;
                        case "556":
                            Item_qigong_7_job10 += num;
                            break;
                        case "554":
                            Item_qigong_8_job10 += num;
                            break;
                        case "555":
                            Item_qigong_9_job10 += num;
                            break;
                        case "557":
                            Item_qigong_10_job10 += num;
                            break;
                        case "560":
                            Item_qigong_11_job10 += num;
                            break;
                        case "650":
                            Item_qigong_0_job11 += num;
                            break;
                        case "651":
                            Item_qigong_1_job11 += num;
                            break;
                        case "653":
                            Item_qigong_2_job11 += num;
                            break;
                        case "654":
                            Item_qigong_3_job11 += num;
                            break;
                        case "656":
                            Item_qigong_4_job11 += num;
                            break;
                        case "657":
                            Item_qigong_5_job11 += num;
                            break;
                        case "658":
                            Item_qigong_7_job11 += num;
                            break;
                        case "659":
                            Item_qigong_8_job11 += num;
                            break;
                        case "660":
                            Item_qigong_9_job11 += num;
                            break;
                        case "661":
                            Item_qigong_10_job11 += num;
                            break;
                        case "318":
                            Item_qigong_11_job11 += num;
                            break;
                        case "281":
                            Item_qigong_0_job12 += num;
                            break;
                        case "282":
                            Item_qigong_1_job12 += num;
                            break;
                        case "283":
                            Item_qigong_2_job12 += num;
                            break;
                        case "284":
                            Item_qigong_3_job12 += num;
                            break;
                        case "285":
                            Item_qigong_4_job12 += num;
                            break;
                        case "286":
                            Item_qigong_7_job12 += num;
                            break;
                        case "289":
                            Item_qigong_9_job12 += num;
                            break;
                        case "290":
                            Item_qigong_10_job12 += num;
                            break;
                        case "291":
                            Item_qigong_11_job12 += num;
                            break;
                        case "450":
                            Item_qigong_0_job13 += num;
                            break;
                        case "451":
                            Item_qigong_1_job13 += num;
                            break;
                        case "452":
                            Item_qigong_2_job13 += num;
                            break;
                        case "453":
                            Item_qigong_3_job13 += num;
                            break;
                        case "454":
                            Item_qigong_4_job13 += num;
                            break;
                        case "455":
                            Item_qigong_5_job13 += num;
                            break;
                        case "456":
                            Item_qigong_7_job13 += num;
                            break;
                        case "457":
                            Item_qigong_8_job13 += num;
                            Item_qigong_5_job5 += num;
                            break;
                        case "458":
                            Item_qigong_9_job13 += num;
                            break;
                        case "459":
                            Item_qigong_10_job13 += num;
                            break;
                        case "460":
                            Item_qigong_11_job13 += num;
                            break;
                    }

                    break;
                case 9:
                    VatPham_ThuocTinh_ThangCap_XacSuat_ThanhCong += num;
                    break;
                case 10:
                    VatPham_ThuocTinh_ThemVao_MucThuongTon += num;
                    break;
                case 11:
                    VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew += num;
                    VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang += num;
                    break;
                case 12:
                    VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += num;
                    break;
                case 13:
                    VatPham_ThuocTinh_TuVong_TonThat_KinhNghiem_GiamBot += num;
                    break;
                case 14:
                    break;
                case 15:
                    VatPham_ThuocTinh_KinhNghiem_ThuHoach_Duoc_GiaTang += num;
                    break;
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Đạt được cơ bản thuộc tính Phạm sai lầm: " + ex);
        }
    }

    private int DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(int level, int CuongHoaGiaiDoan)
    {
        switch (level)
        {
            case 100:
                switch (CuongHoaGiaiDoan)
                {
                    case 1:
                        return 18;
                    case 2:
                        return 24;
                    case 3:
                        return 31;
                    case 4:
                        return 38;
                    case 5:
                        return 45;
                    case 6:
                        return 53;
                    case 7:
                        return 61;
                    case 8:
                        return 76;
                    case 9:
                        return 80;
                    case 10:
                        return 95;
                }

                break;
            case 80:
                switch (CuongHoaGiaiDoan)
                {
                    case 1:
                        return 16;
                    case 2:
                        return 21;
                    case 3:
                        return 27;
                    case 4:
                        return 33;
                    case 5:
                        return 40;
                    case 6:
                        return 47;
                    case 7:
                        return 55;
                    case 8:
                        return 67;
                    case 9:
                        return 71;
                    case 10:
                        return 84;
                }

                break;
            case 60:
                switch (CuongHoaGiaiDoan)
                {
                    case 1:
                        return 14;
                    case 2:
                        return 19;
                    case 3:
                        return 25;
                    case 4:
                        return 30;
                    case 5:
                        return 36;
                    case 6:
                        return 43;
                    case 7:
                        return 49;
                    case 8:
                        return 61;
                    case 9:
                        return 64;
                    case 10:
                        return 76;
                }

                break;
            case 120:
                switch (CuongHoaGiaiDoan)
                {
                    case 1:
                        return 20;
                    case 2:
                        return 27;
                    case 3:
                        return 35;
                    case 4:
                        return 43;
                    case 5:
                        return 52;
                    case 6:
                        return 61;
                    case 7:
                        return 70;
                    case 8:
                        return 87;
                    case 9:
                        return 91;
                    case 10:
                        return 108;
                }

                break;
            case 115:
                switch (CuongHoaGiaiDoan)
                {
                    case 1:
                        return 19;
                    case 2:
                        return 26;
                    case 3:
                        return 33;
                    case 4:
                        return 40;
                    case 5:
                        return 48;
                    case 6:
                        return 57;
                    case 7:
                        return 66;
                    case 8:
                        return 81;
                    case 9:
                        return 85;
                    case 10:
                        return 101;
                }

                break;
            case 140:
                switch (CuongHoaGiaiDoan)
                {
                    case 1:
                        return 29;
                    case 2:
                        return 39;
                    case 3:
                        return 50;
                    case 4:
                        return 61;
                    case 5:
                        return 73;
                    case 6:
                        return 86;
                    case 7:
                        return 99;
                    case 8:
                        return 122;
                    case 9:
                        return 128;
                    case 10:
                        return 152;
                }

                break;
            case 130:
                switch (CuongHoaGiaiDoan)
                {
                    case 1:
                        return 24;
                    case 2:
                        return 32;
                    case 3:
                        return 41;
                    case 4:
                        return 50;
                    case 5:
                        return 60;
                    case 6:
                        return 71;
                    case 7:
                        return 82;
                    case 8:
                        return 101;
                    case 9:
                        return 106;
                    case 10:
                        return 126;
                }

                break;
        }

        return 0;
    }

    private int DatDuocVatPhamShield(ItmeClass Itme, int CuongHoaGiaiDoan)
    {
        switch (FLD_RESIDE2)
        {
            case 1:
            {
                var num4 = 0;
                var num5 = VatPham_ThuocTinh_Manh + (int)VatPham_ThuocTinh_ThemVao_CuongHoa;
                if (Itme.FLD_LEVEL <= 80)
                    num4 = 5;
                else if (Itme.FLD_LEVEL == 90)
                    num4 = 10;
                else if (Itme.FLD_LEVEL == 100)
                    num4 = 20;
                else if (Itme.FLD_LEVEL == 110)
                    num4 = 30;
                else if (Itme.FLD_LEVEL == 120)
                    num4 = 40;
                else if (Itme.FLD_LEVEL == 130)
                    num4 = 50;
                else if (Itme.FLD_LEVEL == 140) num4 = 60;
                VatPham_ThuocTinh_LaChan_GiaTang += num5 * num4;
                switch (num5)
                {
                    case 6:
                        VatPham_ThuocTinh_LaChan_GiaTang += 155;
                        break;
                    case 7:
                        VatPham_ThuocTinh_LaChan_GiaTang += 195;
                        break;
                    case 8:
                        VatPham_ThuocTinh_LaChan_GiaTang += 260;
                        break;
                    case 9:
                        VatPham_ThuocTinh_LaChan_GiaTang += 375;
                        break;
                    case 10:
                        VatPham_ThuocTinh_LaChan_GiaTang += 615;
                        break;
                    case 11:
                        VatPham_ThuocTinh_LaChan_GiaTang += 905;
                        break;
                    case 12:
                        VatPham_ThuocTinh_LaChan_GiaTang += 1295;
                        break;
                    case 13:
                        VatPham_ThuocTinh_LaChan_GiaTang += 1685;
                        break;
                    case 14:
                        VatPham_ThuocTinh_LaChan_GiaTang += 2125;
                        break;
                    case 15:
                        VatPham_ThuocTinh_LaChan_GiaTang += 2565;
                        break;
                    case 16:
                        VatPham_ThuocTinh_LaChan_GiaTang += 3115;
                        break;
                    case 17:
                        VatPham_ThuocTinh_LaChan_GiaTang += 3715;
                        break;
                }

                break;
            }
            case 2:
            case 5:
            {
                var num2 = 0;
                var num3 = VatPham_ThuocTinh_Manh + (int)VatPham_ThuocTinh_ThemVao_CuongHoa;
                if (Itme.FLD_LEVEL <= 80)
                    num2 = 5;
                else if (Itme.FLD_LEVEL == 90)
                    num2 = 7;
                else if (Itme.FLD_LEVEL == 100)
                    num2 = 9;
                else if (Itme.FLD_LEVEL == 110)
                    num2 = 11;
                else if (Itme.FLD_LEVEL == 120)
                    num2 = 13;
                else if (Itme.FLD_LEVEL == 130)
                    num2 = 15;
                else if (Itme.FLD_LEVEL == 140)
                    num2 = 17;
                else if (Itme.FLD_LEVEL == 150) num2 = 22;
                VatPham_ThuocTinh_LaChan_GiaTang += num3 * num2;
                switch (num3)
                {
                    case 6:
                        VatPham_ThuocTinh_LaChan_GiaTang += 5;
                        break;
                    case 7:
                        VatPham_ThuocTinh_LaChan_GiaTang += 15;
                        break;
                    case 8:
                        VatPham_ThuocTinh_LaChan_GiaTang += 33;
                        break;
                    case 9:
                        VatPham_ThuocTinh_LaChan_GiaTang += 55;
                        break;
                    case 10:
                        VatPham_ThuocTinh_LaChan_GiaTang += 124;
                        break;
                    case 11:
                        VatPham_ThuocTinh_LaChan_GiaTang += 207;
                        break;
                    case 12:
                        VatPham_ThuocTinh_LaChan_GiaTang += 305;
                        break;
                    case 13:
                        VatPham_ThuocTinh_LaChan_GiaTang += 443;
                        break;
                    case 14:
                        VatPham_ThuocTinh_LaChan_GiaTang += 611;
                        break;
                    case 15:
                        VatPham_ThuocTinh_LaChan_GiaTang += 814;
                        break;
                    case 16:
                        VatPham_ThuocTinh_LaChan_GiaTang += 1057;
                        break;
                    case 17:
                        VatPham_ThuocTinh_LaChan_GiaTang += 1357;
                        break;
                }

                break;
            }
            case 6:
            {
                var num = 0;
                if (Itme.FLD_LEVEL <= 55)
                    num = 1;
                else if (Itme.FLD_LEVEL <= 65)
                    num = 2;
                else if (Itme.FLD_LEVEL <= 75)
                    num = 3;
                else if (Itme.FLD_LEVEL <= 85)
                    num = 5;
                else if (Itme.FLD_LEVEL == 95)
                    num = 7;
                else if (Itme.FLD_LEVEL == 105)
                    num = 9;
                else if (Itme.FLD_LEVEL == 115)
                    num = 11;
                else if (Itme.FLD_LEVEL == 125)
                    num = 13;
                else if (Itme.FLD_LEVEL == 135)
                    num = 15;
                else if (Itme.FLD_LEVEL == 145)
                    num = 17;
                else if (Itme.FLD_LEVEL == 155) num = 22;
                VatPham_ThuocTinh_LaChan_GiaTang += VatPham_ThuocTinh_Manh * num;
                if (VatPham_ThuocTinh_Manh > 5)
                    switch (VatPham_ThuocTinh_Manh)
                    {
                        case 6:
                            VatPham_ThuocTinh_LaChan_GiaTang += 5;
                            break;
                        case 7:
                            VatPham_ThuocTinh_LaChan_GiaTang += 15;
                            break;
                        case 8:
                            VatPham_ThuocTinh_LaChan_GiaTang += 33;
                            break;
                        case 9:
                            VatPham_ThuocTinh_LaChan_GiaTang += 55;
                            break;
                        case 10:
                            VatPham_ThuocTinh_LaChan_GiaTang += 124;
                            break;
                        case 11:
                            VatPham_ThuocTinh_LaChan_GiaTang += 207;
                            break;
                        case 12:
                            VatPham_ThuocTinh_LaChan_GiaTang += 305;
                            break;
                        case 13:
                            VatPham_ThuocTinh_LaChan_GiaTang += 443;
                            break;
                        case 14:
                            VatPham_ThuocTinh_LaChan_GiaTang += 611;
                            break;
                        case 15:
                            VatPham_ThuocTinh_LaChan_GiaTang += 814;
                            break;
                        case 16:
                            VatPham_ThuocTinh_LaChan_GiaTang += 1057;
                            break;
                        case 17:
                            VatPham_ThuocTinh_LaChan_GiaTang += 1357;
                            break;
                    }

                break;
            }
        }

        return 0;
    }

    private void TinhToanVuKhiThuocTinh(ItmeClass Itme, int cuonghoa_bonus = 0)
    {
        try
        {
            Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 6;
            Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 6;
            Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 6;
            Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 6;
            if (VatPham_ThuocTinh_Manh > 5)
            {
                if (VatPham_ThuocTinh_Manh >= 13)
                {
                }

                if (Itme.FLD_JOB_LEVEL >= 3 && VatPham_ThuocTinh_Manh >= 7)
                {
                    switch (VatPham_ThuocTinh_Manh)
                    {
                        case 6:
                            Vat_Pham_Luc_Cong_Kich += 2;
                            Vat_Pham_Luc_Cong_KichMAX += 2;
                            Vat_Pham_Luc_Cong_KichNew += 2;
                            Vat_Pham_Luc_Cong_KichMaxNew += 2;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 12;
                                Vat_Pham_Luc_Cong_KichMAX += 12;
                                Vat_Pham_Luc_Cong_KichNew += 12;
                                Vat_Pham_Luc_Cong_KichMaxNew += 12;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 7;
                                    Vat_Pham_Luc_Cong_KichMAX += 7;
                                    Vat_Pham_Luc_Cong_KichNew += 7;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 7;
                                }
                            }

                            break;
                        case 7:
                            Vat_Pham_Luc_Cong_Kich += 10;
                            Vat_Pham_Luc_Cong_KichMAX += 10;
                            Vat_Pham_Luc_Cong_KichNew += 10;
                            Vat_Pham_Luc_Cong_KichMaxNew += 10;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 12;
                                Vat_Pham_Luc_Cong_KichMAX += 12;
                                Vat_Pham_Luc_Cong_KichNew += 12;
                                Vat_Pham_Luc_Cong_KichMaxNew += 12;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 7;
                                    Vat_Pham_Luc_Cong_KichMAX += 7;
                                    Vat_Pham_Luc_Cong_KichNew += 7;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 7;
                                }
                            }

                            break;
                        case 8:
                            Vat_Pham_Luc_Cong_Kich += 24;
                            Vat_Pham_Luc_Cong_KichMAX += 24;
                            Vat_Pham_Luc_Cong_KichNew += 24;
                            Vat_Pham_Luc_Cong_KichMaxNew += 24;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 17;
                                Vat_Pham_Luc_Cong_KichMAX += 17;
                                Vat_Pham_Luc_Cong_KichNew += 17;
                                Vat_Pham_Luc_Cong_KichMaxNew += 17;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 12;
                                    Vat_Pham_Luc_Cong_KichMAX += 12;
                                    Vat_Pham_Luc_Cong_KichNew += 12;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 12;
                                }
                            }

                            break;
                        case 9:
                            Vat_Pham_Luc_Cong_Kich += 48;
                            Vat_Pham_Luc_Cong_KichMAX += 48;
                            Vat_Pham_Luc_Cong_KichNew += 48;
                            Vat_Pham_Luc_Cong_KichMaxNew += 48;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 22;
                                Vat_Pham_Luc_Cong_KichMAX += 22;
                                Vat_Pham_Luc_Cong_KichNew += 22;
                                Vat_Pham_Luc_Cong_KichMaxNew += 22;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 37;
                                    Vat_Pham_Luc_Cong_KichMAX += 37;
                                    Vat_Pham_Luc_Cong_KichNew += 37;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 37;
                                }
                            }

                            break;
                        case 10:
                            Vat_Pham_Luc_Cong_Kich += 102;
                            Vat_Pham_Luc_Cong_KichMAX += 102;
                            Vat_Pham_Luc_Cong_KichNew += 102;
                            Vat_Pham_Luc_Cong_KichMaxNew += 102;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 32;
                                Vat_Pham_Luc_Cong_KichMAX += 32;
                                Vat_Pham_Luc_Cong_KichNew += 32;
                                Vat_Pham_Luc_Cong_KichMaxNew += 32;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 57;
                                    Vat_Pham_Luc_Cong_KichMAX += 57;
                                    Vat_Pham_Luc_Cong_KichNew += 57;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 57;
                                }
                            }

                            break;
                        case 11:
                            Vat_Pham_Luc_Cong_Kich += 111;
                            Vat_Pham_Luc_Cong_KichMAX += 111;
                            Vat_Pham_Luc_Cong_KichNew += 111;
                            Vat_Pham_Luc_Cong_KichMaxNew += 111;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 35;
                                Vat_Pham_Luc_Cong_KichMAX += 35;
                                Vat_Pham_Luc_Cong_KichNew += 35;
                                Vat_Pham_Luc_Cong_KichMaxNew += 35;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 57;
                                    Vat_Pham_Luc_Cong_KichMAX += 57;
                                    Vat_Pham_Luc_Cong_KichNew += 57;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 57;
                                }
                            }

                            break;
                        case 12:
                            Vat_Pham_Luc_Cong_Kich += 125;
                            Vat_Pham_Luc_Cong_KichMAX += 125;
                            Vat_Pham_Luc_Cong_KichNew += 125;
                            Vat_Pham_Luc_Cong_KichMaxNew += 125;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 45;
                                Vat_Pham_Luc_Cong_KichMAX += 45;
                                Vat_Pham_Luc_Cong_KichNew += 45;
                                Vat_Pham_Luc_Cong_KichMaxNew += 45;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 57;
                                    Vat_Pham_Luc_Cong_KichMAX += 57;
                                    Vat_Pham_Luc_Cong_KichNew += 57;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 57;
                                }
                            }

                            break;
                        case 13:
                            Vat_Pham_Luc_Cong_Kich += 144;
                            Vat_Pham_Luc_Cong_KichMAX += 144;
                            Vat_Pham_Luc_Cong_KichNew += 144;
                            Vat_Pham_Luc_Cong_KichMaxNew += 144;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 55;
                                Vat_Pham_Luc_Cong_KichMAX += 55;
                                Vat_Pham_Luc_Cong_KichNew += 55;
                                Vat_Pham_Luc_Cong_KichMaxNew += 55;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 57;
                                    Vat_Pham_Luc_Cong_KichMAX += 57;
                                    Vat_Pham_Luc_Cong_KichNew += 57;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 57;
                                }
                            }

                            break;
                        case 14:
                            Vat_Pham_Luc_Cong_Kich += 198;
                            Vat_Pham_Luc_Cong_KichMAX += 198;
                            Vat_Pham_Luc_Cong_KichNew += 198;
                            Vat_Pham_Luc_Cong_KichMaxNew += 198;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 65;
                                Vat_Pham_Luc_Cong_KichMAX += 65;
                                Vat_Pham_Luc_Cong_KichNew += 65;
                                Vat_Pham_Luc_Cong_KichMaxNew += 65;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 57;
                                    Vat_Pham_Luc_Cong_KichMAX += 57;
                                    Vat_Pham_Luc_Cong_KichNew += 57;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 57;
                                }
                            }

                            break;
                        case 15:
                            Vat_Pham_Luc_Cong_Kich += 262;
                            Vat_Pham_Luc_Cong_KichMAX += 262;
                            Vat_Pham_Luc_Cong_KichNew += 262;
                            Vat_Pham_Luc_Cong_KichMaxNew += 262;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 65;
                                Vat_Pham_Luc_Cong_KichMAX += 65;
                                Vat_Pham_Luc_Cong_KichNew += 65;
                                Vat_Pham_Luc_Cong_KichMaxNew += 65;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 57;
                                    Vat_Pham_Luc_Cong_KichMAX += 57;
                                    Vat_Pham_Luc_Cong_KichNew += 57;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 57;
                                }
                            }

                            break;
                        case 16:
                            Vat_Pham_Luc_Cong_Kich += 291;
                            Vat_Pham_Luc_Cong_KichMAX += 291;
                            Vat_Pham_Luc_Cong_KichNew += 291;
                            Vat_Pham_Luc_Cong_KichMaxNew += 291;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 65;
                                Vat_Pham_Luc_Cong_KichMAX += 65;
                                Vat_Pham_Luc_Cong_KichNew += 65;
                                Vat_Pham_Luc_Cong_KichMaxNew += 65;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 57;
                                    Vat_Pham_Luc_Cong_KichMAX += 57;
                                    Vat_Pham_Luc_Cong_KichNew += 57;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 57;
                                }
                            }

                            VatPham_ThemVaoCamLang_CamPhucHoi = 3;
                            break;
                        case 17:
                            Vat_Pham_Luc_Cong_Kich += 320;
                            Vat_Pham_Luc_Cong_KichMAX += 320;
                            Vat_Pham_Luc_Cong_KichNew += 320;
                            Vat_Pham_Luc_Cong_KichMaxNew += 320;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 65;
                                Vat_Pham_Luc_Cong_KichMAX += 65;
                                Vat_Pham_Luc_Cong_KichNew += 65;
                                Vat_Pham_Luc_Cong_KichMaxNew += 65;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 57;
                                    Vat_Pham_Luc_Cong_KichMAX += 57;
                                    Vat_Pham_Luc_Cong_KichNew += 57;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 57;
                                }
                            }

                            VatPham_ThemVaoCamLang_CamPhucHoi = 4;
                            break;
                        case 18:
                            Vat_Pham_Luc_Cong_Kich += 349;
                            Vat_Pham_Luc_Cong_KichMAX += 349;
                            Vat_Pham_Luc_Cong_KichNew += 349;
                            Vat_Pham_Luc_Cong_KichMaxNew += 349;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 65;
                                Vat_Pham_Luc_Cong_KichMAX += 65;
                                Vat_Pham_Luc_Cong_KichNew += 65;
                                Vat_Pham_Luc_Cong_KichMaxNew += 65;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 57;
                                    Vat_Pham_Luc_Cong_KichMAX += 57;
                                    Vat_Pham_Luc_Cong_KichNew += 57;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 57;
                                }
                            }

                            VatPham_ThemVaoCamLang_CamPhucHoi = 5;
                            break;
                        case 19:
                            Vat_Pham_Luc_Cong_Kich += 197;
                            Vat_Pham_Luc_Cong_KichMAX += 197;
                            Vat_Pham_Luc_Cong_KichNew += 197;
                            Vat_Pham_Luc_Cong_KichMaxNew += 197;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 60;
                                Vat_Pham_Luc_Cong_KichMAX += 60;
                                Vat_Pham_Luc_Cong_KichNew += 60;
                                Vat_Pham_Luc_Cong_KichMaxNew += 60;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 57;
                                    Vat_Pham_Luc_Cong_KichMAX += 57;
                                    Vat_Pham_Luc_Cong_KichNew += 57;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 57;
                                }
                            }

                            break;
                        case 20:
                            Vat_Pham_Luc_Cong_Kich += 197;
                            Vat_Pham_Luc_Cong_KichMAX += 197;
                            Vat_Pham_Luc_Cong_KichNew += 197;
                            Vat_Pham_Luc_Cong_KichMaxNew += 197;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 60;
                                Vat_Pham_Luc_Cong_KichMAX += 60;
                                Vat_Pham_Luc_Cong_KichNew += 60;
                                Vat_Pham_Luc_Cong_KichMaxNew += 60;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 57;
                                    Vat_Pham_Luc_Cong_KichMAX += 57;
                                    Vat_Pham_Luc_Cong_KichNew += 57;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 57;
                                }
                            }

                            break;
                        case 21:
                            Vat_Pham_Luc_Cong_Kich += 197;
                            Vat_Pham_Luc_Cong_KichMAX += 197;
                            Vat_Pham_Luc_Cong_KichNew += 197;
                            Vat_Pham_Luc_Cong_KichMaxNew += 197;
                            if (Itme.FLD_Chan == 1)
                            {
                                Vat_Pham_Luc_Cong_Kich += 60;
                                Vat_Pham_Luc_Cong_KichMAX += 60;
                                Vat_Pham_Luc_Cong_KichNew += 60;
                                Vat_Pham_Luc_Cong_KichMaxNew += 60;
                                if (Itme.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += 57;
                                    Vat_Pham_Luc_Cong_KichMAX += 57;
                                    Vat_Pham_Luc_Cong_KichNew += 57;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 57;
                                }
                            }

                            break;
                    }
                }
                else
                {
                    Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 5) * 2;
                    Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 5) * 2;
                    Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 5) * 2;
                    Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 5) * 2;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Cong_Kich += 8;
                        Vat_Pham_Luc_Cong_KichMAX += 8;
                        Vat_Pham_Luc_Cong_KichNew += 8;
                        Vat_Pham_Luc_Cong_KichMaxNew += 8;
                    }
                }

                if (VatPham_ThuocTinh_Manh >= 10 && FLD_RESIDE2 == 4)
                    switch (VatPham_ThuocTinh_Manh)
                    {
                        case 10:
                        {
                            var vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang =
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang + 1;
                            VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang =
                                vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang;
                            break;
                        }
                        case 11:
                            VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 2;
                            break;
                        case 12:
                            VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
                            break;
                        case 13:
                            if (Itme.FLD_Chan == 1)
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
                            else
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
                            break;
                        case 14:
                            if (Itme.FLD_Chan == 1)
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
                            else
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
                            break;
                        case 15:
                            if (Itme.FLD_Chan == 1)
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
                            else
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
                            break;
                        case 16:
                            if (Itme.FLD_Chan == 1)
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
                            else
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
                            break;
                        case 17:
                            if (Itme.FLD_Chan == 1)
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
                            else
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
                            break;
                        case 18:
                            if (Itme.FLD_Chan == 1)
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
                            else
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
                            break;
                        case 19:
                            if (Itme.FLD_Chan == 1)
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
                            else
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
                            break;
                        case 20:
                            if (Itme.FLD_Chan == 1)
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
                            else
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
                            break;
                        case 21:
                            if (Itme.FLD_Chan == 1)
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
                            else
                                VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
                            break;
                    }

                if (VatPham_ThuocTinh_Manh >= 7)
                {
                    var dictionary = new Dictionary<int, Itimesx>();
                    dictionary.Add(0, ThuocTinh1);
                    dictionary.Add(1, ThuocTinh2);
                    dictionary.Add(2, ThuocTinh3);
                    dictionary.Add(3, ThuocTinh4);
                    for (var i = 0; i < 4; i++)
                    {
                        if (dictionary[i].ThuocTinhLoaiHinh == 0) continue;
                        var ThuocTinhSoLuong = dictionary[i].ThuocTinhSoLuong;
                        switch (dictionary[i].ThuocTinhLoaiHinh)
                        {
                            case 7:
                                if (VatPham_ThuocTinh_Manh == 7)
                                {
                                    if (i < 2)
                                    {
                                        var vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang =
                                            VatPham_ThuocTinh_VoCong_LucCongKich + 1;
                                        VatPham_ThuocTinh_VoCong_LucCongKich =
                                            vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang;
                                        vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang =
                                            VatPham_ThuocTinh_VoCong_LucCongKichNew + 1;
                                        VatPham_ThuocTinh_VoCong_LucCongKichNew =
                                            vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang;
                                    }
                                }
                                else if (VatPham_ThuocTinh_Manh <= 12)
                                {
                                    VatPham_ThuocTinh_VoCong_LucCongKich += VatPham_ThuocTinh_Manh - 7;
                                    VatPham_ThuocTinh_VoCong_LucCongKichNew += VatPham_ThuocTinh_Manh - 7;
                                }
                                else if (VatPham_ThuocTinh_Manh == 13)
                                {
                                    var num4 = Math.Round(ThuocTinhSoLuong / 4.0);
                                    VatPham_ThuocTinh_VoCong_LucCongKich += VatPham_ThuocTinh_Manh - 7 + (int)num4;
                                    VatPham_ThuocTinh_VoCong_LucCongKichNew += VatPham_ThuocTinh_Manh - 7 + (int)num4;
                                }
                                else if (VatPham_ThuocTinh_Manh == 14)
                                {
                                    var num5 = Math.Round(ThuocTinhSoLuong / 4.0);
                                    VatPham_ThuocTinh_VoCong_LucCongKich += 9 + (int)num5;
                                    VatPham_ThuocTinh_VoCong_LucCongKichNew += 9 + (int)num5;
                                }
                                else if (VatPham_ThuocTinh_Manh == 15)
                                {
                                    var num6 = Math.Round(ThuocTinhSoLuong / 4.0);
                                    VatPham_ThuocTinh_VoCong_LucCongKich += 14 + (int)num6;
                                    VatPham_ThuocTinh_VoCong_LucCongKichNew += 14 + (int)num6;
                                }
                                else if (VatPham_ThuocTinh_Manh == 16 || VatPham_ThuocTinh_Manh == 17 ||
                                         VatPham_ThuocTinh_Manh == 18)
                                {
                                    var num7 = Math.Round(ThuocTinhSoLuong / 4.0);
                                    VatPham_ThuocTinh_VoCong_LucCongKich += 14 + (int)num7;
                                    VatPham_ThuocTinh_VoCong_LucCongKichNew += 14 + (int)num7;
                                }
                                else
                                {
                                    var num8 = Math.Round(ThuocTinhSoLuong / 4.0);
                                    VatPham_ThuocTinh_VoCong_LucCongKich += VatPham_ThuocTinh_Manh - 8 + (int)num8;
                                    VatPham_ThuocTinh_VoCong_LucCongKichNew += VatPham_ThuocTinh_Manh - 8 + (int)num8;
                                }

                                break;
                            case 8:
                            {
                                var CuongHoa_SL = VatPham_ThuocTinh_Manh + cuonghoa_bonus;
                                if (CuongHoa_SL >= 13)
                                {
                                    var vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang =
                                        VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang + 1;
                                    VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang =
                                        vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang;
                                }

                                break;
                            }
                            case 10:
                                if (VatPham_ThuocTinh_Manh == 7)
                                {
                                    if (i < 2)
                                    {
                                        var vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang =
                                            VatPham_ThuocTinh_ThemVao_MucThuongTon + 1;
                                        VatPham_ThuocTinh_ThemVao_MucThuongTon =
                                            vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang;
                                    }
                                }
                                else if (VatPham_ThuocTinh_Manh <= 12)
                                {
                                    VatPham_ThuocTinh_ThemVao_MucThuongTon += VatPham_ThuocTinh_Manh - 7;
                                }
                                else if (VatPham_ThuocTinh_Manh <= 14)
                                {
                                    VatPham_ThuocTinh_ThemVao_MucThuongTon += VatPham_ThuocTinh_Manh - 7 +
                                                                              (int)Math.Round(ThuocTinhSoLuong / 4.0);
                                }
                                else
                                {
                                    VatPham_ThuocTinh_ThemVao_MucThuongTon += VatPham_ThuocTinh_Manh - 8 +
                                                                              (int)Math.Round(ThuocTinhSoLuong / 4.0);
                                }

                                break;
                            case 3:
                                if (VatPham_ThuocTinh_Manh == 7)
                                {
                                    if (i < 2)
                                    {
                                        var vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang =
                                            VatPham_ThuocTinh_SinhMenhLuc_GiaTang + 1;
                                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang =
                                            vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang;
                                    }
                                }
                                else if (VatPham_ThuocTinh_Manh <= 12)
                                {
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh - 7;
                                }
                                else if (VatPham_ThuocTinh_Manh <= 14)
                                {
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh - 7 +
                                                                             (int)Math.Round(ThuocTinhSoLuong / 4.0);
                                }
                                else
                                {
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh - 8 +
                                                                             (int)Math.Round(ThuocTinhSoLuong / 4.0);
                                }

                                break;
                            case 1:
                                if (VatPham_ThuocTinh_Manh == 7)
                                {
                                    if (i < 2)
                                    {
                                        var vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang =
                                            Vat_Pham_Luc_Cong_Kich + 1;
                                        Vat_Pham_Luc_Cong_Kich = vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang;
                                        vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang =
                                            Vat_Pham_Luc_Cong_KichMAX + 1;
                                        Vat_Pham_Luc_Cong_KichMAX = vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang;
                                        vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang =
                                            Vat_Pham_Luc_Cong_KichNew + 1;
                                        Vat_Pham_Luc_Cong_KichNew = vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang;
                                        vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang =
                                            Vat_Pham_Luc_Cong_KichMaxNew + 1;
                                        Vat_Pham_Luc_Cong_KichMaxNew = vatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang;
                                    }
                                }
                                else if (VatPham_ThuocTinh_Manh <= 12)
                                {
                                    Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh - 7;
                                    Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh - 7;
                                    Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh - 7;
                                    Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh - 7;
                                }
                                else if (VatPham_ThuocTinh_Manh <= 14)
                                {
                                    var num2 = Math.Round(ThuocTinhSoLuong / 4.0);
                                    Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh - 7 + (int)num2;
                                    Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh - 7 + (int)num2;
                                    Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh - 7 + (int)num2;
                                    Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh - 7 + (int)num2;
                                }
                                else
                                {
                                    var num3 = Math.Round(ThuocTinhSoLuong / 4.0);
                                    Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh - 8 + (int)num3;
                                    Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh - 8 + (int)num3;
                                    Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh - 8 + (int)num3;
                                    Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh - 8 + (int)num3;
                                }

                                break;
                        }
                    }

                    dictionary.Clear();
                }
            }

            if (Itme.FLD_LEVEL >= 130 && VatPham_ThuocTinh_Manh == 5)
            {
                Vat_Pham_Luc_Cong_Kich += 2;
                Vat_Pham_Luc_Cong_KichMAX += 2;
                Vat_Pham_Luc_Cong_KichNew += 2;
                Vat_Pham_Luc_Cong_KichMaxNew += 2;
            }
        }
        catch
        {
        }
    }

    private void TinhToanVuKhiThuocTinh_Old(ItmeClass Itme)
    {
        try
        {
            var num = 0;
            var num9 = 0;
            var num10 = 0;
            var num11 = 0;
            var num12 = 0;
            var num2 = 0;
            Dictionary<int, Itimesx> dictionary = null;
            var num3 = 0;
            Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 6;
            Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 6;
            Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 6;
            Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 6;
            num = 79;
            var flag = true;
            while (true)
            {
                if (VatPham_ThuocTinh_Manh > 5)
                {
                    num = 99;
                    while (true)
                    {
                        IL_16c1:
                        num = 87;
                        while (true)
                        {
                            if (Itme.FLD_JOB_LEVEL >= 3)
                            {
                                num = 6;
                                goto IL_0c7b;
                            }

                            goto IL_00ee;
                            IL_1381:
                            VatPham_ThuocTinh_ThemVao_MucThuongTon += VatPham_ThuocTinh_Manh - 7;
                            num = 98;
                            goto IL_1665;
                            IL_00ee:
                            Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 5) * 2;
                            Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 5) * 2;
                            Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 5) * 2;
                            Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 5) * 2;
                            num = 1;
                            IL_0153:
                            if (Itme.FLD_LEVEL >= 130)
                            {
                                num = 129;
                                goto IL_0c36;
                            }

                            goto IL_021b;
                            IL_017b:
                            if (VatPham_ThuocTinh_Manh <= 14)
                            {
                                num = 43;
                                goto IL_15b8;
                            }

                            var num4 = Math.Round(num11 / 4.0);
                            Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh - 8 + (int)num4;
                            Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh - 8 + (int)num4;
                            Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh - 8 + (int)num4;
                            Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh - 8 + (int)num4;
                            num = 105;
                            goto IL_1665;
                            IL_021b:
                            num = 9;
                            IL_0221:
                            if (VatPham_ThuocTinh_Manh >= 10)
                            {
                                num = 113;
                                goto IL_02ac;
                            }

                            goto IL_1097;
                            IL_13bf:
                            if (VatPham_ThuocTinh_Manh <= 14)
                            {
                                num = 32;
                                goto IL_141b;
                            }

                            VatPham_ThuocTinh_ThemVao_MucThuongTon +=
                                VatPham_ThuocTinh_Manh - 8 + (int)Math.Round(num11 / 4.0);
                            num = 82;
                            goto IL_1665;
                            IL_02ac:
                            num = 122;
                            IL_02b2:
                            if (FLD_RESIDE2 == 4)
                            {
                                num = 131;
                                goto IL_02d9;
                            }

                            goto IL_1097;
                            IL_02d0:
                            num = 16;
                            goto IL_10b8;
                            IL_02d9:
                            num10 = VatPham_ThuocTinh_Manh;
                            num = 93;
                            IL_02e6:
                            switch (num10)
                            {
                                case 17:
                                    num = 15;
                                    break;
                                case 16:
                                    num = 15;
                                    break;
                                case 15:
                                    num = 15;
                                    break;
                                case 14:
                                    num = 33;
                                    break;
                                case 13:
                                    num = 36;
                                    break;
                                case 12:
                                    VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
                                    num = 114;
                                    break;
                                case 11:
                                    VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 2;
                                    num = 119;
                                    break;
                                case 10:
                                    VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang++;
                                    num = 64;
                                    break;
                                default:
                                    num = 133;
                                    break;
                            }

                            IL_037d:
                            switch (num)
                            {
                                case 79:
                                    break;
                                case 102:
                                    goto IL_00b9;
                                case 1:
                                    goto IL_0153;
                                case 13:
                                    goto IL_017b;
                                case 7:
                                case 8:
                                case 26:
                                case 30:
                                case 46:
                                case 49:
                                case 54:
                                case 57:
                                case 75:
                                case 127:
                                case 135:
                                    goto IL_021b;
                                case 9:
                                    goto IL_0221;
                                case 50:
                                    goto IL_0240;
                                case 113:
                                    goto IL_02ac;
                                case 122:
                                    goto IL_02b2;
                                case 65:
                                    goto IL_02d0;
                                case 131:
                                    goto IL_02d9;
                                case 93:
                                    goto IL_02e6;
                                case 11:
                                    return;
                                case 0:
                                    if (Itme.FLD_LEVEL >= 130)
                                    {
                                        num = 5;
                                        goto case 5;
                                    }

                                    goto IL_021b;
                                case 2:
                                    if (Itme.FLD_LEVEL >= 130)
                                    {
                                        num = 84;
                                        goto case 84;
                                    }

                                    goto IL_021b;
                                case 5:
                                    Vat_Pham_Luc_Cong_Kich += 60;
                                    Vat_Pham_Luc_Cong_KichMAX += 60;
                                    Vat_Pham_Luc_Cong_KichNew += 60;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 60;
                                    num = 54;
                                    goto IL_021b;
                                case 23:
                                    if (Itme.FLD_LEVEL >= 130)
                                    {
                                        num = 3;
                                        goto case 3;
                                    }

                                    goto IL_021b;
                                case 3:
                                    Vat_Pham_Luc_Cong_Kich += 22;
                                    Vat_Pham_Luc_Cong_KichMAX += 22;
                                    Vat_Pham_Luc_Cong_KichNew += 22;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 22;
                                    num = 46;
                                    goto IL_021b;
                                case 34:
                                    if (Itme.FLD_LEVEL >= 130)
                                    {
                                        num = 125;
                                        goto case 125;
                                    }

                                    goto IL_021b;
                                case 40:
                                    if (Itme.FLD_LEVEL >= 130)
                                    {
                                        num = 76;
                                        goto case 76;
                                    }

                                    goto IL_021b;
                                case 76:
                                    Vat_Pham_Luc_Cong_Kich += 45;
                                    Vat_Pham_Luc_Cong_KichMAX += 45;
                                    Vat_Pham_Luc_Cong_KichNew += 45;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 45;
                                    num = 135;
                                    goto IL_021b;
                                case 83:
                                    if (Itme.FLD_LEVEL >= 130)
                                    {
                                        num = 104;
                                        goto case 104;
                                    }

                                    goto IL_021b;
                                case 84:
                                    Vat_Pham_Luc_Cong_Kich += 60;
                                    Vat_Pham_Luc_Cong_KichMAX += 60;
                                    Vat_Pham_Luc_Cong_KichNew += 60;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 60;
                                    num = 26;
                                    goto IL_021b;
                                case 86:
                                    if (Itme.FLD_LEVEL >= 130)
                                    {
                                        num = 72;
                                        goto case 72;
                                    }

                                    goto IL_021b;
                                case 72:
                                    Vat_Pham_Luc_Cong_Kich += 55;
                                    Vat_Pham_Luc_Cong_KichMAX += 55;
                                    Vat_Pham_Luc_Cong_KichNew += 55;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 55;
                                    num = 57;
                                    goto IL_021b;
                                case 94:
                                    if (Itme.FLD_LEVEL >= 130)
                                    {
                                        num = 12;
                                        goto case 12;
                                    }

                                    goto IL_021b;
                                case 12:
                                    Vat_Pham_Luc_Cong_Kich += 35;
                                    Vat_Pham_Luc_Cong_KichMAX += 35;
                                    Vat_Pham_Luc_Cong_KichNew += 35;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 35;
                                    num = 7;
                                    goto IL_021b;
                                case 103:
                                    num = 75;
                                    goto IL_021b;
                                case 104:
                                    Vat_Pham_Luc_Cong_Kich += 17;
                                    Vat_Pham_Luc_Cong_KichMAX += 17;
                                    Vat_Pham_Luc_Cong_KichNew += 17;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 17;
                                    num = 8;
                                    goto IL_021b;
                                case 125:
                                    Vat_Pham_Luc_Cong_Kich += 32;
                                    Vat_Pham_Luc_Cong_KichMAX += 32;
                                    Vat_Pham_Luc_Cong_KichNew += 32;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 32;
                                    num = 30;
                                    goto IL_021b;
                                case 132:
                                    if (Itme.FLD_LEVEL >= 130)
                                    {
                                        num = 117;
                                        goto case 117;
                                    }

                                    goto IL_021b;
                                case 117:
                                    Vat_Pham_Luc_Cong_Kich += 12;
                                    Vat_Pham_Luc_Cong_KichMAX += 12;
                                    Vat_Pham_Luc_Cong_KichNew += 12;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 12;
                                    num = 127;
                                    goto IL_021b;
                                default:
                                    Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 6;
                                    Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 6;
                                    Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 6;
                                    Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 6;
                                    num = 79;
                                    break;
                                case 15:
                                    if (Itme.FLD_LEVEL >= 130)
                                    {
                                        num = 20;
                                        goto case 20;
                                    }

                                    VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
                                    num = 78;
                                    goto IL_1097;
                                case 20:
                                    VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
                                    num = 25;
                                    goto IL_1097;
                                case 33:
                                    if (Itme.FLD_LEVEL >= 130)
                                    {
                                        num = 53;
                                        goto case 53;
                                    }

                                    VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
                                    num = 35;
                                    goto IL_1097;
                                case 36:
                                    if (Itme.FLD_LEVEL >= 130)
                                    {
                                        num = 134;
                                        goto case 134;
                                    }

                                    VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 3;
                                    num = 92;
                                    goto IL_1097;
                                case 53:
                                    VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
                                    num = 55;
                                    goto IL_1097;
                                case 133:
                                    num = 27;
                                    goto IL_1097;
                                case 134:
                                    VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang += 4;
                                    num = 48;
                                    goto IL_1097;
                                case 129:
                                    goto IL_0c36;
                                case 6:
                                    goto IL_0c7b;
                                case 39:
                                    goto IL_0c81;
                                case 68:
                                    goto IL_0c9f;
                                case 107:
                                    goto IL_0cef;
                                case 45:
                                    goto IL_0cfd;
                                case 25:
                                case 27:
                                case 35:
                                case 48:
                                case 55:
                                case 64:
                                case 78:
                                case 92:
                                case 114:
                                case 119:
                                    goto IL_1097;
                                case 10:
                                    goto IL_109d;
                                case 16:
                                    goto IL_10b8;
                                case 62:
                                    goto IL_10d1;
                                case 73:
                                case 121:
                                    goto IL_111c;
                                case 28:
                                    goto IL_1122;
                                case 71:
                                    goto IL_113b;
                                case 81:
                                    goto IL_1144;
                                case 37:
                                    goto IL_1166;
                                case 61:
                                    goto IL_117f;
                                case 29:
                                    goto IL_11a2;
                                case 80:
                                    goto IL_11be;
                                case 67:
                                    goto IL_11dc;
                                case 47:
                                    goto IL_11e2;
                                case 18:
                                    goto IL_11fc;
                                case 24:
                                    goto IL_1205;
                                case 74:
                                    goto IL_120b;
                                case 17:
                                    goto IL_124e;
                                case 42:
                                    goto IL_126c;
                                case 111:
                                    goto IL_1288;
                                case 70:
                                    goto IL_128e;
                                case 22:
                                    goto IL_12a4;
                                case 116:
                                    goto IL_12c9;
                                case 85:
                                    goto IL_12e5;
                                case 59:
                                    goto IL_133a;
                                case 106:
                                    goto IL_135c;
                                case 97:
                                    goto IL_1381;
                                case 60:
                                    goto IL_13a0;
                                case 130:
                                    goto IL_13bf;
                                case 56:
                                    goto IL_140a;
                                case 32:
                                    goto IL_141b;
                                case 109:
                                    goto IL_144d;
                                case 136:
                                    goto IL_14a4;
                                case 89:
                                    goto IL_14c3;
                                case 120:
                                    goto IL_1524;
                                case 44:
                                    goto IL_1546;
                                case 115:
                                    goto IL_1567;
                                case 128:
                                    goto IL_1599;
                                case 63:
                                    goto IL_159f;
                                case 43:
                                    goto IL_15b8;
                                case 58:
                                    goto IL_1639;
                                case 14:
                                case 19:
                                case 21:
                                case 31:
                                case 41:
                                case 51:
                                case 52:
                                case 66:
                                case 77:
                                case 82:
                                case 88:
                                case 91:
                                case 98:
                                case 100:
                                case 101:
                                case 105:
                                case 118:
                                case 123:
                                    goto IL_1665;
                                case 38:
                                    goto IL_1672;
                                case 4:
                                    goto IL_1697;
                                case 87:
                                    continue;
                                case 99:
                                    goto IL_16c1;
                                case 126:
                                    goto IL_16cb;
                                case 124:
                                    goto IL_170d;
                                case 110:
                                    goto IL_1716;
                                case 108:
                                    goto IL_171c;
                                case 90:
                                    goto IL_173b;
                                case 95:
                                    goto IL_1759;
                                case 96:
                                    goto IL_175f;
                                case 69:
                                    goto IL_177a;
                                case 112:
                                    goto end_IL_0077;
                            }

                            break;
                            IL_0c7b:
                            num = 39;
                            IL_0c81:
                            if (VatPham_ThuocTinh_Manh >= 7)
                            {
                                num = 107;
                                goto IL_0cef;
                            }

                            goto IL_00ee;
                            IL_141b:
                            VatPham_ThuocTinh_ThemVao_MucThuongTon +=
                                VatPham_ThuocTinh_Manh - 7 + (int)Math.Round(num11 / 4.0);
                            num = 91;
                            goto IL_1665;
                            IL_0cef:
                            num2 = VatPham_ThuocTinh_Manh;
                            num = 45;
                            IL_0cfd:
                            switch (num2)
                            {
                                default:
                                    num = 103;
                                    break;
                                case 7:
                                    Vat_Pham_Luc_Cong_Kich += 10;
                                    Vat_Pham_Luc_Cong_KichMAX += 10;
                                    Vat_Pham_Luc_Cong_KichNew += 10;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 10;
                                    num = 132;
                                    break;
                                case 8:
                                    Vat_Pham_Luc_Cong_Kich += 24;
                                    Vat_Pham_Luc_Cong_KichMAX += 24;
                                    Vat_Pham_Luc_Cong_KichNew += 24;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 24;
                                    num = 83;
                                    break;
                                case 9:
                                    Vat_Pham_Luc_Cong_Kich += 48;
                                    Vat_Pham_Luc_Cong_KichMAX += 48;
                                    Vat_Pham_Luc_Cong_KichNew += 48;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 48;
                                    num = 23;
                                    break;
                                case 10:
                                    Vat_Pham_Luc_Cong_Kich += 102;
                                    Vat_Pham_Luc_Cong_KichMAX += 102;
                                    Vat_Pham_Luc_Cong_KichNew += 102;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 102;
                                    num = 34;
                                    break;
                                case 11:
                                    Vat_Pham_Luc_Cong_Kich += 111;
                                    Vat_Pham_Luc_Cong_KichMAX += 111;
                                    Vat_Pham_Luc_Cong_KichNew += 111;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 111;
                                    num = 94;
                                    break;
                                case 12:
                                    Vat_Pham_Luc_Cong_Kich += 125;
                                    Vat_Pham_Luc_Cong_KichMAX += 125;
                                    Vat_Pham_Luc_Cong_KichNew += 125;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 125;
                                    num = 40;
                                    break;
                                case 13:
                                    Vat_Pham_Luc_Cong_Kich += 144;
                                    Vat_Pham_Luc_Cong_KichMAX += 144;
                                    Vat_Pham_Luc_Cong_KichNew += 144;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 144;
                                    num = 86;
                                    break;
                                case 14:
                                    Vat_Pham_Luc_Cong_Kich += 168;
                                    Vat_Pham_Luc_Cong_KichMAX += 168;
                                    Vat_Pham_Luc_Cong_KichNew += 168;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 168;
                                    num = 0;
                                    break;
                                case 15:
                                    Vat_Pham_Luc_Cong_Kich += 197;
                                    Vat_Pham_Luc_Cong_KichMAX += 197;
                                    Vat_Pham_Luc_Cong_KichNew += 197;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 197;
                                    num = 2;
                                    break;
                                case 16:
                                    Vat_Pham_Luc_Cong_Kich += 232;
                                    Vat_Pham_Luc_Cong_KichMAX += 232;
                                    Vat_Pham_Luc_Cong_KichNew += 232;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 232;
                                    num = 2;
                                    break;
                                case 17:
                                    Vat_Pham_Luc_Cong_Kich += 267;
                                    Vat_Pham_Luc_Cong_KichMAX += 267;
                                    Vat_Pham_Luc_Cong_KichNew += 267;
                                    Vat_Pham_Luc_Cong_KichMaxNew += 267;
                                    num = 2;
                                    break;
                            }

                            goto IL_037d;
                            IL_0c36:
                            Vat_Pham_Luc_Cong_Kich += 8;
                            Vat_Pham_Luc_Cong_KichMAX += 8;
                            Vat_Pham_Luc_Cong_KichNew += 8;
                            Vat_Pham_Luc_Cong_KichMaxNew += 8;
                            num = 49;
                            goto IL_021b;
                            IL_1097:
                            num = 10;
                            IL_109d:
                            if (VatPham_ThuocTinh_Manh < 7) goto IL_1716;
                            num = 62;
                            goto IL_10d1;
                            IL_10b8:
                            if (num9 < 2)
                            {
                                num = 85;
                                goto IL_12e5;
                            }

                            goto IL_1665;
                            IL_10d1:
                            dictionary = new Dictionary<int, Itimesx>();
                            dictionary.Add(0, ThuocTinh1);
                            dictionary.Add(1, ThuocTinh2);
                            dictionary.Add(2, ThuocTinh3);
                            dictionary.Add(3, ThuocTinh4);
                            num9 = 0;
                            num = 73;
                            IL_111c:
                            num = 28;
                            IL_1122:
                            if (num9 < 4)
                            {
                                num = 81;
                                goto IL_1144;
                            }

                            num = 56;
                            goto IL_140a;
                            IL_113b:
                            num = 21;
                            goto IL_1665;
                            IL_1144:
                            if (dictionary[num9].ThuocTinhLoaiHinh != 0)
                            {
                                num = 61;
                                goto IL_117f;
                            }

                            goto IL_1665;
                            IL_1166:
                            if (num9 < 2)
                            {
                                num = 42;
                                goto IL_126c;
                            }

                            goto IL_1665;
                            IL_117f:
                            num11 = dictionary[num9].ThuocTinhSoLuong;
                            num3 = dictionary[num9].ThuocTinhLoaiHinh;
                            num = 29;
                            IL_11a2:
                            if (num3 != 1)
                            {
                                num = 67;
                                goto IL_11dc;
                            }

                            num = 4;
                            goto IL_1697;
                            IL_11be:
                            if (VatPham_ThuocTinh_Manh == 7)
                            {
                                num = 18;
                                goto IL_11fc;
                            }

                            num = 22;
                            goto IL_12a4;
                            IL_11dc:
                            num = 47;
                            IL_11e2:
                            if (num3 != 3)
                            {
                                num = 24;
                                goto IL_1205;
                            }

                            num = 80;
                            goto IL_11be;
                            IL_11fc:
                            num = 37;
                            goto IL_1166;
                            IL_1205:
                            num = 74;
                            IL_120b:
                            switch (num3)
                            {
                                case 10:
                                    goto IL_1248;
                                case 8:
                                    goto IL_149b;
                                case 7:
                                    goto IL_1540;
                                case 9:
                                    goto IL_1665;
                            }

                            num = 71;
                            goto IL_113b;
                            IL_1540:
                            num = 44;
                            IL_1546:
                            if (VatPham_ThuocTinh_Manh == 7)
                            {
                                num = 128;
                                goto IL_1599;
                            }

                            num = 38;
                            goto IL_1672;
                            IL_126c:
                            VatPham_ThuocTinh_SinhMenhLuc_GiaTang++;
                            num = 41;
                            goto IL_1665;
                            IL_1599:
                            num = 63;
                            IL_159f:
                            if (num9 < 2)
                            {
                                num = 58;
                                goto IL_1639;
                            }

                            goto IL_1665;
                            IL_12a4:
                            if (VatPham_ThuocTinh_Manh <= 12)
                            {
                                num = 60;
                                goto IL_13a0;
                            }

                            num = 109;
                            goto IL_144d;
                            IL_1639:
                            VatPham_ThuocTinh_VoCong_LucCongKich++;
                            VatPham_ThuocTinh_VoCong_LucCongKichNew++;
                            num = 101;
                            goto IL_1665;
                            IL_12e5:
                            Vat_Pham_Luc_Cong_Kich++;
                            Vat_Pham_Luc_Cong_KichMAX++;
                            Vat_Pham_Luc_Cong_KichNew++;
                            Vat_Pham_Luc_Cong_KichMaxNew++;
                            num = 31;
                            goto IL_1665;
                            IL_1672:
                            if (VatPham_ThuocTinh_Manh <= 12)
                            {
                                num = 102;
                                goto IL_00b9;
                            }

                            num = 50;
                            goto IL_0240;
                            IL_135c:
                            if (VatPham_ThuocTinh_Manh <= 12)
                            {
                                num = 89;
                                goto IL_14c3;
                            }

                            num = 13;
                            goto IL_017b;
                            IL_00b9:
                            VatPham_ThuocTinh_VoCong_LucCongKich += VatPham_ThuocTinh_Manh - 7;
                            VatPham_ThuocTinh_VoCong_LucCongKichNew += VatPham_ThuocTinh_Manh - 7;
                            num = 52;
                            goto IL_1665;
                            IL_13a0:
                            VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh - 7;
                            num = 51;
                            goto IL_1665;
                            IL_0240:
                            if (VatPham_ThuocTinh_Manh <= 14)
                            {
                                num = 68;
                                goto IL_0c9f;
                            }

                            var num5 = Math.Round(num11 / 4.0);
                            VatPham_ThuocTinh_VoCong_LucCongKich += VatPham_ThuocTinh_Manh - 8 + (int)num5;
                            VatPham_ThuocTinh_VoCong_LucCongKichNew += VatPham_ThuocTinh_Manh - 8 + (int)num5;
                            num = 77;
                            goto IL_1665;
                            IL_140a:
                            dictionary.Clear();
                            num = 110;
                            goto IL_1716;
                            IL_0c9f:
                            var num6 = Math.Round(num11 / 4.0);
                            VatPham_ThuocTinh_VoCong_LucCongKich += VatPham_ThuocTinh_Manh - 7 + (int)num6;
                            VatPham_ThuocTinh_VoCong_LucCongKichNew += VatPham_ThuocTinh_Manh - 7 + (int)num6;
                            num = 88;
                            goto IL_1665;
                            IL_144d:
                            if (VatPham_ThuocTinh_Manh <= 14)
                            {
                                num = 115;
                                goto IL_1567;
                            }

                            VatPham_ThuocTinh_SinhMenhLuc_GiaTang +=
                                VatPham_ThuocTinh_Manh - 8 + (int)Math.Round(num11 / 4.0);
                            num = 66;
                            goto IL_1665;
                            IL_149b:
                            num = 136;
                            IL_14a4:
                            if (VatPham_ThuocTinh_Manh >= 13)
                            {
                                num = 120;
                                goto IL_1524;
                            }

                            goto IL_1665;
                            IL_14c3:
                            Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh - 7;
                            Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh - 7;
                            Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh - 7;
                            Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh - 7;
                            num = 118;
                            goto IL_1665;
                            IL_1524:
                            VatPham_ThuocTinh_ToanBo_KhiCong_DangCap_GiaTang++;
                            num = 123;
                            goto IL_1665;
                            IL_1248:
                            num = 17;
                            IL_124e:
                            if (VatPham_ThuocTinh_Manh == 7)
                            {
                                num = 111;
                                goto IL_1288;
                            }

                            num = 59;
                            goto IL_133a;
                            IL_1567:
                            VatPham_ThuocTinh_SinhMenhLuc_GiaTang +=
                                VatPham_ThuocTinh_Manh - 7 + (int)Math.Round(num11 / 4.0);
                            num = 100;
                            goto IL_1665;
                            IL_1288:
                            num = 70;
                            IL_128e:
                            if (num9 < 2)
                            {
                                num = 116;
                                goto IL_12c9;
                            }

                            goto IL_1665;
                            IL_15b8:
                            var num7 = Math.Round(num11 / 4.0);
                            Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh - 7 + (int)num7;
                            Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh - 7 + (int)num7;
                            Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh - 7 + (int)num7;
                            Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh - 7 + (int)num7;
                            num = 19;
                            goto IL_1665;
                            IL_12c9:
                            VatPham_ThuocTinh_ThemVao_MucThuongTon++;
                            num = 14;
                            IL_1665:
                            num9++;
                            num = 121;
                            goto IL_111c;
                            IL_133a:
                            if (VatPham_ThuocTinh_Manh <= 12)
                            {
                                num = 97;
                                goto IL_1381;
                            }

                            num = 130;
                            goto IL_13bf;
                            IL_1697:
                            if (VatPham_ThuocTinh_Manh == 7)
                            {
                                num = 65;
                                goto IL_02d0;
                            }

                            num = 106;
                            goto IL_135c;
                        }

                        break;
                    }

                    continue;
                }

                goto IL_1716;
                IL_16cb:
                Vat_Pham_Luc_Cong_Kich += 2;
                Vat_Pham_Luc_Cong_KichMAX += 2;
                Vat_Pham_Luc_Cong_KichNew += 2;
                Vat_Pham_Luc_Cong_KichMaxNew += 2;
                num = 124;
                IL_170d:
                num12 = 8;
                num = 90;
                goto IL_173b;
                IL_1716:
                num = 108;
                IL_171c:
                if (Itme.FLD_LEVEL >= 130)
                {
                    num = 95;
                    goto IL_1759;
                }

                goto IL_170d;
                IL_173b:
                if (FLD_FJ_LowSoul <= 0) break;
                num = 69;
                goto IL_177a;
                IL_1759:
                num = 96;
                IL_175f:
                if (VatPham_ThuocTinh_Manh == 5)
                {
                    num = 126;
                    goto IL_16cb;
                }

                goto IL_170d;
                IL_177a:
                var num8 = FLD_FJ_LowSoul * num12;
                Vat_Pham_Luc_Cong_Kich += num8;
                Vat_Pham_Luc_Cong_KichMAX += num8;
                Vat_Pham_Luc_Cong_KichNew += num8;
                Vat_Pham_Luc_Cong_KichMaxNew += num8;
                num = 112;
                break;
                continue;
                end_IL_0077:
                break;
            }

            num = 11;
        }
        catch
        {
        }
    }

    private void TinhToanYPhucThuocTinh(ItmeClass Itme)
    {
        try
        {
            var num = 0;
            switch (VatPham_ThuocTinh_Manh)
            {
                case 1:
                    if (Itme.FLD_LEVEL < 60)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 3;
                        Vat_Pham_Luc_Phong_NguNew += 3;
                        break;
                    }

                    Vat_Pham_Luc_Phong_Ngu += 4;
                    Vat_Pham_Luc_Phong_NguNew += 4;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 2;
                        Vat_Pham_Luc_Phong_NguNew += 2;
                    }

                    break;
                case 2:
                    if (Itme.FLD_LEVEL < 60)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 9;
                        Vat_Pham_Luc_Phong_NguNew += 9;
                        break;
                    }

                    Vat_Pham_Luc_Phong_Ngu += 12;
                    Vat_Pham_Luc_Phong_NguNew += 12;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 4;
                        Vat_Pham_Luc_Phong_NguNew += 4;
                    }

                    break;
                case 3:
                    if (Itme.FLD_LEVEL < 60)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 18;
                        Vat_Pham_Luc_Phong_NguNew += 18;
                        break;
                    }

                    Vat_Pham_Luc_Phong_Ngu += 24;
                    Vat_Pham_Luc_Phong_NguNew += 24;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 6;
                        Vat_Pham_Luc_Phong_NguNew += 6;
                    }

                    break;
                case 4:
                    if (Itme.FLD_LEVEL < 60)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 30;
                        Vat_Pham_Luc_Phong_NguNew += 30;
                        break;
                    }

                    Vat_Pham_Luc_Phong_Ngu += 40;
                    Vat_Pham_Luc_Phong_NguNew += 40;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 8;
                        Vat_Pham_Luc_Phong_NguNew += 8;
                    }

                    break;
                case 5:
                    if (Itme.FLD_LEVEL < 60)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 45;
                        Vat_Pham_Luc_Phong_NguNew += 45;
                        break;
                    }

                    Vat_Pham_Luc_Phong_Ngu += 60;
                    Vat_Pham_Luc_Phong_NguNew += 60;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 10;
                        Vat_Pham_Luc_Phong_NguNew += 10;
                    }

                    break;
                case 6:
                    if (Itme.FLD_LEVEL < 60)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 63;
                        Vat_Pham_Luc_Phong_NguNew += 63;
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 5) * 5;
                        break;
                    }

                    Vat_Pham_Luc_Phong_Ngu += 84;
                    Vat_Pham_Luc_Phong_NguNew += 84;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 14;
                        Vat_Pham_Luc_Phong_NguNew += 14;
                    }

                    VatPham_ThuocTinh_GiamXuong_MucThuongTon += (VatPham_ThuocTinh_Manh - 5) * 5;
                    break;
                case 7:
                    if (Itme.FLD_LEVEL < 60)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 84;
                        Vat_Pham_Luc_Phong_NguNew += 84;
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 5) * 5;
                        break;
                    }

                    Vat_Pham_Luc_Phong_Ngu += 112;
                    Vat_Pham_Luc_Phong_NguNew += 112;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 18;
                        Vat_Pham_Luc_Phong_NguNew += 18;
                        if (Itme.FLD_LEVEL >= 150)
                            VatPham_ThemVao_PVE_Defense += 98;
                        else if (Itme.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 84;
                    }
                    else if (Itme.FLD_LEVEL >= 150)
                    {
                        VatPham_ThemVao_PVE_Defense += 84;
                    }
                    else if (Itme.FLD_LEVEL >= 140)
                    {
                        VatPham_ThemVao_PVE_Defense += 70;
                    }

                    VatPham_ThuocTinh_GiamXuong_MucThuongTon += (VatPham_ThuocTinh_Manh - 5) * 5;
                    break;
                case 8:
                    if (Itme.FLD_LEVEL < 60)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 108;
                        Vat_Pham_Luc_Phong_NguNew += 108;
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 5) * 5;
                        break;
                    }

                    Vat_Pham_Luc_Phong_Ngu += 144;
                    Vat_Pham_Luc_Phong_NguNew += 144;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 22;
                        Vat_Pham_Luc_Phong_NguNew += 22;
                        if (Itme.FLD_LEVEL >= 150)
                            VatPham_ThemVao_PVE_Defense += 98;
                        else if (Itme.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 84;
                    }
                    else if (Itme.FLD_LEVEL >= 150)
                    {
                        VatPham_ThemVao_PVE_Defense += 84;
                    }
                    else if (Itme.FLD_LEVEL >= 140)
                    {
                        VatPham_ThemVao_PVE_Defense += 70;
                    }

                    VatPham_ThuocTinh_GiamXuong_MucThuongTon += (VatPham_ThuocTinh_Manh - 5) * 5;
                    break;
                case 9:
                    if (Itme.FLD_LEVEL < 60)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 135;
                        Vat_Pham_Luc_Phong_NguNew += 135;
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 5) * 5;
                        break;
                    }

                    Vat_Pham_Luc_Phong_Ngu += 180;
                    Vat_Pham_Luc_Phong_NguNew += 180;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 26;
                        Vat_Pham_Luc_Phong_NguNew += 26;
                        if (Itme.FLD_LEVEL >= 150)
                            VatPham_ThemVao_PVE_Defense += 98;
                        else if (Itme.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 84;
                    }
                    else if (Itme.FLD_LEVEL >= 150)
                    {
                        VatPham_ThemVao_PVE_Defense += 84;
                    }
                    else if (Itme.FLD_LEVEL >= 140)
                    {
                        VatPham_ThemVao_PVE_Defense += 70;
                    }

                    VatPham_ThuocTinh_GiamXuong_MucThuongTon += 5 + (VatPham_ThuocTinh_Manh - 5) * 5;
                    if (Itme.FLD_Chan == 1)
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 100;
                    else
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 80;
                    break;
                case 10:
                    if (Itme.FLD_LEVEL < 60)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 165;
                        Vat_Pham_Luc_Phong_NguNew += 165;
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 5 + (VatPham_ThuocTinh_Manh - 5) * 5;
                        break;
                    }

                    Vat_Pham_Luc_Phong_Ngu += 230;
                    Vat_Pham_Luc_Phong_NguNew += 230;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 31;
                        Vat_Pham_Luc_Phong_NguNew += 31;
                        if (Itme.FLD_LEVEL >= 150)
                            VatPham_ThemVao_PVE_Defense += 183;
                        else if (Itme.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 163;
                    }
                    else if (Itme.FLD_LEVEL >= 150)
                    {
                        VatPham_ThemVao_PVE_Defense += 163;
                    }
                    else if (Itme.FLD_LEVEL >= 140)
                    {
                        VatPham_ThemVao_PVE_Defense += 143;
                    }

                    VatPham_ThuocTinh_GiamXuong_MucThuongTon += 5 + (VatPham_ThuocTinh_Manh - 5) * 5;
                    if (Itme.FLD_Chan == 1)
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 200;
                    else
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 160;
                    break;
                case 11:
                    Vat_Pham_Luc_Phong_Ngu += 265;
                    Vat_Pham_Luc_Phong_NguNew += 265;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 34;
                        Vat_Pham_Luc_Phong_NguNew += 34;
                        if (Itme.FLD_LEVEL >= 150)
                            VatPham_ThemVao_PVE_Defense += 220;
                        else if (Itme.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 198;
                    }
                    else if (Itme.FLD_LEVEL >= 150)
                    {
                        VatPham_ThemVao_PVE_Defense += 198;
                    }
                    else if (Itme.FLD_LEVEL >= 140)
                    {
                        VatPham_ThemVao_PVE_Defense += 176;
                    }

                    VatPham_ThuocTinh_GiamXuong_MucThuongTon += 10 + (VatPham_ThuocTinh_Manh - 5) * 5;
                    if (Itme.FLD_Chan == 1)
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 200;
                    else
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 160;
                    break;
                case 12:
                    Vat_Pham_Luc_Phong_Ngu += 315;
                    Vat_Pham_Luc_Phong_NguNew += 315;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 36;
                        Vat_Pham_Luc_Phong_NguNew += 36;
                        if (Itme.FLD_LEVEL >= 150)
                            VatPham_ThemVao_PVE_Defense += 257;
                        else if (Itme.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 233;
                    }
                    else if (Itme.FLD_LEVEL >= 150)
                    {
                        VatPham_ThemVao_PVE_Defense += 233;
                    }
                    else if (Itme.FLD_LEVEL >= 140)
                    {
                        VatPham_ThemVao_PVE_Defense += 209;
                    }

                    VatPham_ThuocTinh_GiamXuong_MucThuongTon += 15 + (VatPham_ThuocTinh_Manh - 5) * 5;
                    if (Itme.FLD_Chan == 1)
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 200;
                    else
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 160;
                    break;
                case 13:
                    Vat_Pham_Luc_Phong_Ngu += 365;
                    Vat_Pham_Luc_Phong_NguNew += 365;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 44;
                        Vat_Pham_Luc_Phong_NguNew += 44;
                        if (Itme.FLD_LEVEL >= 150)
                            VatPham_ThemVao_PVE_Defense += 294;
                        else if (Itme.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 268;
                    }
                    else if (Itme.FLD_LEVEL >= 150)
                    {
                        VatPham_ThemVao_PVE_Defense += 268;
                    }
                    else if (Itme.FLD_LEVEL >= 140)
                    {
                        VatPham_ThemVao_PVE_Defense += 242;
                    }

                    VatPham_ThuocTinh_GiamXuong_MucThuongTon += 20 + (VatPham_ThuocTinh_Manh - 5) * 5;
                    if (Itme.FLD_Chan == 1)
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 460;
                    else
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 420;
                    break;
                case 14:
                    Vat_Pham_Luc_Phong_Ngu += 415;
                    Vat_Pham_Luc_Phong_NguNew += 415;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 52;
                        Vat_Pham_Luc_Phong_NguNew += 52;
                        if (Itme.FLD_LEVEL >= 150)
                            VatPham_ThemVao_PVE_Defense += 331;
                        else if (Itme.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 303;
                    }
                    else if (Itme.FLD_LEVEL >= 150)
                    {
                        VatPham_ThemVao_PVE_Defense += 303;
                    }
                    else if (Itme.FLD_LEVEL >= 140)
                    {
                        VatPham_ThemVao_PVE_Defense += 275;
                    }

                    VatPham_ThuocTinh_GiamXuong_MucThuongTon += 25 + (VatPham_ThuocTinh_Manh - 5) * 5;
                    if (Itme.FLD_Chan == 1)
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 560;
                    else
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 520;
                    break;
                case 15:
                    Vat_Pham_Luc_Phong_Ngu += 465;
                    Vat_Pham_Luc_Phong_NguNew += 465;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 60;
                        Vat_Pham_Luc_Phong_NguNew += 60;
                        if (Itme.FLD_LEVEL >= 150)
                            VatPham_ThemVao_PVE_Defense += 368;
                        else if (Itme.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 338;
                    }
                    else if (Itme.FLD_LEVEL >= 150)
                    {
                        VatPham_ThemVao_PVE_Defense += 338;
                    }
                    else if (Itme.FLD_LEVEL >= 140)
                    {
                        VatPham_ThemVao_PVE_Defense += 308;
                    }

                    VatPham_ThuocTinh_GiamXuong_MucThuongTon += 30 + (VatPham_ThuocTinh_Manh - 5) * 5;
                    if (Itme.FLD_Chan == 1)
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 710;
                    else
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 670;
                    break;
                case 16:
                    Vat_Pham_Luc_Phong_Ngu += 515;
                    Vat_Pham_Luc_Phong_NguNew += 515;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 68;
                        Vat_Pham_Luc_Phong_NguNew += 68;
                        if (Itme.FLD_LEVEL >= 150)
                            VatPham_ThemVao_PVE_Defense += 368;
                        else if (Itme.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 338;
                    }
                    else if (Itme.FLD_LEVEL >= 150)
                    {
                        VatPham_ThemVao_PVE_Defense += 338;
                    }
                    else if (Itme.FLD_LEVEL >= 140)
                    {
                        VatPham_ThemVao_PVE_Defense += 308;
                    }

                    VatPham_ThuocTinh_GiamXuong_MucThuongTon += 35 + (VatPham_ThuocTinh_Manh - 5) * 5;
                    if (Itme.FLD_Chan == 1)
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 710;
                    else
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 670;
                    break;
                case 17:
                    Vat_Pham_Luc_Phong_Ngu += 565;
                    Vat_Pham_Luc_Phong_NguNew += 565;
                    if (Itme.FLD_Chan == 1)
                    {
                        Vat_Pham_Luc_Phong_Ngu += 76;
                        Vat_Pham_Luc_Phong_NguNew += 76;
                        if (Itme.FLD_LEVEL >= 150)
                            VatPham_ThemVao_PVE_Defense += 368;
                        else if (Itme.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 338;
                    }
                    else if (Itme.FLD_LEVEL >= 150)
                    {
                        VatPham_ThemVao_PVE_Defense += 338;
                    }
                    else if (Itme.FLD_LEVEL >= 140)
                    {
                        VatPham_ThemVao_PVE_Defense += 308;
                    }

                    VatPham_ThuocTinh_GiamXuong_MucThuongTon += 35 + (VatPham_ThuocTinh_Manh - 5) * 5;
                    if (Itme.FLD_Chan == 1)
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 710;
                    else
                        VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 670;
                    break;
            }

            num = 5;
            if (FLD_FJ_LowSoul > 0)
            {
                var num2 = FLD_FJ_LowSoul * num;
                Vat_Pham_Luc_Phong_Ngu += num2;
                Vat_Pham_Luc_Phong_NguNew += num2;
            }
        }
        catch
        {
        }
    }

    private void DatDuocCuongHoa(string ysqh, int CuongHoaVK, int CuongHoaTB)
    {
        try
        {
            if (!World.Itme.TryGetValue(Buffer.ToInt32(VatPham_ID, 0), out var value)) return;
            switch (ysqh.Length)
            {
                case 8:
                    VatPham_ThuocTinh_Giai_Doan_Loai_Hinh = 0;
                    VatPham_ThuocTinh_So_Giai_Doan = 0;
                    VatPham_ThuocTinh_Manh_Loai_Hinh = int.Parse(ysqh.Substring(ysqh.Length - 8, 1));
                    VatPham_ThuocTinh_Manh = int.Parse(ysqh.Substring(ysqh.Length - 2, 2));
                    if (FLD_RESIDE2 == 4)
                        VatPham_ThuocTinh_Manh += CuongHoaVK;
                    else if (FLD_RESIDE2 == 1 || FLD_RESIDE2 == 2 || FLD_RESIDE2 == 5)
                        VatPham_ThuocTinh_Manh += CuongHoaTB;
                    switch (VatPham_ThuocTinh_Manh_Loai_Hinh)
                    {
                        case 1:
                            if (FLD_RESIDE2 == 10)
                            {
                                if (value.FLD_LEVEL >= 160)
                                {
                                    Vat_Pham_Luc_Cong_Kich += Get_ChiSoTrangSuc(19, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichMAX += Get_ChiSoTrangSuc(19, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichNew += Get_ChiSoTrangSuc(19, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichMaxNew += Get_ChiSoTrangSuc(19, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(19, VatPham_ThuocTinh_Manh);
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(160, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 150)
                                {
                                    Vat_Pham_Luc_Cong_Kich += Get_ChiSoTrangSuc(16, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichMAX += Get_ChiSoTrangSuc(16, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichNew += Get_ChiSoTrangSuc(16, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichMaxNew += Get_ChiSoTrangSuc(16, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(16, VatPham_ThuocTinh_Manh);
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(150, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 140)
                                {
                                    Vat_Pham_Luc_Cong_Kich += Get_ChiSoTrangSuc(13, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichMAX += Get_ChiSoTrangSuc(13, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichNew += Get_ChiSoTrangSuc(13, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichMaxNew += Get_ChiSoTrangSuc(13, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(13, VatPham_ThuocTinh_Manh);
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(140, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 130)
                                {
                                    Vat_Pham_Luc_Cong_Kich += Get_ChiSoTrangSuc(10, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichMAX += Get_ChiSoTrangSuc(10, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichNew += Get_ChiSoTrangSuc(10, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichMaxNew += Get_ChiSoTrangSuc(10, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(10, VatPham_ThuocTinh_Manh);
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(130, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 120)
                                {
                                    Vat_Pham_Luc_Cong_Kich += Get_ChiSoTrangSuc(8, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichMAX += Get_ChiSoTrangSuc(8, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichNew += Get_ChiSoTrangSuc(8, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichMaxNew += Get_ChiSoTrangSuc(8, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(8, VatPham_ThuocTinh_Manh);
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(120, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 110)
                                {
                                    Vat_Pham_Luc_Cong_Kich += Get_ChiSoTrangSuc_Duoi120(5, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichMAX += Get_ChiSoTrangSuc_Duoi120(5, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichNew += Get_ChiSoTrangSuc_Duoi120(5, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichMaxNew +=
                                        Get_ChiSoTrangSuc_Duoi120(5, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(6, VatPham_ThuocTinh_Manh);
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(115, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 100)
                                {
                                    Vat_Pham_Luc_Cong_Kich += Get_ChiSoTrangSuc_Duoi120(4, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichMAX += Get_ChiSoTrangSuc_Duoi120(4, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichNew += Get_ChiSoTrangSuc_Duoi120(4, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Luc_Cong_KichMaxNew +=
                                        Get_ChiSoTrangSuc_Duoi120(4, VatPham_ThuocTinh_Manh);
                                    Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(5, VatPham_ThuocTinh_Manh);
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(100, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 80)
                                {
                                    if (VatPham_ThuocTinh_Manh < 4)
                                    {
                                        Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 3;
                                        Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 3;
                                        Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 3;
                                        Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 3;
                                    }
                                    else
                                    {
                                        Vat_Pham_Luc_Cong_Kich += 9 + (VatPham_ThuocTinh_Manh - 3) * 5;
                                        Vat_Pham_Luc_Cong_KichMAX += 9 + (VatPham_ThuocTinh_Manh - 3) * 5;
                                        Vat_Pham_Luc_Cong_KichNew += 9 + (VatPham_ThuocTinh_Manh - 3) * 5;
                                        Vat_Pham_Luc_Cong_KichMaxNew += 9 + (VatPham_ThuocTinh_Manh - 3) * 5;
                                    }

                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(80, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 60)
                                {
                                    if (VatPham_ThuocTinh_Manh < 4)
                                    {
                                        Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh * 2;
                                        Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh * 2;
                                        Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh * 2;
                                        Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh * 2;
                                    }
                                    else
                                    {
                                        Vat_Pham_Luc_Cong_Kich += 6 + (VatPham_ThuocTinh_Manh - 3) * 3;
                                        Vat_Pham_Luc_Cong_KichMAX += 6 + (VatPham_ThuocTinh_Manh - 3) * 3;
                                        Vat_Pham_Luc_Cong_KichNew += 6 + (VatPham_ThuocTinh_Manh - 3) * 3;
                                        Vat_Pham_Luc_Cong_KichMaxNew += 6 + (VatPham_ThuocTinh_Manh - 3) * 3;
                                    }

                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(60, VatPham_ThuocTinh_Manh);
                                }
                            }
                            else
                            {
                                TinhToanVuKhiThuocTinh(value);
                            }

                            break;
                        case 2:
                            switch (FLD_RESIDE2)
                            {
                                case 3:
                                case 4:
                                    break;
                                case 8:
                                case 9:
                                case 10:
                                case 11:
                                case 12:
                                case 13:
                                    break;
                                case 1:
                                    TinhToanYPhucThuocTinh(value);
                                    DatDuocVatPhamShield(value, VatPham_ThuocTinh_Manh);
                                    break;
                                case 2:
                                case 5:
                                    switch (VatPham_ThuocTinh_Manh)
                                    {
                                        case 1:
                                            Vat_Pham_Luc_Phong_Ngu += 3;
                                            Vat_Pham_Luc_Phong_NguNew += 3;
                                            break;
                                        case 2:
                                            Vat_Pham_Luc_Phong_Ngu += 6;
                                            Vat_Pham_Luc_Phong_NguNew += 6;
                                            break;
                                        case 3:
                                            Vat_Pham_Luc_Phong_Ngu += 9;
                                            Vat_Pham_Luc_Phong_NguNew += 9;
                                            break;
                                        case 4:
                                            Vat_Pham_Luc_Phong_Ngu += 12;
                                            Vat_Pham_Luc_Phong_NguNew += 12;
                                            break;
                                        case 5:
                                            Vat_Pham_Luc_Phong_Ngu += 15;
                                            Vat_Pham_Luc_Phong_NguNew += 15;
                                            break;
                                        case 6:
                                            if (value.FLD_LEVEL < 60)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 18;
                                                Vat_Pham_Luc_Phong_NguNew += 18;
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang +=
                                                    (VatPham_ThuocTinh_Manh - 5) * 5;
                                            }
                                            else
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 19;
                                                Vat_Pham_Luc_Phong_NguNew += 19;
                                                VatPham_ThuocTinh_GiamXuong_MucThuongTon +=
                                                    (VatPham_ThuocTinh_Manh - 5) * 5;
                                            }

                                            break;
                                        case 7:
                                            if (value.FLD_LEVEL < 60)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 21;
                                                Vat_Pham_Luc_Phong_NguNew += 21;
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang +=
                                                    (VatPham_ThuocTinh_Manh - 5) * 5;
                                                break;
                                            }

                                            Vat_Pham_Luc_Phong_Ngu += 23;
                                            Vat_Pham_Luc_Phong_NguNew += 23;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 2;
                                                Vat_Pham_Luc_Phong_NguNew += 2;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 63;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 49;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 49;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 35;
                                            }

                                            VatPham_ThuocTinh_GiamXuong_MucThuongTon +=
                                                (VatPham_ThuocTinh_Manh - 5) * 5;
                                            break;
                                        case 8:
                                            if (value.FLD_LEVEL < 60)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 27;
                                                Vat_Pham_Luc_Phong_NguNew += 27;
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang +=
                                                    (VatPham_ThuocTinh_Manh - 5) * 5;
                                                break;
                                            }

                                            Vat_Pham_Luc_Phong_Ngu += 29;
                                            Vat_Pham_Luc_Phong_NguNew += 29;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 5;
                                                Vat_Pham_Luc_Phong_NguNew += 5;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 63;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 49;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 49;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 35;
                                            }

                                            VatPham_ThuocTinh_GiamXuong_MucThuongTon +=
                                                (VatPham_ThuocTinh_Manh - 5) * 5;
                                            break;
                                        case 9:
                                            if (value.FLD_LEVEL < 60)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 31;
                                                Vat_Pham_Luc_Phong_NguNew += 31;
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang +=
                                                    (VatPham_ThuocTinh_Manh - 5) * 5;
                                                break;
                                            }

                                            Vat_Pham_Luc_Phong_Ngu += 38;
                                            Vat_Pham_Luc_Phong_NguNew += 38;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 8;
                                                Vat_Pham_Luc_Phong_NguNew += 8;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 63;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 49;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 49;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 35;
                                            }

                                            VatPham_ThuocTinh_GiamXuong_MucThuongTon +=
                                                (VatPham_ThuocTinh_Manh - 5) * 5;
                                            if (value.FLD_Chan == 0)
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 80;
                                            else
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 100;
                                            break;
                                        case 10:
                                            if (value.FLD_LEVEL < 60)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 37;
                                                Vat_Pham_Luc_Phong_NguNew += 37;
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang +=
                                                    5 + (VatPham_ThuocTinh_Manh - 5) * 5;
                                                break;
                                            }

                                            Vat_Pham_Luc_Phong_Ngu += 53;
                                            Vat_Pham_Luc_Phong_NguNew += 53;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 133;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 113;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 113;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 93;
                                            }

                                            VatPham_ThuocTinh_GiamXuong_MucThuongTon +=
                                                5 + (VatPham_ThuocTinh_Manh - 5) * 5;
                                            if (value.FLD_Chan == 0)
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 160;
                                            else
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 200;
                                            break;
                                        case 11:
                                            Vat_Pham_Luc_Phong_Ngu += 68;
                                            Vat_Pham_Luc_Phong_NguNew += 68;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 165;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 143;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 143;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 121;
                                            }

                                            VatPham_ThuocTinh_GiamXuong_MucThuongTon +=
                                                10 + (VatPham_ThuocTinh_Manh - 5) * 5;
                                            if (value.FLD_Chan == 0)
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 160;
                                            else
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 200;
                                            break;
                                        case 12:
                                            Vat_Pham_Luc_Phong_Ngu += 83;
                                            Vat_Pham_Luc_Phong_NguNew += 83;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 192;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 173;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 173;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 149;
                                            }

                                            VatPham_ThuocTinh_GiamXuong_MucThuongTon +=
                                                15 + (VatPham_ThuocTinh_Manh - 5) * 5;
                                            if (value.FLD_Chan == 0)
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 160;
                                            else
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 200;
                                            break;
                                        case 13:
                                            Vat_Pham_Luc_Phong_Ngu += 98;
                                            Vat_Pham_Luc_Phong_NguNew += 98;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 229;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 203;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 203;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 177;
                                            }

                                            VatPham_ThuocTinh_GiamXuong_MucThuongTon +=
                                                20 + (VatPham_ThuocTinh_Manh - 5) * 5;
                                            if (value.FLD_Chan == 0)
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 420;
                                            else
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 460;
                                            break;
                                        case 14:
                                            Vat_Pham_Luc_Phong_Ngu += 118;
                                            Vat_Pham_Luc_Phong_NguNew += 118;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 261;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 233;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 233;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 205;
                                            }

                                            VatPham_ThuocTinh_GiamXuong_MucThuongTon +=
                                                25 + (VatPham_ThuocTinh_Manh - 5) * 5;
                                            if (value.FLD_Chan == 0)
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 520;
                                            else
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 560;
                                            break;
                                        case 15:
                                            Vat_Pham_Luc_Phong_Ngu += 138;
                                            Vat_Pham_Luc_Phong_NguNew += 138;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 293;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 263;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 263;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 233;
                                            }

                                            VatPham_ThuocTinh_GiamXuong_MucThuongTon +=
                                                30 + (VatPham_ThuocTinh_Manh - 5) * 5;
                                            if (value.FLD_Chan == 0)
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 670;
                                            else
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 710;
                                            break;
                                        case 16:
                                            Vat_Pham_Luc_Phong_Ngu += 158;
                                            Vat_Pham_Luc_Phong_NguNew += 158;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 293;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 263;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 263;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 233;
                                            }

                                            VatPham_ThuocTinh_GiamXuong_MucThuongTon +=
                                                30 + (VatPham_ThuocTinh_Manh - 5) * 5;
                                            if (value.FLD_Chan == 0)
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 670;
                                            else
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 710;
                                            break;
                                        case 17:
                                            Vat_Pham_Luc_Phong_Ngu += 178;
                                            Vat_Pham_Luc_Phong_NguNew += 178;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 293;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 263;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 263;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 233;
                                            }

                                            VatPham_ThuocTinh_GiamXuong_MucThuongTon +=
                                                30 + (VatPham_ThuocTinh_Manh - 5) * 5;
                                            if (value.FLD_Chan == 0)
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 670;
                                            else
                                                VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 710;
                                            break;
                                    }

                                    DatDuocVatPhamShield(value, VatPham_ThuocTinh_Manh);
                                    break;
                                case 6:
                                    Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh * 3;
                                    Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh * 3;
                                    switch (VatPham_ThuocTinh_Manh)
                                    {
                                        case 6:
                                            Vat_Pham_Luc_Phong_Ngu++;
                                            Vat_Pham_Luc_Phong_NguNew++;
                                            if (value.FLD_LEVEL >= 65)
                                            {
                                                if (value.FLD_Chan == 0)
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 40;
                                                else
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 45;
                                            }

                                            break;
                                        case 7:
                                            Vat_Pham_Luc_Phong_Ngu += 2;
                                            Vat_Pham_Luc_Phong_NguNew += 2;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 2;
                                                Vat_Pham_Luc_Phong_NguNew += 2;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 63;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 49;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 49;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 35;
                                            }

                                            if (value.FLD_LEVEL >= 65)
                                            {
                                                if (value.FLD_Chan == 0)
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 80;
                                                else
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 90;
                                            }

                                            break;
                                        case 8:
                                            Vat_Pham_Luc_Phong_Ngu += 5;
                                            Vat_Pham_Luc_Phong_NguNew += 5;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 5;
                                                Vat_Pham_Luc_Phong_NguNew += 5;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 63;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 49;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 49;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 35;
                                            }

                                            if (value.FLD_LEVEL >= 65)
                                            {
                                                if (value.FLD_Chan == 0)
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 140;
                                                else
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 155;
                                            }

                                            break;
                                        case 9:
                                            Vat_Pham_Luc_Phong_Ngu += 11;
                                            Vat_Pham_Luc_Phong_NguNew += 11;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 8;
                                                Vat_Pham_Luc_Phong_NguNew += 8;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 63;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 49;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 49;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 35;
                                            }

                                            if (value.FLD_LEVEL >= 65)
                                            {
                                                if (value.FLD_Chan == 0)
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 200;
                                                else
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 220;
                                            }

                                            break;
                                        case 10:
                                            Vat_Pham_Luc_Phong_Ngu += 23;
                                            Vat_Pham_Luc_Phong_NguNew += 23;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 133;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 113;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 113;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 93;
                                            }

                                            if (value.FLD_LEVEL >= 65)
                                            {
                                                if (value.FLD_Chan == 0)
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 300;
                                                else
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 330;
                                            }

                                            break;
                                        case 11:
                                            Vat_Pham_Luc_Phong_Ngu += 35;
                                            Vat_Pham_Luc_Phong_NguNew += 35;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 165;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 143;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 143;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 121;
                                            }

                                            if (value.FLD_LEVEL >= 65)
                                            {
                                                if (value.FLD_Chan == 0)
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 300;
                                                else
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 330;
                                            }

                                            break;
                                        case 12:
                                            Vat_Pham_Luc_Phong_Ngu += 47;
                                            Vat_Pham_Luc_Phong_NguNew += 47;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 192;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 173;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 173;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 149;
                                            }

                                            if (value.FLD_LEVEL >= 65)
                                            {
                                                if (value.FLD_Chan == 0)
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 300;
                                                else
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 330;
                                            }

                                            break;
                                        case 13:
                                            Vat_Pham_Luc_Phong_Ngu += 59;
                                            Vat_Pham_Luc_Phong_NguNew += 59;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 229;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 203;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 203;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 177;
                                            }

                                            if (value.FLD_LEVEL >= 65)
                                            {
                                                if (value.FLD_Chan == 0)
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 560;
                                                else
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 590;
                                            }

                                            break;
                                        case 14:
                                            Vat_Pham_Luc_Phong_Ngu += 76;
                                            Vat_Pham_Luc_Phong_NguNew += 76;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 261;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 233;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 233;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 205;
                                            }

                                            if (value.FLD_LEVEL >= 65)
                                            {
                                                if (value.FLD_Chan == 0)
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 660;
                                                else
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 710;
                                            }

                                            break;
                                        case 15:
                                            Vat_Pham_Luc_Phong_Ngu += 93;
                                            Vat_Pham_Luc_Phong_NguNew += 93;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 293;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 263;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 263;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 233;
                                            }

                                            if (value.FLD_LEVEL >= 65)
                                            {
                                                if (value.FLD_Chan == 0)
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 810;
                                                else
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 950;
                                            }

                                            break;
                                        case 16:
                                            Vat_Pham_Luc_Phong_Ngu += 113;
                                            Vat_Pham_Luc_Phong_NguNew += 113;
                                            if (value.FLD_Chan == 1)
                                            {
                                                Vat_Pham_Luc_Phong_Ngu += 11;
                                                Vat_Pham_Luc_Phong_NguNew += 11;
                                                if (value.FLD_LEVEL >= 150)
                                                    VatPham_ThemVao_PVE_Defense += 293;
                                                else if (value.FLD_LEVEL >= 140) VatPham_ThemVao_PVE_Defense += 263;
                                            }
                                            else if (value.FLD_LEVEL >= 150)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 263;
                                            }
                                            else if (value.FLD_LEVEL >= 140)
                                            {
                                                VatPham_ThemVao_PVE_Defense += 233;
                                            }

                                            if (value.FLD_LEVEL >= 65)
                                            {
                                                if (value.FLD_Chan == 0)
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 810;
                                                else
                                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 950;
                                            }

                                            break;
                                    }

                                    DatDuocVatPhamShield(value, VatPham_ThuocTinh_Manh);
                                    break;
                                case 7:
                                    if (value.FLD_LEVEL >= 160)
                                    {
                                        Vat_Pham_Luc_Phong_Ngu += Get_ChiSoTrangSuc(19, VatPham_ThuocTinh_Manh);
                                        Vat_Pham_Luc_Phong_NguNew += Get_ChiSoTrangSuc(19, VatPham_ThuocTinh_Manh);
                                        Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(19, VatPham_ThuocTinh_Manh);
                                        VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                            DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(160, VatPham_ThuocTinh_Manh);
                                    }
                                    else if (value.FLD_LEVEL >= 150)
                                    {
                                        Vat_Pham_Luc_Phong_Ngu += Get_ChiSoTrangSuc(16, VatPham_ThuocTinh_Manh);
                                        Vat_Pham_Luc_Phong_NguNew += Get_ChiSoTrangSuc(16, VatPham_ThuocTinh_Manh);
                                        Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(16, VatPham_ThuocTinh_Manh);
                                        VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                            DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(150, VatPham_ThuocTinh_Manh);
                                    }
                                    else if (value.FLD_LEVEL >= 140)
                                    {
                                        Vat_Pham_Luc_Phong_Ngu += Get_ChiSoTrangSuc(13, VatPham_ThuocTinh_Manh);
                                        Vat_Pham_Luc_Phong_NguNew += Get_ChiSoTrangSuc(13, VatPham_ThuocTinh_Manh);
                                        Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(13, VatPham_ThuocTinh_Manh);
                                        VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                            DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(140, VatPham_ThuocTinh_Manh);
                                    }
                                    else if (value.FLD_LEVEL >= 130)
                                    {
                                        Vat_Pham_Luc_Phong_Ngu += Get_ChiSoTrangSuc(10, VatPham_ThuocTinh_Manh);
                                        Vat_Pham_Luc_Phong_NguNew += Get_ChiSoTrangSuc(10, VatPham_ThuocTinh_Manh);
                                        Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(10, VatPham_ThuocTinh_Manh);
                                        VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                            DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(130, VatPham_ThuocTinh_Manh);
                                    }
                                    else if (value.FLD_LEVEL >= 120)
                                    {
                                        Vat_Pham_Luc_Phong_Ngu += Get_ChiSoTrangSuc(8, VatPham_ThuocTinh_Manh);
                                        Vat_Pham_Luc_Phong_NguNew += Get_ChiSoTrangSuc(8, VatPham_ThuocTinh_Manh);
                                        Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(8, VatPham_ThuocTinh_Manh);
                                        VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                            DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(120, VatPham_ThuocTinh_Manh);
                                    }
                                    else if (value.FLD_LEVEL >= 110)
                                    {
                                        Vat_Pham_Luc_Phong_Ngu += Get_ChiSoTrangSuc_Duoi120(5, VatPham_ThuocTinh_Manh);
                                        Vat_Pham_Luc_Phong_NguNew +=
                                            Get_ChiSoTrangSuc_Duoi120(5, VatPham_ThuocTinh_Manh);
                                        Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(5, VatPham_ThuocTinh_Manh);
                                        VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                            DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(115, VatPham_ThuocTinh_Manh);
                                    }
                                    else if (value.FLD_LEVEL >= 100)
                                    {
                                        Vat_Pham_Luc_Phong_Ngu += Get_ChiSoTrangSuc_Duoi120(4, VatPham_ThuocTinh_Manh);
                                        Vat_Pham_Luc_Phong_NguNew +=
                                            Get_ChiSoTrangSuc_Duoi120(4, VatPham_ThuocTinh_Manh);
                                        Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(4, VatPham_ThuocTinh_Manh);
                                        VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                            DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(100, VatPham_ThuocTinh_Manh);
                                    }
                                    else if (value.FLD_LEVEL >= 80)
                                    {
                                        if (VatPham_ThuocTinh_Manh < 4)
                                        {
                                            Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh * 3;
                                            Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh * 3;
                                        }
                                        else
                                        {
                                            Vat_Pham_Luc_Phong_Ngu += 9 + (VatPham_ThuocTinh_Manh - 3) * 5;
                                            Vat_Pham_Luc_Phong_NguNew += 9 + (VatPham_ThuocTinh_Manh - 3) * 5;
                                        }

                                        VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                            DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(80, VatPham_ThuocTinh_Manh);
                                    }
                                    else if (value.FLD_LEVEL >= 60)
                                    {
                                        if (VatPham_ThuocTinh_Manh < 4)
                                        {
                                            Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh * 2;
                                            Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh * 2;
                                        }
                                        else
                                        {
                                            Vat_Pham_Luc_Phong_Ngu += 6 + (VatPham_ThuocTinh_Manh - 3) * 3;
                                            Vat_Pham_Luc_Phong_NguNew += 6 + (VatPham_ThuocTinh_Manh - 3) * 3;
                                        }

                                        VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                            DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(60, VatPham_ThuocTinh_Manh);
                                    }

                                    break;
                                case 14:
                                    switch (VatPham_ThuocTinh_Manh)
                                    {
                                        case 1:
                                        case 2:
                                        case 3:
                                        case 4:
                                        case 5:
                                            Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh * 3;
                                            Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh * 3;
                                            break;
                                        case 6:
                                        case 7:
                                        case 8:
                                        case 9:
                                            Vat_Pham_Luc_Phong_Ngu += 15 + (VatPham_ThuocTinh_Manh - 5) * 4;
                                            Vat_Pham_Luc_Phong_NguNew += 15 + (VatPham_ThuocTinh_Manh - 5) * 4;
                                            VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh - 25;
                                            break;
                                        case 10:
                                            Vat_Pham_Luc_Phong_Ngu += 37;
                                            Vat_Pham_Luc_Phong_NguNew += 37;
                                            VatPham_ThuocTinh_SinhMenhLuc_GiaTang += 30;
                                            break;
                                    }

                                    break;
                            }

                            break;
                        case 3:
                            if (FLD_RESIDE2 == 8)
                            {
                                if (value.FLD_LEVEL >= 160)
                                {
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 160;
                                    Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(19, VatPham_ThuocTinh_Manh);
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(160, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 150)
                                {
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 130;
                                    Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(16, VatPham_ThuocTinh_Manh);
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(150, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 140)
                                {
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 100;
                                    Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(13, VatPham_ThuocTinh_Manh);
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(140, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 130)
                                {
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 80;
                                    Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(10, VatPham_ThuocTinh_Manh);
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(130, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 120)
                                {
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 70;
                                    Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(8, VatPham_ThuocTinh_Manh);
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(120, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 115)
                                {
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 40;
                                    Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(6, VatPham_ThuocTinh_Manh);
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(115, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 100)
                                {
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 30;
                                    Vat_Pham_Gia_Tang_Da_Kich += Get_ChiSoTrangSuc(5, VatPham_ThuocTinh_Manh);
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(100, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 80)
                                {
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 15;
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(80, VatPham_ThuocTinh_Manh);
                                }
                                else if (value.FLD_LEVEL >= 60)
                                {
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh * 5;
                                    VatPham_ThuocTinh_TangKhaNangHoiPhuc_LaChan +=
                                        DatDuocTrangSucCuongHoaGiaTangRecoveryLuong(60, VatPham_ThuocTinh_Manh);
                                }
                            }

                            break;
                        case 4:
                            if (VatPham_ThuocTinh_Manh >= 100) VatPham_ThuocTinh_Manh = 100;
                            if (VatPham_ThuocTinh_Manh > 1)
                            {
                                if ((VatPham_ThuocTinh_Manh & 1) == 1)
                                {
                                    Vat_Pham_Luc_Phong_Ngu += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
                                    Vat_Pham_Luc_Phong_NguNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
                                    Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
                                    Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
                                    Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
                                    Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 1) / 2 * 10;
                                    VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += (VatPham_ThuocTinh_Manh - 1) * 2;
                                    if (VatPham_ThuocTinh_Manh > 10)
                                        VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh - 10;
                                }
                                else
                                {
                                    Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh / 2 + 5;
                                    Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh / 2 + 5;
                                    Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh / 2 + 5;
                                    Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh / 2 + 5;
                                    Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh / 2 + 5;
                                    Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh / 2 + 5;
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh / 2 * 10;
                                    VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh * 2;
                                    if (VatPham_ThuocTinh_Manh > 10)
                                        VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh - 10;
                                }
                            }
                            else
                            {
                                Vat_Pham_Luc_Phong_Ngu += 5;
                                Vat_Pham_Luc_Phong_NguNew += 5;
                            }

                            break;
                        case 5:
                        {
                            var fLD_MAGIC = FLD_MAGIC2;
                            if (VatPham_ThuocTinh_Manh_Loai_Hinh != 5) break;
                            if (VatPham_ThuocTinh_Manh >= 100) VatPham_ThuocTinh_Manh = 100;
                            if (VatPham_ThuocTinh_Manh > 1)
                            {
                                if (value.FLD_PID == **********)
                                {
                                    Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu +=
                                        (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 2 + 10;
                                    Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich +=
                                        (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 2 + 10;
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang =
                                        (int)Math.Floor(VatPham_ThuocTinh_Manh / 2.0) * 10 + 10;
                                }
                                else if (value.FLD_PID == 1000001171)
                                {
                                    Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu +=
                                        (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.785) + 9;
                                    Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich +=
                                        (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.785) + 9;
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang =
                                        (int)Math.Floor(VatPham_ThuocTinh_Manh / 2.0) * 10 + 8;
                                }
                                else if (value.FLD_PID == 1000001172)
                                {
                                    Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu +=
                                        (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.089 + 5.5);
                                    Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich +=
                                        (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.089 + 5.5);
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang =
                                        (int)Math.Floor(VatPham_ThuocTinh_Manh / 2.0) * 10 + 1;
                                }
                                else if (value.FLD_PID == 1000001173)
                                {
                                    Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu +=
                                        (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.196) + 6;
                                    Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich +=
                                        (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.196) + 6;
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang =
                                        (int)Math.Floor(VatPham_ThuocTinh_Manh / 2.0) * 10 + 2;
                                }
                                else if (value.FLD_PID == 1000001174)
                                {
                                    Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu +=
                                        (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.785) + 9;
                                    Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich +=
                                        (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.785) + 9;
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang =
                                        (int)Math.Floor(VatPham_ThuocTinh_Manh / 2.0) * 10;
                                }
                                else if (value.FLD_PID == 1000001175)
                                {
                                    Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu +=
                                        (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.0) + 5;
                                    Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich +=
                                        (int)((Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) - 1.0) * 1.0) + 5;
                                    VatPham_ThuocTinh_SinhMenhLuc_GiaTang =
                                        (int)Math.Floor(VatPham_ThuocTinh_Manh / 2.0) * 10 + 6;
                                }

                                switch (fLD_MAGIC)
                                {
                                    case 0:
                                        Vat_Pham_Luc_Phong_Ngu += (int)Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) + 4;
                                        Vat_Pham_Luc_Phong_NguNew +=
                                            (int)Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) + 4;
                                        Vat_Pham_Luc_Cong_Kich += (int)Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) + 4;
                                        Vat_Pham_Luc_Cong_KichNew +=
                                            (int)Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) + 4;
                                        break;
                                    case 1:
                                        Vat_Pham_Luc_Phong_Ngu +=
                                            (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.1875) + 5;
                                        Vat_Pham_Luc_Phong_NguNew +=
                                            (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.1875) + 5;
                                        Vat_Pham_Luc_Cong_Kich +=
                                            (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.1875) + 5;
                                        Vat_Pham_Luc_Cong_KichNew +=
                                            (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.1875) + 5;
                                        Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu *= 2;
                                        Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich *= 2;
                                        break;
                                    case 2:
                                        Vat_Pham_Luc_Phong_Ngu +=
                                            (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.5 + 5.5);
                                        Vat_Pham_Luc_Phong_NguNew +=
                                            (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.5 + 5.5);
                                        Vat_Pham_Luc_Cong_Kich +=
                                            (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.5 + 5.5);
                                        Vat_Pham_Luc_Cong_KichNew +=
                                            (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 1.5 + 5.5);
                                        Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu *= 4;
                                        Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich *= 4;
                                        break;
                                    case 3:
                                        Vat_Pham_Luc_Phong_Ngu +=
                                            (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
                                        Vat_Pham_Luc_Phong_NguNew +=
                                            (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
                                        Vat_Pham_Luc_Cong_Kich +=
                                            (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
                                        Vat_Pham_Luc_Cong_KichNew +=
                                            (int)(Math.Ceiling(VatPham_ThuocTinh_Manh / 2.0) * 2.0) + 8;
                                        Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu *= 8;
                                        Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich *= 8;
                                        break;
                                }
                            }
                            else if (VatPham_ThuocTinh_Manh == 1)
                            {
                                var num3 = 5;
                                var num4 = 10;
                                switch (fLD_MAGIC)
                                {
                                    case 1:
                                        num3 += 6;
                                        num4 += 20;
                                        break;
                                    case 2:
                                        num3 += 7;
                                        num4 += 40;
                                        break;
                                    case 3:
                                        num3 += 10;
                                        num4 += 80;
                                        break;
                                }

                                Vat_Pham_Luc_Phong_Ngu += num3;
                                Vat_Pham_Luc_Phong_NguNew += num3;
                                Vat_Pham_Luc_Cong_Kich += num3;
                                Vat_Pham_Luc_Cong_KichNew += num3;
                                Vat_Pham_Chong_Lai_Quai_Luc_Phong_Ngu += num4;
                                Vat_Pham_Chong_Lai_Quai_Luc_Cong_Kich += num4;
                            }

                            break;
                        }
                    }

                    break;
                case 9:
                    VatPham_ThuocTinh_Giai_Doan_Loai_Hinh = 0;
                    VatPham_ThuocTinh_So_Giai_Doan = 0;
                    VatPham_ThuocTinh_Manh_Loai_Hinh = int.Parse(ysqh.Substring(ysqh.Length - 9, 1));
                    VatPham_ThuocTinh_Manh = int.Parse(ysqh.Substring(ysqh.Length - 3, 3));
                    if (VatPham_ThuocTinh_Manh_Loai_Hinh != 4) break;
                    if (VatPham_ThuocTinh_Manh >= 100) VatPham_ThuocTinh_Manh = 100;
                    if (VatPham_ThuocTinh_Manh > 1)
                    {
                        if ((VatPham_ThuocTinh_Manh & 1) == 1)
                        {
                            Vat_Pham_Luc_Phong_Ngu += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
                            Vat_Pham_Luc_Phong_NguNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
                            Vat_Pham_Luc_Cong_Kich += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
                            Vat_Pham_Luc_Cong_KichMAX += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
                            Vat_Pham_Luc_Cong_KichNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
                            Vat_Pham_Luc_Cong_KichMaxNew += (VatPham_ThuocTinh_Manh - 1) / 2 + 5;
                            VatPham_ThuocTinh_SinhMenhLuc_GiaTang += (VatPham_ThuocTinh_Manh - 1) / 2 * 10;
                            VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += (VatPham_ThuocTinh_Manh - 1) * 2;
                        }
                        else
                        {
                            Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_Manh / 2 + 5;
                            Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_Manh / 2 + 5;
                            Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_Manh / 2 + 5;
                            Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_Manh / 2 + 5;
                            Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_Manh / 2 + 5;
                            Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_Manh / 2 + 5;
                            VatPham_ThuocTinh_SinhMenhLuc_GiaTang += VatPham_ThuocTinh_Manh / 2 * 10;
                            VatPham_ThuocTinh_ThuHoach_DuocTienTai_GiaTang += VatPham_ThuocTinh_Manh * 2;
                        }
                    }
                    else
                    {
                        Vat_Pham_Luc_Phong_Ngu += 5;
                        Vat_Pham_Luc_Phong_NguNew += 5;
                    }

                    break;
                case 10:
                    VatPham_ThuocTinh_Giai_Doan_Loai_Hinh = int.Parse(ysqh.Substring(ysqh.Length - 4, 1));
                    VatPham_ThuocTinh_So_Giai_Doan = int.Parse(ysqh.Substring(ysqh.Length - 3, 1)) + 1;
                    VatPham_ThuocTinh_Manh_Loai_Hinh = int.Parse(ysqh.Substring(ysqh.Length - 8, 1));
                    VatPham_ThuocTinh_Manh = int.Parse(ysqh.Substring(ysqh.Length - 2, 2));
                    if (FLD_RESIDE2 == 4)
                        VatPham_ThuocTinh_Manh += CuongHoaVK;
                    else if (FLD_RESIDE2 == 1 || FLD_RESIDE2 == 2 || FLD_RESIDE2 == 5)
                        VatPham_ThuocTinh_Manh += CuongHoaTB;
                    if (VatPham_ThuocTinh_Manh_Loai_Hinh == 1)
                    {
                        if (value.FLD_RESIDE2 != 4) break;
                        TinhToanVuKhiThuocTinh(value);
                        if (VatPham_ThuocTinh_Manh > 5 && VatPham_ThuocTinh_Giai_Doan_Loai_Hinh != 0)
                            VatPham_ThuocTinh_So_Giai_Doan += VatPham_ThuocTinh_Manh - 5;
                        var num = 1;
                        if (CuongHoaVK == 2) num = 2;
                        if (VatPham_ThuocTinh_Giai_Doan_Loai_Hinh != 0 && VatPham_ThuocTinh_So_Giai_Doan > 0)
                            switch (VatPham_ThuocTinh_Giai_Doan_Loai_Hinh)
                            {
                                case 1:
                                    VatPham_ThuocTinh_GiamXuong_TiLePhanTram_PhongNgu =
                                        VatPham_ThuocTinh_So_Giai_Doan * 0.005 * num;
                                    break;
                                case 2:
                                    VatPham_ThuocTinh_BanDau_HoaPhanNo_XacSuat_TiLe_PhanTram =
                                        VatPham_ThuocTinh_So_Giai_Doan * num;
                                    break;
                                case 3:
                                    VatPham_ThuocTinh_GiaTang_TiLe_PhanTram_TrungDich +=
                                        VatPham_ThuocTinh_So_Giai_Doan * 0.01 * num;
                                    break;
                                case 4:
                                    VatPham_ThuocTinh_VoCong_LucCongKich +=
                                        (int)(VatPham_ThuocTinh_So_Giai_Doan * 0.5) * num;
                                    VatPham_ThuocTinh_VoCong_LucCongKichNew +=
                                        (int)(VatPham_ThuocTinh_So_Giai_Doan * 0.5) * num;
                                    break;
                                case 5:
                                    VatPham_ThuocTinh_ThemVao_MucThuongTon += VatPham_ThuocTinh_So_Giai_Doan * 3 * num;
                                    break;
                                case 6:
                                    VatPham_ThuocTinh_ThemVao_TrungDoc_TiLe_TiLePhanTram +=
                                        VatPham_ThuocTinh_So_Giai_Doan * 0.01 * num;
                                    Vat_Pham_Luc_Cong_Kich += VatPham_ThuocTinh_So_Giai_Doan * 3 * num;
                                    Vat_Pham_Luc_Cong_KichMAX += VatPham_ThuocTinh_So_Giai_Doan * 3 * num;
                                    Vat_Pham_Luc_Cong_KichNew += VatPham_ThuocTinh_So_Giai_Doan * 3 * num;
                                    Vat_Pham_Luc_Cong_KichMaxNew += VatPham_ThuocTinh_So_Giai_Doan * 3 * num;
                                    break;
                            }
                    }
                    else
                    {
                        if (VatPham_ThuocTinh_Manh_Loai_Hinh != 2 || value.FLD_RESIDE2 != 1) break;
                        TinhToanYPhucThuocTinh(value);
                        DatDuocVatPhamShield(value, VatPham_ThuocTinh_Manh);
                        var num2 = 1;
                        if (CuongHoaTB == 1) num2 = 2;
                        if (VatPham_ThuocTinh_Giai_Doan_Loai_Hinh != 0 && VatPham_ThuocTinh_So_Giai_Doan > 0)
                            switch (VatPham_ThuocTinh_Giai_Doan_Loai_Hinh)
                            {
                                case 1:
                                    VatPham_ThuocTinh_GiamXuong_TiLePhanTram_CongKich =
                                        VatPham_ThuocTinh_So_Giai_Doan * 0.01 * num2;
                                    break;
                                case 2:
                                    VatPham_ThuocTinh_PhanNo_GiaTri_GiaTang = VatPham_ThuocTinh_So_Giai_Doan * num2;
                                    break;
                                case 3:
                                    VatPham_ThuocTinh_Gia_Tang_TiLe_PhanTram_NeTranh +=
                                        VatPham_ThuocTinh_So_Giai_Doan * 0.01 * num2;
                                    break;
                                case 4:
                                    VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang +=
                                        VatPham_ThuocTinh_So_Giai_Doan * 8 * num2;
                                    VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTangNew +=
                                        VatPham_ThuocTinh_So_Giai_Doan * 4 * num2;
                                    break;
                                case 5:
                                    Vat_Pham_Luc_Phong_Ngu += VatPham_ThuocTinh_So_Giai_Doan * 3 * num2;
                                    Vat_Pham_Luc_Phong_NguNew += VatPham_ThuocTinh_So_Giai_Doan * 3 * num2;
                                    break;
                                case 6:
                                    VatPham_ThuocTinh_ThemVao_TrungDoc_TiLe_TiLePhanTram +=
                                        VatPham_ThuocTinh_So_Giai_Doan * 0.01 * num2;
                                    break;
                            }
                    }

                    break;
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "DatDuocCuongHoa   error：" + ex);
        }
    }

    public int Get_ChiSoTrangSuc(int num, int cuonghoa)
    {
        var thamso = num;
        for (var i = 0; i < cuonghoa - 1; i++)
        {
            thamso += num + 1;
            num++;
        }

        return thamso;
    }

    public int Get_ChiSoTrangSuc_Duoi120(int num, int cuonghoa)
    {
        var thamso = 0;
        var bienso = num - 1;
        if (cuonghoa < 4) return num * cuonghoa;
        return num * cuonghoa + bienso * (cuonghoa - 3);
    }

    public int Get_ChiSoTrangBi(int num, int cuonghoa)
    {
        var thamso = num;
        for (var i = 0; i < cuonghoa - 1; i++)
        {
            thamso += num * cuonghoa;
            num++;
        }

        return thamso;
    }
}