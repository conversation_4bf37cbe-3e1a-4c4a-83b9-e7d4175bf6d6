﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace RxjhServer;

public partial class Players
{
    
	public void ChangeEquipment(byte[] PacketData, int PacketSize)
	{
		try
		{
			if (Exiting)
			{
				Form1.WriteLine(6, "退出复制BUG  [" + base.Userid + "][" + base.UserName + "][" + base.Client.ToString() + "]  ");
			}
			else
			{
				
				int fromType = PacketData[10];
				int fromIndex = PacketData[12];
				int toType = PacketData[14];
				int toIndex = PacketData[16];
				int num13 = BitConverter.ToInt32(PacketData, 18);

				if (OpenWarehouse || (CuaHangCaNhan != null && CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa))
				{
					try
					{
						if (fromType == 1 && (toType == 24 || toType == 49) && 
							!HopThanhVatPham_Table.ContainsKey(1) && 
							World.Itme.TryGetValue((int)Item_In_Bag[fromIndex].GetVatPham_ID, out var item) && 
							new[] { 4, 1, 2, 5, 6 }.Contains(item.FLD_RESIDE2))
						{
							byte[] data = Converter.HexStringToByte("aa55000000005000cc00ff010000000000000000000000000000060000008992da1700000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055aa");
							System.Buffer.BlockCopy(BitConverter.GetBytes(toType == 24 ? 511 : 611), 0, data, 10, 2);
							System.Buffer.BlockCopy(BitConverter.GetBytes(fromIndex), 0, data, 26, 2);
							Enhance_Neo(data);
						}
					}
					catch (Exception Ex)
					{
                        Form1.WriteLine(1,"ChangEQuipmentNeo" + Ex.Message);
					}
					
					return;
				}
				if (GMMode == 8)
				{
                    HeThongNhacNho($"fromType {fromType}-{fromIndex} to {toType}-{toIndex} num13 {num13}");
				}
				if (num13 < 1 || num13 > 9999)
				{
					return;
				}
				byte[] VatPham_byte = new byte[World.Item_Db_Byte_Length];
				
				switch (fromType)
				{
				case 59:
					if (BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[fromIndex].VatPham_ID, 0) == 0)
					{
						break;
					}
					switch (toType)
					{
					case 60:
						if (toIndex >= 16)
						{
							break;
						}
						if (BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_ID, 0) == 0)
						{
							if (BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[fromIndex].VatPham_ID, 0) != 0)
							{
								CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte = CharacterBeast.ThuCungVaTrangBi[fromIndex].VatPham_byte;
								CharacterBeast.ThuCungVaTrangBi[fromIndex].VatPham_byte = VatPham_byte;
								ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte, BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPhamSoLuong, 0));
							}
						}
						else
						{
							if (BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[fromIndex].VatPham_ID, 0) == 0)
							{
								break;
							}
							ItmeClass itmeClass = World.Itme[BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[fromIndex].VatPham_ID, 0)];
							if (BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_ID, 0) != 0 && !CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].Khoa_Chat)
							{
								ItmeClass itmeClass13 = World.Itme[BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_ID, 0)];
								if (itmeClass.FLD_RESIDE2 == itmeClass13.FLD_RESIDE2 && itmeClass13.FLD_RESIDE2 >= 1 && itmeClass13.FLD_RESIDE2 <= 16)
								{
									byte[] VatPham_byte12 = CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte;
									CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte = CharacterBeast.ThuCungVaTrangBi[fromIndex].VatPham_byte;
									Item_Wear[fromIndex].VatPham_byte = VatPham_byte12;
									ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte, BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPhamSoLuong, 0));
								}
							}
						}
						break;
					case 59:
						if (BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[toIndex].VatPham_ID, 0) == 0)
						{
							CharacterBeast.ThuCungVaTrangBi[toIndex].VatPham_byte = CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte;
							CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte = VatPham_byte;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, CharacterBeast.ThuCungVaTrangBi[toIndex].VatPham_byte, BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[toIndex].VatPhamSoLuong, 0));
						}
						else
						{
							byte[] VatPham_byte13 = CharacterBeast.ThuCungVaTrangBi[toIndex].VatPham_byte;
							CharacterBeast.ThuCungVaTrangBi[toIndex].VatPham_byte = CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte;
							CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte = VatPham_byte13;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, CharacterBeast.ThuCungVaTrangBi[toIndex].VatPham_byte, BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[toIndex].VatPhamSoLuong, 0));
						}
						break;
					case 1:
						if (!CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].Vat_Pham_Khoa_Lai)
						{
							if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0)
							{
								Item_In_Bag[toIndex].VatPham_byte = CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte;
								CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte = VatPham_byte;
								ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
							}
							else
							{
								byte[] VatPham_byte11 = Item_In_Bag[toIndex].VatPham_byte;
								Item_In_Bag[toIndex].VatPham_byte = CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte;
								CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte = VatPham_byte11;
								ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
							}
							UpdateMoneyAndWeight();
						}
						break;
					}
					break;
				case 1:
				{
					X_Vat_Pham_Loai VatPhamCLass2 = Item_In_Bag[fromIndex];
					if (BitConverter.ToInt32(VatPhamCLass2.VatPham_ID, 0) == 0)
					{
						break;
					}
					switch (toType)
					{
					case 1:
					{
						if (Item_In_Bag[fromIndex].Khoa_Chat || num13 != Item_In_Bag[fromIndex].GetVatPhamSoLuong || BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) == 0)
						{
							break;
						}
						ItmeClass itmeClass3 = World.Itme[BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0)];
						if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0)
						{
							Item_In_Bag[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
							Item_In_Bag[fromIndex].VatPham_byte = VatPham_byte;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
							break;
						}
						ItmeClass itmeClass5 = World.Itme[BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0)];
						if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) && (itmeClass5.FLD_SIDE == 1 || itmeClass3.FLD_SIDE == 1))
						{
							break;
						}
						if (ItmeClass.Get_OrbLevel(BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0)) != 0 && ItmeClass.ItemTayLuyenThanChau(BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0)))
						{
							if (BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) == World.ItemTayLuyenThanChau1)
							{
								int option = RandomOrbOptionsClass.Get_OrbOptions(ItmeClass.Get_OrbLevel(itmeClass5.FLD_PID), itmeClass5.FLD_RESIDE2, 1);
								if (option != 0)
								{
									Item_In_Bag[toIndex].FLD_MAGIC1 = option;
								}
								SubtractItems(fromIndex, 1);
								Update_Item_In_Bag();
							}
							else if (BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) == World.ItemTayLuyenThanChau2)
							{
								int option2 = RandomOrbOptionsClass.Get_OrbOptions(ItmeClass.Get_OrbLevel(itmeClass5.FLD_PID), itmeClass5.FLD_RESIDE2, 2);
								if (option2 != 0)
								{
									Item_In_Bag[toIndex].FLD_MAGIC2 = option2;
								}
								SubtractItems(fromIndex, 1);
								Update_Item_In_Bag();
							}
							else if (BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) == World.ItemTayLuyenThanChau3)
							{
								int option3 = RandomOrbOptionsClass.Get_OrbOptions(ItmeClass.Get_OrbLevel(itmeClass5.FLD_PID), itmeClass5.FLD_RESIDE2, 3);
								if (option3 != 0)
								{
									Item_In_Bag[toIndex].FLD_MAGIC3 = option3;
								}
								SubtractItems(fromIndex, 1);
								Update_Item_In_Bag();
							}
							else if (BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) == World.ItemTayLuyenThanChau4)
							{
								int option4 = RandomOrbOptionsClass.Get_OrbOptions(ItmeClass.Get_OrbLevel(itmeClass5.FLD_PID), itmeClass5.FLD_RESIDE2, 4);
								if (option4 != 0)
								{
									Item_In_Bag[toIndex].FLD_MAGIC4 = option4;
								}
								SubtractItems(fromIndex, 1);
								Update_Item_In_Bag();
							}
						}
						else if (UpgradeItemClass.GetUpgradeItem((int)Item_In_Bag[toIndex].GetVatPham_ID) != null)
						{
							int error = 0;
							UpgradeItemClass upgradeItem = UpgradeItemClass.GetUpgradeItem((int)Item_In_Bag[toIndex].GetVatPham_ID);
							if (upgradeItem.NguyenLieu_ID != 0 && upgradeItem.NguyenLieu_ID == (int)Item_In_Bag[fromIndex].GetVatPham_ID)
							{
								try
								{
									Item_In_Bag[toIndex].DatDuocVatPham_ThuocTinhPhuongThuc(0, 0);
									if (upgradeItem.YeuCauCuongHoa != 0 && Item_In_Bag[toIndex].VatPham_ThuocTinh_Manh < upgradeItem.YeuCauCuongHoa)
									{
										HeThongNhacNho("Vật phẩm phải đạt cấp cường hóa [" + upgradeItem.YeuCauCuongHoa + "] trở lên mới có thể nâng cấp", 6, "Chú ý");
									}
									else
									{
										error = 1;
										UpgradeItemClass upgradeItemClass = World.List_UpgradeItem[(int)Item_In_Bag[toIndex].GetVatPham_ID];
										error = 2;
										int num11 = upgradeItemClass.ItemIDX;
										error = 3;
										if (num11 != 0)
										{
											error = 4;
											error = 5;
											int num12 = new Random(World.GetRandomSeed()).Next(0, 10000);
											error = 6;
											error = 7;
											if (num12 > upgradeItemClass.Upgrade_PP)
											{
												error = 8;
												Item_In_Bag[toIndex].VatPham_ID = BitConverter.GetBytes(num11);
												SubtractItems(fromIndex, 1);
												if (upgradeItem.GiamCuongHoa != 0 && Item_In_Bag[toIndex].VatPham_ThuocTinh_Manh > 0)
												{
													int fLD_MAGIC = Item_In_Bag[toIndex].FLD_MAGIC0 - upgradeItem.GiamCuongHoa;
													Item_In_Bag[toIndex].FLD_MAGIC0 = fLD_MAGIC;
												}
												Update_Item_In_Bag();
												HeThongNhacNho("Nâng cấp trang bị thành công", 6, "Kết quả");
											}
											else
											{
												error = 9;
												SubtractItems(fromIndex, 1);
												Update_Item_In_Bag();
												HeThongNhacNho("Nâng cấp trang bị thất bại", 6, "Kết quả");
											}
										}
										else
										{
											HeThongNhacNho("Trang bị nâng cấp chưa phù hợp", 6, "Chú ý");
										}
									}
									break;
								}
								catch (Exception ex2)
								{
									Form1.WriteLine(1, "Upgrade Pet Error [" + error + "] : " + ex2);
									break;
								}
							}
							HeThongNhacNho("Nguyên liệu không phù hợp để nâng cấp", 6, "Chú ý");
							byte[] vatPham_byte6 = Item_In_Bag[toIndex].VatPham_byte;
							Item_In_Bag[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
							Item_In_Bag[fromIndex].VatPham_byte = vatPham_byte6;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
						}
						else if (itmeClass5.FLD_PetNameType == 6)
						{
							if (Check_NangCapThanThu(itmeClass5.FLD_PetStage, BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0)))
							{
								int random = RNG.Next(1, 100);
								int success = GetRateNangCapThanThu(itmeClass5.FLD_PetStage);
								if (random > success)
								{
									Item_In_Bag[toIndex].VatPham_byte = new byte[World.Item_Db_Byte_Length];
									int new_pid = Get_ThanThuIDNew(itmeClass5.FLD_PetStage, itmeClass5.FLD_BS);
									IncreaseItemWithAttributes(new_pid, toIndex, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
									HeThongNhacNho("Tiến hoá Cổ Long thành công", 6, "Kết quả");
								}
								else
								{
									HeThongNhacNho("Tiến hóa Cổ Long thất bại", 6, "Kết quả");
								}
								SubtractItems(fromIndex, 1);
								Update_Item_In_Bag();
							}
							else
							{
								HeThongNhacNho("Không có Thần Long Bảo Thạch phù hợp", 6, "Chú ý");
								byte[] VatPham_byte4 = Item_In_Bag[toIndex].VatPham_byte;
								Item_In_Bag[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
								Item_In_Bag[fromIndex].VatPham_byte = VatPham_byte4;
								ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
							}
						}
						else
						{
							byte[] VatPham_byte5 = Item_In_Bag[toIndex].VatPham_byte;
							Item_In_Bag[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
							Item_In_Bag[fromIndex].VatPham_byte = VatPham_byte5;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
						}
						break;
					}
					case 0:
					{
						if (BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) == 0 || num13 != Item_In_Bag[fromIndex].GetVatPhamSoLuong || Item_In_Bag[fromIndex].Khoa_Chat)
						{
							break;
						}
						ItmeClass itmeClass8 = World.Itme[BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0)];
						if (!CheckEquipItem(itmeClass8.FLD_RESIDE2, toIndex, 0) || (base.Player_Job == 11 && itmeClass8.FLD_RESIDE1 != 11 && itmeClass8.FLD_RESIDE2 != 13 && itmeClass8.FLD_RESIDE2 != 12 && itmeClass8.FLD_RESIDE2 != 14 && itmeClass8.FLD_RESIDE2 != 15 && itmeClass8.FLD_RESIDE2 != 16 && itmeClass8.FLD_RESIDE2 != 30 && itmeClass8.FLD_RESIDE2 != 31) || itmeClass8.FLD_LEVEL > base.Player_Level || (base.Player_Job == 11 && (itmeClass8.FLD_RESIDE2 == 7 || itmeClass8.FLD_RESIDE2 == 8 || itmeClass8.FLD_RESIDE2 == 10) && base.Player_Job != itmeClass8.FLD_RESIDE1) || (itmeClass8.FLD_ZX != 0 && itmeClass8.FLD_ZX != base.Player_Zx))
						{
							break;
						}
						if (itmeClass8.FLD_RESIDE1 != 0)
						{
							if (BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) == 1000000148)
							{
								if (base.Player_Job != 11 && base.Player_Job != 4)
								{
									break;
								}
							}
							else if (itmeClass8.FLD_RESIDE1 != base.Player_Job)
							{
								break;
							}
						}
						if (itmeClass8.FLD_PID >= 900102 && itmeClass8.FLD_PID <= 900112)
						{
							if (itmeClass8.FLD_PID == 900102 && base.GangLevel < 4)
							{
								HeThongNhacNho("Bạn chưa gia nhập bang hội hoặc bang chưa đạt cấp 4", 10, "Bang hội");
								break;
							}
							if (itmeClass8.FLD_PID == 900103 && base.GangLevel < 5)
							{
								HeThongNhacNho("Bạn chưa gia nhập bang hội hoặc bang chưa đạt cấp 5", 10, "Bang hội");
								break;
							}
							if (itmeClass8.FLD_PID == 900104 && base.GangLevel < 6)
							{
								HeThongNhacNho("Bạn chưa gia nhập bang hội hoặc bang chưa đạt cấp 6", 10, "Bang hội");
								break;
							}
							if (itmeClass8.FLD_PID >= 900105 && itmeClass8.FLD_PID <= 900108)
							{
								if (base.GangLevel < 7)
								{
									HeThongNhacNho("Bạn chưa gia nhập bang hội hoặc bang chưa đạt cấp 7", 10, "Bang hội");
									break;
								}
								if (base.GangCharacterLevel < 2)
								{
									HeThongNhacNho("Chỉ có Bang chủ mới có thể sử dụng", 10, "Bang hội");
									break;
								}
							}
							if (itmeClass8.FLD_PID >= 900109 && itmeClass8.FLD_PID <= 900112 && base.GangLevel < 7)
							{
								HeThongNhacNho("Bạn chưa gia nhập bang hội hoặc bang chưa đạt cấp 7", 10, "Bang hội");
								break;
							}
						}
						if ((itmeClass8.FLD_JOB_LEVEL != 0 && itmeClass8.FLD_JOB_LEVEL > base.Player_Job_level) || (itmeClass8.FLD_SEX != 0 && itmeClass8.FLD_SEX != base.Player_Sex) || ((itmeClass8.FLD_RESIDE2 < 1 || itmeClass8.FLD_RESIDE2 > 16) && itmeClass8.FLD_RESIDE2 != 31) || (itmeClass8.FLD_XWJD >= 1 && itmeClass8.FLD_XWJD > base.VoHuanGiaiDoan) || (toIndex == 14 && CharacterBeast != null))
						{
							break;
						}
						if (BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0) == 0)
						{
							if (itmeClass8.FLD_RESIDE2 != toIndex + 1 && ((itmeClass8.FLD_RESIDE2 != 2 && itmeClass8.FLD_RESIDE2 != 3 && itmeClass8.FLD_RESIDE2 != 8 && itmeClass8.FLD_RESIDE2 != 9 && itmeClass8.FLD_RESIDE2 != 10 && itmeClass8.FLD_RESIDE2 != 11 && itmeClass8.FLD_RESIDE2 != 31) || (itmeClass8.FLD_RESIDE2 != toIndex && itmeClass8.FLD_RESIDE2 != 31)))
							{
								break;
							}
							Item_Wear[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
							Item_In_Bag[fromIndex].VatPham_byte = VatPham_byte;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Item_Wear[toIndex].VatPhamSoLuong, 0));
						}
						else
						{
							if (BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0) == 0)
							{
								break;
							}
							ItmeClass itmeClass9 = World.Itme[BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0)];
							if (itmeClass8.FLD_RESIDE2 != itmeClass9.FLD_RESIDE2)
							{
								break;
							}
							byte[] VatPham_byte7 = Item_Wear[toIndex].VatPham_byte;
							Item_Wear[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
							Item_In_Bag[fromIndex].VatPham_byte = VatPham_byte7;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Item_Wear[toIndex].VatPhamSoLuong, 0));
						}
						UpdateCharacterData(this);
						UpdateBroadcastCharacterData();
						UpdateEquipmentEffects();
						CalculateCharacterEquipmentData();
						AoChoangG7();
						UpdateMartialArtsAndStatus();
						UpdateMoneyAndWeight();
						CapNhat_HP_MP_SP();
						break;
					}
					case 169:
					{
						if (!Item_In_Bag[fromIndex].Khoa_Chat && World.Itme.TryGetValue(BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0), out var value) && value.FLD_RESIDE2 == 12 && BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPham_ID, 0) == 0)
						{
							AoChang_HanhLy[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
							Item_In_Bag[fromIndex].VatPham_byte = VatPham_byte;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, AoChang_HanhLy[toIndex].VatPham_byte, BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPhamSoLuong, 0));
						}
						break;
					}
					case 123:
					{
						if (BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) == 0 || Item_In_Bag[fromIndex].Khoa_Chat)
						{
							break;
						}
						ItmeClass itmeClass6 = World.Itme[BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0)];
						if (itmeClass6.FLD_LEVEL > base.Player_Level || (itmeClass6.FLD_ZX != 0 && itmeClass6.FLD_ZX != base.Player_Zx))
						{
							break;
						}
						if (itmeClass6.FLD_RESIDE1 != 0)
						{
							if (base.Player_Job != 1 && base.Player_Job != 8)
							{
								if (itmeClass6.FLD_RESIDE1 != base.Player_Job)
								{
									break;
								}
							}
							else if (itmeClass6.FLD_RESIDE1 != base.Player_Job && itmeClass6.FLD_RESIDE1 != 9)
							{
								break;
							}
						}
						if ((itmeClass6.FLD_JOB_LEVEL != 0 && itmeClass6.FLD_JOB_LEVEL > base.Player_Job_level) || (itmeClass6.FLD_SEX != 0 && itmeClass6.FLD_SEX != base.Player_Sex) || itmeClass6.FLD_RESIDE2 < 1 || itmeClass6.FLD_RESIDE2 > 16 || (itmeClass6.FLD_XWJD >= 1 && itmeClass6.FLD_XWJD > base.VoHuanGiaiDoan) || (toIndex == 14 && CharacterBeast != null))
						{
							break;
						}
						if (BitConverter.ToInt32(Sub_Item_Wear[toIndex].VatPham_ID, 0) == 0)
						{
							if (itmeClass6.FLD_RESIDE2 != toIndex + 1 && ((itmeClass6.FLD_RESIDE2 != 2 && itmeClass6.FLD_RESIDE2 != 3 && itmeClass6.FLD_RESIDE2 != 8 && itmeClass6.FLD_RESIDE2 != 9 && itmeClass6.FLD_RESIDE2 != 10 && itmeClass6.FLD_RESIDE2 != 11) || itmeClass6.FLD_RESIDE2 != toIndex))
							{
								break;
							}
							Sub_Item_Wear[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
							Item_In_Bag[fromIndex].VatPham_byte = VatPham_byte;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Sub_Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Sub_Item_Wear[toIndex].VatPhamSoLuong, 0));
						}
						else
						{
							if (BitConverter.ToInt32(Sub_Item_Wear[toIndex].VatPham_ID, 0) == 0)
							{
								break;
							}
							ItmeClass itmeClass7 = World.Itme[BitConverter.ToInt32(Sub_Item_Wear[toIndex].VatPham_ID, 0)];
							if (itmeClass6.FLD_RESIDE2 != itmeClass7.FLD_RESIDE2)
							{
								break;
							}
							byte[] VatPham_byte6 = Sub_Item_Wear[toIndex].VatPham_byte;
							Sub_Item_Wear[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
							Item_In_Bag[fromIndex].VatPham_byte = VatPham_byte6;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Sub_Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Sub_Item_Wear[toIndex].VatPhamSoLuong, 0));
						}
						UpdateCharacterData(this);
						UpdateBroadcastCharacterData();
						UpdateEquipmentEffects();
						CalculateCharacterEquipmentData();
						UpdateMartialArtsAndStatus();
						UpdateMoneyAndWeight();
						CapNhat_HP_MP_SP();
						break;
					}
					case 171:
					{
						if (Item_In_Bag[fromIndex].Vat_Pham_Khoa_Lai || !Item_In_Bag[fromIndex].GetVatPham_ID.ToString().Contains("1008000") || BitConverter.ToInt32(NgungThanChauBag[toIndex].VatPham_ID, 0) != 0)
						{
							break;
						}
						NgungThanChauBag[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
						Item_In_Bag[fromIndex].VatPham_byte = VatPham_byte;
						ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, NgungThanChauBag[toIndex].VatPham_byte, BitConverter.ToInt32(NgungThanChauBag[toIndex].VatPhamSoLuong, 0));
						int num17 = 0;
						for (int j = 0; j < 6; j++)
						{
							if (BitConverter.ToInt32(NgungThanChauBag[j].VatPham_ID, 0) != 0)
							{
								if (num17 == 3)
								{
									break;
								}
								if (NgungThanChauBag[j].FLD_MAGIC1 != NgungThanChauBag[j].FLD_MAGIC0)
								{
									num17++;
									NgungThanChauUse(1, j, BitConverter.ToInt32(NgungThanChauBag[j].VatPham_ID, 0), NgungThanChauBag[j].FLD_MAGIC1, NgungThanChauBag[j].FLD_MAGIC0);
								}
							}
						}
						UpdateMoneyAndWeight();
						break;
					}
					case 188:
					{
						if (BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0) == 0 || num13 != Item_In_Bag[fromIndex].GetVatPhamSoLuong || Item_In_Bag[fromIndex].Khoa_Chat)
						{
							break;
						}
						ItmeClass itmeClass4 = World.Itme[BitConverter.ToInt32(Item_In_Bag[fromIndex].VatPham_ID, 0)];
						if ((itmeClass4.FLD_LEVEL > base.Player_Level || itmeClass4.FLD_RESIDE2 >= 32) && (toIndex != 0 || itmeClass4.FLD_RESIDE2 == 33) && ((toIndex != 1 && toIndex != 2) || itmeClass4.FLD_RESIDE2 == 35) && (toIndex != 3 || itmeClass4.FLD_RESIDE2 == 32) && (toIndex != 4 || itmeClass4.FLD_RESIDE2 == 36) && (toIndex != 5 || itmeClass4.FLD_RESIDE2 == 34) && (itmeClass4.FLD_JOB_LEVEL == 0 || itmeClass4.FLD_JOB_LEVEL <= base.Player_Job_level) && (itmeClass4.FLD_SEX == 0 || itmeClass4.FLD_SEX == base.Player_Sex) && (itmeClass4.FLD_XWJD < 1 || itmeClass4.FLD_XWJD <= base.VoHuanGiaiDoan))
						{
							if (ThietBiTab3[toIndex].GetVatPham_ID != 0)
							{
								byte[] VatPhamByte8 = ThietBiTab3[toIndex].VatPham_byte;
								ThietBiTab3[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
								Item_In_Bag[fromIndex].VatPham_byte = VatPhamByte8;
							}
							else
							{
								ThietBiTab3[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
								Item_In_Bag[fromIndex].VatPham_byte = VatPham_byte;
							}
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, ThietBiTab3[toIndex].VatPham_byte, BitConverter.ToInt32(ThietBiTab3[toIndex].VatPhamSoLuong, 0));
							UpdateCharacterData(this);
							UpdateBroadcastCharacterData();
							UpdateEquipmentEffects();
							CalculateCharacterEquipmentData();
							UpdateMartialArtsAndStatus();
							UpdateMoneyAndWeight();
							CapNhat_HP_MP_SP();
						}
						break;
					}
					case 60:
						if (Item_In_Bag[fromIndex].Vat_Pham_Khoa_Lai || (BitConverter.ToInt32(VatPhamCLass2.VatPham_ID, 0) != 601100001 && BitConverter.ToInt32(VatPhamCLass2.VatPham_ID, 0) != 601100002 && BitConverter.ToInt32(VatPhamCLass2.VatPham_ID, 0) != 601100003 && BitConverter.ToInt32(VatPhamCLass2.VatPham_ID, 0) != 601100004 && BitConverter.ToInt32(VatPhamCLass2.VatPham_ID, 0) != 601100005 && BitConverter.ToInt32(VatPhamCLass2.VatPham_ID, 0) != 601100006 && BitConverter.ToInt32(VatPhamCLass2.VatPham_ID, 0) != 601100007))
						{
							break;
						}
						if (BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_ID, 0) == 0)
						{
							if (CharacterBeast.FLD_JOB_LEVEL == 1)
							{
								if (toIndex > 8)
								{
									break;
								}
							}
							else if (CharacterBeast.FLD_JOB_LEVEL == 2)
							{
								if (toIndex > 12)
								{
									break;
								}
							}
							else if (CharacterBeast.FLD_JOB_LEVEL == 3)
							{
								if (toIndex > 16)
								{
									break;
								}
							}
							else if (toIndex > 4)
							{
								break;
							}
							CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
							Item_In_Bag[fromIndex].VatPham_byte = VatPham_byte;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte, BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPhamSoLuong, 0));
						}
						else
						{
							byte[] VatPham_byte3 = CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte;
							CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte = Item_In_Bag[fromIndex].VatPham_byte;
							Item_In_Bag[fromIndex].VatPham_byte = VatPham_byte3;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPham_byte, BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[toIndex].VatPhamSoLuong, 0));
						}
						UpdateTheWeightOfTheBeast();
						break;
					}
					break;
				}
				case 0:
				{
					X_Vat_Pham_Loai VatPhamCLass = Item_Wear[fromIndex];
					if (BitConverter.ToInt32(VatPhamCLass.VatPham_ID, 0) == 0 || VatPhamCLass.Khoa_Chat)
					{
						break;
					}
					switch (toType)
					{
					case 169:
					{
						if (Item_Wear[fromIndex].Khoa_Chat || BitConverter.ToInt32(Item_Wear[fromIndex].VatPham_ID, 0) == 0 || Item_Wear[fromIndex].Khoa_Chat)
						{
							return;
						}
						ItmeClass itmeClass20 = World.Itme[BitConverter.ToInt32(Item_Wear[fromIndex].VatPham_ID, 0)];
						if ((itmeClass20.FLD_SEX != 0 && itmeClass20.FLD_SEX != base.Player_Sex) || itmeClass20.FLD_RESIDE2 != 12)
						{
							return;
						}
						if (BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPham_ID, 0) == 0)
						{
							AoChang_HanhLy[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
							Item_Wear[fromIndex].VatPham_byte = VatPham_byte;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, AoChang_HanhLy[toIndex].VatPham_byte, BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPhamSoLuong, 0));
							break;
						}
						ItmeClass itmeClass22 = World.Itme[BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPham_ID, 0)];
						if (itmeClass20.FLD_RESIDE2 != itmeClass22.FLD_RESIDE2)
						{
							return;
						}
						byte[] VatPham_byte16 = AoChang_HanhLy[toIndex].VatPham_byte;
						AoChang_HanhLy[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
						Item_Wear[fromIndex].VatPham_byte = VatPham_byte16;
						ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, AoChang_HanhLy[toIndex].VatPham_byte, BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPhamSoLuong, 0));
						break;
					}
					case 1:
					{
						if (toIndex >= World.SoLuongTrangBi_ToiDa || (fromIndex == 14 && CharacterBeast != null) || num13 != Item_Wear[fromIndex].GetVatPhamSoLuong)
						{
							return;
						}
						if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0)
						{
							if (BitConverter.ToInt32(Item_Wear[fromIndex].VatPham_ID, 0) == 0)
							{
								return;
							}
							Item_In_Bag[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
							Item_Wear[fromIndex].VatPham_byte = VatPham_byte;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
							break;
						}
						if (BitConverter.ToInt32(Item_Wear[fromIndex].VatPham_ID, 0) == 0)
						{
							return;
						}
						ItmeClass itmeClass17 = World.Itme[BitConverter.ToInt32(Item_Wear[fromIndex].VatPham_ID, 0)];
						if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0 || Item_In_Bag[toIndex].Khoa_Chat)
						{
							return;
						}
						ItmeClass itmeClass18 = World.Itme[BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0)];
						if (!CheckEquipItem(itmeClass17.FLD_RESIDE2, toIndex, 0) || (base.Player_Job == 11 && itmeClass17.FLD_RESIDE1 != 11 && itmeClass17.FLD_RESIDE2 != 13 && itmeClass17.FLD_RESIDE2 != 12 && itmeClass17.FLD_RESIDE2 != 14 && itmeClass17.FLD_RESIDE2 != 15 && itmeClass17.FLD_RESIDE2 != 16 && itmeClass17.FLD_RESIDE2 != 30 && itmeClass17.FLD_RESIDE2 != 31) || itmeClass17.FLD_RESIDE2 != itmeClass18.FLD_RESIDE2 || itmeClass18.FLD_LEVEL > base.Player_Level || (itmeClass18.FLD_ZX != 0 && itmeClass18.FLD_ZX != base.Player_Zx))
						{
							return;
						}
						if (itmeClass18.FLD_RESIDE1 != 0)
						{
							if (base.Player_Job != 1 && base.Player_Job != 8)
							{
								if (itmeClass18.FLD_RESIDE1 != base.Player_Job)
								{
									return;
								}
							}
							else if (itmeClass18.FLD_RESIDE1 != base.Player_Job && itmeClass18.FLD_RESIDE1 != 10)
							{
								return;
							}
						}
						if ((itmeClass18.FLD_JOB_LEVEL != 0 && itmeClass18.FLD_JOB_LEVEL > base.Player_Job_level) || (itmeClass18.FLD_SEX != 0 && itmeClass18.FLD_SEX != base.Player_Sex) || (itmeClass18.FLD_XWJD >= 1 && itmeClass18.FLD_XWJD > base.VoHuanGiaiDoan) || itmeClass18.FLD_RESIDE2 < 1 || itmeClass18.FLD_RESIDE2 > 16)
						{
							return;
						}
						byte[] VatPham_byte15 = Item_In_Bag[toIndex].VatPham_byte;
						Item_In_Bag[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
						Item_Wear[fromIndex].VatPham_byte = VatPham_byte15;
						ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
						break;
					}
					case 0:
					{
						if (fromIndex == 14 && CharacterBeast != null)
						{
							return;
						}
						ItmeClass itmeClass15 = World.Itme[BitConverter.ToInt32(Item_Wear[fromIndex].VatPham_ID, 0)];
						if (itmeClass15.FLD_LEVEL > base.Player_Level || (itmeClass15.FLD_ZX != 0 && itmeClass15.FLD_ZX != base.Player_Zx))
						{
							return;
						}
						if (itmeClass15.FLD_RESIDE1 != 0)
						{
							if (base.Player_Job != 1 && base.Player_Job != 8)
							{
								if (itmeClass15.FLD_RESIDE1 != base.Player_Job)
								{
									return;
								}
							}
							else if (itmeClass15.FLD_RESIDE1 != base.Player_Job && itmeClass15.FLD_RESIDE1 != 10)
							{
								return;
							}
						}
						if ((itmeClass15.FLD_JOB_LEVEL != 0 && itmeClass15.FLD_JOB_LEVEL > base.Player_Job_level) || (itmeClass15.FLD_SEX != 0 && itmeClass15.FLD_SEX != base.Player_Sex) || (itmeClass15.FLD_XWJD >= 1 && itmeClass15.FLD_XWJD > base.VoHuanGiaiDoan) || itmeClass15.FLD_RESIDE2 < 1 || itmeClass15.FLD_RESIDE2 > 16)
						{
							return;
						}
						if (BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0) == 0)
						{
							if (itmeClass15.FLD_RESIDE2 != toIndex + 1 && ((itmeClass15.FLD_RESIDE2 != 2 && itmeClass15.FLD_RESIDE2 != 3 && itmeClass15.FLD_RESIDE2 != 8 && itmeClass15.FLD_RESIDE2 != 9 && itmeClass15.FLD_RESIDE2 != 10 && itmeClass15.FLD_RESIDE2 != 11) || itmeClass15.FLD_RESIDE2 != toIndex))
							{
								return;
							}
							Item_Wear[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
							Item_Wear[fromIndex].VatPham_byte = VatPham_byte;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Item_Wear[toIndex].VatPhamSoLuong, 0));
							break;
						}
						if (BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0) == 0)
						{
							return;
						}
						ItmeClass itmeClass16 = World.Itme[BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0)];
						if (itmeClass15.FLD_RESIDE2 != itmeClass16.FLD_RESIDE2)
						{
							return;
						}
						byte[] VatPham_byte14 = Item_Wear[toIndex].VatPham_byte;
						Item_Wear[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
						Item_Wear[fromIndex].VatPham_byte = VatPham_byte14;
						ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Item_Wear[toIndex].VatPhamSoLuong, 0));
						break;
					}
					}
					UpdateCharacterData(this);
					UpdateBroadcastCharacterData();
					UpdateEquipmentEffects();
					CalculateCharacterEquipmentData();
					AoChoangG7();
					UpdateMartialArtsAndStatus();
					UpdateMoneyAndWeight();
					CapNhat_HP_MP_SP();
					break;
				}
				case 169:
					if (AoChang_HanhLy[fromIndex].Khoa_Chat || BitConverter.ToInt32(AoChang_HanhLy[fromIndex].VatPham_ID, 0) == 0)
					{
						break;
					}
					switch (toType)
					{
					case 169:
						if (!AoChang_HanhLy[fromIndex].Khoa_Chat)
						{
							if (BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPham_ID, 0) == 0)
							{
								AoChang_HanhLy[toIndex].VatPham_byte = AoChang_HanhLy[fromIndex].VatPham_byte;
								AoChang_HanhLy[fromIndex].VatPham_byte = VatPham_byte;
								ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, AoChang_HanhLy[toIndex].VatPham_byte, BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPhamSoLuong, 0));
							}
							else
							{
								byte[] VatPham_byte2 = AoChang_HanhLy[toIndex].VatPham_byte;
								AoChang_HanhLy[toIndex].VatPham_byte = AoChang_HanhLy[fromIndex].VatPham_byte;
								AoChang_HanhLy[fromIndex].VatPham_byte = VatPham_byte2;
								ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, AoChang_HanhLy[toIndex].VatPham_byte, BitConverter.ToInt32(AoChang_HanhLy[toIndex].VatPhamSoLuong, 0));
							}
						}
						break;
					case 1:
						if (base.Player_Money < 1000000)
						{
							HeThongNhacNho("Ngươi thật nghèo, 100 Vạn đều không có.");
							break;
						}
						base.Player_Money -= 1000000L;
						if (BitConverter.ToInt32(AoChang_HanhLy[fromIndex].VatPham_ID, 0) != 0 && !AoChang_HanhLy[fromIndex].Khoa_Chat && BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0)
						{
							Item_In_Bag[toIndex].VatPham_byte = AoChang_HanhLy[fromIndex].VatPham_byte;
							AoChang_HanhLy[fromIndex].VatPham_byte = VatPham_byte;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
						}
						break;
					case 0:
					{
						if (BitConverter.ToInt32(AoChang_HanhLy[fromIndex].VatPham_ID, 0) == 0 || AoChang_HanhLy[fromIndex].Khoa_Chat)
						{
							break;
						}
						ItmeClass itmeClass23 = World.Itme[BitConverter.ToInt32(AoChang_HanhLy[fromIndex].VatPham_ID, 0)];
						if ((itmeClass23.FLD_SEX != 0 && itmeClass23.FLD_SEX != base.Player_Sex) || itmeClass23.FLD_RESIDE2 != 12)
						{
							break;
						}
						if (BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0) == 0)
						{
							if (base.Player_Money < 1000000)
							{
								HeThongNhacNho("Ngươi thật nghèo, 100 Vạn đều không có.");
								break;
							}
							base.Player_Money -= 1000000L;
							if (itmeClass23.FLD_RESIDE2 != toIndex + 1 && ((itmeClass23.FLD_RESIDE2 != 2 && itmeClass23.FLD_RESIDE2 != 3 && itmeClass23.FLD_RESIDE2 != 8 && itmeClass23.FLD_RESIDE2 != 9 && itmeClass23.FLD_RESIDE2 != 10 && itmeClass23.FLD_RESIDE2 != 11) || itmeClass23.FLD_RESIDE2 != toIndex))
							{
								break;
							}
							Item_Wear[toIndex].VatPham_byte = AoChang_HanhLy[fromIndex].VatPham_byte;
							AoChang_HanhLy[fromIndex].VatPham_byte = VatPham_byte;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Item_Wear[toIndex].VatPhamSoLuong, 0));
						}
						else
						{
							if (BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0) == 0)
							{
								break;
							}
							ItmeClass itmeClass2 = World.Itme[BitConverter.ToInt32(Item_Wear[toIndex].VatPham_ID, 0)];
							if (itmeClass23.FLD_RESIDE2 != itmeClass2.FLD_RESIDE2)
							{
								break;
							}
							byte[] VatPham_byte17 = Item_Wear[toIndex].VatPham_byte;
							Item_Wear[toIndex].VatPham_byte = AoChang_HanhLy[fromIndex].VatPham_byte;
							AoChang_HanhLy[fromIndex].VatPham_byte = VatPham_byte17;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Item_Wear[toIndex].VatPhamSoLuong, 0));
						}
						UpdateCharacterData(this);
						UpdateEquipmentEffects();
						CalculateCharacterEquipmentData();
						UpdateMartialArtsAndStatus();
						CapNhat_HP_MP_SP();
						break;
					}
					}
					break;
				case 188:
				{
					X_Vat_Pham_Loai VatPhamClass3 = ThietBiTab3[fromIndex];
					if (BitConverter.ToInt32(VatPhamClass3.VatPham_ID, 0) == 0 || VatPhamClass3.Khoa_Chat)
					{
						break;
					}
					int num15 = toType;
					int num16 = num15;
					if (num16 != 1 || toIndex >= 36 || num13 != ThietBiTab3[fromIndex].GetVatPhamSoLuong)
					{
						break;
					}
					if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0)
					{
						if (BitConverter.ToInt32(ThietBiTab3[fromIndex].VatPham_ID, 0) == 0)
						{
							break;
						}
						Item_In_Bag[toIndex].VatPham_byte = ThietBiTab3[fromIndex].VatPham_byte;
						ThietBiTab3[fromIndex].VatPham_byte = VatPham_byte;
						ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
					}
					else
					{
						if (BitConverter.ToInt32(ThietBiTab3[fromIndex].VatPham_ID, 0) == 0)
						{
							break;
						}
						ItmeClass itmeClass19 = World.Itme[BitConverter.ToInt32(ThietBiTab3[fromIndex].VatPham_ID, 0)];
						if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0 || Item_In_Bag[toIndex].Khoa_Chat)
						{
							break;
						}
						ItmeClass itmeClass21 = World.Itme[BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0)];
						if (itmeClass19.FLD_RESIDE2 != itmeClass21.FLD_RESIDE2 || itmeClass21.FLD_LEVEL > base.Player_Level || (itmeClass21.FLD_ZX != 0 && itmeClass21.FLD_ZX != base.Player_Zx))
						{
							break;
						}
						if (itmeClass21.FLD_RESIDE1 != 0)
						{
							if (base.Player_Job != 1 && base.Player_Job != 8)
							{
								if (itmeClass21.FLD_RESIDE1 != base.Player_Job)
								{
									break;
								}
							}
							else if (itmeClass21.FLD_RESIDE1 != base.Player_Job && itmeClass21.FLD_RESIDE1 != 10)
							{
								break;
							}
						}
						if ((itmeClass21.FLD_JOB_LEVEL != 0 && itmeClass21.FLD_JOB_LEVEL > base.Player_Job_level) || (itmeClass21.FLD_SEX != 0 && itmeClass21.FLD_SEX != base.Player_Sex) || (itmeClass21.FLD_XWJD >= 1 && itmeClass21.FLD_XWJD > base.VoHuanGiaiDoan) || itmeClass21.FLD_RESIDE2 < 1 || itmeClass21.FLD_RESIDE2 > 16)
						{
							break;
						}
						byte[] VatPham_Byte5 = Item_In_Bag[toIndex].VatPham_byte;
						Item_In_Bag[toIndex].VatPham_byte = ThietBiTab3[fromIndex].VatPham_byte;
						ThietBiTab3[fromIndex].VatPham_byte = VatPham_Byte5;
						ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
					}
					UpdateCharacterData(this);
					UpdateBroadcastCharacterData();
					UpdateEquipmentEffects();
					CalculateCharacterEquipmentData();
					UpdateMartialArtsAndStatus();
					UpdateMoneyAndWeight();
					CapNhat_HP_MP_SP();
					break;
				}
				case 123:
				{
					X_Vat_Pham_Loai VatPhamCLass3 = Sub_Item_Wear[fromIndex];
					if (BitConverter.ToInt32(VatPhamCLass3.VatPham_ID, 0) == 0 || VatPhamCLass3.Khoa_Chat)
					{
						break;
					}
					switch (toType)
					{
					case 1:
					{
						if (toIndex >= World.SoLuongTrangBi_ToiDa || (fromIndex == 14 && CharacterBeast != null))
						{
							return;
						}
						if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0)
						{
							if (BitConverter.ToInt32(Sub_Item_Wear[fromIndex].VatPham_ID, 0) == 0)
							{
								return;
							}
							Item_In_Bag[toIndex].VatPham_byte = Sub_Item_Wear[fromIndex].VatPham_byte;
							Sub_Item_Wear[fromIndex].VatPham_byte = VatPham_byte;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
							break;
						}
						if (BitConverter.ToInt32(Sub_Item_Wear[fromIndex].VatPham_ID, 0) == 0)
						{
							return;
						}
						ItmeClass itmeClass12 = World.Itme[BitConverter.ToInt32(Sub_Item_Wear[fromIndex].VatPham_ID, 0)];
						if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0 || Item_In_Bag[toIndex].Khoa_Chat)
						{
							return;
						}
						ItmeClass itmeClass14 = World.Itme[BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0)];
						if (itmeClass12.FLD_RESIDE2 != itmeClass14.FLD_RESIDE2 || itmeClass14.FLD_LEVEL > base.Player_Level || (itmeClass14.FLD_ZX != 0 && itmeClass14.FLD_ZX != base.Player_Zx))
						{
							return;
						}
						if (itmeClass14.FLD_RESIDE1 != 0)
						{
							if (base.Player_Job != 1 && base.Player_Job != 8)
							{
								if (itmeClass14.FLD_RESIDE1 != base.Player_Job)
								{
									return;
								}
							}
							else if (itmeClass14.FLD_RESIDE1 != base.Player_Job && itmeClass14.FLD_RESIDE1 != 10)
							{
								return;
							}
						}
						if ((itmeClass14.FLD_JOB_LEVEL != 0 && itmeClass14.FLD_JOB_LEVEL > base.Player_Job_level) || (itmeClass14.FLD_SEX != 0 && itmeClass14.FLD_SEX != base.Player_Sex) || (itmeClass14.FLD_XWJD >= 1 && itmeClass14.FLD_XWJD > base.VoHuanGiaiDoan) || itmeClass14.FLD_RESIDE2 < 1 || itmeClass14.FLD_RESIDE2 > 16)
						{
							return;
						}
						byte[] VatPham_byte9 = Item_In_Bag[toIndex].VatPham_byte;
						Item_In_Bag[toIndex].VatPham_byte = Sub_Item_Wear[fromIndex].VatPham_byte;
						Sub_Item_Wear[fromIndex].VatPham_byte = VatPham_byte9;
						ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
						break;
					}
					case 0:
					{
						if (fromIndex == 14 && CharacterBeast != null)
						{
							return;
						}
						ItmeClass itmeClass10 = World.Itme[BitConverter.ToInt32(Sub_Item_Wear[fromIndex].VatPham_ID, 0)];
						if (itmeClass10.FLD_LEVEL > base.Player_Level || (itmeClass10.FLD_ZX != 0 && itmeClass10.FLD_ZX != base.Player_Zx))
						{
							return;
						}
						if (itmeClass10.FLD_RESIDE1 != 0)
						{
							if (base.Player_Job != 1 && base.Player_Job != 8)
							{
								if (itmeClass10.FLD_RESIDE1 != base.Player_Job)
								{
									return;
								}
							}
							else if (itmeClass10.FLD_RESIDE1 != base.Player_Job && itmeClass10.FLD_RESIDE1 != 10)
							{
								return;
							}
						}
						if ((itmeClass10.FLD_JOB_LEVEL != 0 && itmeClass10.FLD_JOB_LEVEL > base.Player_Job_level) || (itmeClass10.FLD_SEX != 0 && itmeClass10.FLD_SEX != base.Player_Sex) || (itmeClass10.FLD_XWJD >= 1 && itmeClass10.FLD_XWJD > base.VoHuanGiaiDoan) || itmeClass10.FLD_RESIDE2 < 1 || itmeClass10.FLD_RESIDE2 > 16)
						{
							return;
						}
						if (BitConverter.ToInt32(Sub_Item_Wear[toIndex].VatPham_ID, 0) == 0)
						{
							if (itmeClass10.FLD_RESIDE2 != toIndex + 1 && ((itmeClass10.FLD_RESIDE2 != 2 && itmeClass10.FLD_RESIDE2 != 3 && itmeClass10.FLD_RESIDE2 != 8 && itmeClass10.FLD_RESIDE2 != 9 && itmeClass10.FLD_RESIDE2 != 10 && itmeClass10.FLD_RESIDE2 != 11) || itmeClass10.FLD_RESIDE2 != toIndex))
							{
								return;
							}
							Sub_Item_Wear[toIndex].VatPham_byte = Sub_Item_Wear[fromIndex].VatPham_byte;
							Sub_Item_Wear[fromIndex].VatPham_byte = VatPham_byte;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Sub_Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Sub_Item_Wear[toIndex].VatPhamSoLuong, 0));
							break;
						}
						if (BitConverter.ToInt32(Sub_Item_Wear[toIndex].VatPham_ID, 0) == 0)
						{
							return;
						}
						ItmeClass itmeClass11 = World.Itme[BitConverter.ToInt32(Sub_Item_Wear[toIndex].VatPham_ID, 0)];
						if (itmeClass10.FLD_RESIDE2 != itmeClass11.FLD_RESIDE2)
						{
							return;
						}
						byte[] VatPham_byte8 = Sub_Item_Wear[toIndex].VatPham_byte;
						Sub_Item_Wear[toIndex].VatPham_byte = Item_Wear[fromIndex].VatPham_byte;
						Sub_Item_Wear[fromIndex].VatPham_byte = VatPham_byte8;
						ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Sub_Item_Wear[toIndex].VatPham_byte, BitConverter.ToInt32(Sub_Item_Wear[toIndex].VatPhamSoLuong, 0));
						break;
					}
					}
					UpdateCharacterData(this);
					UpdateBroadcastCharacterData();
					UpdateEquipmentEffects();
					CalculateCharacterEquipmentData();
					UpdateMartialArtsAndStatus();
					UpdateMoneyAndWeight();
					CapNhat_HP_MP_SP();
					break;
				}
				case 60:
					if (!CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].Vat_Pham_Khoa_Lai)
					{
						if (BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) == 0)
						{
							Item_In_Bag[toIndex].VatPham_byte = CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte;
							CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte = VatPham_byte;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
						}
						else
						{
							byte[] VatPham_byte10 = Item_In_Bag[toIndex].VatPham_byte;
							Item_In_Bag[toIndex].VatPham_byte = CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte;
							CharacterBeast.ThuCung_Thanh_TrangBi[fromIndex].VatPham_byte = VatPham_byte10;
							ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
						}
						UpdateTheWeightOfTheBeast();
					}
					break;
				case 171:
				{
					if (NgungThanChauBag[fromIndex].Vat_Pham_Khoa_Lai || BitConverter.ToInt32(Item_In_Bag[toIndex].VatPham_ID, 0) != 0)
					{
						break;
					}
					Item_In_Bag[toIndex].VatPham_byte = NgungThanChauBag[fromIndex].VatPham_byte;
					NgungThanChauBag[fromIndex].VatPham_byte = VatPham_byte;
					ChangeEquipmentLocation(fromType, fromIndex, toType, toIndex, Item_In_Bag[toIndex].VatPham_byte, BitConverter.ToInt32(Item_In_Bag[toIndex].VatPhamSoLuong, 0));
					NgungThanChauUse(2, toIndex, BitConverter.ToInt32(NgungThanChauBag[fromIndex].VatPham_ID, 0), NgungThanChauBag[fromIndex].FLD_MAGIC0, NgungThanChauBag[fromIndex].FLD_MAGIC1);
					int num14 = 0;
					for (int i = 0; i < 6; i++)
					{
						if (BitConverter.ToInt32(NgungThanChauBag[i].VatPhamSoLuong, 0) != 0)
						{
							if (num14 == 3)
							{
								break;
							}
							num14++;
							NgungThanChauUse(1, i, BitConverter.ToInt32(NgungThanChauBag[i].VatPham_ID, 0), NgungThanChauBag[i].FLD_MAGIC0, NgungThanChauBag[i].FLD_MAGIC1);
						}
					}
					UpdateMoneyAndWeight();
					break;
				}
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "更换装备error  [" + base.Userid + "][" + base.UserName + "]  " + ex.Message);
		}
	}

	public void OpenItem(byte[] PacketData, int PacketSize)
	{
		if (base.NhanVat_HP <= 0)
		{
			return;
		}
		PacketModification(PacketData, PacketSize);
		if (OpenWarehouse || (CuaHangCaNhan != null && CuaHangCaNhan.CuaHangCaNhanPhaiChangMoRa))
		{
			return;
		}
		try
		{
			byte[] array = new byte[4];
			System.Buffer.BlockCopy(PacketData, 14, array, 0, 4);
			int num = BitConverter.ToInt32(array, 0);
			int num2 = PacketData[10];
			int num3 = PacketData[11];
			BitConverter.ToInt32(PacketData, 26);
			switch (num2)
			{
			case 60:
				if (num3 != 14 || CharacterBeast == null)
				{
					ItmeClass itmeClass = World.Itme[BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[num3].VatPham_ID, 0)];
					if (BitConverter.ToInt32(CharacterBeast.ThuCungVaTrangBi[4].VatPham_ID, 0) == 0)
					{
						CharacterBeast.ThuCungVaTrangBi[itmeClass.FLD_RESIDE2 - 1].VatPham_byte = CharacterBeast.ThuCung_Thanh_TrangBi[num3].VatPham_byte;
						CharacterBeast.ThuCung_Thanh_TrangBi[num3].VatPham_byte = new byte[World.Item_Db_Byte_Length];
						ChangeEquipmentLocation(num2, num3, num2 - 1, itmeClass.FLD_RESIDE2 - 1, CharacterBeast.ThuCungVaTrangBi[4].VatPham_byte, 1);
						UpdateTheWeightOfTheBeast();
						UpdateTheSpiritBeastExperienceAndTrainExperience();
						CharacterBeast.CalculateBasicData();
						UpdateSpiritBeastHP_MP_SP();
						UpdateSpiritBeastMartialArtsAndStatus();
						UpdateCharacterData(this);
					}
				}
				break;
			case 1:
			{
				if (Item_In_Bag[num3].GetVatPham_ID == 8000008)
				{
					if (GetAbnormalState(28))
					{
						break;
					}
					if (World.Item_Db_Byte_Length >= 13 && checkkepskill)
					{
						if (base.Player_Job == 4 && base.CurrentlyActiveSkill_ID != 0)
						{
							if (!checkkepskill || (int)DateTime.Now.Subtract(time_PK).TotalMilliseconds >= 850)
							{
								time_PK = DateTime.Now;
								checkkepskill = true;
								WalkingState(BitConverter.GetBytes(1), 1);
							}
						}
						else if (base.Player_Job == 6)
						{
							if (!checkkepskill || (int)DateTime.Now.Subtract(time_PK).TotalMilliseconds >= 300)
							{
								time_PK = DateTime.Now;
								checkkepskill = true;
								WalkingState(BitConverter.GetBytes(1), 1);
							}
						}
						else if (!checkkepskill || (int)DateTime.Now.Subtract(time_PK).TotalMilliseconds >= 700)
						{
							time_PK = DateTime.Now;
							checkkepskill = true;
							WalkingState(BitConverter.GetBytes(1), 1);
						}
					}
					else
					{
						HeThongNhacNho("Chỉ có thể sử dụng lúc PK");
					}
					break;
				}
				int itemid = BitConverter.ToInt32(Item_In_Bag[num3].VatPham_ID, 0);
				if ((Item_In_Bag[num3].Vat_Pham_Khoa_Lai || BitConverter.ToInt32(Item_In_Bag[num3].VatPham_ID, 0) == 1008000188 || BitConverter.ToInt32(Item_In_Bag[num3].VatPham_ID, 0) == 1008000388 || BitConverter.ToInt32(Item_In_Bag[num3].VatPham_ID, 0) == 1008001836) && BitConverter.ToInt32(Item_In_Bag[num3].VatPham_ID, 0) != 1008001022)
				{
					num -= 20000;
				}
				if ((BitConverter.ToInt32(Item_In_Bag[num3].VatPham_ID, 0) == num && !Item_In_Bag[num3].Khoa_Chat) || BitConverter.ToInt32(Item_In_Bag[num3].VatPham_ID, 0) == 1008000188 || BitConverter.ToInt32(Item_In_Bag[num3].VatPham_ID, 0) == 1008000388 || BitConverter.ToInt32(Item_In_Bag[num3].VatPham_ID, 0) == 1008001836)
				{
					ItmeClass itmeClass2 = World.Itme[BitConverter.ToInt32(Item_In_Bag[num3].VatPham_ID, 0)];
					PillClass Vpill;
					if (itmeClass2.FLD_RESIDE2 >= 1 && itmeClass2.FLD_RESIDE2 <= 1)
					{
						if (itmeClass2.FLD_LEVEL <= base.Player_Level && (itmeClass2.FLD_ZX == 0 || itmeClass2.FLD_ZX == base.Player_Zx) && (itmeClass2.FLD_RESIDE1 == 0 || itmeClass2.FLD_RESIDE1 == base.Player_Job) && (itmeClass2.FLD_JOB_LEVEL == 0 || itmeClass2.FLD_JOB_LEVEL <= base.Player_Job_level) && (itmeClass2.FLD_SEX == 0 || itmeClass2.FLD_SEX == base.Player_Sex) && (itmeClass2.FLD_XWJD < 1 || itmeClass2.FLD_XWJD <= base.VoHuanGiaiDoan))
						{
							ItmeClass itmeClass3 = World.Itme[BitConverter.ToInt32(Item_In_Bag[num3].VatPham_ID, 0)];
							if (BitConverter.ToInt32(Item_Wear[itmeClass3.FLD_RESIDE2 - 1].VatPham_ID, 0) == 0)
							{
								Item_Wear[itmeClass3.FLD_RESIDE2 - 1].VatPham_byte = Item_In_Bag[num3].VatPham_byte;
								Item_In_Bag[num3].VatPham_byte = new byte[World.Item_Db_Byte_Length];
								ChangeEquipmentLocation(num2, num3, 0, itmeClass3.FLD_RESIDE2 - 1, Item_Wear[itmeClass3.FLD_RESIDE2 - 1].VatPham_byte, 1);
								CalculateCharacterEquipmentData();
								UpdateEquipmentEffects();
								UpdateMartialArtsAndStatus();
								UpdateMoneyAndWeight();
								CapNhat_HP_MP_SP();
							}
						}
					}
					else if (itmeClass2.FLD_RESIDE2 == 17)
					{
						Unboxing(PacketData);
					}
					else if (itmeClass2.FLD_RESIDE2 == 177)
					{
						OpenChest(PacketData);
					}
					else if (itmeClass2.FLD_RESIDE2 == 19)
					{
						QigongBook(PacketData);
					}
					else if (itmeClass2.FLD_RESIDE2 == 20)
					{
						TurnOnItemTrigger(PacketData);
					}
					else if (itmeClass2.FLD_RESIDE2 == 1792)
					{
						MartialArtsBook(PacketData);
					}
					else if (itmeClass2.FLD_RESIDE2 == 6969)
					{
						int slot = GetParcelVacancy(this);
						if (slot != -1)
						{
							ItemUse(1, num3, 1);
							IncreaseItemWithAttributes(1000000269, slot, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0);
						}
						else
						{
							HeThongNhacNho("Túi đồ của bạn không còn chỗ trống!", 7);
						}
					}
					else if (itmeClass2.FLD_RESIDE2 == 31)
					{
						byte[] dst = Converter.HexStringToByte("aa55000000001a000c0001001800000010000100000055aa");
						System.Buffer.BlockCopy(BitConverter.GetBytes(num3), 0, dst, 12, 1);
						ChangeEquipment(dst, dst.Length);
					}
					else if (itmeClass2.FLD_RESIDE2 == 199)
					{
						if (World.BatTat_TrieuHoi_Boss == 0)
						{
							HeThongNhacNho("Không thể sử dụng tại kênh này", 6, "Chú ý");
							break;
						}
						int num5 = SummonBossClass.Get_BossID(itmeClass2.FLD_PID);
						if (num5 != 0)
						{
							World.AddNpc(num5, base.NhanVatToaDo_X, base.NhanVatToaDo_Y, base.NhanVatToaDo_BanDo);
							ItemMinusTheNumberOfAttributes(num3, 1);
							Update_Item_In_Bag();
							HeThongNhacNho("Triệu hồi thành công", 6, "Boss");
						}
						else
						{
							HeThongNhacNho("Vật phẩm hiện không thể sử dụng", 6, "Chú ý");
						}
					}
					else if (World.List_Pill.TryGetValue(itemid, out Vpill))
					{
						Use_PillClass(PacketData, Vpill);
					}
					else
					{
						if (World.CamSuDungItem != "")
						{
							string[] Arritem = World.CamSuDungItem.Split(new char[] { ',' });
							for (int i = 0; i < Arritem.Length; i++)
							{
								if (int.Parse(Arritem[i]) == itemid)
                                {
									this.HeThongNhacNho("Vật phẩm hiện không thể sử dụng", 7);
									return;
                                }
                            }	
						}	
						TakeMedicine(PacketData);
					}
				}
				CalculateCharacterEquipmentData();
				UpdateMartialArtsAndStatus();
				UpdateMoneyAndWeight();
				CapNhat_HP_MP_SP();
				break;
			}
			case 0:
			{
				if (num3 == 14 && CharacterBeast != null)
				{
					break;
				}
				int num4 = 0;
				while (true)
				{
					if (num4 >= 36)
					{
						return;
					}
					if (BitConverter.ToInt32(Item_In_Bag[num4].VatPham_ID, 0) == 0)
					{
						break;
					}
					num4++;
				}
				Item_In_Bag[num4].VatPham_byte = Item_Wear[num3].VatPham_byte;
				Item_Wear[num3].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				ChangeEquipmentLocation(num2, num3, 1, num4, Item_In_Bag[num4].VatPham_byte, 1);
				UpdateEquipmentEffects();
				CalculateCharacterEquipmentData();
				UpdateMartialArtsAndStatus();
				UpdateMoneyAndWeight();
				CapNhat_HP_MP_SP();
				break;
			}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Mở VatPham error [" + base.Userid + "][" + base.UserName + "]  " + ex.Message);
		}
	}

	public void OpenChest(byte[] data)
	{
		int num = 0;
		Open_Time = DateTime.Now;
		int Index = data[11];
		byte[] array = new byte[4];
		System.Buffer.BlockCopy(data, 14, array, 0, 4);
		int ItemID = BitConverter.ToInt32(array, 0);
		List_Item_Chest = new Dictionary<int, OpenChestClass>();
		OpenChestClass.Get_ItemChest(ItemID, this);
		if (List_Item_Chest.Count > 0)
		{
			try
			{
				num = 1;
				if (GetParcelVacancyNumber() >= List_Item_Chest.Count)
				{
					num = 2;
					foreach (OpenChestClass ItemChest in List_Item_Chest.Values)
					{
						num = 3;
						int vitri = GetParcelVacancy(this);
						num = 35;
						string[] item = ItemChest.FLD_ITEM.Split(',');
						IncreaseItemWithAttributes(int.Parse(item[0]), vitri, int.Parse(item[1]), int.Parse(item[2]), int.Parse(item[3]), int.Parse(item[4]), int.Parse(item[5]), int.Parse(item[6]), int.Parse(item[7]), int.Parse(item[8]), int.Parse(item[9]), int.Parse(item[10]), int.Parse(item[11]));
						num = 4;
					}
					List_Item_Chest.Clear();
					num = 5;
					ItemMinusTheNumberOfAttributes(Index, 1);
					num = 6;
					Init_Item_In_Bag();
					num = 7;
					UpdateMoneyAndWeight();
					num = 8;
				}
				else
				{
					HeThongNhacNho("Không còn chỗ trống", 10, "Túi đồ");
				}
				return;
			}
			catch
			{
				HeThongNhacNho("Open Chest case : " + num, 10);
				return;
			}
		}
		HeThongNhacNho("Bạn không đủ điều kiện nhận vật phẩm từ túi", 10);
		ItemMinusTheNumberOfAttributes(Index, 1);
		Init_Item_In_Bag();
		UpdateMoneyAndWeight();
	}

	public void Unboxing(byte[] data)
	{
		try
		{
			if ((int)DateTime.Now.Subtract(MoRuongThoiGian).TotalMilliseconds < 1000)
			{
				return;
			}
			MoRuongThoiGian = DateTime.Now;
			int num = data[11];
			byte[] array = new byte[4];
			System.Buffer.BlockCopy(data, 14, array, 0, 4);
			int num2 = BitConverter.ToInt32(array, 0);
			if (Item_In_Bag[num].Vat_Pham_Khoa_Lai)
			{
				num2 -= 20000;
			}
			if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) != num2 || num2 == 0)
			{
				return;
			}
			using (new Lock(World.MoRuong_Lock, "MoRuong_Lock"))
			{
				OpenClass open = OpenClass.GetOpen(num2, base.Player_Job, base.Player_Zx);
				if (open == null)
				{
					ItemUse(1, num, 1);
					HeThongNhacNho("Túi không chứa đựng vật phẩm nào", 50);
					return;
				}
				if (!World.Itme.TryGetValue(open.FLD_PIDX, out var value))
				{
					Form1.WriteLine(1, "开箱  error1  VatPham  [" + open.FLD_PID + "][" + open.FLD_NAME + "]开[" + open.FLD_PIDX + "][" + open.FLD_NAMEX + "]  [" + base.Userid + "][" + base.UserName + "]  ");
					return;
				}
				int num3 = open.FLD_MAGIC1;
				int value2 = open.FLD_MAGIC2;
				if (value.FLD_RESIDE2 == 16)
				{
					if (num3 == 0)
					{
						switch (value.FLD_PID)
						{
						case *********:
						case *********:
						case *********:
						case *********:
						case *********:
						case *********:
						case *********:
						case *********:
						case *********:
						case *********:
						case *********:
						case *********:
						case *********:
						case *********:
							num3 = World.GetValue(value.FLD_PID, 6);
							break;
						}
					}
				}
				else
				{
					switch (value.FLD_PID)
					{
					case 1000000321:
						num3 = RNG.Next(0, 1000);
						value2 = RNG.Next(10, 50);
						break;
					case 1000000323:
						num3 = RNG.Next(0, 1000);
						value2 = RNG.Next(100, 150);
						break;
					case 1000000325:
						num3 = RNG.Next(0, 1000);
						value2 = RNG.Next(400, 699);
						break;
					case 1000000327:
						num3 = RNG.Next(0, 1000);
						value2 = RNG.Next(2000, 2499);
						break;
					}
				}
				ItemUse(1, num, 1);
				byte[] bytes = BitConverter.GetBytes(RxjhClass.GetDBItmeId());
				if (open.CoMoThongBao != 0)
				{
					foreach (Players Player in World.allConnectedChars.Values)
					{
						Player.HeThongNhacNho1258(base.UserName + " mõÒ hôòp nhâòn ðýõòc " + open.FLD_NAMEX, 8);
					}
				}
				int numz = GetParcelVacancy(this);
				if (numz != -1)
				{
					IncreaseItemWithAttributes(open.FLD_PIDX, numz, open.FLD_NUMBER, num3, value2, open.FLD_MAGIC3, open.FLD_MAGIC4, open.FLD_MAGIC5, open.FLD_ThucTinh, open.FLD_TrungCapPhuHon, open.FLD_TienHoa, 0, open.FLD_DAYS);
					RxjhClass.DropRecord(base.Userid, base.UserName, BitConverter.ToInt64(bytes, 0), value.FLD_PID, value.ItmeNAME, value.FLD_MAGIC0, value.FLD_MAGIC1, value.FLD_MAGIC2, value.FLD_MAGIC3, value.FLD_MAGIC4, base.NhanVatToaDo_BanDo, (int)base.NhanVatToaDo_X, (int)base.NhanVatToaDo_Y, "开箱");
				}
			}
		}
		catch (Exception ex)
		{
			byte[] array2 = new byte[4];
			System.Buffer.BlockCopy(data, 14, array2, 0, 4);
			Form1.WriteLine(1, "开箱  error  [" + base.Userid + "][" + base.UserName + "]  箱ID是：" + BitConverter.ToInt32(array2, 0) + ex.Message);
		}
	}

	public void QigongBook(byte[] PacketData)
	{
		try
		{
			if (base.PlayerTuVong)
			{
				return;
			}
			int num = PacketData[11];
			byte[] array = new byte[4];
			byte[] array2 = new byte[4];
			System.Buffer.BlockCopy(PacketData, 14, array, 0, 4);
			System.Buffer.BlockCopy(PacketData, 26, array2, 0, 4);
			int num2 = BitConverter.ToInt32(array, 0);
			BitConverter.ToInt32(array2, 0);
			if (Item_In_Bag[num].Vat_Pham_Khoa_Lai)
			{
				num2 -= 20000;
			}
			if (BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) != num2 || BitConverter.ToInt32(Item_In_Bag[num].VatPham_ID, 0) == 0 || !World.Itme.TryGetValue(num2, out var value))
			{
				return;
			}
			foreach (X_Thang_Thien_Khi_Cong_Tong_Loai value2 in World.ThangThienKhiCongList.Values)
			{
				if (value2.VatPham_ID != num2)
				{
					continue;
				}
				if (value.FLD_LEVEL > base.Player_Level || (value.FLD_ZX != 0 && value.FLD_ZX != base.Player_Zx) || (value.FLD_RESIDE1 != 0 && value.FLD_RESIDE1 != base.Player_Job) || (value.FLD_JOB_LEVEL != 0 && value.FLD_JOB_LEVEL > base.Player_Job_level))
				{
					break;
				}
				if (value.FLD_NEED_MONEY > 0 && base.Player_Money < value.FLD_NEED_MONEY)
				{
					HeThongNhacNho("Tiền trò chơi không đủ.");
				}
				else
				{
					if (value.FLD_NEED_FIGHTEXP > 0 && base.Player_Money < value.FLD_NEED_FIGHTEXP)
					{
						break;
					}
					switch (base.Player_Job)
					{
					case 1:
						if (value2.NhanVatNgheNghiep1 == 0)
						{
							return;
						}
						break;
					case 2:
						if (value2.NhanVatNgheNghiep2 == 0)
						{
							return;
						}
						break;
					case 3:
						if (value2.NhanVatNgheNghiep3 == 0)
						{
							return;
						}
						break;
					case 4:
						if (value2.NhanVatNgheNghiep4 == 0)
						{
							return;
						}
						break;
					case 5:
						if (value2.NhanVatNgheNghiep5 == 0)
						{
							return;
						}
						break;
					case 6:
						if (value2.NhanVatNgheNghiep6 == 0)
						{
							return;
						}
						break;
					case 7:
						if (value2.NhanVatNgheNghiep7 == 0)
						{
							return;
						}
						break;
					case 8:
						if (value2.NhanVatNgheNghiep8 == 0)
						{
							return;
						}
						break;
					case 9:
						if (value2.NhanVatNgheNghiep9 == 0)
						{
							return;
						}
						break;
					case 10:
						if (value2.NhanVatNgheNghiep10 == 0)
						{
							return;
						}
						break;
					case 11:
						if (value2.NhanVatNgheNghiep11 == 0)
						{
							return;
						}
						break;
					case 12:
						if (value2.NhanVatNgheNghiep12 == 0)
						{
							return;
						}
						break;
					case 13:
						if (value2.NhanVatNgheNghiep13 == 0)
						{
							return;
						}
						break;
					}
					X_Thang_Thien_Khi_Cong_Loai ThangThienKhiCongClass = new X_Thang_Thien_Khi_Cong_Loai();
					ThangThienKhiCongClass.KhiCongID = value2.KhiCongID;
					if (GetSTQG(ThangThienKhiCongClass.KhiCongID))
					{
						break;
					}
					if (ThangThienKhiCongClass.KhiCongID == 380 || ThangThienKhiCongClass.KhiCongID == 381 || ThangThienKhiCongClass.KhiCongID == 382 || ThangThienKhiCongClass.KhiCongID == 383 || ThangThienKhiCongClass.KhiCongID == 384 || ThangThienKhiCongClass.KhiCongID == 385 || ThangThienKhiCongClass.KhiCongID == 386)
					{
						int countlearn = 0;
						ThangThienKhiCong.TryGetValue(380, out var valuecheck);
						if (valuecheck != null)
						{
							countlearn++;
						}
						ThangThienKhiCong.TryGetValue(381, out valuecheck);
						if (valuecheck != null)
						{
							countlearn++;
						}
						ThangThienKhiCong.TryGetValue(382, out valuecheck);
						if (valuecheck != null)
						{
							countlearn++;
						}
						ThangThienKhiCong.TryGetValue(383, out valuecheck);
						if (valuecheck != null)
						{
							countlearn++;
						}
						ThangThienKhiCong.TryGetValue(384, out valuecheck);
						if (valuecheck != null)
						{
							countlearn++;
						}
						ThangThienKhiCong.TryGetValue(385, out valuecheck);
						if (valuecheck != null)
						{
							countlearn++;
						}
						ThangThienKhiCong.TryGetValue(386, out valuecheck);
						if (valuecheck != null)
						{
							countlearn++;
						}
						if (countlearn > 4)
						{
							HeThongNhacNho("Đã học đủ 5 loại khí công thăng thiên, không thể học thêm");
							break;
						}
						ThangThienKhiCong.Add(ThangThienKhiCongClass.KhiCongID, ThangThienKhiCongClass);
						ItemUse(1, num, 1);
						base.Player_Money -= value.FLD_NEED_MONEY;
						base.Player_ExpErience -= value.FLD_NEED_FIGHTEXP;
						LearningSkillsTips();
						UpdateMartialArtsAndStatus();
						UpdateMoneyAndWeight();
						GhiDuLieuKhiCongThangThien();
					}
					else
					{
						ThangThienKhiCong.Add(ThangThienKhiCongClass.KhiCongID, ThangThienKhiCongClass);
						ItemUse(1, num, 1);
						base.Player_Money -= value.FLD_NEED_MONEY;
						base.Player_ExpErience -= value.FLD_NEED_FIGHTEXP;
						LearningSkillsTips();
						UpdateMartialArtsAndStatus();
						UpdateMoneyAndWeight();
						GhiDuLieuKhiCongThangThien();
					}
				}
				break;
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "KhiCong书 error [" + base.Userid + "][" + base.UserName + "] " + ex.Message);
		}
	}

	public void TurnOnItemTrigger(byte[] PacketData)
	{
		try
		{
			int num2 = 0;
			int num3 = 0;
			if (base.PlayerTuVong)
			{
				return;
			}
			num2 = PacketData[11];
			byte[] array = new byte[4];
			byte[] array2 = new byte[4];
			System.Buffer.BlockCopy(PacketData, 14, array, 0, 4);
			System.Buffer.BlockCopy(PacketData, 22, array2, 0, 4);
			num3 = BitConverter.ToInt32(array, 0);
			if (Item_In_Bag[num2].Vat_Pham_Khoa_Lai)
			{
				num3 -= 20000;
			}
			BitConverter.ToInt32(array2, 0);
			if (BitConverter.ToInt32(Item_In_Bag[num2].VatPham_ID, 0) != num3 || !World.Itme.TryGetValue(num3, out var _) || BitConverter.ToInt32(Item_In_Bag[num2].VatPham_ID, 0) == 0)
			{
				return;
			}
			try
			{
				if (World.ScriptClass.MoRaVatPhamEvent != null)
				{
					if (DateTime.Now.Subtract(time_Script).TotalSeconds < 2.0)
					{
						time_Script = DateTime.Now;
						HeThongNhacNho("Giãn cách mỗi lần sử dụng: 2 giây!", 10);
						return;
					}
					object[] args = new object[4]
					{
						base.CharacterFullServerID,
						num3,
						num2,
						BitConverter.ToInt32(Item_In_Bag[num2].VatPhamSoLuong, 0)
					};
					World.ScriptClass.MoRaVatPhamEvent.Call(args);
				}
			}
			catch (Exception ex)
			{
				Form1.WriteLine(2, "OpenItmeTrigGer(" + base.CharacterFullServerID + "," + num3 + "," + num2 + "," + BitConverter.ToInt32(Item_In_Bag[num2].VatPhamSoLuong, 0) + ")");
				Form1.WriteLine(2, "Mở ra vật phẩm phát động khí 1 Phạm sai lầm --" + ex.Message);
			}
		}
		catch (Exception ex2)
		{
			Form1.WriteLine(2, "Mở ra vật phẩm phát động khí 2 Phạm sai lầm --" + ex2.Message);
		}
	}
	public void PickUpGetItems(int Position, byte[] VatPhamSoLuong, byte[] ItemGlobal_ID, byte[] VatPham_ID, byte[] VatPham_ThuocTinh, int TYPE)
	{
		if (BitConverter.ToInt32(VatPhamSoLuong, 0) <= 0 || !World.Itme.TryGetValue(BitConverter.ToInt32(VatPham_ID, 0), out var value))
		{
			return;
		}
		if (value.FLD_QUESTITEM == 0)
		{
			if (TYPE == 0 && HeavenAndEarthTalisman() != 0 && (value.FLD_RESIDE2 == 1 || value.FLD_RESIDE2 == 2 || value.FLD_RESIDE2 == 4 || value.FLD_RESIDE2 == 5))
			{
				Dictionary<int, ItmeClass> dictionary = FetchProfessionalGoods(value.FLD_RESIDE2, HeavenAndEarthTalisman(), base.Player_Zx, base.Player_Sex, value.FLD_LEVEL);
				if (dictionary != null)
				{
					int key = RNG.Next(0, dictionary.Count - 1);
					VatPham_ID = BitConverter.GetBytes(dictionary[key].FLD_PID);
				}
			}
			if (value.CoMoThongBao != 0)
			{
				foreach (Players value2 in World.allConnectedChars.Values)
				{
					value2.HeThongNhacNho1258(base.UserName + " nhãòt ðýõòc " + value.ItmeNAME + " taòi " + X_Toa_Do_Class.getname1258(base.NhanVatToaDo_BanDo), 22, "KyÌ baÒo");
				}
			}
			byte[] array = Converter.HexStringToByte("aa55000000020d00770001000000c4221e4216e3d40000cd9a3b0000000003000000000000000100000002250002000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055aa");
			System.Buffer.BlockCopy(ItemGlobal_ID, 0, array, 14, 8);
			System.Buffer.BlockCopy(VatPham_ID, 0, array, 22, 4);
			System.Buffer.BlockCopy(VatPhamSoLuong, 0, array, 30, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(Position), 0, array, 40, 1);
			System.Buffer.BlockCopy(VatPham_ThuocTinh, 0, array, 46, VatPham_ThuocTinh.Length);
			
            byte[] name = Encoding.GetEncoding(1252).GetBytes(base.UserName);
            System.Buffer.BlockCopy(name, 0, array, 0x72, name.Length);
			byte[] array2 = new byte[World.Item_Db_Byte_Length];
			System.Buffer.BlockCopy(ItemGlobal_ID, 0, array2, 0, 8);
			System.Buffer.BlockCopy(VatPham_ID, 0, array2, 8, 4);
			System.Buffer.BlockCopy(VatPhamSoLuong, 0, array2, 12, 4);
			System.Buffer.BlockCopy(VatPham_ThuocTinh, 0, array2, 16, VatPham_ThuocTinh.Length);
			Item_In_Bag[Position].VatPham_byte = array2;
			System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, array, 4, 2);
			if (base.Client != null)
			{
				base.Client.SendMultiplePackage(array, array.Length);
			}
			if (TeamID == 0 || !World.WToDoi.TryGetValue(TeamID, out var Value))
			{
				return;
			}
			{
				foreach (Players Player in Value.ToDoi_NguoiChoi.Values)
				{
					if (Player.CharacterFullServerID == base.CharacterFullServerID)
					{
						Player.HeThongNhacNho1258("Baòn výÌa nhãòt ðýõòc " + ItmeClass.DatDuocVatPhamTen_XungHao(value.FLD_PID), 2, "TôÒ ðôòi");
					}
					else
					{
						Player.HeThongNhacNho1258(base.UserName + " výÌa nhãòt ðýõòc " + ItmeClass.DatDuocVatPhamTen_XungHao(value.FLD_PID), 2, "TôÒ ðôòi");
					}
				}
				return;
			}
		}
		SetUpQuestItems(BitConverter.ToInt32(VatPham_ID, 0), BitConverter.ToInt32(VatPhamSoLuong, 0));
	}
	public void AddItemByMinutes(int VatPham_ID, int Position, int SoLuong, int VatPham_ThuocTinh0, int VatPham_ThuocTinh1, int VatPham_ThuocTinh2, int VatPham_ThuocTinh3, int VatPham_ThuocTinh4, int SoCapPhuHon, int TrungCapPhuHon, int TienHoa, int KhoaLai, int NgaySuDung)
	{
		try
		{
			if (!World.Itme.TryGetValue(VatPham_ID, out var value))
			{
				return;
			}
			byte[] array = new byte[56];
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh0), 0, array, 0, 4);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh1), 0, array, 4, 4);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh2), 0, array, 8, 4);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh3), 0, array, 12, 4);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh4), 0, array, 16, 4);
			Buffer.BlockCopy(Buffer.GetBytes(SoCapPhuHon), 0, array, 46, 4);
			if (TrungCapPhuHon > 0)
			{
				Buffer.BlockCopy(Buffer.GetBytes(1), 0, array, 22, 2);
			}
			Buffer.BlockCopy(Buffer.GetBytes(TrungCapPhuHon), 0, array, 24, 4);
			Buffer.BlockCopy(Buffer.GetBytes(TienHoa), 0, array, 52, 4);
			long value2 = RxjhClass.GetDBItmeId();
			string string_ = "AA55720000000D00640001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA";
			byte[] array2 = Converter.HexStringToByte(string_);
			byte[] array3 = new byte[77];
			if (value.FLD_SIDE != 0)
			{
				bool khoa = false;
				if (KhoaLai == 1)
				{
					khoa = true;
				}
				X_Vat_Pham_Loai VatPhamCLass = GetCharacterItemType(VatPham_ID, VatPham_ThuocTinh0, khoa);
				if (VatPham_ID == 1008000044 || VatPham_ID == 1008000045)
				{
					value2 = RxjhClass.GetDBItmeId();
				}
				else if (VatPhamCLass != null)
				{
					Position = VatPhamCLass.VatPhamViTri;
					value2 = BitConverter.ToInt64(VatPhamCLass.ItemGlobal_ID, 0);
					SoLuong = Buffer.ToInt32(VatPhamCLass.VatPhamSoLuong, 0) + SoLuong;
				}
			}
			else
			{
				SoLuong = 1;
			}
			Buffer.BlockCopy(Buffer.GetBytes(value2), 0, array3, 0, 8);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ID), 0, array3, 8, 4);
			Buffer.BlockCopy(Buffer.GetBytes(SoLuong), 0, array3, 12, 4);
			Buffer.BlockCopy(array, 0, array3, 16, array.Length);
			if (NgaySuDung > 0)
			{
				DateTime value3 = new DateTime(1970, 1, 1, 8, 0, 0);
				Buffer.BlockCopy(Buffer.GetBytes((int)DateTime.Now.Subtract(value3).TotalSeconds), 0, array3, 52, 4);
				Buffer.BlockCopy(Buffer.GetBytes((int)DateTime.Now.AddMinutes(NgaySuDung).Subtract(value3).TotalSeconds), 0, array3, 56, 4);
			}
			if (value.FLD_NJ > 0 && (value.FLD_RESIDE2 == 1 || value.FLD_RESIDE2 == 2 || value.FLD_RESIDE2 == 5 || value.FLD_RESIDE2 == 4 || value.FLD_RESIDE2 == 6))
			{
				Buffer.BlockCopy(Buffer.GetBytes(1000), 0, array3, 60, 2);
			}
			Item_In_Bag[Position].VatPham_byte = array3;
			Buffer.BlockCopy(Buffer.GetBytes(Position), 0, array2, 40, 2);
			Buffer.BlockCopy(array3, 0, array2, 14, 12);
			Buffer.BlockCopy(array3, 12, array2, 30, 4);
			Buffer.BlockCopy(array3, 16, array2, 46, array.Length);
			if (KhoaLai == 1)
			{
				Buffer.BlockCopy(Buffer.GetBytes(1), 0, array3, 72, 1);
				Buffer.BlockCopy(Buffer.GetBytes(VatPham_ID + 20000), 0, array2, 22, 4);
			}
			else
			{
				Buffer.BlockCopy(Buffer.GetBytes(0), 0, array3, 72, 1);
			}
			Buffer.BlockCopy(Buffer.GetBytes(base.CharacterFullServerID), 0, array2, 4, 2);
			if (base.Client != null)
			{
				base.Client.Send_Map_Data(array2, array2.Length);
			}
			//Init_Item_In_Bag();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Gia tăng vật phẩm phạm sai lầm 1 [" + base.Userid + "][" + base.UserName + "] Vị trí [" + Position + "] Số lượng [" + SoLuong + "]" + ex.Message);
		}
	}

	public void IncreaseItemWithAttributes(int VatPham_ID, int Position, int SoLuong, int VatPham_ThuocTinh0, int VatPham_ThuocTinh1, int VatPham_ThuocTinh2, int VatPham_ThuocTinh3, int VatPham_ThuocTinh4, int SoCapPhuHon, int TrungCapPhuHon, int TienHoa, int KhoaLai, int NgaySuDung)
	{
		try
		{
			if (!World.Itme.TryGetValue(VatPham_ID, out var value))
			{
				return;
			}
			byte[] array = new byte[56];
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh0), 0, array, 0, 4);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh1), 0, array, 4, 4);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh2), 0, array, 8, 4);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh3), 0, array, 12, 4);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh4), 0, array, 16, 4);
			Buffer.BlockCopy(Buffer.GetBytes(SoCapPhuHon), 0, array, 46, 4);
			if (TrungCapPhuHon > 0)
			{
				Buffer.BlockCopy(Buffer.GetBytes(1), 0, array, 22, 2);
			}
			Buffer.BlockCopy(Buffer.GetBytes(TrungCapPhuHon), 0, array, 24, 4);
			Buffer.BlockCopy(Buffer.GetBytes(TienHoa), 0, array, 52, 4);
			long value2 = RxjhClass.GetDBItmeId();
			string string_ = "AA55720000000D00640001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA";
			byte[] array2 = Converter.HexStringToByte(string_);
			byte[] array3 = new byte[77];
			if (value.FLD_SIDE != 0)
			{
				bool khoa = false;
				if (KhoaLai == 1)
				{
					khoa = true;
				}
				X_Vat_Pham_Loai VatPhamCLass = GetCharacterItemType(VatPham_ID, VatPham_ThuocTinh0, khoa);
				if (VatPham_ID == 1008000044 || VatPham_ID == 1008000045)
				{
					value2 = RxjhClass.GetDBItmeId();
				}
				else if (VatPhamCLass != null)
				{
					Position = VatPhamCLass.VatPhamViTri;
					value2 = BitConverter.ToInt64(VatPhamCLass.ItemGlobal_ID, 0);
					SoLuong = Buffer.ToInt32(VatPhamCLass.VatPhamSoLuong, 0) + SoLuong;
				}
			}
			else
			{
				SoLuong = 1;
			}
			Buffer.BlockCopy(Buffer.GetBytes(value2), 0, array3, 0, 8);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ID), 0, array3, 8, 4);
			Buffer.BlockCopy(Buffer.GetBytes(SoLuong), 0, array3, 12, 4);
			Buffer.BlockCopy(array, 0, array3, 16, array.Length);
			if (NgaySuDung > 0)
			{
				DateTime value3 = new DateTime(1970, 1, 1, 8, 0, 0);
				Buffer.BlockCopy(Buffer.GetBytes((int)DateTime.Now.Subtract(value3).TotalSeconds), 0, array3, 52, 4);
				Buffer.BlockCopy(Buffer.GetBytes((int)DateTime.Now.AddDays(NgaySuDung).Subtract(value3).TotalSeconds), 0, array3, 56, 4);
			}
			if (value.FLD_NJ > 0 && (value.FLD_RESIDE2 == 1 || value.FLD_RESIDE2 == 2 || value.FLD_RESIDE2 == 5 || value.FLD_RESIDE2 == 4 || value.FLD_RESIDE2 == 6))
			{
				Buffer.BlockCopy(Buffer.GetBytes(1000), 0, array3, 60, 2);
			}
			Item_In_Bag[Position].VatPham_byte = array3;
			Buffer.BlockCopy(Buffer.GetBytes(Position), 0, array2, 40, 2);
			Buffer.BlockCopy(array3, 0, array2, 14, 12);
			Buffer.BlockCopy(array3, 12, array2, 30, 4);
			Buffer.BlockCopy(array3, 16, array2, 46, array.Length);
			if (KhoaLai == 1)
			{
				Buffer.BlockCopy(Buffer.GetBytes(1), 0, array3, 72, 1);
				Buffer.BlockCopy(Buffer.GetBytes(VatPham_ID + 20000), 0, array2, 22, 4);
			}
			else
			{
				Buffer.BlockCopy(Buffer.GetBytes(0), 0, array3, 72, 1);
			}
			Buffer.BlockCopy(Buffer.GetBytes(base.CharacterFullServerID), 0, array2, 4, 2);
			if (base.Client != null)
			{
				base.Client.Send_Map_Data(array2, array2.Length);
			}
			//Init_Item_In_Bag();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Gia tăng vật phẩm phạm sai lầm 1 [" + base.Userid + "][" + base.UserName + "] Vị trí [" + Position + "] Số lượng [" + SoLuong + "]" + ex.Message);
		}
	}

	public void IncreaseItemWithAttributes2(int VatPham_ID, int Position, int SoLuong, int VatPham_ThuocTinh0, int VatPham_ThuocTinh1, int VatPham_ThuocTinh2, int VatPham_ThuocTinh3, int VatPham_ThuocTinh4, int SoCapPhuHon, int TrungCapPhuHon, int TienHoa, int KhoaLai, int NgaySuDung)
	{
		try
		{
			if (!World.Itme.TryGetValue(VatPham_ID, out var value))
			{
				return;
			}
			byte[] array = new byte[60];
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh0), 0, array, 0, 4);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh1), 0, array, 4, 4);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh2), 0, array, 8, 4);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh3), 0, array, 12, 4);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ThuocTinh4), 0, array, 16, 4);
			Buffer.BlockCopy(Buffer.GetBytes(SoCapPhuHon), 0, array, 46, 4);
			if (TrungCapPhuHon > 0)
			{
				Buffer.BlockCopy(Buffer.GetBytes(1), 0, array, 22, 2);
			}
			Buffer.BlockCopy(Buffer.GetBytes(TrungCapPhuHon), 0, array, 24, 4);
			Buffer.BlockCopy(Buffer.GetBytes(TienHoa), 0, array, 52, 4);
			long value2 = RxjhClass.GetDBItmeId();
			string string_ = "AA5573000100000D00640001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA";
			byte[] array2 = Converter.HexStringToByte(string_);
			byte[] array3 = new byte[77];
			if (value.FLD_SIDE != 0)
			{
				bool khoa = false;
				if (KhoaLai == 1)
				{
					khoa = true;
				}
				X_Vat_Pham_Loai VatPhamCLass = GetCharacterItemType(VatPham_ID, VatPham_ThuocTinh0, khoa);
				if (VatPham_ID == 1008000044 || VatPham_ID == 1008000045)
				{
					value2 = RxjhClass.GetDBItmeId();
				}
				else if (VatPhamCLass != null)
				{
					Position = VatPhamCLass.VatPhamViTri;
					value2 = BitConverter.ToInt64(VatPhamCLass.ItemGlobal_ID, 0);
					SoLuong = Buffer.ToInt32(VatPhamCLass.VatPhamSoLuong, 0) + SoLuong;
				}
			}
			else
			{
				SoLuong = 1;
			}
			Buffer.BlockCopy(Buffer.GetBytes(value2), 0, array3, 0, 8);
			Buffer.BlockCopy(Buffer.GetBytes(VatPham_ID), 0, array3, 8, 4);
			Buffer.BlockCopy(Buffer.GetBytes(SoLuong), 0, array3, 12, 4);
			Buffer.BlockCopy(array, 0, array3, 16, array.Length);
			if (NgaySuDung > 0)
			{
				DateTime value3 = new DateTime(1970, 1, 1, 8, 0, 0);
				Buffer.BlockCopy(Buffer.GetBytes((int)DateTime.Now.Subtract(value3).TotalSeconds), 0, array3, 52, 4);
				Buffer.BlockCopy(Buffer.GetBytes((int)DateTime.Now.AddDays(NgaySuDung).Subtract(value3).TotalSeconds), 0, array3, 56, 4);
			}
			if (value.FLD_NJ > 0 && (value.FLD_RESIDE2 == 1 || value.FLD_RESIDE2 == 2 || value.FLD_RESIDE2 == 5 || value.FLD_RESIDE2 == 4 || value.FLD_RESIDE2 == 6))
			{
				Buffer.BlockCopy(Buffer.GetBytes(1000), 0, array3, 60, 2);
			}
			Item_In_Bag[Position].VatPham_byte = array3;
			Buffer.BlockCopy(Buffer.GetBytes(Position), 0, array2, 40, 2);
			Buffer.BlockCopy(array3, 0, array2, 15, 12);
			Buffer.BlockCopy(array3, 12, array2, 31, 4);
			Buffer.BlockCopy(array3, 16, array2, 47, array.Length);
			if (KhoaLai == 1)
			{
				Buffer.BlockCopy(Buffer.GetBytes(1), 0, array3, 76, 1);
				Buffer.BlockCopy(Buffer.GetBytes(VatPham_ID + 20000), 0, array2, 23, 4);
			}
			else
			{
				Buffer.BlockCopy(Buffer.GetBytes(0), 0, array3, 76, 1);
			}
			Buffer.BlockCopy(Buffer.GetBytes(base.CharacterFullServerID), 0, array2, 5, 2);
			if (base.Client != null)
			{
				base.Client.Send_Map_Data(array2, array2.Length);
			}
			Init_Item_In_Bag();
			LoadCharacterWearItem();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Gia tăng vật phẩm phạm sai lầm 1 [" + base.Userid + "][" + base.UserName + "] Vị trí [" + Position + "] Số lượng [" + SoLuong + "]" + ex.Message);
		}
	}


	public void giamDoBenVuKhi()
	{
		int bUpdate = 0;
		int[] listItemIndex = new int[1] { 3 };
		int[] array = listItemIndex;
		foreach (int itemIndex in array)
		{
			if (World.Itme.TryGetValue((int)Item_Wear[itemIndex].GetVatPham_ID, out var class2) && class2.FLD_NJ != 0 && Item_Wear[itemIndex].FLD_FJ_NJ > 0)
			{
				Item_Wear[itemIndex].FLD_FJ_NJ = Item_Wear[itemIndex].FLD_FJ_NJ - World.VuKhiPK_RoiDoBen;
				if (Item_Wear[itemIndex].FLD_FJ_NJ == 100)
				{
					bUpdate = 1;
				}
				if (Item_Wear[itemIndex].FLD_FJ_NJ == 0)
				{
					bUpdate = 2;
				}
			}
		}
		if (DateTime.Now.Subtract(time_UpdateItemVH).TotalMilliseconds > 120000.0)
		{
			time_UpdateItemVH = DateTime.Now;
			UpdateEquipmentEffects();
		}
		switch (bUpdate)
		{
		case 1:
			LoadCharacterWearItem();
			break;
		case 2:
			CalculateCharacterEquipmentData();
			LoadCharacterWearItem();
			UpdateMartialArtsAndStatus();
			break;
		}
	}

	public void giamDoBenTrangBi()
	{
		int bUpdate = 0;
		int[] listItemIndex = new int[10] { 0, 1, 2, 4, 5, 6, 7, 8, 9, 10 };
		int[] array = listItemIndex;
		foreach (int itemIndex in array)
		{
			if (World.Itme.TryGetValue((int)Item_Wear[itemIndex].GetVatPham_ID, out var class2) && class2.FLD_NJ != 0 && Item_Wear[itemIndex].FLD_FJ_NJ > 0)
			{
				Item_Wear[itemIndex].FLD_FJ_NJ = Item_Wear[itemIndex].FLD_FJ_NJ - World.DoPhongNguPK_RoiDoBen;
				if (Item_Wear[itemIndex].FLD_FJ_NJ == 100)
				{
					bUpdate = 1;
				}
				if (Item_Wear[itemIndex].FLD_FJ_NJ == 0)
				{
					bUpdate = 2;
				}
			}
		}
		if (DateTime.Now.Subtract(time_UpdateItemVH).TotalMilliseconds > 120000.0)
		{
			time_UpdateItemVH = DateTime.Now;
			UpdateEquipmentEffects();
		}
		switch (bUpdate)
		{
		case 1:
			LoadCharacterWearItem();
			break;
		case 2:
			CalculateCharacterEquipmentData();
			LoadCharacterWearItem();
			UpdateMartialArtsAndStatus();
			break;
		}
	}
	public void FourGodsChangePrompt(int VatPhamViTri, int DaoCu_ID, int NhacNho_ID)
	{
		try
		{
			SendingClass SendingClass = new SendingClass();
			SendingClass.Write1(1);
			SendingClass.Write1(VatPhamViTri);
			SendingClass.WriteInt(0);
			SendingClass.Write4(DaoCu_ID);
			SendingClass.Write4(0);
			SendingClass.Write4(NhacNho_ID);
			SendingClass.WriteInt(1);
			if (base.Client != null)
			{
				base.Client.SendPak(SendingClass, 15104, base.CharacterFullServerID);
			}
		}
		catch
		{
		}
	}

	public void MartialArtsBook(byte[] PacketData)
	{
		try
		{
			if (base.PlayerTuVong || NhanVatKhoa_Chat)
			{
				return;
			}
			byte num = PacketData[10];
			int num2 = PacketData[11];
			byte[] array = new byte[4];
			byte[] array2 = new byte[4];
			System.Buffer.BlockCopy(PacketData, 14, array, 0, 4);
			System.Buffer.BlockCopy(PacketData, 22, array2, 0, 4);
			int num3 = BitConverter.ToInt32(array, 0);
			BitConverter.ToInt32(array2, 0);
			if (Item_In_Bag[num2].Vat_Pham_Khoa_Lai)
			{
				num3 -= 20000;
			}
			if (num == 60)
			{
				if (BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[num2].VatPham_ID, 0) != num3 || BitConverter.ToInt32(CharacterBeast.ThuCung_Thanh_TrangBi[num2].VatPham_ID, 0) == 0)
				{
					return;
				}
			}
			else if (BitConverter.ToInt32(Item_In_Bag[num2].VatPham_ID, 0) != num3 || BitConverter.ToInt32(Item_In_Bag[num2].VatPham_ID, 0) == 0)
			{
				return;
			}
			if ((World.Itme.TryGetValue(num3, out var value) && BitConverter.ToInt32(Item_In_Bag[num2].VatPham_ID, 0) == 0) || value.FLD_LEVEL > base.Player_Level || (value.FLD_ZX != 0 && value.FLD_ZX != base.Player_Zx) || (value.FLD_RESIDE1 != 0 && value.FLD_RESIDE1 != base.Player_Job) || (value.FLD_JOB_LEVEL != 0 && value.FLD_JOB_LEVEL > base.Player_Job_level) || (value.FLD_NEED_FIGHTEXP > 0 && base.Player_ExpErience < value.FLD_NEED_FIGHTEXP))
			{
				return;
			}
			if (value.FLD_NEED_MONEY > 0 && base.Player_Money < value.FLD_NEED_MONEY)
			{
				HeThongNhacNho("Tiền trò chơi không đủ.");
				return;
			}
			switch (num3)
			{
			case 1000001320:
			case 1000001321:
			case 1000001322:
			case 1000001323:
			case 1000001324:
			case 1000001325:
			case 1000001326:
			case 1000001327:
			case 1000001328:
			case 1000001329:
			case 1000001330:
			case 1000001331:
			case 1000001332:
			case 1000001333:
			case 1000001337:
			case 1000001338:
			case 1000001341:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 16))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 16);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 17);
				break;
			case 1000001334:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 19))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 19);
				break;
			case 1000001335:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 19))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 19);
				break;
			case 1000001336:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 22))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 22);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 23);
				break;
			case 1000001339:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 21))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 21);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 22);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 23);
				break;
			case 1000001340:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 21))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 21);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 22);
				break;
			case 1000000300:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000000301:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000000302:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000000303:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000000304:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000000305:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000000313:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 17))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 17);
				break;
			case 1000000314:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 18))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 18);
				break;
			case 1000000315:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 21))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 21);
				break;
			case 1000000316:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 22))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 22);
				break;
			case 1000000317:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 23))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 23);
				break;
			case 1000000318:
			{
				X_Vo_Cong_Loai wg = X_Vo_Cong_Loai.GetWg(801401);
				if (wg != null)
				{
					if ((wg.FLD_ZX != 0 && base.Player_Zx != wg.FLD_ZX) || (wg.FLD_JOB != 0 && base.Player_Job != wg.FLD_JOB) || (wg.FLD_JOBLEVEL != 0 && base.Player_Job_level < wg.FLD_JOBLEVEL) || (wg.FLD_LEVEL != 0 && base.Player_Level < wg.FLD_LEVEL))
					{
						return;
					}
					VoCongMoi[wg.FLD_VoCongLoaiHinh, wg.FLD_INDEX] = new X_Vo_Cong_Loai(wg.FLD_PID);
				}
				break;
			}
			case 1000000320:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 9))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 9);
				break;
			case 1000000200:
			{
				if (base.FLD_Couple.Length == 0)
				{
					return;
				}
				if (FLD_loveDegreeLevel > 5)
				{
					HeThongNhacNho("Tình nhân phải đạt cấp 4 trở lên");
					return;
				}
				if (WhetherMarried != 1)
				{
					return;
				}
				int key2 = 0;
				if (base.Player_Job == 1)
				{
					key2 = 100301;
				}
				else if (base.Player_Job == 2)
				{
					key2 = 200301;
				}
				else if (base.Player_Job == 3)
				{
					key2 = 300301;
				}
				else if (base.Player_Job == 4)
				{
					key2 = 400301;
				}
				else if (base.Player_Job == 5)
				{
					key2 = 500301;
				}
				else if (base.Player_Job == 6)
				{
					key2 = 800301;
				}
				else if (base.Player_Job == 7)
				{
					key2 = 900301;
				}
				else if (base.Player_Job == 8)
				{
					key2 = 1000301;
				}
				else if (base.Player_Job == 9)
				{
					key2 = 2000301;
				}
				else if (base.Player_Job == 10)
				{
					key2 = 3000301;
				}
				else if (base.Player_Job == 11)
				{
					key2 = 4000301;
				}
				else if (base.Player_Job == 12)
				{
					key2 = 5000301;
				}
				else if (base.Player_Job == 13)
				{
					key2 = 6000301;
				}
				if (World.TBL_KONGFU.TryGetValue(key2, out var value3))
				{
					VoCongMoi[value3.FLD_VoCongLoaiHinh, value3.FLD_INDEX] = new X_Vo_Cong_Loai(value3.FLD_PID);
					UpdateKinhNghiemVaTraiNghiem();
					UpdateMartialArtsAndStatus();
					LearningSkillsTips();
					CalculateMartialArtsAttackPowerOfHusbandAndWifeData();
				}
				ItemUse(1, num2, 1);
				base.Player_ExpErience -= value.FLD_NEED_FIGHTEXP;
				base.Player_Money -= value.FLD_NEED_MONEY;
				LearningSkillsTips();
				UpdateMartialArtsAndStatus();
				UpdateMoneyAndWeight();
				UpdateKinhNghiemVaTraiNghiem();
				CapNhat_HP_MP_SP();
				RxjhClass.DrugRecord(base.Userid, base.UserName, num3, value.ItmeNAME, 1);
				break;
			}
			case 1000000213:
				if (base.FLD_Couple.Length != 0)
				{
					if (FLD_loveDegreeLevel > 5)
					{
						HeThongNhacNho("Tình nhân phải đạt cấp 4 trở lên");
						return;
					}
					if (WhetherMarried != 1)
					{
						return;
					}
					int key = 0;
					if (base.Player_Sex == 1)
					{
						key = 601201;
					}
					else if (base.Player_Sex == 2)
					{
						key = 601202;
					}
					if (World.TBL_KONGFU.TryGetValue(key, out var value2))
					{
						VoCongMoi[value2.FLD_VoCongLoaiHinh, value2.FLD_INDEX] = new X_Vo_Cong_Loai(value2.FLD_PID);
						UpdateKinhNghiemVaTraiNghiem();
						UpdateMartialArtsAndStatus();
						LearningSkillsTips();
						CalculateMartialArtsAttackPowerOfHusbandAndWifeData();
					}
					ItemUse(1, num2, 1);
					base.Player_ExpErience -= value.FLD_NEED_FIGHTEXP;
					base.Player_Money -= value.FLD_NEED_MONEY;
					LearningSkillsTips();
					UpdateMartialArtsAndStatus();
					UpdateMoneyAndWeight();
					UpdateKinhNghiemVaTraiNghiem();
					CapNhat_HP_MP_SP();
					RxjhClass.DrugRecord(base.Userid, base.UserName, num3, value.ItmeNAME, 1);
					break;
				}
				return;
			case 1000000217:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000000218:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000000219:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000000220:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000000221:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000000222:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000000223:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000000224:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000000225:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000000226:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000000227:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000000228:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000000229:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000000230:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000000231:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000000232:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000000233:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000000234:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000000235:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000000236:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000000237:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000000238:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000000239:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000000240:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000000241:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000000242:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000000243:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000000244:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000000245:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000000246:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000000247:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 5))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 5);
				break;
			case 1000000248:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 5))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 5);
				break;
			case 1000000249:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 13))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 13);
				break;
			case 1000001003:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 23))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 23);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 24);
				break;
			case 1000001004:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 19))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 19);
				break;
			case 1000001005:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 9))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 9);
				break;
			case 1000001006:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 12))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 12);
				break;
			case 1000001007:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 14))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 14);
				break;
			case 1000001008:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 17))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 17);
				break;
			case 1000001009:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 5))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 5);
				break;
			case 1000001010:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 15))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 13);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 14);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 15);
				break;
			case 1000001013:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 16))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 16);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 17);
				break;
			case 1000001046:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 13))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 13);
				break;
			case 1000001047:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 18))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 18);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 19);
				break;
			case 1000001049:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 25);
				break;
			case 1000001050:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 13))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 13);
				break;
			case 1000000564:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 19))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 19);
				break;
			case 1000000565:
			{
				X_Vo_Cong_Loai wg3 = X_Vo_Cong_Loai.GetWg(1010701);
				if (wg3 == null || (wg3.FLD_ZX != 0 && base.Player_Zx != wg3.FLD_ZX) || (wg3.FLD_JOB != 0 && base.Player_Job != wg3.FLD_JOB) || (wg3.FLD_JOBLEVEL != 0 && base.Player_Job_level < wg3.FLD_JOBLEVEL) || (wg3.FLD_LEVEL != 0 && base.Player_Level < wg3.FLD_LEVEL))
				{
					return;
				}
				VoCongMoi[wg3.FLD_VoCongLoaiHinh, wg3.FLD_INDEX] = new X_Vo_Cong_Loai(wg3.FLD_PID);
				VoCongMoi[wg3.FLD_VoCongLoaiHinh, wg3.FLD_INDEX].VoCong_DangCap = 1;
				break;
			}
			case 1000000566:
			{
				X_Vo_Cong_Loai wg2 = X_Vo_Cong_Loai.GetWg(1020701);
				if (wg2 == null || (wg2.FLD_ZX != 0 && base.Player_Zx != wg2.FLD_ZX) || (wg2.FLD_JOB != 0 && base.Player_Job != wg2.FLD_JOB) || (wg2.FLD_JOBLEVEL != 0 && base.Player_Job_level < wg2.FLD_JOBLEVEL) || (wg2.FLD_LEVEL != 0 && base.Player_Level < wg2.FLD_LEVEL))
				{
					return;
				}
				VoCongMoi[wg2.FLD_VoCongLoaiHinh, wg2.FLD_INDEX] = new X_Vo_Cong_Loai(wg2.FLD_PID);
				VoCongMoi[wg2.FLD_VoCongLoaiHinh, wg2.FLD_INDEX].VoCong_DangCap = 1;
				break;
			}
			case 1000000567:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 1))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 1);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 10);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 22);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 23);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 24);
				break;
			case 1000000568:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 5))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 5);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 11);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 13);
				break;
			case 1000000569:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 9))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 9);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 12);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 14);
				break;
			case 1000000570:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 10))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 10);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 11);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 12);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 13);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 14);
				break;
			case 1000000488:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000000489:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000000490:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000000491:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000000492:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000000493:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000001223:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 1, 17))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 17);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 1, 18);
				break;
			case 1000001164:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 1))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 1);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 2);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 3);
				break;
			case 1000001165:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 5))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 5);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 6);
				break;
			case 1000001166:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 9))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 9);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 10);
				break;
			case 1000001167:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 13))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 13);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 14);
				break;
			case 1000001205:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 17))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 17);
				break;
			case 1000001206:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 20))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 20);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 21);
				break;
			case 1000001191:
			case 1000001192:
			case 1000001193:
			case 1000001194:
			case 1000001195:
			case 1000001196:
			case 1000001197:
			case 1000001198:
			case 1000001199:
			case 1000001200:
			case 1000001201:
			case 1000001202:
			case 1000001203:
			case 1000001204:
			case 1000001207:
			case 1000001208:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 13))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 13);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 14);
				break;
			case 1000001209:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 17))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 17);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 18);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 19);
				break;
			case 1000001210:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 17))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 17);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 18);
				break;
			case 1000001100:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000001101:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				break;
			case 1000001102:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000001103:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 26))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				break;
			case 1000001104:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000001105:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 27))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				break;
			case 1000000336:
			case 1000000337:
			case 1000000338:
			case 1000000339:
			case 1000000340:
			case 1000000341:
			case 1000000342:
			case 1000000343:
			case 1000000344:
			case 1000000345:
			case 1000000346:
			case 1000000347:
			case 1000000494:
			case 1000000497:
			case 1000001106:
			case 1000001107:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 1) || !X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 2))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 1);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 2);
				break;
			case 1000000388:
			case 1000000389:
			case 1000000390:
			case 1000000391:
			case 1000000392:
			case 1000000393:
			case 1000000394:
			case 1000000395:
			case 1000000396:
			case 1000000397:
			case 1000000398:
			case 1000000399:
			case 1000000495:
			case 1000000498:
			case 1000001108:
			case 1000001109:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 4) || !X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 5))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 4);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 5);
				break;
			case 1000000464:
			case 1000000465:
			case 1000000466:
			case 1000000467:
			case 1000000468:
			case 1000000469:
			case 1000000470:
			case 1000000471:
			case 1000000472:
			case 1000000473:
			case 1000000474:
			case 1000000475:
			case 1000000496:
			case 1000000499:
			case 1000001110:
			case 1000001111:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 7) || !X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 8))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 7);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 8);
				break;
			case 1000001032:
			case 1000001033:
			case 1000001034:
			case 1000001035:
			case 1000001036:
			case 1000001037:
			case 1000001038:
			case 1000001039:
			case 1000001040:
			case 1000001041:
			case 1000001042:
			case 1000001043:
			case 1000001044:
			case 1000001045:
			case 1000001112:
			case 1000001113:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 10) || !X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 11))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 10);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 11);
				break;
			case 1000001532:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 1))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 1);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 2);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 3);
				break;
			case 1000001533:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 5))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 5);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 6);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 7);
				break;
			case 1000001534:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 9))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 9);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 10);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 11);
				break;
			case 1000001535:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 13))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 13);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 14);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 15);
				break;
			case 1000001284:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 1))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 1);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 2);
				break;
			case 1000001285:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 0, 25))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 25);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 26);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 27);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 28);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 0, 29);
				break;
			case 1000001286:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 4))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 4);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 5);
				break;
			case 1000001287:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 7))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 7);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 8);
				break;
			case 1000001288:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 10))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 10);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 11);
				break;
			case 1000001289:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 13))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 13);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 14);
				break;
			case 1000001231:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 17))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 17);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 18);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 19);
				break;
			case 1000001235:
				if (!X_Vo_Cong_Loai.KiemTra_DieuKienTuLuyen(this, 3, 17))
				{
					return;
				}
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 17);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 18);
				X_Vo_Cong_Loai.LearnMartialArtsBook(this, 3, 19);
				break;
			}
			ItemUse(1, num2, 1);
			base.Player_ExpErience -= value.FLD_NEED_FIGHTEXP;
			base.Player_Money -= value.FLD_NEED_MONEY;
			LearningSkillsTips();
			UpdateMartialArtsAndStatus();
			UpdateMoneyAndWeight();
			UpdateKinhNghiemVaTraiNghiem();
			CapNhat_HP_MP_SP();
		}
		catch
		{
		}
	}

	private void CtrlAltClickDeleteItem(byte[] data)
	{
		int i14 = data[11];
			try
			{
				if (Item_In_Bag[i14].GetVatPham_ID != 0)
				{
					if (World.Itme.TryGetValue((int)Item_In_Bag[i14].GetVatPham_ID, out var value17))
					{
						RxjhClass.DelItemRecord(base.Userid, base.UserName, Item_In_Bag[i14].GetVatPham_ID, value17.ItmeNAME, Item_In_Bag[i14].FLD_MAGIC0, Item_In_Bag[i14].FLD_MAGIC1, Item_In_Bag[i14].FLD_MAGIC2, Item_In_Bag[i14].FLD_MAGIC3, Item_In_Bag[i14].FLD_MAGIC4, Item_In_Bag[i14].GetVatPhamSoLuong, "Xoa");
					}
					ManufacturingSubtractItems(i14, 1);
					Init_Item_In_Bag();
					UpdateMoneyAndWeight();
				}
				HeThongNhacNho("Vật phẩm ở vị trí " + i14, 10, "Đã xóa");
			}
			catch (Exception e)
			{
				Form1.WriteLine(1,"CtrlAltClickDeleteItem Error " + e.Message);
			}
		return;
	}
}