﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace RxjhServer;

public partial class Players
{
    
	public void KetNoi_DangNhap2(string id, string aa, string OriginalServerIP, string OriginalPort, string CoinIP, string SilverCoinPort, string OriginalServerSerialNumber, string OriginalServerID)
	{
		int num = 0;
		try
		{
			base.Client.Online = true;
			if (aa != null && !(aa == "Online"))
			{
				foreach (NetState value in World.list.Values)
				{
					if (base.Client != null && value != base.Client && value.Player.Userid == id)
					{
						value.Dispose();
						if (base.Client != null)
						{
							base.Client.Online = true;
						}
						if (base.Client != null)
						{
							base.Client.Dispose();
							Form1.WriteLine(100, "Disconnected![" + base.Userid + "]-[" + base.UserName + "][Mã dis 19]");
						}
						return;
					}
				}
				num = 1;
				base.Client.Online = true;
				base.Client.Login = true;
				DataTable dBToDataTable = DBA.GetDBToDataTable($"select  FLD_PASSWORD,FLD_RXPIONT,FLD_RXPIONTX,FLD_VIP,FLD_VIPTIM,FLD_COIN,FLD_SAFEWORD,FLD_LASTLOGINIP,FLD_Mail  from  [TBL_ACCOUNT]  where  FLD_ID=@Userid", new SqlParameter[1] { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, id) }, "rxjhaccount");
				num = 2;
				base.Password = dBToDataTable.Rows[0]["FLD_PASSWORD"].ToString();
				base.FLD_RXPIONT = (int)dBToDataTable.Rows[0]["FLD_RXPIONT"];
				base.FLD_RXPIONTX = (int)dBToDataTable.Rows[0]["FLD_RXPIONTX"];
				Form1.WriteLine(2, "FLD_RXPIONTX : " + base.FLD_RXPIONTX);
				base.FLD_Coin = (int)dBToDataTable.Rows[0]["FLD_COIN"];
				base.FLD_VIP = int.Parse(dBToDataTable.Rows[0]["FLD_VIP"].ToString());
				base.FLD_VIPTIM = DateTime.Parse(dBToDataTable.Rows[0]["FLD_VIPTIM"].ToString());
				base.lastloginip = dBToDataTable.Rows[0]["FLD_LASTLOGINIP"].ToString();
				string email = dBToDataTable.Rows[0]["FLD_Mail"].ToString();
				
				if (dBToDataTable.Rows[0]["FLD_SAFEWORD"].ToString().Length == 0)
				{
					GameSecurityCode = "********";
				}
				else
				{
					GameSecurityCode = dBToDataTable.Rows[0]["FLD_SAFEWORD"].ToString();
				}
				if (World.VIPLine == 1)
				{
					if (base.FLD_VIP != 1)
					{
						if (base.Client != null)
						{
							base.Client.Dispose();
							Form1.WriteLine(100, "Disconnected![" + base.Userid + "]-[" + base.UserName + "][Mã dis 20]");
						}
						return;
					}
					if (base.FLD_VIP == 1 && DateTime.Now > base.FLD_VIPTIM)
					{
						if (base.Client != null)
						{
							base.Client.Dispose();
							Form1.WriteLine(100, "Disconnected![" + base.Userid + "]-[" + base.UserName + "][Mã dis 21]");
						}
						return;
					}
				}
				base.OriginalServerSerialNumber = int.Parse(OriginalServerSerialNumber);
				base.OriginalServerIP = OriginalServerIP;
				base.OriginalServerPort = int.Parse(OriginalPort);
				base.OriginalServerID = int.Parse(OriginalServerID);
				base.SilverCoinSquareServerIP = CoinIP;
				base.SilverCoinSquareServerPort = int.Parse(SilverCoinPort);
				num = 3;
				byte[] array = Converter.HexStringToByte("aa550000c004020038000000000001000000000000000100000000000000010000000000f8916d88ffffffff78bb898600000000e80300000000000000000a5d6d0355aa");
				//System.Buffer.BlockCopy(BitConverter.GetBytes(51), 0, array, 22, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(World.ServerID - 1), 0, array, 26, 4);
				System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, array, 4, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(0), 0, array, 14, 2);
				num = 5;
				if (base.Client != null)
				{
					base.Client.Send_Map_Data(array, array.Length);
				}
				if (World.Encrypt2 == 1)
				{
					base.Client.Encryption = true;
				}
				Form1.WriteLine(3, "Kết nối -> Đăng nhập thành công [" + base.CharacterFullServerID + "]-[" + id.ToString() + "]");
				num = 6;
				dBToDataTable.Dispose();
				return;
			}
			foreach (NetState value2 in World.list.Values)
			{
				if (base.Client != null && value2 != base.Client && value2.Player.Userid == id)
				{
					value2.Dispose();
					break;
				}
			}
			if (base.Client != null)
			{
				base.Client.Dispose();
				Form1.WriteLine(100, "Disconnected![" + base.Userid + "]-[" + base.UserName + "][Mã dis 22]");
			}
		}
		catch (Exception ex)
		{
			if (base.Client != null)
			{
				base.Client.Dispose();
				Form1.WriteLine(100, "Disconnected![" + base.Userid + "]-[" + base.UserName + "][Mã dis 23][" + num + "]" + ex.ToString());
			}
			Form1.WriteLine(1, "KetNoi_DangNhap2()error：" + num + ex);
		}
	}
	public void SwitchChannelAccountLogin(string id, int ServerID, int SoKyTu, int WorldID, string BindAccount, string OriginalServerIP, string OriginalServerPort, string SilverSquareIP, string SilverSquarePort, string OriginalServerSerialNumber, string OriginalServerID, string NewServerID, string SealPackageLogin)
	{
		string str = "";
		try
		{
			DataTable dataTable = null;
			DataTable dataTable2 = null;
			base.Client.BindAccount = BindAccount;
			base.Client.VersionVerification = true;
			if (World.ChoPhep_TreoMay == 1)
			{
				base.Client.TreoMay = false;
			}
			base.Client.Online = true;
			base.Client.Login = true;
			dataTable = DBA.GetDBToDataTable("select  FLD_PASSWORD,FLD_RXPIONT,FLD_RXPIONTX,FLD_VIP,FLD_VIPTIM,FLD_COIN,FLD_SAFEWORD,FLD_ZT,FLD_LASTLOGINIP  from  [TBL_ACCOUNT]  where  FLD_ID=@Userid", new SqlParameter[1] { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, id) }, "rxjhaccount");
			if (dataTable == null && base.Client != null)
			{
				kickidlog("Change line account Login      error     table1==null");
				base.Client.Dispose();
				return;
			}
			if (SealPackageLogin != "1")
			{
				if (base.Client == null)
				{
					return;
				}
				OpClient(1);
				base.Client.Dispose();
			}
			str = "1";
			if ((int)dataTable.Rows[0]["FLD_ZT"] > 0)
			{
				dataTable.Dispose();
				dataTable = null;
				if (base.Client != null)
				{
					kickidlog("换线账号登陆      账号已停封");
					OpClient(1);
					base.Client.Dispose();
					return;
				}
			}
			str = "2";
			base.OriginalServerSerialNumber = int.Parse(OriginalServerSerialNumber);
			str = "3      变量值" + OriginalServerSerialNumber;
			base.OriginalServerIP = OriginalServerIP;
			try
			{
				base.OriginalServerPort = int.Parse(OriginalServerPort);
			}
			catch (Exception)
			{
				base.OriginalServerPort = World.GameServerPort;
			}
			try
			{
				base.OriginalServerID = int.Parse(NewServerID);
			}
			catch (Exception)
			{
				base.OriginalServerID = World.ServerID;
			}
			str = "5      变量值" + NewServerID;
			base.SilverCoinSquareServerIP = SilverSquareIP;
			try
			{
				base.SilverCoinSquareServerPort = int.Parse(SilverSquarePort);
			}
			catch (Exception)
			{
				base.SilverCoinSquareServerPort = World.GameServerPort;
			}
			str = "6      变量值" + SilverSquarePort;
			base.Userid = id;
			base.Password = dataTable.Rows[0]["FLD_PASSWORD"].ToString();
			base.FLD_RXPIONT = (int)dataTable.Rows[0]["FLD_RXPIONT"];
			base.FLD_RXPIONTX = (int)dataTable.Rows[0]["FLD_RXPIONTX"];
			base.FLD_Coin = (int)dataTable.Rows[0]["FLD_COIN"];
			base.FLD_VIP = int.Parse(dataTable.Rows[0]["FLD_VIP"].ToString());
			base.FLD_VIPTIM = DateTime.Parse(dataTable.Rows[0]["FLD_VIPTIM"].ToString());
			base.lastloginip = dataTable.Rows[0]["FLD_LASTLOGINIP"].ToString();
			if (dataTable.Rows[0]["FLD_SAFEWORD"].ToString().Length == 0)
			{
				GameSecurityCode = "********";
			}
			else
			{
				GameSecurityCode = dataTable.Rows[0]["FLD_SAFEWORD"].ToString();
			}
			dataTable.Dispose();
			if (World.VIPLine == 1)
			{
				if (base.FLD_VIP != 1)
				{
					if (base.Client != null)
					{
						kickidlog("非VIP登录VIP线路");
						base.Client.Dispose();
					}
					return;
				}
				if (base.FLD_VIP == 1 && DateTime.Now > base.FLD_VIPTIM)
				{
					if (base.Client != null)
					{
						kickidlog("VIP过期");
						OpClient(1);
						base.Client.Dispose();
					}
					return;
				}
			}
			str = "7";
			Form1.WriteLine(3, "Đô\u0309i sợi dây gắn kết tiếp đăng lục thành công [" + base.CharacterFullServerID + "]-[" + id.ToString() + "]");
			str = "8";
			dataTable2 = DBA.GetDBToDataTable("select  FLD_NAME,FLD_X,FLD_Y,FLD_MENOW  from  [TBL_XWWL_Char]  where  FLD_ID=@Userid  and  FLD_INDEX=@index", new SqlParameter[2]
			{
				SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, id),
				SqlDBA.MakeInParam("@index", SqlDbType.Int, 0, SoKyTu)
			});
			if (dataTable2 != null)
			{
				if (dataTable2.Rows.Count == 0 && base.Client != null)
				{
					Form1.WriteLine(1, "Đô\u0309i tuyến thu hoạch nhân vật phạm sai lầm 3, [" + base.Userid + "][" + base.UserName + "]");
					dataTable2.Dispose();
					base.Client.Dispose();
				}
				else
				{
					string string_12 = dataTable2.Rows[0]["FLD_NAME"].ToString();
					float num2 = float.Parse(dataTable2.Rows[0]["FLD_X"].ToString());
					float num3 = float.Parse(dataTable2.Rows[0]["FLD_Y"].ToString());
					int int_ = (int)dataTable2.Rows[0]["FLD_MENOW"];
					dataTable2.Dispose();
					new Thread(new ThreadWithState(this, string_12, num2.ToString(), num3.ToString(), int_).ThreadProc2).Start();
				}
			}
			else if (base.Client != null)
			{
				kickidlog("获取人物出错4");
				Form1.WriteLine(1, "Thu hoạch nhân vật phạm sai lầm 4, [" + base.Userid + "][" + base.UserName + "]");
				OpClient(1);
				base.Client.Dispose();
			}
		}
		catch (Exception ex4)
		{
			if (base.Client != null)
			{
				kickidlog("换线账号登陆出错");
				base.Client.Dispose();
			}
			Form1.WriteLine(1, "Đô\u0309i tuyến tài khoản đăng lục Phạm sai lầm Dấu hiệu:" + str + " Tin tức 2:" + ex4);
		}
	}
    public void KetNoi_DangNhap(byte[] PacketData, int PacketSize)
	{
		try
		{
			PacketReader packetReader = new PacketReader(PacketData, PacketSize, bool_0: false);
			packetReader.Seek(10, SeekOrigin.Begin);
			string text = packetReader.ReadString(29).Trim();
			if (!Regex.IsMatch(text, ""))
			{
				Form1.WriteLine(3, "Connection login error|" + Converter.ToString(PacketData));
				if (base.Client == null)
				{
				}
				return;
			}
			if (base.Userid.Length != 0)
			{
				Title(72, base.Userid, "UserName incorect");
				return;
			}
			Players players = World.KiemTraNguoiChoi(text);
			if (players != null)
			{
				if (players.Client.TreoMay)
				{
					World.OffLine_SoLuong--;
					if (World.OffLine_SoLuong < 0)
					{
						World.OffLine_SoLuong = 0;
					}
					players.Client.DisposedOffline();
				}
				else
				{
					players.Client.Dispose();
					players.HeThongNhacNho("Đã được đăng nhập ở nơi khác, hãy kiểm tra lại", 10, "Tài khoản");
				}
				base.Client.Dispose();
				return;
			}
			packetReader.Seek(94, SeekOrigin.Begin);
			string mac = packetReader.ReadString(16).Trim();
			packetReader.Seek(78, SeekOrigin.Begin);
			base.BindAccount = packetReader.ReadString(28).Replace("\0", string.Empty).Trim();
			base.UserIP = base.Client.ToString();
			if (DetectionLimit(base.BindAccount))
			{
				foreach (Players value in World.allConnectedChars.Values)
				{
					if (Buffer.IsEquals(value.BindAccount, base.BindAccount))
					{
						if (!value.Client.TreoMay)
						{
							value.Client.Dispose();
						}
						break;
					}
				}
			}
			MacAddress = mac;
			base.Userid = text;
			Form1.WriteLine(1, "Dia Chi IP: [" + base.UserIP + "]");
			World.conn.Transmit("用户登陆|" + text + "|" + base.Client.ToString() + "|" + World.ServerID + "|" + base.CharacterFullServerID + "|" + base.BindAccount + "|0|NULL");
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "KetNoi_DangNhaperror[" + base.CharacterFullServerID + "]-[" + base.Client.ToString() + "]" + ex.Message);
		}
	}

	public void KetNoi_DangNhapX(byte[] PacketData, int PacketSize)
	{
		try
        {
            PacketReader packetReader = new PacketReader(PacketData, PacketSize, bool_0: false);
            packetReader.Seek(10, SeekOrigin.Begin);
             packetReader.Seek(74, SeekOrigin.Begin);
            int clientVer = packetReader.ReadInt16();
            string text = packetReader.ReadString(29).Trim();
            packetReader.Seek(78, SeekOrigin.Begin);
            string bindAccount = packetReader.ReadString(28).Replace("\0", string.Empty).Trim();
            packetReader.Seek(94, SeekOrigin.Begin);
            string mac = packetReader.ReadString(16).Trim();
            LoginPlayerX(text,clientVer,bindAccount,mac);
        }
        catch (Exception ex)
		{
			Form1.WriteLine(1, "KetNoi_DangNhaperror[" + base.CharacterFullServerID + "]-[" + base.Client.ToString() + "]" + ex.Message);
		}
	}

    public void LoginPlayerX( string text,int clientVer, string bindAccount, string userMac)
    {
        base.BindAccount = bindAccount;
        MacAddress = userMac;
        if (!Regex.IsMatch(text, ""))
        {
            //Form1.WriteLine(3, "Connection login error|" + Converter.ToString(PacketData));
            if (base.Client == null)
            {
            }
            return;
        }
        if (base.Userid.Length != 0)
        {
            Title(72, base.Userid, "UserName incorect");
            return;
        }
        Players players = World.KiemTraNguoiChoi(text);
        if (players != null)
        {
            if (players.Client.TreoMay)
            {
                World.OffLine_SoLuong--;
                if (World.OffLine_SoLuong < 0)
                {
                    World.OffLine_SoLuong = 0;
                }
                players.Client.DisposedOffline();
            }
            else
            {
                players.Client.Dispose();
                players.HeThongNhacNho("Đã được đăng nhập ở nơi khác, hãy kiểm tra lại", 10, "Tài khoản");
            }
            base.Client.Dispose();
            return;
        }

        base.UserIP = base.Client.ToString();
        if (DetectionLimit(base.BindAccount))
        {
            foreach (Players value in World.allConnectedChars.Values)
            {
                if (Buffer.IsEquals(value.BindAccount, base.BindAccount))
                {
                    if (!value.Client.TreoMay)
                    {
                        value.Client.Dispose();
                    }
                    break;
                }
            }
        }
        base.Userid = text;
        Form1.WriteLine(1, "Dia Chi IP: [" + base.UserIP + "]");
        World.conn.Transmit("用户登陆|" + text + "|" + base.Client.ToString() + "|" + World.ServerID + "|" + base.CharacterFullServerID + "|" + base.BindAccount + "|0|NULL");
    }

    public void CharacterRelog()
	{
		try
		{
			SaveCharacterData();
			PhaiChang_NhanVatDaDangNhap = false;
			Exiting = true;
			Thread.Sleep(3000);
			Logout();
			byte[] array = Converter.HexStringToByte("AA5512000C035700040004000000000000000000000055AA");
			byte[] array2 = Converter.HexStringToByte("AA5516000C03630008000100000000000001000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, array, 4, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, array2, 4, 2);
			if (base.Client != null)
			{
				base.Client.Send_Map_Data(array, array.Length);
			}
			if (base.Client != null)
			{
				base.Client.Send_Map_Data(array2, array2.Length);
			}
		}
		catch
		{
			HeThongNhacNho("Loi relog nhan vat !");
			Form1.WriteLine(1, "Loi relog nhan vat! [" + base.Userid + "] - [" + base.UserName + "]");
		}
	}
public void DangXuat(byte[] data, int length)
    {
        PacketModification(data, length);
		PhaiChang_NhanVatDaDangNhap = false;
		Exiting = true;
        Logout();
        byte[] array = Converter.HexStringToByte("AA553800000004002A00010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
        byte[] array2 = new byte[base.Userid.Length];
        byte[] bytes = Encoding.GetEncoding(World.Language_Charset).GetBytes(base.Userid);
        if(base.UserName.Length > 0)
        {
            byte[] array3 = new byte[base.UserName.Length];
            byte[] bytes2 = Encoding.GetEncoding(World.Language_Charset).GetBytes(base.UserName);
            System.Buffer.BlockCopy(bytes2, 0, array, 18, bytes2.Length);
        }
        System.Buffer.BlockCopy(bytes, 0, array, 31, bytes.Length);
        System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, array, 4, 2);
        base.Client?.Send_Map_Data(array, array.Length);
        if(base.Client != null)
        {
            base.Client.Dispose();
            Form1.WriteLine(100, "Disconnected![" + base.Userid + "]-[" + base.UserName + "][Mã dis 38]");
        }
    }
	public void DangXuat2(byte[] data, int length)
	{
		PacketModification(data, length);
		PhaiChang_NhanVatDaDangNhap = false;
		Exiting = true;
		Logout();
		byte[] array = Converter.HexStringToByte("AA553800000004002A00010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		byte[] bytes = Encoding.Default.GetBytes(base.Userid);
		System.Buffer.BlockCopy(bytes, 0, array, 31, bytes.Length);
		System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, array, 4, 2);
		if (base.Client != null)
		{
			base.Client.Send_Map_Data(array, array.Length);
		}
	}
	public void Send361()
	{
		base.Client.VersionVerification = true;
		byte[] array2 = Converter.HexStringToByte("aa550000fc0069010200060055aa");
		System.Buffer.BlockCopy(Buffer.GetBytes(base.CharacterFullServerID), 0, array2, 4, 2);
		base.Client?.Send_Map_Data(array2, array2.Length);
	}

	public void GetAListOfPeople(byte[] data, int length)
    {
        try
        {
            if (data.Length >0)
                PacketModification(data, length);
            Send361();
            base.UserName = string.Empty;
            allChars = new Dictionary<int, string>();
            byte[] src = new byte[World.Item_Db_Byte_Length];
            byte[] array = Converter.HexStringToByte("AA550B00D80311000500FF45859A0455AA");
            //string initialPacket="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";
            string initialPacket = "aa550000c0041100310f0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055aa";
            // string initialPacket =
            //     "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";
            //
            //     byte[] arrayx = Converter.HexStringToByte(initialPacket);
            //     System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, arrayx, 4, 2);
            //     Client?.SendX(arrayx,arrayx.Length);
            //     return;
                

            DataTable dBToDataTable = DBA.GetDBToDataTable("select  *  from  [TBL_XWWL_Char]  where  FLD_ID=@Userid ORDER BY FLD_INDEX ASC", new SqlParameter[1] { SqlDBA.MakeInParam("@Userid", SqlDbType.VarChar, 30, base.Userid) });

            if(dBToDataTable == null || dBToDataTable.Rows.Count == 0)
            {
                base.Client?.Send_Map_Data(array, array.Length);
                return;
            }

            if(dBToDataTable.Rows.Count > 4)
            {
                Form1.WriteLine(1, $"Lỗi nhân vật quá số lượng tk: [{base.Userid}] name [{base.UserName}] CharacterFullServerID=[{base.CharacterFullServerID}] [{base.Client}] 数[{dBToDataTable.Rows.Count}]");
                dBToDataTable.Dispose();
                base.Client.Dispose();
                return;
            }

            foreach(DataRow row in dBToDataTable.Rows)
            {
                int FLD_INDEX = (int)row["FLD_INDEX"];
                string FLD_NAME = row["FLD_NAME"].ToString();
                if(allChars.ContainsKey(FLD_INDEX))
                {
                    allChars.Remove(FLD_INDEX);
                }
                allChars.Add(FLD_INDEX, FLD_NAME);

                byte[] sendingData = Converter.HexStringToByte(initialPacket);
                System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, sendingData, 4, 2);

                byte[] character_name = Encoding.GetEncoding(1252).GetBytes(FLD_NAME);

                BufferBlockCopyData(sendingData, row, FLD_INDEX, character_name);

                base.Client?.Send_Map_Data(sendingData, sendingData.Length);
            }

            dBToDataTable.Dispose();
        } catch(Exception ex)
        {
            Form1.WriteLine(1, $"GetAListOfPeopleerror[{base.Userid}] {ex.Message}");
        }
    }
	
	private static void BufferBlockCopyData(byte[] sendingData, DataRow row, int FLD_INDEX, byte[] character_name)
    {
        int offset = 10;
        System.Buffer.BlockCopy(BitConverter.GetBytes(FLD_INDEX), 0, sendingData, offset, 1);
        System.Buffer.BlockCopy(character_name, 0, sendingData, offset+1, character_name.Length);
        offset = 49;
        System.Buffer.BlockCopy(BitConverter.GetBytes((int)row["FLD_ZX"]), 0, sendingData, offset, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes((int)row["FLD_LEVEL"]), 0, sendingData, offset + 2, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes((int)row["FLD_JOB_LEVEL"]), 0, sendingData, offset + 4, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes((int)row["FLD_JOB"]), 0, sendingData, offset + 6, 1);

        var characterTemplate = new X_Character_Template_Class((byte[])row["FLD_FACE"]);
        System.Buffer.BlockCopy(BitConverter.GetBytes(characterTemplate.MauToc), 0, sendingData, offset + 7, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(characterTemplate.KieuToc), 0, sendingData, offset + 9, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(characterTemplate.GioiTinh), 0, sendingData, offset+21, 1);
        byte[] character_x = BitConverter.GetBytes(float.Parse(row["FLD_X"].ToString()));
        byte[] character_y = BitConverter.GetBytes(float.Parse(row["FLD_Y"].ToString()));
        byte[] character_z = BitConverter.GetBytes(float.Parse(row["FLD_Z"].ToString()));
        byte[] character_map = BitConverter.GetBytes((int)row["FLD_MENOW"]);

        System.Buffer.BlockCopy(character_x, 0, sendingData, offset+22, 4);

        byte[] dst = sendingData;
        System.Buffer.BlockCopy(character_z, 0, dst, offset+26, 4);

        byte[] dst2 = sendingData;
        System.Buffer.BlockCopy(character_y, 0, dst2, offset+30, 4);

        System.Buffer.BlockCopy(character_map, 0, sendingData, offset+34, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(Math.Min((int)row["FLD_HP"], 30000)), 0, sendingData, offset+118, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(Math.Min((int)row["FLD_MP"], 30000)), 0, sendingData, offset+120, 2);

        FillCharacterWearItems(sendingData, (byte[])row["FLD_WEARITEM"]);
    }

    private static void FillCharacterWearItems(byte[] sendingData, byte[] character_wearitem)
    {
        byte[] src = new byte[World.Item_Db_Byte_Length];

        for(int j = 0; j < 15; j++)
        {
            try
            {
                byte[] itemData = new byte[12];
                System.Buffer.BlockCopy(character_wearitem, j * World.Item_Db_Byte_Length, itemData, 0, 12);
                int itemId = BitConverter.ToInt32(itemData, 8);
               // Form1.WriteLine(1, $"{j} {itemId}");
                if(itemId != 0)
                {
                    if(j == 11 && World.Itme[itemId].FLD_SERIES == 1)
                    {
                        itemId = GetNextItemId(itemId);
                    } else if(World.Itme[itemId].FLD_INTEGRATION == 1)
                    {
                        itemId -= 5000;
                    }
                    System.Buffer.BlockCopy(BitConverter.GetBytes(itemId), 0, itemData, 8, 4);
                }

                System.Buffer.BlockCopy(itemData, 0, sendingData, 203 + j * World.Item_Byte_Length_92, 12);
                System.Buffer.BlockCopy(character_wearitem, j * World.Item_Db_Byte_Length + 12, sendingData, 203 + j * World.Item_Byte_Length_92 + 16, 60);
            } catch
            {
                System.Buffer.BlockCopy(src, 0, sendingData, 203 + j * World.Item_Byte_Length_92, 72);
            }
        }
    }
    
    private static int GetNextItemId(int itemId)
    {
	    itemId = int.Parse(itemId.ToString().Remove(7) + "0");
	    for(var k = 0; k < 7; k++)
	    {
		    if(World.Itme.TryGetValue(itemId, out var value) && value.FLD_SERIES == 2)
		    {
			    itemId = value.FLD_PID;
			    break;
		    }
		    itemId++;
	    }
	    return itemId >= 16900830 && itemId <= 16900836 ? 16900832 : itemId;
    }

	public void BackToPeopleList(byte[] 封包数据, int 封包大小)
	{
		PhaiChang_NhanVatDaDangNhap = false;
		Exiting = true;
		Logout();
		byte[] array = Converter.HexStringToByte("AA5512000C035700040004000000000000000000000055AA");
		byte[] array2 = Converter.HexStringToByte("AA5516000C03630008000100000000000001000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, array, 4, 2);
		System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, array2, 4, 2);
		if (base.Client != null)
		{
			base.Client.Send_Map_Data(array, array.Length);
		}
		if (base.Client != null)
		{
			base.Client.Send_Map_Data(array2, array2.Length);
		}
	}
	public void CharacterLogin(byte[] data, int length)
	{
		int num2 = 0;
		DataTable dataTable = null;
		PacketModification(data, length);
		int num3 = 0;
		if (base.Client != null && !base.Client.Login)
		{
			if (base.Client != null)
			{
				base.Client.Dispose();
			}
			return;
		}
		try
        {
            if (World.allConnectedChars.TryGetValue(base.CharacterFullServerID, out var _))
            {
                Title(72, base.Userid, "BanedLogin");
                return;
            }
            if (PhaiChang_NhanVatDaDangNhap)
            {
                Title(72, base.Userid, "BannedLogin2");
                return;
            }
            PhaiChang_NhanVatDaDangNhap = true;
            num3 = 1;
            
            num3 = 2;
            int characterIndex = data[10];
            HandleCharacterLogin(characterIndex);
        }
        catch (Exception ex)
		{
			Form1.WriteLine(100, "Nhân vật đăng lục phạm sai lầm 2[" + base.Userid + "]-[" + base.UserName + "] " + num3 + " " + ex.Message);
			if (base.Client != null)
			{
				base.Client.Dispose();
			}
		}
	}

    public void HandleCharacterLogin( int characterIndex)
    {
        if (!allChars.TryGetValue(characterIndex, out var charaterId))
        {
            base.Client?.Dispose();
            return;
        }
        base.UserName = charaterId;
        if (base.UserName.Length == 0)
        {
            base.Client.Dispose();
        }
        if (base.UserName.Length > 14)
        {
            Form1.WriteLine(99, "Phi pháp nhân vật tên [" + base.Userid + "][" + base.UserName + "] [" + base.Client.ToString() + "]");
            base.Client.Dispose();
        }
        ReadCharacterData();
        HonoraryTitle();
        ObtainTheAttributeOfTheRoseTitle();
        LoadTitleRank();
        ServerTime();

        //Send12579();
        //Send4116(); // Send 1971-1-1 0:00
        //Send8596();

        Detection();
        KhoiTaoKhiCong();
        Init_Item_In_Bag();
        UpdateNTCBag();
        if (PhaiChangMangTheoAoChang_HanhLy)
        {
            KhoiTaoAoChang_HanhLy();
        }
        InitEventBag();
        GuiDi_NhiemVu_VatPham_List();
        LoadCharacterWearItem();
        KhoiTaoAoChang_HanhLy();
        SetPersonalMedicine();
        UpdateMoneyAndWeight();
        NumberOfSpiritBeasts();
        SetPublicGoods();
        setThoiGianVatPham();
        SetTitleItems();
        SetAdditionalStatusItems();
        CalculateCharacterEquipmentData();
        UpdateCharacterTask2(0);
        SendCompletedTaskData();
        SendEarthenTalismanData();
        //Display();
        //Case202();
        if (Logoin())
        {
            Online = true;
            UpdateProductionSystem();
            GetReviewScopeNpc();
            GetTheReviewRangePlayers();
            ObtainGroundObjectsInTheReviewArea();
            if (base.GangId != 0)
            {
                ChaGang();
            }
            InitializeCareerSkills();
            if (MasterData.TID != -1)
            {
                ApprenticeUpdateMentoringSystem();
            }
            else
            {
                for (int num2 = 0; num2 < 3; num2++)
                {
                    if (ApprenticeData[num2].TID != -1)
                    {
                        MasterUpdatesTheMentoringSystem(num2);
                    }
                    CalculateMentorAndApprenticeAttributes(num2);
                }
            }
            UpdateHonor();
            if (World.CoHayKhongMoRaGiftCode == 1)
            {
                KiemTra_MaGiftCode();
            }
            ViewBiography();
            ShowHelpCommand();
            UpdateMovementSpeed();
            Other();
            UpdateCharacterData(this);
            //	UpdateVoHuanEffect();
            UpdateMartialArtsAndStatus();
            CapNhat_HP_MP_SP();
            NoviceOnline();
            UpdateKinhNghiemVaTraiNghiem();
            Kiem_tra_dia_do();
            CheckMaritalStatus();
            UpdateMartialArtsCoolDown();
            LoadCongThanhChienData();
            SerList.Clear();
            Connect conn2 = World.conn;
            string string_2 = "PlayerGetServerList|" + base.Userid + "|" + base.OriginalServerSerialNumber + "|" + base.OriginalServerIP + "|" + base.OriginalServerPort + "|" + base.OriginalServerID;
            conn2.Transmit(string_2);
            Connect conn = World.conn;
            string string_ = "Check UserID|" + base.Userid + "|" + base.OriginalServerSerialNumber + "|" + base.OriginalServerIP + "|" + base.OriginalServerPort + "|" + base.OriginalServerID;
            conn.Transmit(string_);
            Ket_hon_BUFF();
            AoChoangG7();
            PhaiChang_NhanVatDaDangNhap = true;
            XacDinhXem_CoDangNhap_BangPacketHayKhong = 1;
            foreach (X_Cong_Thanh_So_Lieu value15 in World.CongThanhSoLieu_list.Values)
            {
                var dataTable = DBA.GetDBToDataTable("select  *  from  [CongThanhChien_ThanhChu]  ");
                HeThongNhacNho("Thiên Ma Thần Cung do bang <<" + dataTable.Rows[0]["CongThanhChien_TenBang"].ToString() + ">> chiếm lĩnh!", 10, ":");
                if (value15.CongThanhChien_TenBang == base.GangName && DateTime.Now < value15.ThienMaCongThanhChienBanThuongThoiGian)
                {
                    if (!AppendStatusList.ContainsKey(**********))
                    {
                        long ticks = value15.ThienMaCongThanhChienBanThuongThoiGian.ToUniversalTime().Ticks;
                        long ticks2 = DateTime.Now.ToUniversalTime().Ticks;
                        long num4 = (ticks - ticks2) / 10000;
                        X_Them_Vao_Trang_Thai_Loai ThemVaoTrangThaiClass = new X_Them_Vao_Trang_Thai_Loai(this, num4, **********.0, 1.0);
                        AppendStatusList.Add(ThemVaoTrangThaiClass.FLD_PID, ThemVaoTrangThaiClass);
                        StatusEffect(BitConverter.GetBytes(**********), 1, (int)num4);
                    }
                    HeThongNhacNho("Thời gian chiếm lĩnh:" + value15.ThienMaCongThanhChienBanThuongThoiGian.ToString("yyyy:Năm/MM:Tháng/dd:Ngày  hh:Giờ/mm:Phút"), 9, ":");
                }
                else if (value15.CongThanhChien_TenBang != base.GangName && AppendStatusList.ContainsKey(**********))
                {
                    AppendStatusList[**********].ThoiGianKetThucSuKien();
                }
                dataTable.Dispose();
            }
            ResetMap();
            CapnhatdiemthuongGuild(1);
            if ((PublicDrugs == null || !PublicDrugs.ContainsKey(1008000312)) && base.FLD_VIPTIM > DateTime.Now)
            {
                base.FLD_VIP = 1;
                DateTime value13 = new DateTime(1970, 1, 1, 7, 0, 0);
                TimeSpan timeSpan13 = base.FLD_VIPTIM.Subtract(value13);
                X_Cong_Huu_Duoc_Pham_Loai PublicDrugsClass13 = new X_Cong_Huu_Duoc_Pham_Loai();
                PublicDrugsClass13.DuocPhamID = 1008000312;
                PublicDrugsClass13.ThoiGian = (int)timeSpan13.TotalSeconds;
                PublicDrugs.Add(PublicDrugsClass13.DuocPhamID, PublicDrugsClass13);
                SetPublicGoods(PublicDrugsClass13);
            }
            TeleCharacter(base.NhanVatToaDo_X + 1f, base.NhanVatToaDo_Y + 1f, 15f);
            BatTuHieuUng(8000);
            if (World.ThietLap_ThuHoiVatPham_PID != null)
            {
                for (int i = 0; i < World.ThietLap_ThuHoiVatPham_PID.Length; i++)
                {
                    TuDongThuHoiVatPham(int.Parse(World.ThietLap_ThuHoiVatPham_PID[i]));
                }
            }
            Display();
            send4325();
        }
        else if (base.Client != null)
        {
            Form1.WriteLine(100, "Nhân vật đô\u0309i tuyến đăng lục phạm sai lầm");
            OpClient(1);
            base.Client.Dispose();
        }
    }

    private void Send12579()
    {
        byte[] array = Converter.HexStringToByte("aa5500000000233140000200000001000000b022924fc0d3944f020000004092914140c19e4100000000000000000000000000000000000000000000000000000000000000000000000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(CharacterFullServerID), 0, array, 4, 2);
        Client?.Send_Map_Data(array, array.Length);
    }
	    private void Send4116()
    {
        byte[] array = Converter.HexStringToByte("aa550000ee051410250009000000000000000000ffff000001001100313937312d30312d30312030303a303000010055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(CharacterFullServerID), 0, array, 4, 2);
        Client?.Send_Map_Data(array, array.Length);
    }
	    private void Send8596()
    {
         byte[] array = Converter.HexStringToByte("aa550000ee05302104007e8f696755aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(CharacterFullServerID), 0, array, 4, 2);
        Client?.Send_Map_Data(array, array.Length);
    }
	 private void Case202()
    {
        byte[] array = Converter.HexStringToByte("aa550000ee05ca00150001000000ef040000ef04000007004c4348204b313055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(CharacterFullServerID), 0, array, 4, 2);
        Client?.Send_Map_Data(array, array.Length);
    }
	    public void send4325()
    {
       var array = Converter.HexStringToByte("AA 55 00 00 EE 05 8B 10 0C 00 B9 01 00 00 EE 05 00 00 01 00 00 00 55 AA".Replace(" ", "")); //4325
        System.Buffer.BlockCopy(Buffer.GetBytes(base.CharacterFullServerID), 0, array, 4, 2); //1212
        base.Client?.Send_Map_Data(array, array.Length);
    }
    private void LoadTitleRank()
    {
        if (TitlePoints >= int.Parse(World.ToiCaoPhanThuong_DanhHieu[0]))
        {
            base.TitleAddedAttack = int.Parse(World.ToiCaoPhanThuong_DanhHieu[1]);
            base.TitleAddedDefense = int.Parse(World.ToiCaoPhanThuong_DanhHieu[2]);
            base.TitleAddedHP = int.Parse(World.ToiCaoPhanThuong_DanhHieu[3]);
            base.FLD_HonorID = 601;
            GetTheTitleType(1008001567, 1);
        }
        else if (TitlePoints >= int.Parse(World.VoSongPhanThuong_DanhHieu[0]))
        {
            base.TitleAddedAttack = int.Parse(World.VoSongPhanThuong_DanhHieu[1]);
            base.TitleAddedDefense = int.Parse(World.VoSongPhanThuong_DanhHieu[2]);
            base.TitleAddedHP = int.Parse(World.VoSongPhanThuong_DanhHieu[3]);
            base.FLD_HonorID = 602;
            GetTheTitleType(1008001568, 1);
        }
        else if (TitlePoints >= int.Parse(World.ThongTriPhanThuong_DanhHieu[0]))
        {
            base.TitleAddedAttack = int.Parse(World.ThongTriPhanThuong_DanhHieu[1]);
            base.TitleAddedDefense = int.Parse(World.ThongTriPhanThuong_DanhHieu[2]);
            base.TitleAddedHP = int.Parse(World.ThongTriPhanThuong_DanhHieu[3]);
            base.FLD_HonorID = 603;
            GetTheTitleType(1008001569, 1);
        }
        else if (TitlePoints >= int.Parse(World.AnhHungPhanThuong_DanhHieu[0]))
        {
            base.TitleAddedAttack = int.Parse(World.AnhHungPhanThuong_DanhHieu[1]);
            base.TitleAddedDefense = int.Parse(World.AnhHungPhanThuong_DanhHieu[2]);
            base.TitleAddedHP = int.Parse(World.AnhHungPhanThuong_DanhHieu[3]);
            base.FLD_HonorID = 604;
            GetTheTitleType(1008001570, 1);
        }
        else if (TitlePoints >= int.Parse(World.HaoKietPhanThuong_DanhHieu[0]))
        {
            base.TitleAddedAttack = int.Parse(World.HaoKietPhanThuong_DanhHieu[1]);
            base.TitleAddedDefense = int.Parse(World.HaoKietPhanThuong_DanhHieu[2]);
            base.TitleAddedHP = int.Parse(World.HaoKietPhanThuong_DanhHieu[3]);
            base.FLD_HonorID = 605;
            GetTheTitleType(1008001571, 1);
        }
    }
}