﻿using System;
using System.Collections.Generic;

namespace RxjhServer
{
     public partial class Players
    {
        private bool isSortAscending = true;
        private bool isSortAscending1 = true;
        private bool isSortAscending2 = true;
        private void RearrangeItem(byte[] array)
        {
            var bagType = array[10];
            switch (bagType)
            {
                case 0:
                    // TUi do
                    SortItems(ref Item_In_Bag, ref isSortAscending);
                    Init_Item_In_Bag();
                    break;
                case 1:
                    SortItems(ref PersonalWarehouse, ref isSortAscending1);
                    OpenPersonalWarehouse();
                    // Kho rieng
                    break;
                case 2:
                    SortItems(ref PublicWarehouse, ref isSortAscending2);
                    OpenTheComprehensiveWarehouse();
                    // Kho Chung
                    break;
            }

            return;
        }
 
        // Tính toán thứ tự ưu tiên của vật phẩm
        private long GetItemPriority(X_Vat_Pham_Loai item)
        {
            // Vật phẩm trống có độ ưu tiên thấp nhất
            if (item.GetVatPham_ID == 0)
                return long.MaxValue;
            
            // Ưu tiên theo nhóm, loại, và ID
            return (long)item.FLD_RESIDE1 * 1000000 + (long)item.FLD_RESIDE2 * 1000 + item.GetVatPham_ID;
        }
        
        // So sánh hai vật phẩm
        private int CompareItems(X_Vat_Pham_Loai x, X_Vat_Pham_Loai y, bool ascending)
        {
            // Vật phẩm trống luôn xếp cuối
            if (x.GetVatPham_ID == 0 && y.GetVatPham_ID != 0)
                return 1;
            if (x.GetVatPham_ID != 0 && y.GetVatPham_ID == 0)
                return -1;
                
            // Cả hai đều trống
            if (x.GetVatPham_ID == 0 && y.GetVatPham_ID == 0)
                return 0;
            
            // So sánh theo thứ tự các trường
            if (ascending)
            {
                if (x.FLD_RESIDE1 != y.FLD_RESIDE1)
                    return x.FLD_RESIDE1.CompareTo(y.FLD_RESIDE1);
                
                if (x.FLD_RESIDE2 != y.FLD_RESIDE2)
                    return x.FLD_RESIDE2.CompareTo(y.FLD_RESIDE2);
                
                return x.GetVatPham_ID.CompareTo(y.GetVatPham_ID);
            }
            else
            {
                if (x.FLD_RESIDE1 != y.FLD_RESIDE1)
                    return y.FLD_RESIDE1.CompareTo(x.FLD_RESIDE1);
                
                if (x.FLD_RESIDE2 != y.FLD_RESIDE2)
                    return y.FLD_RESIDE2.CompareTo(x.FLD_RESIDE2);
                
                return y.GetVatPham_ID.CompareTo(x.GetVatPham_ID);
            }
        }

        private void SortItems(ref X_Vat_Pham_Loai[] items, ref bool isSortAscending)
        {
            // Đảo trạng thái sắp xếp
            isSortAscending = !isSortAscending;
    
            // Sắp xếp thủ công bằng Insertion Sort
            if (isSortAscending)  // Sắp xếp tăng dần (ascending)
            {
                for (int i = 1; i < items.Length; i++)
                {
                    X_Vat_Pham_Loai key = new X_Vat_Pham_Loai { VatPham_byte = items[i].VatPham_byte }; // Tạo bản sao mới
                    int j = i - 1;
            
                    // Di chuyển các phần tử lớn hơn key đến vị trí sau
                    while (j >= 0 && CompareItems(items[j], key, isSortAscending) > 0)
                    {
                        items[j + 1].VatPham_byte = items[j].VatPham_byte; // Chỉ gán VatPham_byte
                        j--;
                    }
                    items[j + 1].VatPham_byte = key.VatPham_byte; // Gán lại VatPham_byte của key
                }
            }
            else  // Sắp xếp giảm dần (descending)
            {
                for (int i = 1; i < items.Length; i++)
                {
                    X_Vat_Pham_Loai key = new X_Vat_Pham_Loai { VatPham_byte = items[i].VatPham_byte }; // Tạo bản sao mới
                    int j = i - 1;
            
                    // Di chuyển các phần tử nhỏ hơn key lên trước
                    while (j >= 0 && CompareItems(items[j], key, isSortAscending) < 0)
                    {
                        items[j + 1].VatPham_byte = items[j].VatPham_byte; // Chỉ gán VatPham_byte
                        j--;
                    }
                    items[j + 1].VatPham_byte = key.VatPham_byte; // Gán lại VatPham_byte của key
                }
            }
        }
    }
}
