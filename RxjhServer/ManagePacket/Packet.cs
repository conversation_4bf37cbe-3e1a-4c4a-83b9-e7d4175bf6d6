﻿using System;
using System.Linq;

namespace RxjhServer;

public partial class Players
{
    public void ManagePacket(byte[] clientsend, int length)
    {
        length -= 2;
        var data = new byte[length];
        System.Buffer.BlockCopy(clientsend, 0, data, 0, 6);
        System.Buffer.BlockCopy(clientsend, 8, data, 6, data.Length - 6);
        int Opcode = BitConverter.ToInt16(data, 6);
        if (GMMode == 1) HeThongNhacNho("Debug[" + Opcode + "]");
        System.Buffer.BlockCopy(BitConverter.GetBytes(Client.WorldId), 0, data, 4, 2);
        try
        {
            if (!World.allConnectedChars.TryGetValue(CharacterFullServerID, out var _))
            {
                switch (Opcode)
                {
                    case 20:
                        <PERSON><PERSON><PERSON><PERSON>cter(data, length);
                        break;
                    case 16:
                        GetAListOfPeople(data, length);
                        break;
                    case 1:
                        Client.VersionVerification = true;
                        KetNoi_DangNhap(data, length);
                        break;
                    case 3:
                        Dang<PERSON>uat(data, length);
                        break;
                    case 5:
                        CharacterLogin(data, length);
                        break;
                    case 143:
                        Display();
                        break;
                    case 56:
                        KiemTraNhanVat_CoTonTaiHayKhong(data, length);
                        break;
                    case 30:
                        XoaBoNhanVat(data, length);
                        break;
                    case 371:
                        XoaBoNhanVat(data, length);
                        break;
                    case 836:
                        XacMinhThongTinDangNhapID(data, length);
                        break;
                    case 218:
                        SwitchChannelVerification(data, length);
                        break;
                    case 211:
                        SwitchChannelVerification(data, length);
                        break;
                    case 16666:
                        IsAttackConfirmation(data, length);
                        break;
                    case 5638:
                    case 8212:
                        VersionVerification(data, length);
                        break;
                }

                return;
            }

            if (!ConnectionSucceeded)
			{
             	throw new Exception("Connection not succeeded");   
			}

            switch (Opcode)
            {
                case 950:
                    DeleteBuff(data);
                    break;
                case 14:
                    Nem_vat_pham(data, length);
                    break;
                case 3:
                    DangXuat(data, length);
                    break;
                case 7:
                case 1293:
                    if (TrangThai_BatThuong.ContainsKey(4) || TrangThai_BatThuong.ContainsKey(8) ||
                        TrangThai_BatThuong.ContainsKey(26) || TrangThai_BatThuong.ContainsKey(24) ||
                        TrangThai_BatThuong.ContainsKey(23))
                    {
                        HeThongNhacNho("Trạng thái không thể di chuyển");
                        return;
                    }

                    if (AutomaticAttack != null)
                    {
                        AutomaticAttack.Enabled = false;
                        AutomaticAttack.Close();
                        AutomaticAttack.Dispose();
                        AutomaticAttack = null;
                    }

                    CharacterMove(data, length);
                    GetReviewScopeNpc();
                    break;
                case 8:
                    Speak(data, length);
                    break;
                case 9:
                    time_AFK = DateTime.Now;
                    ThoiGianXacNhanTanCong = (int)DateTime.Now.Subtract(ThoiGianTanCong).TotalMilliseconds;
                    Attack(data, length);
                    break;
                case 11:
                    PickUpItems(data, length);
                    break;
                case 26:
                    ChangeEquipment(data, length);
                    break;
                case 22:
                    UpdateConfiguration(data, length);
                    break;
                case 16:
                    GetAListOfPeople(data, length);
                    break;
                case 40:
                    CaptainManagement(data, length);
                    break;
                case 36:
                    if (World.KepSkillConfig == 1 && CharacterPKMode != 0)
                    {
                        Kep_skill = 1;
                        WalkingState(BitConverter.GetBytes(1), 1);
                        ActionExpression(data, length);
                        break;
                    }

                    if (!checkkepskill)
                    {
                        ActionExpression(data, length);
                        break;
                    }

                    HeThongNhacNho("Hiện không thể sử dụng", 50);
                    return;
                case 67:
                    Tang_Diem_Khi_Cong(data, length);
                    break;
                case 48:
                    SendTeam(data, length);
                    break;
                case 50:
                    ICancelTheTeam(data, length);
                    break;
                case 52:
                    TheOpponentCancelsTheTeam(data, length);
                    break;
                case 54:
                    LeaveTheTeam(data, length);
                    break;
                case 56:
                    KiemTraNhanVat_CoTonTaiHayKhong(data, length);
                    break;
                case 58:
                    OpenItem(data, length);
                    break;
                case 60:
                    Khinh_cong(data, length);
                    break;
                case 42:
                    TeamItemAllocationRules(data, length);
                    break;
                case 72:
                    if (PublicDrugs.ContainsKey(1008000312))
                    {
                        if (PKTuVong)
                        {
                            PKTuVong = false;
                            if (InvincibleTimeCounter != null)
                            {
                                InvincibleTimeCounter.Enabled = false;
                                InvincibleTimeCounter.Close();
                                InvincibleTimeCounter.Dispose();
                            }

                            Player_VoDich = true;
                            InvincibleTimeCounter = new System.Timers.Timer(1500.0);
                            InvincibleTimeCounter.Elapsed += TimeEndEvent1;
                            InvincibleTimeCounter.Enabled = true;
                            InvincibleTimeCounter.AutoReset = false;
                            IsPlayerDeath = false;
                            PlayerTuVong = false;
                            foreach (var item in World.DiDong)
                                if (item.Rxjh_Map == NhanVatToaDo_BanDo)
                                {
                                    DeathMove(item.Rxjh_X, item.Rxjh_Y, item.Rxjh_Z, item.Rxjh_Map);
                                    BatTuHieuUng(5000);
                                    break;
                                }
                        }
                        else
                        {
                            Tu_vong_ve_thanh(data, length);
                        }
                    }
                    else
                    {
                        IsPlayerDeath = false;
                        PlayerTuVong = false;
                        foreach (var item2 in World.DiDong)
                            if (item2.Rxjh_Map == NhanVatToaDo_BanDo)
                            {
                                DeathMove(item2.Rxjh_X, item2.Rxjh_Y, item2.Rxjh_Z, item2.Rxjh_Map);
                                BatTuHieuUng(5000);
                                break;
                            }
                    }

                    NhanVat_HP = CharacterMax_HP;
                    NhanVat_AP = Player_Shield_Max;
                    CapNhat_HP_MP_SP();
                    GetTheReviewRangePlayers();
                    GetReviewScopeNpc();
                    ObtainGroundObjectsInTheReviewArea();
                    break;
                case 69:
                    Hoc_tap_ky_nang_bao(data, length);
                    break;
                case 131:
                    MissionSystem_Script(data, length);
                    break;
                case 86:
                    BackToPeopleList(data, length);
                    break;
                case 80:
                    SynthesisSystem(data, length);
                    break;
                case 176:
                    HeartbeatDetection(data, length);
                    break;
                case 177:
                case 178:
                    ViewBiography(data, length);
                    break;
                case 179:
                    GuiDiTruyenThu(data, length);
                    break;
                case 181:
                    ReadingBiography(data, length);
                    break;
                case 143:
                    Display();
                    break;
                case 144:
                    OpenStore(data, length);
                    break;
                case 146:
                    BuyAndSellThings(data, length);
                    break;
                case 148:
                    WarehouseAccess(data, length);
                    break;
                case 151:
                    TransactionCategories(data, length);
                    break;
                case 153:
                    TradeAndPutThings(data, length);
                    break;
                case 203:
                    Shop(data, length);
                    break;
                case 194:
                    if (Player_Job != 7) KyNangLienHoan2(data, length);
                    break;
                case 196:
                    InsufficientMagicTips();
                    CharacterMove(MobileLastPacket, MobileLastPacket.Length);
                    break;
                case 197:
                    Khinh_cong(data, length);
                    break;
                case 199:
                    if (Player_Job != 7) KyNangLienHoan(data, length);
                    break;
                case 189:
                    Nha_kho_tiem_thuoc(data, length);
                    break;
                case 209:
                    Npc_Truyen_tong(data, length);
                    break;
                case 206:
                    IntoTheStore(data, length);
                    break;
                case 321:
                    Dang_ky_chieu_combo(data, length);
                    break;
                case 222:
                    Trang_bi_them_giai_toa(data, length);
                    break;
                case 225:
                    Xe_chi_luon_kim(data, length);
                    break;
                case 227:
                    TaoBangPhaiXacNhan(data, length);
                    break;
                case 229:
                    TaoBangPhai(data, length);
                    break;
                case 231:
                    GiaNhapBangPhai(data, length);
                    break;
                case 232:
                    ChaGang();
                    break;
                case 234:
                    Dat_duoc_mon_huy(data, length);
                    break;
                case 236:
                    Xin_mon_huy(data, length);
                    break;
                case 238:
                    Giao_pho_chuc_vi(data, length);
                    break;
                case 212:
                    BachBao(data, length);
                    break;
                case 325:
                    MarketPlace(data);
                    break;
                case 529:
                    //Case529(array);
                    break;
                case 530:
                    //Case530(array);
                    break;
                case 884:
                    RearrangeItem(data);
                    break;
                case 660:
                    BachBaoNew(data, length);
                    break;
                case 641:
                case 645:
                    BachBaoNew_ShopData(data, length);
                    break;
                case 643:
                    BachBaoNew_ItemDetail(data, length);
                    break;
                case 655:
                    BachBaonew_History_Grid(data);
                    break;
                case 670:
                    BachBaoNew_History(data);
                    break;
                case 647:
                    BachBaonew_BuyItem(data, length);
                    break;
                case 345:
                    OpenTheHallOfHonor(data, length);
                    break;
                case 342:
                    CollectMartialArtsProperty(data, length);
                    break;
                case 518:
                    Nhap_GiftCode(data, length);
                    break;
                case 774:
                    OpenTheRoseRanking(data, length);
                    break;
                case 772:
                    GiftRoses(data, length);
                    break;
                case 401:
                    Doi_moi_vinh_du(data, length);
                    break;
                case 1217:
                    ThienMaThanCungBangXepHang(data, length);
                    break;
                case 789:
                    TopThreeHonors(data, length);
                    break;
                case 901:
                    CtrlAltClickDeleteItem(data);
                    break;
                case 1268:
                    Hoc_tap_than_nu_ky_nang(data, length);
                    break;
                case 1247:
                    Tieu_chi_bieu_lo(data, length);
                    break;
                case 1225:
                    ThienMaThanCungThongTin(data, length);
                    break;
                case 4108:
                    Bao_ton_Tho_Linh_phu(data, length);
                    break;
                case 4110:
                    Xoa_bo_Tho_Linh_phu(data, length);
                    break;
                case 4112:
                    DyeHair(data, length);
                    break;
                case 4101:
                    Su_dung_Tho_Linh_phu(data, length);
                    break;
                case 4097:
                    Truong_Bach_dan(data, length);
                    break;
                case 4147:
                    MobileTrainingPlace(data, length);
                    break;
                case 4145:
                    Sua_chua_bang_phai_thong_cao(data, length);
                    break;
                case 4115:
                    OpenSymbol(data, length);
                    break;
                case 4117:
                    PkSwitch(data, length);
                    break;
                case 4119:
                    PutInTheShortcutBar(data, length);
                    break;
                case 4156:
                    Xin_bang_chien(data, length);
                    break;
                case 4154:
                    Huy_bo_bang_chien(data, length);
                    break;
                case 4180:
                    PetActionPack(data, length);
                    break;
                case 4176:
                    SummonPets(data, length);
                    break;
                case 4160:
                    Su_do_he_thong(data, length);
                    break;
                case 4162:
                    Su_do_he_thong_thinh_cau(data, length);
                    break;
                case 4164:
                    Su_do_he_thong_huy_bo(data, length);
                    break;
                case 4166:
                    Su_do_he_thong_giai_tru(data, length);
                    break;
                case 4168:
                    Su_do_truyen_thu_vo_cong(data, length);
                    break;
                case 4186:
                    SpiritBeastTransformation(data, length);
                    break;
                case 4182:
                    Sung_vat_dat_ten_bao(data, length);
                    break;
                case 5441:
                    Thay_doi_mon_phuc(data, length);
                    break;
                case 4232:
                {
                    var temo_int = BitConverter.ToInt32(data, 10);
                    if (temo_int == DEBUG_TargetID) break;
                    if (Player_Job == 4) KiemTra_ThoiGian_DoiTarget = DateTime.Now;
                    DEBUG_TargetID = temo_int;
                    if (DEBUG_TargetID <= 10000) break;
                    foreach (var class2 in NpcList.Values)
                        if (class2.FLD_INDEX == DEBUG_TargetID && GMMode == 8)
                        {
                            HeThongNhacNho(
                                "INDEX: " + class2.FLD_INDEX + " | ID: " + class2.FLD_PID + "| Level: " + class2.Level +
                                " | HP: " + class2.Rxjh_HP, 10);
                            break;
                        }

                    break;
                }
                case 4192:
                    MasterAndApprenticeMartialArtsInspection(data, length);
                    break;
                case 5648:
                    GroupTeleport(data, length);
                    break;
                case 5639:
                    HairDressing(data, length);
                    break;
                case 5724:
                    AuxiliaryEquipmentSwitching(data, length);
                    break;
                case 5680:
                    Bang_phai_truyen_tong(data, length);
                    break;
                case 5654:
                    Rename(data, length);
                    break;
                case 5920:
                    NguyenBao_HopThanh(data, length);
                    break;
                case 5922:
                    NguyenBao_HopThanh2(data, length);
                    break;
                case 5924:
                    Tu_vong_ve_thanh(data, length);
                    break;
                case 5914:
                    Che_duoc_he_thong(data, length);
                    break;
                case 5954:
                    Xoa_bo_che_tac_ky_thuat(data, length);
                    break;
                case 5952:
                    Che_tac_he_thong_dong_tac(data, length);
                    break;
                case 5936:
                    Che_tac_phan_giai_he_thong(data, length);
                    break;
                case 5938:
                    Che_tac_phan_giai_kiem_tra(data, length);
                    break;
                case 5941:
                    ProductionSystemProduction(data, length);
                    break;
                case 5943:
                    Che_tac_he_thong_kiem_tra(data, length);
                    break;
                case 5944:
                    Hoc_tap_che_tac_ky_nang(data, length);
                    break;
                case 5946:
                    AssigningMartialArtsWuxun(data, length);
                    break;
                case 5971:
                    Hai_thuoc(data, length);
                    break;
                case 5968:
                    Tiem_hanh(data, length);
                    break;
                case 6009:
                    CoupleSystem(data, length);
                    break;
                case 6003:
                    Keo_Bua_Bao(data, length);
                    break;
                case 6000:
                    Danh_tu_kieu_dang(data, length);
                    break;
                case 6150:
                    ThemThangThienVoCongPoint(data, length);
                    break;
                case 6144:
                    SearchTeam(data, length);
                    break;
                case 6433:
                    SetDungeonDifficulty(data, length);
                    break;
                case 6418:
                    ViewEquipment(data, length);
                    break;
                case 6402:
                    Dao_cu_to_hop(data, length);
                    break;
                case 7179:
                    Nguyen_bao_nguoi_cua_hang(data, length);
                    break;
                case 6435:
                    XemKhiCong(data, length);
                    break;
                case 7195:
                    YuanbaoPersonalStoreInquiryAgreementOpened(data, length);
                    break;
                case 7194:
                    YuanbaoPersonalStoreInquiryAgreement(data, length);
                    break;
                case 7181:
                    EnterYuanbaoPersonalStore(data, length);
                    break;
                case 12580:
                    EquipmentRepair(data, length);
                    break;
                case 12403:
                    OpenChangeCharacter(data, length);
                    break;
                case 8724:
                    TheLucChien_HeThong(data, length);
                    break;
                case 20760:
                    ThienMaVeThanh(data, length);
                    break;
                case 20742:
                    Thien_Ma_Than_cung_moi(data, length);
                    break;
                case 20740:
                    Thien_Ma_Than_cung_moi_ngan_te(data, length);
                    break;
            }

            if (Opcode != 7) NhanVatDangDiChuyen = false;
        }
        catch (Exception ex)
        {
            var obj = new string[8]
            {
                "ManagePacket()出错",
                Client.WorldId.ToString(),
                "|",
                Client.ToString(),
                " ",
                Opcode.ToString(),
                "    ",
                null
            };
            obj[7] = ex?.ToString();
            Form1.WriteLine(1, string.Concat(obj));
            Console.WriteLine(ex);
            Client.Dispose();
        }
    }

    private void DeleteBuff(byte[] data)
    {
       var num = BitConverter.ToInt32(data, 10);
		try
		{
			var source = new int[11]
			{
				501301, 501302, 501303, 501401, 501402, 501403, 401301, 401302, 401201, 401202,
				401203
			};
			if (source.Contains(num) && AppendStatusList.ContainsKey(num))
			{
				AppendStatusList[num].ThoiGianKetThucSuKien();
			}
		}
		catch (Exception ex)
		{
            Form1.WriteLine(1, "Lỗi khi xóa buff" + ex.Message);
		}
    }
}