using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace RxjhServer;

public partial class Players
{
    public void BachBao(byte[] PacketData, int PacketSize)
    {
        var array = new byte[4];
        System.Buffer.BlockCopy(PacketData, 10, array, 0, 1);
        switch (BitConverter.ToInt32(array, 0))
        {
            case 2:
            {
                var array2 = Converter.HexStringToByte("AA5507007100D50001000255AA");
                System.Buffer.BlockCopy(BitConverter.GetBytes(CharacterFullServerID), 0, array2, 4, 2);
                Client?.Send_Map_Data(array2, array2.Length);
                break;
            }
            case 1:
            {
                var array2 = Converter.HexStringToByte("aa5500003d01d500080006010000603b000055aa");
                System.Buffer.BlockCopy(BitConverter.GetBytes(CharacterFullServerID), 0, array2, 4, 2);
                Client?.Send_Map_Data(array2, array2.Length);
                break;
            }
        }
    }

    public void BachBaoNew(byte[] PacketData, int PacketSize) // 660
    {
        try
        {
            var typeByte = PacketData[10]; // Directly access the byte you're interested in
            int typeInt = typeByte; // Implicit conversion from byte to int
            if (typeInt != 3) throw new Exception("BachBaoNew not 3");

            SendingClass packetDataClass = new();

            var noParentCount = 0;
            List<World.X_WebShop_Category> rowsWithoutParent = new();
            List<World.X_WebShop_Category> rowsWithParent = new();

            foreach (var row in World.WebShopCategoryList.Values)
                if (row.PARENTID == 0) // Checking for null parent
                {
                    noParentCount++;
                    rowsWithoutParent.Add(row);
                }
                else
                {
                    rowsWithParent.Add(row);
                }

            var index = 1;
            // Write the number of items with no parent
            //  Form1.WriteLine(88, "Total Parent " + noParentCount);
            packetDataClass.Write4(noParentCount);
            // Process items without parent
            foreach (var row in rowsWithoutParent)
            {
                packetDataClass.Write4(0);
                packetDataClass.Write4(index);
                packetDataClass.Write4(row.ID);
                index++;
                // packetDataClass.WriteString("Test",32);
                packetDataClass.WriteStringCut(Unitoccp1258(row.NAME), 32, Encoding.GetEncoding(1252));
            }

            // Process items with parent
            packetDataClass.Write4(rowsWithParent.Count);
            index = 1;
            foreach (var row in rowsWithParent)
            {
                packetDataClass.Write4(index);
                packetDataClass.Write4(row.ID);
                index++;
                packetDataClass.Write4(row.PARENTID);
                // packetDataClass.WriteString("Test",32);
                packetDataClass.WriteStringCut(Unitoccp1258(row.NAME), 32, Encoding.GetEncoding(1252));
            }
            packetDataClass.Write4(0);
            // Send packet 640
            Client?.SendPak(packetDataClass, 32770, CharacterFullServerID);

            // Send Cash 653
            // 653
            UpdateCash();
        }
        catch (Exception Ex)
        {
            Form1.WriteLine(1, "BachBaoNew Error " + Ex.Message);
        }
    }

    public void UpdateCash()
    {
        var array2 = Converter.HexStringToByte("aa551800b5078d0210000100000000000000000000000000000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(CharacterFullServerID), 0, array2, 4, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(FLD_RXPIONT), 0, array2, 14, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(FLD_RXPIONTX), 0, array2, 18, 4);
        Client?.Send_Map_Data(array2, array2.Length);
    }

    public void BachBaoNew_ShopData(byte[] PacketData, int PacketSize) // 641
    {
        //SendTest();
        //return;
        var check = 0;
        try
        {
            int typeInt = PacketData[10];
            int subInt = PacketData[14];

            var pageByte = PacketData[18];
            int pageInt = pageByte;

            var itemsPerPage = 12; // Items per page
            var offsetItem = pageInt * itemsPerPage;

            List<int> parentId = new()
            {
                typeInt
            };
            foreach (var row in World.WebShopCategoryList.Values)
            {
                if (subInt == 0)
                {
                    if (typeInt == row.PARENTID) parentId.Add(row.ID);
                }
                else if (subInt == row.ID)
                {
                    parentId.Add(row.ID);
                }
            }

            int requestType = BitConverter.ToInt16(PacketData, 6);


            List<X_Bach_Bao_Cac_Loai> items = new();
            switch (requestType)
            {
                case 645:
                    var searchQueryByte = new byte[14];
                    System.Buffer.BlockCopy(PacketData, 26, searchQueryByte, 0, 14);
                    var actualLength = Array.IndexOf(searchQueryByte, (byte)0);
                    if (actualLength == -1)
                        actualLength = 14;

                    var searchQuery = Encoding.ASCII.GetString(searchQueryByte, 0, actualLength).TrimEnd('\0');
                    var normalizedSearchQuery = NormalizeString(searchQuery);

                    foreach (var row in World.WebShopItemList.Values)
                    {
                        var normalizedName = NormalizeString(row.NAME);
                        var normalizedDesc = NormalizeString(row.DESC);

                        if (normalizedName.Contains(normalizedSearchQuery) ||
                            normalizedDesc.Contains(normalizedSearchQuery))
                            items.Add(row);
                    }

                    break;
                default:
                    foreach (var row in World.WebShopItemList.Values)
                        if (parentId.Contains(row.CATEGORY_ID))
                            items.Add(row);

                    break;
            }


            // Manual Pagination
            var totalItems = items.Count;
            var endIndex = Math.Min(offsetItem + itemsPerPage, totalItems);
            var totalPages = totalItems / itemsPerPage;
            var currentItemCount = endIndex - offsetItem; // Calculate the actual number of items in this page

            var packetDataClass = CreateCashShopList(typeInt, pageInt, items, offsetItem, endIndex, totalPages,
                currentItemCount);
            // 642
            Client?.SendPak(packetDataClass, 33282, CharacterFullServerID);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "BachBaoNew Error offset" + check + " " + ex.Message);
        }
    }

    private SendingClass CreateCashShopList(int typeInt, int pageInt, List<X_Bach_Bao_Cac_Loai> items,
        int startIndex, int endIndex, int totalPages, int currentItemCount)
    {
        SendingClass packetDataClass = new();
        packetDataClass.Write4(typeInt);
        packetDataClass.Write4(0);
        packetDataClass.Write4(pageInt);
        packetDataClass.Write4(totalPages); // Total number of items without pagination
        packetDataClass.Write4(currentItemCount); // Correctly set the number of items on this page
        for (var i = startIndex; i < endIndex; i++)
        {
            var row = items[i];
            try
            {
                var PRODUCT_CODE = row.ID.ToString();
                var FLD_ID = row.PID;
                var FLD_NAME = Unitoccp1258(row.NAME);
                var FLD_TYPE = Convert.ToInt32(row.CATEGORY_ID);
                var FLD_PRICE = Convert.ToInt32(row.PRICE);

                var FLD_OLD_PRICE = DBNull.Value.Equals(row.PRICE_OLD) ? FLD_PRICE : Convert.ToInt32(row.PRICE_OLD);
                var FLD_NUMBER = Convert.ToInt32(row.NUMBER);

                packetDataClass.WriteStringCut(PRODUCT_CODE, 16);
                packetDataClass.Write4(FLD_ID);
                packetDataClass.Write4(0);
                packetDataClass.WriteStringCut(FLD_NAME, 32, Encoding.GetEncoding(1252));
                packetDataClass.Write4(FLD_NUMBER);
                packetDataClass.Write4(FLD_OLD_PRICE);
                packetDataClass.Write4(FLD_PRICE);
                packetDataClass.Write4(FLD_TYPE);
                packetDataClass.Write4(0); //sub type
                packetDataClass.Write(new byte[84]);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"Error processing row {i}: {ex.Message}");
            }
        }

        return packetDataClass;
    }

    public static string NormalizeString(string text)
    {
        var normalizedString = text.Normalize(NormalizationForm.FormD);
        var stringBuilder = new StringBuilder();

        foreach (var c in normalizedString)
        {
            var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
            if (unicodeCategory != UnicodeCategory.NonSpacingMark) stringBuilder.Append(c);
        }

        // Remove non-alphanumeric characters and convert to lowercase
        var alphanumericString = Regex.Replace(stringBuilder.ToString(), "[^a-zA-Z0-9]", string.Empty);
        return alphanumericString.Normalize(NormalizationForm.FormC).ToLower();
    }

    public void BachBaoNew_ItemDetail(byte[] PacketData, int PacketSize)
    {
        try
        {
            var productIdByte = new byte[14];
            System.Buffer.BlockCopy(PacketData, 10, productIdByte, 0, 14);
            var productId = Encoding.ASCII.GetString(productIdByte).TrimEnd('\0');

            /* string query = @"SELECT TOP 1 ID,PRODUCT_CODE,FLD_PID,FLD_NAME,FLD_PRICE,FLD_PRICE_OLD,FLD_DESC, FLD_NUMBER,FLD_LOCK,FLD_DAYS,CATEGORY_ID FROM [ITEM] WHERE PRODUCT_CODE = @productID";
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@productID", SqlDbType.NVarChar) { Value = productId });
            DataTable dataTable = DBA.GetDBToDataTable(query, sqlParameters.ToArray(), "BBG");

            if (dataTable.Rows.Count == 0)
            {
                return;
            }
            DataRow row = dataTable.Rows[0];
            */
            World.WebShopItemList.TryGetValue(productId, out var row);
            var itemName = Unitoccp1258(row.NAME);
            var itemDesc = Unitoccp1258(row.DESC);

            var FLD_PRICE = Convert.ToInt32(row.PRICE);
            var FLD_OLD_PRICE = row.PRICE_OLD != 0 ? Convert.ToInt32(row.PRICE_OLD) : FLD_PRICE;
            var FLD_NUMBER = Convert.ToInt32(row.NUMBER);
            var CATEGORY_ID = Convert.ToInt32(row.CATEGORY_ID);

            SendingClass packetDataClass = new();
            packetDataClass.WriteStringCut(productId, 16);
            packetDataClass.Write4(row.PID);
            packetDataClass.Write4(0);
            packetDataClass.WriteStringCut(itemName, 32, Encoding.GetEncoding(1252));
            packetDataClass.WriteStringCut(itemDesc, 4096, Encoding.GetEncoding(1252));

            packetDataClass.Write4(FLD_NUMBER);
            packetDataClass.Write4(FLD_OLD_PRICE);
            packetDataClass.Write4(FLD_PRICE);
            packetDataClass.Write4(CATEGORY_ID);
            packetDataClass.Write4(CATEGORY_ID);
            packetDataClass.Write2(0);
            packetDataClass.Write1(0);
            packetDataClass.Write1(1);
            packetDataClass.Write(new byte[91]);

            Client?.SendPak(packetDataClass, 33794, CharacterFullServerID);
        }
        catch (Exception Ex)
        {
            Form1.WriteLine(1, "BachBaoNew_ItemDetail Error: " + Ex.Message);
        }
    }

    public void BachBaonew_BuyItem(byte[] PacketData, int PacketSize)
    {
        // Type 647
        try
        {
            //{
            //    if ( <= 0)
            //    {
            //        HeThongNhacNho("Máy chủ tạm khóa mua vật phẩm!");
            //        Send652();
            //        throw new Exception("Máy chủ tạm khóa mua vật phẩm!");
            //    }

            var typeByte = PacketData[10];
            int amount = typeByte;
            if (amount <= 0)
            {
                HeThongNhacNho("Không thể mua ít hơn 1 số lượng!");
                Send652();
                throw new Exception($"Không thể mua ít hơn 1 số lượng {UserName}");
            }

            var productIdByte = new byte[14];
            System.Buffer.BlockCopy(PacketData, 14, productIdByte, 0, 14);
            var productId = Encoding.ASCII.GetString(productIdByte).TrimEnd('\0').Replace(" ","");
            var beforeCash = FLD_RXPIONT;

            if (!World.WebShopItemList.TryGetValue(productId, out var item))
            {
                HeThongNhacNho("!Không tìm thấy vật phẩm cần mua! Liên hệ Admin!!!");
                throw new Exception($"!Không tìm thấy vật phẩm cần mua! {UserName}");
            }

            var total = amount * item.PRICE;
            if (FLD_RXPIONT < total)
            {
                //SendNotificationPopUp($"!Không đủ Cash! Cần : {total} Cash");
                HeThongNhacNho($"!Không đủ Cash! Cần : {total} Cash", 7, "CASHSHOP");
                sendCashShopMessage(0x45);
                return;
                //throw new Exception(
                //    $"!Không đủ Cash! Cần : {total} Cash | User {base.UserName} | Cash {base.FLD_RXPIONT}");
            }

            // Get Empty Inventory Slot
            var parcelVacancy = GetParcelVacancy(this);
            if (parcelVacancy == -1)
            {
                HeThongNhacNho("Thùng đồ không còn chỗ trống");
                sendCashShopMessage(0x19);
                throw new Exception("Thùng đồ không còn slot trống");
            }

            var FLD_MAGIC0 = item.MAGIC0;
            switch (item.PID)
            {
                case *********:
                    FLD_MAGIC0 = RNG.Next(200002, 200010);
                    break;
                case *********:
                    FLD_MAGIC0 = RNG.Next(100002, 100010);
                    break;
                case *********:
                    FLD_MAGIC0 = RNG.Next(1, 22);
                    break;
                case *********:
                    FLD_MAGIC0 = RNG.Next(23, 51);
                    break;
                case *********:
                    FLD_MAGIC0 = RNG.Next(70, 81);
                    break;
                case *********:
                    FLD_MAGIC0 = RNG.Next(1000002, 1000010);
                    break;
                case *********:
                    FLD_MAGIC0 = RNG.Next(700002, 700010);
                    break;
                case *********:
                    FLD_MAGIC0 = int.Parse("200" + RNG.Next(0, 6) + "000");
                    break;
            }

            CheckTheNumberOfIngotsInBaibaoge();
            KiemSoatNguyenBao_SoLuong(total, 0);
            Save_NguyenBaoData();
            var res = RxjhClass.InsertCashShopLog("REQUEST", item.NAME, UserName, amount, total, item.ID, productId);
            if (res == -1)
            {
                HeThongNhacNho("Có lỗi xảy ra khi mua vật phẩm.Vui lòng liên hệ ADMIN");
                Send652();
                return;
            }

            if (amount > 1)
            {
                for (var i = 0; i < amount; i++)
                {
                    var parcelVacancy2 = GetParcelVacancy(this);
                    FLD_MAGIC0 = item.PID switch
                    {
                        ********* => RNG.Next(200002, 200010),
                        ********* => RNG.Next(100002, 100010),
                        ********* => RNG.Next(1, 22),
                        ********* => RNG.Next(23, 51),
                        ********* => RNG.Next(70, 81),
                        ********* => RNG.Next(1000002, 1000010),
                        ********* => RNG.Next(700002, 700010),
                        ********* => int.Parse("200" + RNG.Next(0, 6) + "000"),
                        _ => FLD_MAGIC0
                    };

                    IncreaseItemWithAttributes(item.PID, parcelVacancy2, 1, FLD_MAGIC0, item.MAGIC1, item.MAGIC2,
                        item.MAGIC3, item.MAGIC4, item.ThucTinh, item.TrungCapHon, item.TienHoa, item.KhoaLai,
                        item.NgaySuDung);
                    // Working
                    UpdateCash();
                    send648();
                }
            }
            else
            {
                IncreaseItemWithAttributes(item.PID, parcelVacancy, 1, FLD_MAGIC0, item.MAGIC1, item.MAGIC2,
                    item.MAGIC3, item.MAGIC4, item.ThucTinh, item.TrungCapHon, item.TienHoa, item.KhoaLai,
                    item.NgaySuDung);
                HeThongNhacNho(
                    "Nhận thành công vật phẩm [" + ItmeClass.DatDuocVatPhamTen_XungHao(item.PID) + "]", 7);
                // Working
                UpdateCash();
                send648();
            }

            RxjhClass.UpdateBachBaoNewRecord(res, "SUCCESS");
            UpdateCharacterData(this);
            //35842
        }
        catch (Exception Ex)
        {
            Form1.WriteLine(1, "BachBaonew_BuyItem Error: " + Ex.Message);
        }
    }

    public void BachBaonew_History_Grid(byte[] data)
    {
        //byte[] array = Converter.HexStringToByte("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");
        //                   System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, array, 4, 2);
        //                   Client?.SendX(array, array.Length);
        //string query = @"SELECT TOP 1 ID,PRODUCT_CODE,FLD_PID,FLD_NAME,FLD_PRICE,FLD_PRICE_OLD,FLD_DESC, FLD_NUMBER,FLD_LOCK,FLD_DAYS,CATEGORY_ID FROM [ITEM] WHERE PRODUCT_CODE = @productID";

        //List<SqlParameter> sqlParameters = new List<SqlParameter>();
        //sqlParameters.Add(new SqlParameter("@productID", SqlDbType.NVarChar) { Value = productId });
        //DataTable dataTable = DBA.GetDBToDataTable(query, sqlParameters.ToArray(), "BBG"); 
        var query = "SELECT * FROM CASH_SHOP_LOG WHERE USERNAME = @UserName AND STATUS='SUCCESS' ORDER BY ID DESC";
        var parameters = new[]
        {
            new SqlParameter("@UserName", UserName)
        };

        var items = DBA.GetDBToDataTable(query, parameters, "BBG");
        // Form1.WriteLine(1, "Total Logs " + items.Rows.Count + "UserName :  ");
        var totalItems = items.Rows.Count;
        var totalPages = totalItems / 12;
        // Calculate the actual number of items in this page
        var pageInt = data[0x12];
        var currentItemCount = totalPages - pageInt > 0 ? 12 : totalItems - 12 * totalPages;
        // Form1.WriteLine(1,"Total Page " + totalPages +" Current Item " + currentItemCount);
        SendingClass packetDataClass = new();
        packetDataClass.Write4(0);
        packetDataClass.Write4(0);
        packetDataClass.Write4(pageInt);
        packetDataClass.Write4(totalPages); // Total number of items without pagination
        packetDataClass.Write4(currentItemCount); // Correctly set the number of items on this page
        for (var i = 0; i < items.Rows.Count; i++)
            try
            {
                if (i + pageInt * 12 > totalItems)
                    break;
                var item = items.Rows[i + pageInt * i];
                World.WebShopItemList.TryGetValue(item["PRODUCT_ID"].ToString(), out var row);

                var PRODUCT_CODE = row.ID.ToString();
                var FLD_ID = row.PID;
                var FLD_NAME = Unitoccp1258(row.NAME);
                var FLD_TYPE = Convert.ToInt32(row.CATEGORY_ID);
                var FLD_PRICE = Convert.ToInt32(row.PRICE);

                var FLD_OLD_PRICE = DBNull.Value.Equals(row.PRICE_OLD) ? FLD_PRICE : Convert.ToInt32(row.PRICE_OLD);
                var FLD_NUMBER = Convert.ToInt32(row.NUMBER);

                packetDataClass.WriteStringCut(PRODUCT_CODE, 16, Encoding.ASCII);
                packetDataClass.Write4(FLD_ID);
                packetDataClass.Write4(0);
                packetDataClass.WriteStringCut(FLD_NAME, 32, Encoding.GetEncoding(1252));
                packetDataClass.Write4(FLD_NUMBER);
                packetDataClass.Write4(FLD_OLD_PRICE);
                packetDataClass.Write4(FLD_PRICE);
                packetDataClass.Write4(FLD_TYPE);
                packetDataClass.Write4(0); //sub type
                packetDataClass.Write(new byte[84]);
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, $"Error processing row {i}: {ex.Message}");
                //packetDataClass.WriteStringCut("0", 16, Encoding.ASCII);
                //packetDataClass.Write4(0);
                //packetDataClass.Write4(0);
                //packetDataClass.WriteStringCut("", 32, Encoding.GetEncoding(1252));
                //packetDataClass.Write4(0);
                //packetDataClass.Write4(0);
                //packetDataClass.Write4(0);
                //packetDataClass.Write4(0);
                //packetDataClass.Write4(0); //sub type
                //packetDataClass.Write(new byte[84]);
            }

        // 642
        Client?.SendPak(packetDataClass, 36866, CharacterFullServerID);
    }

    public void BachBaoNew_History(byte[] PacketData)
    {
        int offset = 2;
        try
        {
            int page = PacketData[0x68 - offset];
            int date = PacketData[0x64 - offset];
            var typeByte = new byte[16];
            System.Buffer.BlockCopy(PacketData, 0xC - offset, typeByte, 0, 16);

            var commandByte = new byte[16];
            System.Buffer.BlockCopy(PacketData, 0x20 - offset, commandByte, 0, 16);

            var type = Encoding.GetEncoding(1252)
                .GetString(typeByte)
                .TrimEnd('\0')
                .Trim()
                .ToLower();
            var command = Encoding.GetEncoding(1252).GetString(commandByte).TrimEnd('\0')
                .Trim()
                .ToLower();

            Form1.WriteLine(1, $"{type} {command} {page} {date}");
            byte[] array;
            switch (type)
            {
                case "myshop":
                    // Form1.WriteLine(1, "Send Myshop");
                    switch (command)
                    {
                        case "buy":
                            //byte[] itemx = Converter.HexStringToByte("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");
                            // System.Buffer.BlockCopy(BitConverter.GetBytes(base.CharacterFullServerID), 0, itemx, 4, 2);
                            //Client?.SendX(itemx, itemx.Length);
                            //break;
                            var totalQuery = @"
                                SELECT COUNT(*) 
                                FROM CASH_SHOP_LOG
                                WHERE USERNAME = @Username
                                AND MONTH(CREATED_AT) = @Month;
                            ";

                            var parameters2 = new[]
                            {
                                new SqlParameter("@UserName", UserName),
                                new SqlParameter("@Month", date)
                            };
                            var query = @"
                                WITH PaginatedResults AS (
                                    SELECT 
                                        *,
                                        ROW_NUMBER() OVER (ORDER BY CREATED_AT DESC) AS RowNum
                                    FROM 
                                        CASH_SHOP_LOG
                                    WHERE 
                                        USERNAME = @Username
                                        AND MONTH(CREATED_AT) = @Month
                                )
                                SELECT *
                                FROM PaginatedResults
                                WHERE 
                                    RowNum BETWEEN (@PageNumber - 1) * @PageSize + 1 AND @PageNumber * @PageSize;
                             ";
                            var parameters = new[]
                            {
                                new SqlParameter("@UserName", UserName),
                                new SqlParameter("@Month", date),
                                new SqlParameter("@PageNumber", page),
                                new SqlParameter("@PageSize", 10)
                            };

                            var items = DBA.GetDBToDataTable(query, parameters, "BBG");
                            var totalItems = DBA.ExeSqlCommand(totalQuery, "BBG", parameters2);
                            // Form1.WriteLine(1, "Total " + totalItems);
                            var formatedData = new List<Dictionary<string, object>>();
                            var rowIndex = 1;
                            foreach (DataRow row in items.Rows)
                            {
                                World.WebShopItemList.TryGetValue(row["PRODUCT_ID"].ToString(), out var item);
                                if (item == null)
                                    break;
                                var rowDict = new Dictionary<string, object>
                                {
                                    { "rownum", rowIndex },
                                    { "item_name", unitoccp1258(item.NAME) },
                                    { "item_cnt", int.Parse(row["AMOUNT"].ToString()) },
                                    { "buycash", int.Parse(row["PRICE"].ToString()) },
                                    {
                                        "buydate",
                                        row["CREATED_AT"] != DBNull.Value
                                            ? Convert.ToDateTime(row["CREATED_AT"]).ToString("yyyy-MM-dd HH:mm:ss")
                                            : string.Empty
                                    },
                                    { "cashtype", string.Empty },
                                    { "dtype", string.Empty },
                                    { "state", string.Empty },
                                    { "buynum", 0 }
                                };

                                formatedData.Add(rowDict);
                                rowIndex++;
                            }

                            var jsonResult = JsonConvert.SerializeObject(new { data = formatedData });

                            items.Dispose();
                            SendingClass packet = new();
                            packet.Write4(0);
                            packet.Write(new byte[32]);
                            packet.Write4(0);
                            packet.Write4(page);
                            packet.Write4(date);
                            packet.Write1(0x52);
                            packet.Write1(0x6);
                            packet.Write2(0);
                            packet.Write4(totalItems); // total item
                            packet.Write(Encoding.GetEncoding(1252).GetBytes(jsonResult));
                            Client?.SendPak(packet, 40706, CharacterFullServerID);

                            break;
                    }

                    break;
                case "present":
                    Form1.WriteLine(1, "Send present");
                    array = Converter.HexStringToByte(
                        "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");
                    System.Buffer.BlockCopy(BitConverter.GetBytes(CharacterFullServerID), 0, array, 4, 2);
                    Client?.Send_Map_Data(array, array.Length);
                    break;
                default:
                    Form1.WriteLine(1, $"Default {type}");
                    break;
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Bachbaonew_History Error: " + ex.Message);
        }
    }

    public void CheckTheNumberOfIngotsInBaibaoge()
    {
        var text =
            "SELECT FLD_SEX, FLD_RXPIONT, FLD_RXPIONTX, FLD_VIP, FLD_VIPTIM, FLD_COIN FROM [TBL_ACCOUNT] WHERE FLD_ID = @Userid";
        var sqlParameter_ = new SqlParameter[1]
        {
            new("@Userid", SqlDbType.VarChar, 30) { Value = Userid }
        };
        var dBToDataTable = DBA.GetDBToDataTable(text, sqlParameter_, "rxjhaccount");
        FLD_RXPIONT = Math.Max(0, (int)dBToDataTable.Rows[0]["FLD_RXPIONT"]);
        FLD_RXPIONTX = Math.Max(0, (int)dBToDataTable.Rows[0]["FLD_RXPIONTX"]);
        FLD_Coin = Math.Max(0, (int)dBToDataTable.Rows[0]["FLD_COIN"]);
        dBToDataTable.Dispose();
        if (FLD_RXPIONT > World.GioiHan_TongSoNguyenBao_1TaiKhoan)
        {
            Form1.WriteLine(77,
                "Tổng số Cash của người chơi vượt quá hệ thống cho phép CaoNhat[" + Userid + "]-[" +
                UserName + "]  [Cash tổng cộng：" + FLD_RXPIONT + "] [Hệ thống cho phép CaoNhatSoLuong：" +
                World.GioiHan_TongSoNguyenBao_1TaiKhoan + "]");
            switch (World.HoatDong_PhatHienPhoi)
            {
                case 2:
                    Title(112, Userid, "CashVuotQuaSoLuong");
                    break;
                case 1:
                    FLD_RXPIONT = 0;
                    Save_NguyenBaoData();
                    break;
            }
        }
    }

    public string PakTreasureCourtBuyAndSellThings(int VatPham_ID, int VatPhamSoLuong, int yuanbao, int LoaiHinh,
        int FLD_MAGIC0, int FLD_MAGIC1, int FLD_MAGIC2, int FLD_MAGIC3, int FLD_MAGIC4, int TrungCapHon, int ThucTinh,
        int TienHoa, int KhoaLai, int NgaySuDung)
    {
        try
        {
            var fLD_RXPIONT = FLD_RXPIONT;
            //if (World.Cho_Phep_Mo_Class_Den_So_Luong <= 0)
            //{
            //    HeThongNhacNho("Máy chủ tạm khóa mua vật phẩm!", 7);
            //    return "KhoaMua";
            //}

            if (!World.BachBaoCat_ThuocTinhVatPhamClassList.TryGetValue(VatPham_ID, out var value)) return "购买错误";

            if (value.PID != VatPham_ID)
            {
                Form1.WriteLine(77,
                    "Sửa đổi bất hợp pháp PacketTitle 5_Baobaoge MuaBan[" + Userid + "][" + UserName +
                    "]  CharacterFullServerID=[" + CharacterFullServerID + "]  [" + Client + "]");
                if (Client != null)
                {
                    logo.kickid("Disconnect: 102");
                    Client.Dispose();
                }

                return "你已经被封号请联系管理";
            }

            if (value.TYPE != LoaiHinh)
            {
                Form1.WriteLine(77,
                    "Sửa đổi bất hợp pháp PacketTitle 3_Baobaoge MuaBan[" + Userid + "][" + UserName +
                    "]  CharacterFullServerID=[" + CharacterFullServerID + "]  [" + Client + "]");
                if (Client != null)
                {
                    logo.kickid("Disconnect: 103");
                    Client.Dispose();
                }

                return "你已经被封号请联系管理";
            }

            if (VatPhamSoLuong == 1)
            {
                if (yuanbao != value.PRICE)
                {
                    Form1.WriteLine(77,
                        "Sửa đổi bất hợp pháp PacketTitle 2_Baobaoge MuaBan[" + Userid + "][" + UserName +
                        "]  CharacterFullServerID=[" + CharacterFullServerID + "]  [" + Client +
                        "]");
                    if (Client != null)
                    {
                        logo.kickid("Disconnect: 104");
                        Client.Dispose();
                    }

                    return "购买错误";
                }
            }
            else if (yuanbao != value.PRICE * VatPhamSoLuong)
            {
                Form1.WriteLine(77,
                    "Sửa đổi bất hợp pháp PacketTitle 1_Baobaoge MuaBan[" + Userid + "][" + UserName +
                    "]  CharacterFullServerID=[" + CharacterFullServerID + "]  [" + Client + "]");
                if (Client != null)
                {
                    logo.kickid("Disconnect: 105");
                    Client.Dispose();
                }

                return "购买错误";
            }

            if (VatPhamSoLuong >= 1 && yuanbao >= 0)
            {
                var parcelVacancy = GetParcelVacancy(this);
                if (parcelVacancy == -1) return "没有空位了";

                CheckTheNumberOfIngotsInBaibaoge();
                if ((VatPhamSoLuong >= 1 && yuanbao > 0 && FLD_RXPIONT >= yuanbao) ||
                    (VatPhamSoLuong >= 1 && yuanbao > 0 && FLD_RXPIONTX >= yuanbao) ||
                    (VatPhamSoLuong >= 1 && yuanbao > 0 && FLD_Coin >= yuanbao))
                {
                    switch (VatPham_ID)
                    {
                        case *********:
                            FLD_MAGIC0 = RNG.Next(200002, 200010);
                            break;
                        case *********:
                            FLD_MAGIC0 = RNG.Next(100002, 100010);
                            break;
                        case *********:
                            FLD_MAGIC0 = RNG.Next(1, 22);
                            break;
                        case *********:
                            FLD_MAGIC0 = RNG.Next(23, 51);
                            break;
                        case *********:
                            FLD_MAGIC0 = RNG.Next(70, 81);
                            break;
                        case *********:
                            FLD_MAGIC0 = RNG.Next(1000002, 1000010);
                            break;
                        case *********:
                            FLD_MAGIC0 = RNG.Next(700002, 700010);
                            break;
                        case *********:
                            FLD_MAGIC0 = int.Parse("200" + RNG.Next(0, 6) + "000");
                            break;
                    }

                    if (VatPhamSoLuong > 1)
                    {
                        for (var i = 0; i < VatPhamSoLuong; i++)
                        {
                            var parcelVacancy2 = GetParcelVacancy(this);
                            switch (VatPham_ID)
                            {
                                case *********:
                                    FLD_MAGIC0 = RNG.Next(200002, 200010);
                                    break;
                                case *********:
                                    FLD_MAGIC0 = RNG.Next(100002, 100010);
                                    break;
                                case *********:
                                    FLD_MAGIC0 = RNG.Next(1, 22);
                                    break;
                                case *********:
                                    FLD_MAGIC0 = RNG.Next(23, 51);
                                    break;
                                case *********:
                                    FLD_MAGIC0 = RNG.Next(70, 81);
                                    break;
                                case *********:
                                    FLD_MAGIC0 = RNG.Next(1000002, 1000010);
                                    break;
                                case *********:
                                    FLD_MAGIC0 = RNG.Next(700002, 700010);
                                    break;
                                case *********:
                                    FLD_MAGIC0 = int.Parse("200" + RNG.Next(0, 6) + "000");
                                    break;
                            }

                            IncreaseItemWithAttributes(VatPham_ID, parcelVacancy2, 1, FLD_MAGIC0, FLD_MAGIC1,
                                FLD_MAGIC2, FLD_MAGIC3, FLD_MAGIC4, ThucTinh, TrungCapHon, TienHoa, KhoaLai,
                                NgaySuDung);
                        }
                    }
                    else
                    {
                        IncreaseItemWithAttributes(VatPham_ID, parcelVacancy, 1, FLD_MAGIC0, FLD_MAGIC1, FLD_MAGIC2,
                            FLD_MAGIC3, FLD_MAGIC4, ThucTinh, TrungCapHon, TienHoa, KhoaLai, NgaySuDung);
                        HeThongNhacNho(
                            "Nhận thành công vật phẩm [" + ItmeClass.DatDuocVatPhamTen_XungHao(VatPham_ID) + "]",
                            7);
                    }

                    Save_NguyenBaoData();
                    return "PurchaseSuccessful";
                }

                return "元宝不够了";
            }

            return "购买错误";
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1,
                "PakTreasureCourtBuyAndSellThings  error  [" + Userid + "][" + UserName + "]  " + ex.Message);
            return "购买错误";
        }
    }

    public void Send652_2()
    {
        var array = Converter.HexStringToByte("aa55000099058c020c008802000000000000bf28000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(CharacterFullServerID), 0, array, 4, 2);
        Client?.Send_Map_Data(array, array.Length);
    }

    public void send648()
    {
        var array = Converter.HexStringToByte("aa5500009e018802150001000000f05989c170140000287c90d0721400000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(CharacterFullServerID), 0, array, 4, 2);
        Client?.Send_Map_Data(array, array.Length);
    }

    public void sendCashShopMessage(int type)
    {
        // type 0x45 : Khong du cash
        var array = Converter.HexStringToByte("aa550000b4018c020c0088020000000000004528000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(CharacterFullServerID), 0, array, 4, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(type), 0, array, 0x12, 1);
        Client?.Send_Map_Data(array, array.Length);
    }

    public void Send652()
    {
        var array = Converter.HexStringToByte("aa55000099058c020c008802000000000000bf28000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(CharacterFullServerID), 0, array, 4, 2);
        Client?.Send_Map_Data(array, array.Length);
    }

    public void Send652_NoEmptySlot()
    {
        // "aa5500009e0100008c020c0088020000000000001928000055aa";
        var array = Converter.HexStringToByte("aa5500009e018c020c0088020000000000001928000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(CharacterFullServerID), 0, array, 4, 2);
        Client?.Send_Map_Data(array, array.Length);
    }

    public void SendNotificationPopUp(string message)
    {
        try
        {
            var array = Converter.HexStringToByte(
                "AA55BC0000006600B4000A003A00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000FFFFFFFF6500000000000000000000000000000055AA");
            System.Buffer.BlockCopy(BitConverter.GetBytes(CharacterFullServerID), 0, array, 4, 2);
            var messageByte = Encoding.GetEncoding(1252).GetBytes(Unitoccp1258(message));
            System.Buffer.BlockCopy(messageByte, 0, array, 34, message.Length);
            Client?.Send_Map_Data(array, array.Length);
        }
        catch (Exception Ex)
        {
            Form1.WriteLine(1, "Failed to send notification " + Ex.Message);
        }
    }

    private string Unitoccp1258(string nguon)
    {
        var text =
            "ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚÝàáâãèéêìíòóôõùúýĂăĐđĨĩŨũƠơƯưẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặẸẹẺẻẼẽẾếỀềỂểỄễỆệỈỉỊịỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợỤụỦủỨứỪừỬửỮữỰựỲỳỴỵỶỷỸỹ";
        var array = new string[134]
        {
            "AÌ", "Aì", "Â", "AÞ", "EÌ", "Eì", "Ê", "IÌ", "Iì", "OÌ",
            "Oì", "Ô", "OÞ", "UÌ", "Uì", "Yì", "aÌ", "aì", "â", "aÞ",
            "eÌ", "eì", "ê", "iÌ", "iì", "oÌ", "oì", "ô", "oÞ", "uÌ",
            "uì", "yì", "Ã", "ã", "Ð", "ð", "IÞ", "iÞ", "UÞ", "uÞ",
            "Õ", "õ", "Ý", "ý", "Aò", "aò", "AÒ", "aÒ", "Âì", "âì",
            "ÂÌ", "âÌ", "ÂÒ", "âÒ", "ÂÞ", "âÞ", "Âò", "âò", "Ãì", "ãì",
            "ÃÌ", "ãÌ", "ÃÒ", "ãÒ", "ÃÞ", "ãÞ", "Ãò", "ãò", "Eò", "eò",
            "EÒ", "eÒ", "EÞ", "eÞ", "Êì", "êì", "ÊÌ", "êÌ", "ÊÒ", "êÒ",
            "ÊÞ", "êÞ", "Êò", "êò", "IÒ", "iÒ", "Iò", "iò", "Oò", "oò",
            "OÒ", "oÒ", "Ôì", "ôì", "ÔÌ", "ôÌ", "ÔÒ", "ôÒ", "ÔÞ", "ôÞ",
            "Ôò", "ôò", "Õì", "õì", "ÕÌ", "õÌ", "ÕÒ", "õÒ", "ÕÞ", "õÞ",
            "Õò", "õò", "Uò", "uò", "UÒ", "uÒ", "Ýì", "ýì", "ÝÌ", "ýÌ",
            "ÝÒ", "ýÒ", "ÝÞ", "ýÞ", "Ýò", "ýò", "YÌ", "yÌ", "Yò", "yò",
            "yÒ", "yÒ", "YÞ", "yÞ"
        };
        var text2 = string.Empty;
        for (var i = 0; i < nguon.Length; i++)
        {
            var num = text.IndexOf(nguon[i]);
            text2 = num <= 0 ? text2 + nguon[i] : text2 + array[num];
        }

        return text2;
    }
}