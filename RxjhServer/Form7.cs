using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class Form7 : Form
{
    private Button button1;

    private Button button2;

    private Button button3;
    private IContainer components;

    private GroupBox groupBox1;

    private GroupBox groupBox2;

    private GroupBox groupBox3;

    private Label label1;

    private Label label10;

    private Label label11;

    private Label label12;

    private Label label13;

    private Label label14;

    private Label label15;

    private Label label16;

    private Label label17;

    private Label label18;

    private Label label19;

    private Label label2;

    private Label label20;

    private Label label21;

    private Label label25;

    private Label label27;

    private Label label28;

    private Label label3;

    private Label label4;

    private Label label5;

    private Label label6;

    private Label label7;

    private Label label8;

    private Label label9;

    private ListBox listBox1;

    private RadioButton radioButton1;

    private RadioButton radioButton2;

    private TextBox textBox1;

    private TextBox textBox10;

    private TextBox textBox11;

    private TextBox textBox12;

    private TextBox textBox13;

    private TextBox textBox14;

    private TextBox textBox15;

    private TextBox textBox16;

    private TextBox textBox2;

    private TextBox textBox21;

    private TextBox textBox22;

    private TextBox textBox23;

    private TextBox textBox24;

    private TextBox textBox25;

    private TextBox textBox26;

    private TextBox textBox3;

    private TextBox textBox4;

    private TextBox textBox5;

    private TextBox textBox6;

    private TextBox textBox7;

    private TextBox textBox8;

    private TextBox textBox9;

    public Form7()
    {
        InitializeComponent();
        label28.Text = "0";
    }

    private void 刷新()
    {
        listBox1.Items.Clear();
        var string_ = "select FLD_NAME from TBL_XWWL_ITEM";
        var dBToDataTable = DBA.GetDBToDataTable(string_, "PublicDb");
        for (var i = 0; i < dBToDataTable.Rows.Count; i++)
            listBox1.Items.Add(dBToDataTable.Rows[i]["FLD_NAME"].ToString());
        label28.Text = dBToDataTable.Rows.Count.ToString();
        dBToDataTable.Dispose();
    }

    private void button3_Click(object sender, EventArgs e)
    {
        listBox1.Items.Clear();
        var string_ = "select FLD_NAME from TBL_XWWL_ITEM";
        var dBToDataTable = DBA.GetDBToDataTable(string_, "PublicDb");
        for (var i = 0; i < dBToDataTable.Rows.Count; i++)
            listBox1.Items.Add(dBToDataTable.Rows[i]["FLD_NAME"].ToString());
        label28.Text = dBToDataTable.Rows.Count.ToString();
        dBToDataTable.Dispose();
    }

    private void listBox1_SelectedValueChanged(object sender, EventArgs e)
    {
        var string_ = "select * from TBL_XWWL_ITEM where FLD_NAME='" + listBox1.Text + "'";
        var dBToDataTable = DBA.GetDBToDataTable(string_, "PublicDb");
        if (dBToDataTable.Rows.Count > 0)
        {
            textBox1.Text = dBToDataTable.Rows[0]["FLD_PID"].ToString();
            textBox2.Text = dBToDataTable.Rows[0]["FLD_QUESTITEM"].ToString();
            textBox3.Text = dBToDataTable.Rows[0]["FLD_Name"].ToString();
            textBox4.Text = dBToDataTable.Rows[0]["FLD_NJ"].ToString();
            textBox5.Text = dBToDataTable.Rows[0]["FLD_RESIDE1"].ToString();
            textBox6.Text = dBToDataTable.Rows[0]["FLD_RESIDE2"].ToString();
            textBox7.Text = dBToDataTable.Rows[0]["FLD_SEX"].ToString();
            textBox8.Text = dBToDataTable.Rows[0]["FLD_DF"].ToString();
            textBox9.Text = dBToDataTable.Rows[0]["FLD_AT1"].ToString();
            textBox10.Text = dBToDataTable.Rows[0]["FLD_AT2"].ToString();
            textBox11.Text = dBToDataTable.Rows[0]["FLD_LEVEL"].ToString();
            textBox12.Text = dBToDataTable.Rows[0]["FLD_JOB_LEVEL"].ToString();
            textBox13.Text = dBToDataTable.Rows[0]["FLD_ZX"].ToString();
            textBox14.Text = dBToDataTable.Rows[0]["FLD_DES"].ToString();
            textBox21.Text = dBToDataTable.Rows[0]["FLD_TYPE"].ToString();
            textBox22.Text = dBToDataTable.Rows[0]["FLD_WEIGHT"].ToString();
            textBox23.Text = dBToDataTable.Rows[0]["FLD_MONEY"].ToString();
            textBox24.Text = dBToDataTable.Rows[0]["FLD_EL"].ToString();
            textBox25.Text = dBToDataTable.Rows[0]["FLD_WXJD"].ToString();
            textBox26.Text = dBToDataTable.Rows[0]["FLD_WX"].ToString();
        }

        dBToDataTable.Dispose();
    }

    private void button2_Click(object sender, EventArgs e)
    {
        DataTable dataTable = null;
        var num3 = 0;
        var num4 = 0;
        if (listBox1.Items.Count == 0)
        {
            MessageBox.Show("Vui lòng kiểm tra dữ liệu trước!", "Msg");
            return;
        }

        if (textBox15.Text == "")
        {
            MessageBox.Show("Vui lòng nhập nội dung cần truy vấn!", "Msg");
            return;
        }

        if (radioButton1.Checked)
            try
            {
                if (!int.TryParse(textBox15.Text, NumberStyles.Integer, NumberFormatInfo.CurrentInfo, out var _))
                {
                    MessageBox.Show("Vui lòng nhập đúng ID vật phẩm!", "Msg");
                }
                else
                {
                    try
                    {
                        while (true)
                        {
                            switch (int.Parse(textBox15.Text) <= 0 ? 2 : 0)
                            {
                                case 0:
                                    break;
                                case 3:
                                    return;
                                case 2:
                                    MessageBox.Show("Phải là số nguyên");
                                    return;
                                default:
                                    continue;
                            }

                            break;
                        }
                    }
                    catch (FormatException)
                    {
                        MessageBox.Show("Phải là số nguyên");
                        return;
                    }

                    var num5 = Convert.ToInt32(textBox15.Text);
                    var string_ = $"select * from TBL_XWWL_ITEM where FLD_PID={num5}";
                    dataTable = DBA.GetDBToDataTable(string_, "PublicDb");
                    if (dataTable.Rows.Count > 0)
                    {
                        textBox1.Text = dataTable.Rows[0]["FLD_PID"].ToString();
                        textBox2.Text = dataTable.Rows[0]["FLD_QUESTITEM"].ToString();
                        textBox3.Text = dataTable.Rows[0]["FLD_Name"].ToString();
                        textBox4.Text = dataTable.Rows[0]["FLD_NJ"].ToString();
                        textBox5.Text = dataTable.Rows[0]["FLD_RESIDE1"].ToString();
                        textBox6.Text = dataTable.Rows[0]["FLD_RESIDE2"].ToString();
                        textBox7.Text = dataTable.Rows[0]["FLD_SEX"].ToString();
                        textBox8.Text = dataTable.Rows[0]["FLD_DF"].ToString();
                        textBox9.Text = dataTable.Rows[0]["FLD_AT1"].ToString();
                        textBox10.Text = dataTable.Rows[0]["FLD_AT2"].ToString();
                        textBox11.Text = dataTable.Rows[0]["FLD_LEVEL"].ToString();
                        textBox12.Text = dataTable.Rows[0]["FLD_JOB_LEVEL"].ToString();
                        textBox13.Text = dataTable.Rows[0]["FLD_ZX"].ToString();
                        textBox14.Text = dataTable.Rows[0]["FLD_DES"].ToString();
                        textBox21.Text = dataTable.Rows[0]["FLD_TYPE"].ToString();
                        textBox22.Text = dataTable.Rows[0]["FLD_WEIGHT"].ToString();
                        textBox23.Text = dataTable.Rows[0]["FLD_MONEY"].ToString();
                        textBox24.Text = dataTable.Rows[0]["FLD_EL"].ToString();
                        textBox25.Text = dataTable.Rows[0]["FLD_WXJD"].ToString();
                        textBox26.Text = dataTable.Rows[0]["FLD_WX"].ToString();
                        textBox16.Text = dataTable.Rows[0]["FLD_HEAD_WEAR"].ToString();
                        for (num3 = 0; num3 < listBox1.Items.Count; num3++)
                            if (listBox1.Items[num3].Equals(textBox3.Text))
                            {
                                listBox1.SetSelected(num3, true);
                                return;
                            }
                    }
                    else
                    {
                        MessageBox.Show("Không có vật phẩm như vậy, vui lòng kiểm tra lại PID！", "Msg");
                    }

                    dataTable.Dispose();
                }

                return;
            }
            catch (Exception ex2)
            {
                MessageBox.Show(ex2.ToString(), "Lỗi");
                return;
            }

        if (radioButton2.Checked)
            try
            {
                var flag = true;
                while (true)
                {
                    var string_2 = "select * from TBL_XWWL_ITEM where FLD_NAME like '%" + textBox15.Text + "%'";
                    var dBToDataTable = DBA.GetDBToDataTable(string_2, "PublicDb");
                    while (true)
                    {
                        IL_0a09:
                        if (dBToDataTable.Rows.Count > 0)
                        {
                            var flag2 = true;
                            while (true)
                            {
                                IL_05b6:
                                textBox1.Text = dBToDataTable.Rows[0]["FLD_PID"].ToString();
                                textBox2.Text = dBToDataTable.Rows[0]["FLD_QUESTITEM"].ToString();
                                textBox3.Text = dBToDataTable.Rows[0]["FLD_Name"].ToString();
                                textBox4.Text = dBToDataTable.Rows[0]["FLD_NJ"].ToString();
                                textBox5.Text = dBToDataTable.Rows[0]["FLD_RESIDE1"].ToString();
                                textBox6.Text = dBToDataTable.Rows[0]["FLD_RESIDE2"].ToString();
                                textBox7.Text = dBToDataTable.Rows[0]["FLD_SEX"].ToString();
                                textBox8.Text = dBToDataTable.Rows[0]["FLD_DF"].ToString();
                                textBox9.Text = dBToDataTable.Rows[0]["FLD_AT1"].ToString();
                                textBox10.Text = dBToDataTable.Rows[0]["FLD_AT2"].ToString();
                                textBox11.Text = dBToDataTable.Rows[0]["FLD_LEVEL"].ToString();
                                textBox12.Text = dBToDataTable.Rows[0]["FLD_JOB_LEVEL"].ToString();
                                textBox13.Text = dBToDataTable.Rows[0]["FLD_ZX"].ToString();
                                textBox14.Text = dBToDataTable.Rows[0]["FLD_DES"].ToString();
                                textBox21.Text = dBToDataTable.Rows[0]["FLD_TYPE"].ToString();
                                textBox22.Text = dBToDataTable.Rows[0]["FLD_WEIGHT"].ToString();
                                textBox23.Text = dBToDataTable.Rows[0]["FLD_MONEY"].ToString();
                                textBox24.Text = dBToDataTable.Rows[0]["FLD_EL"].ToString();
                                textBox25.Text = dBToDataTable.Rows[0]["FLD_WXJD"].ToString();
                                textBox26.Text = dBToDataTable.Rows[0]["FLD_WX"].ToString();
                                textBox16.Text = dBToDataTable.Rows[0]["FLD_HEAD_WEAR"].ToString();
                                num4 = 0;
                                var flag3 = true;
                                while (true)
                                {
                                    switch (num4 >= listBox1.Items.Count ? 11 : 6)
                                    {
                                        case 10:
                                            goto IL_05b6;
                                        case 1:
                                            return;
                                        case 5:
                                            return;
                                        case 6:
                                            if (!listBox1.Items[num4].Equals(textBox3.Text))
                                            {
                                                num4++;
                                                continue;
                                            }

                                            goto case 4;
                                        case 4:
                                            listBox1.SetSelected(num4, true);
                                            return;
                                        case 2:
                                        case 3:
                                        case 9:
                                            continue;
                                        case 7:
                                        case 8:
                                        case 11:
                                            goto IL_09fe;
                                        case 0:
                                            goto IL_0a09;
                                    }

                                    break;
                                }

                                break;
                            }

                            break;
                        }

                        MessageBox.Show("Không có vật phẩm như vậy, vui lòng kiểm tra lại！", "Msg");
                        IL_09fe:
                        dBToDataTable.Dispose();
                        return;
                    }
                }
            }
            catch (Exception ex3)
            {
                MessageBox.Show(ex3.ToString(), "错误");
                return;
            }

        MessageBox.Show("请选择查询的类型", "Msg");
    }

    private void button1_Click(object sender, EventArgs e)
    {
        if (listBox1.Items.Count == 0)
        {
            MessageBox.Show("Please query the database first", "Msg");
            return;
        }

        if (textBox1.Text == "")
        {
            MessageBox.Show("Vui lòng chọn mục cần sửa đổi!", "Msg");
            return;
        }

        if (textBox14.Text.Length > 120)
        {
            MessageBox.Show("Mô tả thiết bị không được quá 120 ký tự!");
            return;
        }

        if (textBox3.Text.Length > 15)
        {
            MessageBox.Show("Tên thiết bị không được quá 15 ký tự!");
            return;
        }

        try
        {
            var string_ = string.Format(
                "UPDATE TBL_XWWL_ITEM  SET FLD_Name='{1}',FLD_RESIDE1={2}, FLD_RESIDE2={3},FLD_SEX={4},FLD_DF={5},FLD_AT1={6},FLD_AT2={7},FLD_LEVEL={8},FLD_JOB_LEVEL={9},FLD_ZX={10},FLD_WEIGHT={11},FLD_MONEY={12},FLD_WXJD={13},FLD_WX={14},FLD_HEAD_WEAR={15} WHERE FLD_PID={0}",
                int.Parse(textBox1.Text), textBox3.Text, int.Parse(textBox5.Text), int.Parse(textBox6.Text),
                int.Parse(textBox7.Text), int.Parse(textBox8.Text), int.Parse(textBox9.Text), int.Parse(textBox10.Text),
                int.Parse(textBox11.Text), int.Parse(textBox12.Text), int.Parse(textBox13.Text),
                int.Parse(textBox22.Text), int.Parse(textBox23.Text), int.Parse(textBox25.Text),
                int.Parse(textBox26.Text), int.Parse(textBox16.Text));
            DBA.ExeSqlCommand(string_, "PublicDb");
            MessageBox.Show("Sửa đổi thành công!");
        }
        catch (Exception)
        {
            MessageBox.Show("Điều chỉnh lỗi!");
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        groupBox3 = new GroupBox();
        button2 = new Button();
        textBox15 = new TextBox();
        radioButton2 = new RadioButton();
        radioButton1 = new RadioButton();
        label28 = new Label();
        label27 = new Label();
        groupBox2 = new GroupBox();
        textBox16 = new TextBox();
        label21 = new Label();
        label20 = new Label();
        button1 = new Button();
        textBox14 = new TextBox();
        textBox21 = new TextBox();
        textBox22 = new TextBox();
        textBox23 = new TextBox();
        textBox24 = new TextBox();
        textBox25 = new TextBox();
        textBox26 = new TextBox();
        textBox13 = new TextBox();
        textBox12 = new TextBox();
        textBox11 = new TextBox();
        textBox10 = new TextBox();
        textBox9 = new TextBox();
        textBox8 = new TextBox();
        textBox7 = new TextBox();
        textBox6 = new TextBox();
        textBox5 = new TextBox();
        textBox4 = new TextBox();
        textBox3 = new TextBox();
        textBox2 = new TextBox();
        textBox1 = new TextBox();
        label25 = new Label();
        label19 = new Label();
        label18 = new Label();
        label17 = new Label();
        label16 = new Label();
        label15 = new Label();
        label14 = new Label();
        label13 = new Label();
        label12 = new Label();
        label11 = new Label();
        label10 = new Label();
        label9 = new Label();
        label8 = new Label();
        label7 = new Label();
        label6 = new Label();
        label5 = new Label();
        label4 = new Label();
        label3 = new Label();
        label2 = new Label();
        label1 = new Label();
        groupBox1 = new GroupBox();
        button3 = new Button();
        listBox1 = new ListBox();
        groupBox3.SuspendLayout();
        groupBox2.SuspendLayout();
        groupBox1.SuspendLayout();
        SuspendLayout();
        groupBox3.Controls.Add(button2);
        groupBox3.Controls.Add(textBox15);
        groupBox3.Controls.Add(radioButton2);
        groupBox3.Controls.Add(radioButton1);
        groupBox3.Location = new Point(12, 10);
        groupBox3.Name = "groupBox3";
        groupBox3.Size = new Size(785, 52);
        groupBox3.TabIndex = 15;
        groupBox3.TabStop = false;
        groupBox3.Text = "查找";
        button2.Location = new Point(286, 21);
        button2.Name = "button2";
        button2.Size = new Size(67, 25);
        button2.TabIndex = 9;
        button2.Text = "确定";
        button2.UseVisualStyleBackColor = true;
        button2.Click += button2_Click;
        textBox15.Location = new Point(16, 23);
        textBox15.Name = "textBox15";
        textBox15.Size = new Size(132, 20);
        textBox15.TabIndex = 6;
        radioButton2.AutoSize = true;
        radioButton2.Location = new Point(216, 24);
        radioButton2.Name = "radioButton2";
        radioButton2.Size = new Size(61, 17);
        radioButton2.TabIndex = 8;
        radioButton2.TabStop = true;
        radioButton2.Text = "物品名";
        radioButton2.UseVisualStyleBackColor = true;
        radioButton1.AutoSize = true;
        radioButton1.Location = new Point(169, 24);
        radioButton1.Name = "radioButton1";
        radioButton1.Size = new Size(43, 17);
        radioButton1.TabIndex = 7;
        radioButton1.TabStop = true;
        radioButton1.Text = "PID";
        radioButton1.UseVisualStyleBackColor = true;
        label28.AutoSize = true;
        label28.ForeColor = Color.Red;
        label28.Location = new Point(79, 430);
        label28.Name = "label28";
        label28.Size = new Size(41, 13);
        label28.TabIndex = 14;
        label28.Text = "label28";
        label27.AutoSize = true;
        label27.Location = new Point(14, 430);
        label27.Name = "label27";
        label27.Size = new Size(58, 13);
        label27.TabIndex = 13;
        label27.Text = "物品总数:";
        groupBox2.Controls.Add(textBox16);
        groupBox2.Controls.Add(label21);
        groupBox2.Controls.Add(label20);
        groupBox2.Controls.Add(button1);
        groupBox2.Controls.Add(textBox14);
        groupBox2.Controls.Add(textBox21);
        groupBox2.Controls.Add(textBox22);
        groupBox2.Controls.Add(textBox23);
        groupBox2.Controls.Add(textBox24);
        groupBox2.Controls.Add(textBox25);
        groupBox2.Controls.Add(textBox26);
        groupBox2.Controls.Add(textBox13);
        groupBox2.Controls.Add(textBox12);
        groupBox2.Controls.Add(textBox11);
        groupBox2.Controls.Add(textBox10);
        groupBox2.Controls.Add(textBox9);
        groupBox2.Controls.Add(textBox8);
        groupBox2.Controls.Add(textBox7);
        groupBox2.Controls.Add(textBox6);
        groupBox2.Controls.Add(textBox5);
        groupBox2.Controls.Add(textBox4);
        groupBox2.Controls.Add(textBox3);
        groupBox2.Controls.Add(textBox2);
        groupBox2.Controls.Add(textBox1);
        groupBox2.Controls.Add(label25);
        groupBox2.Controls.Add(label19);
        groupBox2.Controls.Add(label18);
        groupBox2.Controls.Add(label17);
        groupBox2.Controls.Add(label16);
        groupBox2.Controls.Add(label15);
        groupBox2.Controls.Add(label14);
        groupBox2.Controls.Add(label13);
        groupBox2.Controls.Add(label12);
        groupBox2.Controls.Add(label11);
        groupBox2.Controls.Add(label10);
        groupBox2.Controls.Add(label9);
        groupBox2.Controls.Add(label8);
        groupBox2.Controls.Add(label7);
        groupBox2.Controls.Add(label6);
        groupBox2.Controls.Add(label5);
        groupBox2.Controls.Add(label4);
        groupBox2.Controls.Add(label3);
        groupBox2.Controls.Add(label2);
        groupBox2.Controls.Add(label1);
        groupBox2.Location = new Point(269, 81);
        groupBox2.Name = "groupBox2";
        groupBox2.Size = new Size(528, 456);
        groupBox2.TabIndex = 12;
        groupBox2.TabStop = false;
        groupBox2.Text = "物品属性";
        textBox16.Location = new Point(356, 209);
        textBox16.Name = "textBox16";
        textBox16.Size = new Size(136, 20);
        textBox16.TabIndex = 55;
        label21.AutoSize = true;
        label21.Location = new Point(274, 211);
        label21.Name = "label21";
        label21.Size = new Size(29, 13);
        label21.TabIndex = 54;
        label21.Text = "Hair:";
        label20.AutoSize = true;
        label20.Location = new Point(268, 350);
        label20.Name = "label20";
        label20.Size = new Size(76, 13);
        label20.TabIndex = 53;
        label20.Text = "注:120字以内";
        button1.Location = new Point(356, 407);
        button1.Name = "button1";
        button1.Size = new Size(136, 25);
        button1.TabIndex = 52;
        button1.Text = "修改(重读物品)";
        button1.UseVisualStyleBackColor = true;
        button1.Click += button1_Click;
        textBox14.Location = new Point(356, 243);
        textBox14.Multiline = true;
        textBox14.Name = "textBox14";
        textBox14.Size = new Size(136, 147);
        textBox14.TabIndex = 51;
        textBox21.Location = new Point(356, 177);
        textBox21.Name = "textBox21";
        textBox21.Size = new Size(136, 20);
        textBox21.TabIndex = 44;
        textBox22.Location = new Point(356, 146);
        textBox22.Name = "textBox22";
        textBox22.Size = new Size(136, 20);
        textBox22.TabIndex = 43;
        textBox23.Location = new Point(356, 116);
        textBox23.Name = "textBox23";
        textBox23.Size = new Size(136, 20);
        textBox23.TabIndex = 42;
        textBox24.Location = new Point(356, 86);
        textBox24.Name = "textBox24";
        textBox24.Size = new Size(136, 20);
        textBox24.TabIndex = 41;
        textBox25.Location = new Point(356, 55);
        textBox25.Name = "textBox25";
        textBox25.Size = new Size(136, 20);
        textBox25.TabIndex = 40;
        textBox26.Location = new Point(356, 25);
        textBox26.Name = "textBox26";
        textBox26.Size = new Size(136, 20);
        textBox26.TabIndex = 39;
        textBox13.Location = new Point(108, 379);
        textBox13.Name = "textBox13";
        textBox13.Size = new Size(136, 20);
        textBox13.TabIndex = 38;
        textBox12.Location = new Point(108, 350);
        textBox12.Name = "textBox12";
        textBox12.Size = new Size(136, 20);
        textBox12.TabIndex = 37;
        textBox11.Location = new Point(108, 321);
        textBox11.Name = "textBox11";
        textBox11.Size = new Size(136, 20);
        textBox11.TabIndex = 36;
        textBox10.Location = new Point(108, 291);
        textBox10.Name = "textBox10";
        textBox10.Size = new Size(136, 20);
        textBox10.TabIndex = 35;
        textBox9.Location = new Point(108, 262);
        textBox9.Name = "textBox9";
        textBox9.Size = new Size(136, 20);
        textBox9.TabIndex = 34;
        textBox8.Location = new Point(108, 233);
        textBox8.Name = "textBox8";
        textBox8.Size = new Size(136, 20);
        textBox8.TabIndex = 33;
        textBox7.Location = new Point(108, 204);
        textBox7.Name = "textBox7";
        textBox7.Size = new Size(136, 20);
        textBox7.TabIndex = 32;
        textBox6.Location = new Point(108, 174);
        textBox6.Name = "textBox6";
        textBox6.Size = new Size(136, 20);
        textBox6.TabIndex = 31;
        textBox5.Location = new Point(108, 145);
        textBox5.Name = "textBox5";
        textBox5.Size = new Size(136, 20);
        textBox5.TabIndex = 30;
        textBox4.Location = new Point(108, 116);
        textBox4.Name = "textBox4";
        textBox4.Size = new Size(136, 20);
        textBox4.TabIndex = 29;
        textBox3.Location = new Point(108, 87);
        textBox3.Name = "textBox3";
        textBox3.Size = new Size(136, 20);
        textBox3.TabIndex = 28;
        textBox2.Location = new Point(108, 57);
        textBox2.Name = "textBox2";
        textBox2.Size = new Size(136, 20);
        textBox2.TabIndex = 27;
        textBox1.Enabled = false;
        textBox1.Location = new Point(108, 28);
        textBox1.Name = "textBox1";
        textBox1.Size = new Size(136, 20);
        textBox1.TabIndex = 26;
        label25.AutoSize = true;
        label25.Location = new Point(274, 278);
        label25.Name = "label25";
        label25.Size = new Size(58, 13);
        label25.TabIndex = 24;
        label25.Text = "FLD_DES:";
        label19.AutoSize = true;
        label19.Location = new Point(274, 174);
        label19.Name = "label19";
        label19.Size = new Size(64, 13);
        label19.TabIndex = 18;
        label19.Text = "FLD_TYPE:";
        label18.AutoSize = true;
        label18.Location = new Point(12, 61);
        label18.Name = "label18";
        label18.Size = new Size(99, 13);
        label18.TabIndex = 17;
        label18.Text = "FLD_QUESTITEM:";
        label17.AutoSize = true;
        label17.Location = new Point(274, 57);
        label17.Name = "label17";
        label17.Size = new Size(67, 13);
        label17.TabIndex = 16;
        label17.Text = "FLD_WXJD:";
        label16.AutoSize = true;
        label16.Location = new Point(274, 28);
        label16.Name = "label16";
        label16.Size = new Size(54, 13);
        label16.TabIndex = 15;
        label16.Text = "FLD_WX:";
        label15.AutoSize = true;
        label15.Location = new Point(274, 87);
        label15.Name = "label15";
        label15.Size = new Size(49, 13);
        label15.TabIndex = 14;
        label15.Text = "FLD_EL:";
        label14.AutoSize = true;
        label14.Location = new Point(274, 116);
        label14.Name = "label14";
        label14.Size = new Size(75, 13);
        label14.TabIndex = 13;
        label14.Text = "FLD_MONEY:";
        label13.AutoSize = true;
        label13.Location = new Point(12, 119);
        label13.Name = "label13";
        label13.Size = new Size(49, 13);
        label13.TabIndex = 12;
        label13.Text = "FLD_NJ:";
        label12.AutoSize = true;
        label12.Location = new Point(12, 236);
        label12.Name = "label12";
        label12.Size = new Size(50, 13);
        label12.TabIndex = 11;
        label12.Text = "FLD_DF:";
        label11.AutoSize = true;
        label11.Location = new Point(12, 295);
        label11.Name = "label11";
        label11.Size = new Size(56, 13);
        label11.TabIndex = 10;
        label11.Text = "FLD_AT2:";
        label10.AutoSize = true;
        label10.Location = new Point(12, 265);
        label10.Name = "label10";
        label10.Size = new Size(56, 13);
        label10.TabIndex = 9;
        label10.Text = "FLD_AT1:";
        label9.AutoSize = true;
        label9.Location = new Point(274, 145);
        label9.Name = "label9";
        label9.Size = new Size(80, 13);
        label9.TabIndex = 8;
        label9.Text = "FLD_WEIGHT:";
        label8.AutoSize = true;
        label8.Location = new Point(12, 178);
        label8.Name = "label8";
        label8.Size = new Size(82, 13);
        label8.TabIndex = 7;
        label8.Text = "FLD_RESIDE2:";
        label7.AutoSize = true;
        label7.Location = new Point(12, 207);
        label7.Name = "label7";
        label7.Size = new Size(57, 13);
        label7.TabIndex = 6;
        label7.Text = "FLD_SEX:";
        label6.AutoSize = true;
        label6.Location = new Point(12, 353);
        label6.Name = "label6";
        label6.Size = new Size(95, 13);
        label6.TabIndex = 5;
        label6.Text = "FLD_JOB_LEVEL:";
        label5.AutoSize = true;
        label5.Location = new Point(12, 324);
        label5.Name = "label5";
        label5.Size = new Size(69, 13);
        label5.TabIndex = 4;
        label5.Text = "FLD_LEVEL:";
        label4.AutoSize = true;
        label4.Location = new Point(12, 148);
        label4.Name = "label4";
        label4.Size = new Size(82, 13);
        label4.TabIndex = 3;
        label4.Text = "FLD_RESIDE1:";
        label3.AutoSize = true;
        label3.Location = new Point(12, 382);
        label3.Name = "label3";
        label3.Size = new Size(50, 13);
        label3.TabIndex = 2;
        label3.Text = "FLD_ZX:";
        label2.AutoSize = true;
        label2.Location = new Point(12, 90);
        label2.Name = "label2";
        label2.Size = new Size(67, 13);
        label2.TabIndex = 1;
        label2.Text = "FLD_NAME:";
        label1.AutoSize = true;
        label1.Location = new Point(12, 31);
        label1.Name = "label1";
        label1.Size = new Size(54, 13);
        label1.TabIndex = 0;
        label1.Text = "FLD_PID:";
        groupBox1.Controls.Add(button3);
        groupBox1.Controls.Add(label28);
        groupBox1.Controls.Add(label27);
        groupBox1.Controls.Add(listBox1);
        groupBox1.Location = new Point(12, 81);
        groupBox1.Name = "groupBox1";
        groupBox1.Size = new Size(233, 456);
        groupBox1.TabIndex = 11;
        groupBox1.TabStop = false;
        groupBox1.Text = "物品列表";
        button3.Location = new Point(134, 423);
        button3.Name = "button3";
        button3.Size = new Size(75, 25);
        button3.TabIndex = 53;
        button3.Text = "查询物品";
        button3.UseVisualStyleBackColor = true;
        button3.Click += button3_Click;
        listBox1.FormattingEnabled = true;
        listBox1.Location = new Point(17, 35);
        listBox1.Name = "listBox1";
        listBox1.Size = new Size(192, 381);
        listBox1.TabIndex = 1;
        listBox1.SelectedIndexChanged += listBox1_SelectedValueChanged;
        AutoScaleDimensions = new SizeF(6f, 13f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(817, 548);
        Controls.Add(groupBox3);
        Controls.Add(groupBox2);
        Controls.Add(groupBox1);
        Name = "Form7";
        Text = "YBtool";
        groupBox3.ResumeLayout(false);
        groupBox3.PerformLayout();
        groupBox2.ResumeLayout(false);
        groupBox2.PerformLayout();
        groupBox1.ResumeLayout(false);
        groupBox1.PerformLayout();
        ResumeLayout(false);
    }
}