namespace RxjhServer;

public class BbgSellClass
{
    public int FLD_NJ { get; set; }

    public int FLD_中级附魂 { get; set; }

    public int FLD_绑定 { get; set; }

    public int 使用天数 { get; set; }

    public string Name { get; set; }

    public int PID { get; set; }

    public int Type { get; set; }

    public int MAGIC1 { get; set; }

    public int MAGIC2 { get; set; }

    public int MAGIC3 { get; set; }

    public int MAGIC5 { get; set; }

    public int MAGIC4 { get; set; }

    public int FLD_觉醒 { get; set; }

    public int FLD_进化 { get; set; }

    public int Price { get; set; }

    public int number { get; set; }

    public int retrun { get; set; }

    public static BbgSellClass 取物品数据(string string_1)
    {
        using (var enumerator = World.BachBaoCac_SoLieu.Values.GetEnumerator())
        {
            BbgSellClass bbgSellClass = null;
            while (enumerator.MoveNext())
            {
                bbgSellClass = enumerator.Current;
                if (bbgSellClass.Name == string_1) return bbgSellClass;
            }
        }

        return null;
    }

    public static BbgSellClass GetItem(int int_16)
    {
        if (World.BachBaoCac_SoLieu.TryGetValue(int_16, out var value)) return value;
        return null;
    }

    public static string smethod_0(int int_16)
    {
        if (World.BachBaoCac_SoLieu.TryGetValue(int_16, out var value)) return value.Name;
        return string.Empty;
    }
}