using System.Collections;
using System.Windows.Forms;

namespace RxjhServer;

public class ListViewColumnSorter : IComparer
{
    private readonly CaseInsensitiveComparer ObjectCompare;

    public ListViewColumnSorter()
    {
        SortColumn = 0;
        Order = SortOrder.None;
        ObjectCompare = new CaseInsensitiveComparer();
    }

    public int SortColumn { get; set; }

    public SortOrder Order { get; set; }

    public int Compare(object object_0, object object_1)
    {
        while (true)
        {
            var num = ObjectCompare.Compare(((ListViewItem)object_0).SubItems[SortColumn].Text,
                ((ListViewItem)object_1).SubItems[SortColumn].Text);
            while (true)
            {
                switch (Order != SortOrder.Ascending ? 3 : 0)
                {
                    case 0:
                        return num;
                    case 3:
                        if (Order == SortOrder.Descending) goto case 1;
                        return 0;
                    case 1:
                        return -num;
                    case 2:
                        continue;
                }

                break;
            }
        }
    }
}