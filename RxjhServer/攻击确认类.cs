using System;

namespace RxjhServer;

public class 攻击确认类
{
    public 攻击确认类()
    {
        人物ID = 0;
        被攻击人物ID = 0;
        武功ID = 0;
        攻击状态 = false;
        攻击类型 = 0;
        攻击间隔 = 0;
        攻击时间 = DateTime.Now;
    }

    public int 人物ID { get; set; }

    public int 被攻击人物ID { get; set; }

    public int 武功ID { get; set; }

    public bool 攻击状态 { get; set; }

    public int 攻击类型 { get; set; }

    public DateTime 攻击时间 { get; set; }

    public int 攻击间隔 { get; set; }

    public void 初始化(int int_5, int int_6, int int_7, int int_8, int int_9)
    {
        人物ID = int_5;
        被攻击人物ID = int_6;
        武功ID = int_7;
        攻击类型 = int_8;
        攻击间隔 = int_9;
        攻击时间 = DateTime.Now;
        攻击状态 = true;
    }
}