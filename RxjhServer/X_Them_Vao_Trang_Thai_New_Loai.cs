using System;
using System.Timers;

namespace RxjhServer;

public class X_Them_Vao_Trang_Thai_New_Loai : IDisposable
{
    public System.Timers.Timer npcyd;

    public Players Play;

    public DateTime time;

    public X_Them_Vao_Trang_Thai_New_Loai(Players Play_, int 物品ID, int 时间, int 数量, int 数量类型)
    {
        FLD_PID = 物品ID;
        SoLuong = 数量;
        SoLuongLoaiHinh = 数量类型;
        time = DateTime.Now;
        time = time.AddMilliseconds(时间);
        Play = Play_;
        npcyd = new System.Timers.Timer(时间);
        npcyd.Elapsed += 时间结束事件2;
        npcyd.Enabled = true;
        npcyd.AutoReset = false;
    }

    public int FLD_PID { get; set; }

    public int SoLuong { get; set; }

    public int SoLuongLoaiHinh { get; set; }

    public int FLD_sj => getsj();

    public void Dispose()
    {
        var num = 0;
        while (true)
        {
            switch (num)
            {
                case 1:
                    npcyd.Enabled = false;
                    npcyd.Close();
                    npcyd.Dispose();
                    npcyd = null;
                    num = 2;
                    continue;
                default:
                    if (npcyd != null)
                    {
                        num = 1;
                        continue;
                    }

                    break;
                case 2:
                    break;
            }

            break;
        }

        Play = null;
    }

    public int getsj()
    {
        return (int)time.Subtract(DateTime.Now).TotalMilliseconds;
    }

    public void 时间结束事件2(object sender, ElapsedEventArgs e)
    {
        ThoiGianKetThucSuKien();
    }

    public void ThoiGianKetThucSuKien()
    {
        var flag = true;
        while (true)
        {
            if (npcyd != null) goto IL_001a;
            IL_09fc:
            while (true)
            {
                IL_09fc_2:
                var flag2 = true;
                while (true)
                {
                    switch (Play == null ? 3 : 6)
                    {
                        case 8:
                            goto IL_001a;
                        case 3:
                            Dispose();
                            return;
                        case 6:
                            if (!Play.Exiting) goto case 4;
                            goto case 0;
                        case 4:
                        case 10:
                            if (!Play.Client.Running) goto case 0;
                            goto case 11;
                        case 0:
                            if (Play.AppendStatusNewList != null) goto case 9;
                            goto case 1;
                        case 9:
                            Play.AppendStatusNewList.Clear();
                            goto case 1;
                        case 1:
                            Dispose();
                            return;
                        case 11:
                            try
                            {
                                switch (FLD_PID)
                                {
                                    case 1:
                                        if (SoLuongLoaiHinh == 2)
                                        {
                                            Play.delFLD_ThemVaoTiLePhanTram_Attack(0.01 * SoLuong);
                                            if (Play.FLD_ThemVaoTiLePhanTram_CongKich < 0.0)
                                                Play.FLD_ThemVaoTiLePhanTram_CongKich = 0.0;
                                        }
                                        else if (SoLuongLoaiHinh == 1)
                                        {
                                            Play.FLD_NhanVat_ThemVao_CongKich -= SoLuong;
                                            if (Play.FLD_NhanVat_ThemVao_CongKich < 0)
                                                Play.FLD_NhanVat_ThemVao_CongKich = 0;
                                        }

                                        Play.UpdateMartialArtsAndStatus();
                                        break;
                                    case 2:
                                        if (SoLuongLoaiHinh == 2)
                                        {
                                            Play.delFLD_ThemVaoTiLePhanTram_PhongNgu(0.01 * SoLuong);
                                            if (Play.FLD_ThemVaoTiLePhanTram_PhongNgu < 0.0)
                                                Play.FLD_ThemVaoTiLePhanTram_PhongNgu = 0.0;
                                        }
                                        else if (SoLuongLoaiHinh == 1)
                                        {
                                            Play.FLD_NhanVat_ThemVao_PhongNgu -= SoLuong;
                                            if (Play.FLD_NhanVat_ThemVao_PhongNgu < 0)
                                                Play.FLD_NhanVat_ThemVao_PhongNgu = 0;
                                        }

                                        Play.UpdateMartialArtsAndStatus();
                                        break;
                                    case 3:
                                        if (SoLuongLoaiHinh == 2)
                                        {
                                            Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat -= 0.01 * SoLuong;
                                            if (Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat < 0.0)
                                                Play.FLD_ThemVaoTiLePhanTram_HPCaoNhat = 0.0;
                                        }
                                        else if (SoLuongLoaiHinh == 1)
                                        {
                                            Play.CharactersToAddMax_HP -= SoLuong;
                                            if (Play.CharactersToAddMax_HP < 0) Play.CharactersToAddMax_HP = 0;
                                        }

                                        if (Play.NhanVat_HP > Play.CharacterMax_HP)
                                            Play.NhanVat_HP = Play.CharacterMax_HP;
                                        Play.CapNhat_HP_MP_SP();
                                        break;
                                    case 4:
                                        if (SoLuongLoaiHinh == 2)
                                        {
                                            Play.BuffExpTlc = 0.0;
                                        }
                                        else if (SoLuongLoaiHinh == 1)
                                        {
                                            Play.CharactersToAddMax_MP -= SoLuong;
                                            if (Play.CharactersToAddMax_MP < 0) Play.CharactersToAddMax_MP = 0;
                                        }

                                        if (Play.NhanVat_MP > Play.CharacterMax_MP)
                                            Play.NhanVat_MP = Play.CharacterMax_MP;
                                        Play.CapNhat_HP_MP_SP();
                                        break;
                                    case 5:
                                        if (SoLuongLoaiHinh == 2)
                                        {
                                            Play.FLD_ThemVaoTiLePhanTram_TrungDich -= 0.01 * SoLuong;
                                            if (Play.FLD_ThemVaoTiLePhanTram_TrungDich < 0.0)
                                                Play.FLD_ThemVaoTiLePhanTram_TrungDich = 0.0;
                                            Play.UpdateMartialArtsAndStatus();
                                        }
                                        else if (SoLuongLoaiHinh == 1)
                                        {
                                            Play.FLD_NhanVat_ThemVao_TrungDich -= SoLuong;
                                            if (Play.FLD_NhanVat_ThemVao_TrungDich < 0)
                                                Play.FLD_NhanVat_ThemVao_TrungDich = 0;
                                            Play.UpdateMartialArtsAndStatus();
                                        }

                                        break;
                                    case 6:
                                        if (SoLuongLoaiHinh == 2)
                                        {
                                            Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh -= 0.01 * SoLuong;
                                            if (Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh < 0.0)
                                                Play.FLD_NhanVat_ThemVaoTiLePhanTram_NeTranh = 0.0;
                                            Play.UpdateMartialArtsAndStatus();
                                        }
                                        else if (SoLuongLoaiHinh == 1)
                                        {
                                            Play.FLD_NhanVat_ThemVao_NeTranh -= SoLuong;
                                            if (Play.FLD_NhanVat_ThemVao_NeTranh < 0)
                                                Play.FLD_NhanVat_ThemVao_NeTranh = 0;
                                            Play.UpdateMartialArtsAndStatus();
                                        }

                                        break;
                                    case 7:
                                        if (SoLuongLoaiHinh == 2)
                                        {
                                            Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram -= 0.01 * SoLuong;
                                            if (Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram < 0.0)
                                                Play.FLD_NhanVat_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
                                        }

                                        Play.UpdateMartialArtsAndStatus();
                                        break;
                                    case 8:
                                        if (SoLuongLoaiHinh == 2)
                                        {
                                            Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram -= 0.01 * SoLuong;
                                            if (Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram < 0.0)
                                                Play.FLD_NhanVat_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
                                        }

                                        Play.UpdateMartialArtsAndStatus();
                                        break;
                                    case 9:
                                        if (SoLuongLoaiHinh == 2)
                                        {
                                            Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem -= 0.01 * SoLuong;
                                            if (Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem < 0.0)
                                                Play.FLD_NhanVat_ThemVao_PhanTramKinhNghiem = 0.0;
                                        }

                                        break;
                                    case 10:
                                        if (SoLuongLoaiHinh == 2)
                                            Play.FLD_NhanVat_ThemVao_HopThanh_XacXuatThanhCong_TiLePhanTram -=
                                                0.01 * SoLuong;
                                        break;
                                    case 11:
                                        if (SoLuongLoaiHinh == 2) Play.BuffExpTlc = 0.0;
                                        break;
                                    case 12:
                                        if (SoLuongLoaiHinh == 2)
                                            Play.FLD_NhanVat_ThemVao_ThuHoachDuocTienTrongGame_TiLePhanTram -=
                                                0.01 * SoLuong;
                                        break;
                                    case 13:
                                        if (SoLuongLoaiHinh == 2)
                                            Play.FLD_NhanVat_ThemVao_XacXuatRotVatPham_TiLePhanTram -= 0.01 * SoLuong;
                                        break;
                                    case 14:
                                        Play.FLD_NhanVat_ThemVao_KhiCong -= SoLuong;
                                        Play.UpdateKhiCong();
                                        Play.UpdateMartialArtsAndStatus();
                                        break;
                                    case 15:
                                        if (SoLuongLoaiHinh == 2)
                                        {
                                            Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem -= 0.01 * SoLuong;
                                            if (Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem < 0.0)
                                                Play.FLD_NhanVat_ThemVao_PhanTramTraiNghiem = 0.0;
                                        }

                                        break;
                                }

                                Play.AppendStatusNewList?.Remove(FLD_PID);
                                Play.StateEffectsNew(FLD_PID, 0, FLD_sj, SoLuong, SoLuongLoaiHinh);
                                Dispose();
                                return;
                            }
                            catch (Exception ex)
                            {
                                Form1.WriteLine(1,
                                    "Thêm vào trạng thái New Loại Thời gian kết thúc sự kiệnPhạm sai lầm: [" + FLD_PID +
                                    "]" + ex);
                                return;
                            }
                            finally
                            {
                                Dispose();
                            }
                        case 5:
                            continue;
                        case 2:
                            goto IL_09fc_2;
                    }

                    break;
                }

                break;
            }

            continue;
            IL_001a:
            npcyd.Enabled = false;
            npcyd.Close();
            npcyd.Dispose();
            npcyd = null;
            goto IL_09fc;
        }
    }
}