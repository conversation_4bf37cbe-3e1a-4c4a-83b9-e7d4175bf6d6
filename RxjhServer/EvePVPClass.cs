using System;
using System.Collections.Generic;
using System.Timers;
using RxjhServer.DbClss;

namespace RxjhServer;

public class EvePVPClass : IDisposable
{
    private DateTime kssj;

    private DateTime kssjgj;

    private int kssjint;

    private Players PlayerA;

    private Players PlayerB;
    private readonly ThreadSafeDictionary<int, Players> playlist;

    private readonly System.Timers.Timer ThoiGian1;

    private System.Timers.Timer ThoiGian2;

    private System.Timers.Timer ThoiGian3;

    private System.Timers.Timer ThoiGian4;

    private System.Timers.Timer ThoiGian5;

    private System.Timers.Timer ThoiGian6;

    public EvePVPClass(ThreadSafeDictionary<int, Players> players)
    {
        playlist = new ThreadSafeDictionary<int, Players>();
        try
        {
            foreach (var value in players.Values)
            {
                playlist.Add(value.CharacterFullServerID, value);
                if (PlayerA == null)
                {
                    PlayerA = value;
                    PlayerA.PVPScore = 0;
                    PlayerA.PVP逃跑次数 = 0;
                }
                else if (PlayerB == null)
                {
                    PlayerB = value;
                    PlayerB.PVPScore = 0;
                    PlayerB.PVP逃跑次数 = 0;
                }
            }

            kssj = DateTime.Now.AddMinutes(3.0);
            World.Eve90Progress = 1;
            ThoiGian1 = new System.Timers.Timer(60000.0);
            ThoiGian1.Elapsed += 时间结束事件1;
            ThoiGian1.Enabled = true;
            ThoiGian1.AutoReset = true;
            时间结束事件1(null, null);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "PVP EveClassPhạm sai lầm: " + ex);
        }
    }

    public void Dispose()
    {
        IEnumerator<Players> enumerator = null;
        Players players = null;
        World.Eve90Progress = 0;
        World.Eve90_ThoiGian = 0;
        var num = 3;
        if (ThoiGian1 != null)
        {
            num = 7;
            ThoiGian1.Enabled = false;
            ThoiGian1.Close();
            ThoiGian1.Dispose();
            num = 13;
        }

        num = 8;
        if (ThoiGian2 != null)
        {
            num = 0;
            ThoiGian2.Enabled = false;
            ThoiGian2.Close();
            ThoiGian2.Dispose();
            num = 11;
        }

        num = 10;
        if (ThoiGian3 != null)
        {
            num = 12;
            ThoiGian3.Enabled = false;
            ThoiGian3.Close();
            ThoiGian3.Dispose();
            num = 6;
        }

        num = 2;
        if (ThoiGian4 != null)
        {
            num = 15;
            ThoiGian4.Enabled = false;
            ThoiGian4.Close();
            ThoiGian4.Dispose();
            num = 9;
        }

        num = 14;
        if (ThoiGian6 != null)
        {
            num = 1;
            ThoiGian6.Enabled = false;
            ThoiGian6.Close();
            ThoiGian6.Dispose();
            num = 4;
        }

        enumerator = World.allConnectedChars.Values.GetEnumerator();
        num = 5;
        try
        {
            num = 0;
            while (true)
            {
                num = 3;
                if (!enumerator.MoveNext()) break;
                players = enumerator.Current;
                num = 2;
                if (players.NhanVatToaDo_BanDo == 2301)
                {
                    num = 5;
                    players.Mobile(529f, 1528f, 15f, 101);
                    players.SwitchPkMode(0);
                    num = 4;
                }
            }

            num = 1;
            num = 6;
        }
        finally
        {
            num = 1;
            while (true)
            {
                switch (num)
                {
                    case 2:
                        break;
                    case 0:
                        enumerator.Dispose();
                        num = 2;
                        continue;
                    default:
                        if (enumerator != null)
                        {
                            num = 0;
                            continue;
                        }

                        break;
                }

                break;
            }
        }

        PlayerA = null;
        PlayerB = null;
        World.evePlayers.Clear();
        World.evePlayers = null;
        World.EVEPVP = null;
    }

    public void 时间结束事件1(object sender, ElapsedEventArgs e)
    {
        try
        {
            var num2 = 0;
            Players players = null;
            IEnumerator<Players> enumerator = null;
            var num3 = (int)kssj.Subtract(DateTime.Now).TotalSeconds;
            num2 = 4;
            if (num3 <= 0)
            {
                num2 = 8;
                World.Eve90Progress = 2;
                num3 = 0;
                num2 = 6;
            }

            kssjint = num3;
            enumerator = World.allConnectedChars.Values.GetEnumerator();
            num2 = 12;
            try
            {
                num2 = 1;
                while (true)
                {
                    num2 = 3;
                    if (!enumerator.MoveNext()) break;
                    players = enumerator.Current;
                    num2 = 5;
                    if (!players.Client.TreoMay)
                    {
                        num2 = 2;
                        players.HeThongNhacNho(
                            "[" + PlayerA.UserName + "] Cùng [" + PlayerB.UserName +
                            "] Sãìp taòi cao cấp sân thi ðâìu ðoò sýìc, Hệ Thống. seÞ taòi." + kssjint +
                            "Giây sau tyÒ Số.", 2, ":");
                        players.HeThongNhacNho(
                            "NgýõÌi chõi coì thêÒ ðýa vaÌo chiÒ lêònh. [! Cao cấp aìp chuì ðõn ] Hoãòc laÌ [! Cao cấp aìp chuì song ] Ðãòt cýõòc. LâÌn naÌy ðãòt cýõòc Số lượng laÌ" +
                            World.SoLuongChoPhep_NguoiChoiDatCuoc + "Nguyên baÒo", 13, ":");
                        num2 = 0;
                    }
                }

                num2 = 4;
                num2 = 6;
            }
            finally
            {
                num2 = 0;
                while (true)
                {
                    switch (num2)
                    {
                        case 2:
                            break;
                        case 1:
                            enumerator.Dispose();
                            num2 = 2;
                            continue;
                        default:
                            if (enumerator != null)
                            {
                                num2 = 1;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            num2 = 3;
            if (kssjint <= 0)
            {
                num2 = 5;
                num2 = 11;
                if (PlayerA.CheckIfThePlayerIsInTheDuelZone(PlayerA))
                {
                    num2 = 7;
                    PlayerA.Mobile(120f, 0f, 15f, 2301);
                    num2 = 9;
                }

                num2 = 0;
                if (PlayerB.CheckIfThePlayerIsInTheDuelZone(PlayerB))
                {
                    num2 = 10;
                    PlayerB.Mobile(120f, 0f, 15f, 2301);
                    num2 = 2;
                }

                PlayerA.SystemNotification("bắt đầu, kêìt quaÒ ðang câòp nhâòt...");
                PlayerB.SystemNotification("bắt đầu, kêìt quaÒ ðang câòp nhâòt...");
                ThoiGian1.Enabled = false;
                ThoiGian1.Close();
                ThoiGian1.Dispose();
                World.Eve90Progress = 3;
                kssjgj = DateTime.Now.AddMinutes(10.0);
                ThoiGian2 = new System.Timers.Timer(10000.0);
                ThoiGian2.Elapsed += 时间结束事件2;
                ThoiGian2.Enabled = true;
                ThoiGian2.AutoReset = true;
                ThoiGian6 = new System.Timers.Timer(1000.0);
                ThoiGian6.Elapsed += 时间结束事件6;
                ThoiGian6.Enabled = true;
                ThoiGian6.AutoReset = true;
                num2 = 1;
            }

            num2 = 13;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "PVP Thời gian kết thúc sự kiện 222 Phạm sai lầm: " + ex);
        }
    }

    public void 时间结束事件2(object sender, ElapsedEventArgs e)
    {
        try
        {
            var num2 = 0;
            var num3 = World.Eve90_ThoiGian = (int)kssjgj.Subtract(DateTime.Now).TotalSeconds;
            var enumerator = playlist.Values.GetEnumerator();
            num2 = 3;
            try
            {
                num2 = 1;
                while (true)
                {
                    num2 = 2;
                    if (!enumerator.MoveNext()) break;
                    var current = enumerator.Current;
                    current.HeThongNhacNho(
                        PlayerA.UserName + "->[" + PlayerA.PVPScore + "] VS " + PlayerB.UserName + "->[" +
                        PlayerA.PVPScore + "] Tranh taÌi kết thúc ðêìm ngýõòc" + World.Eve90_ThoiGian + "giây！", 13,
                        ":");
                    num2 = 0;
                }

                num2 = 3;
                num2 = 4;
            }
            finally
            {
                num2 = 0;
                while (true)
                {
                    switch (num2)
                    {
                        case 2:
                            break;
                        case 1:
                            enumerator.Dispose();
                            num2 = 2;
                            continue;
                        default:
                            if (enumerator != null)
                            {
                                num2 = 1;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            num2 = 1;
            if (num3 > 0)
            {
                num2 = 7;
                num2 = 2;
                if (PlayerA.Client.Running)
                {
                    num2 = 4;
                    num2 = 5;
                    if (PlayerB.Client.Running) goto IL_022c;
                    num2 = 6;
                }
            }

            ThoiGian2.Enabled = false;
            ThoiGian2.Close();
            ThoiGian2.Dispose();
            ThoiGian6.Enabled = false;
            ThoiGian6.Close();
            ThoiGian6.Dispose();
            World.Eve90Progress = 4;
            ThoiGian3 = new System.Timers.Timer(10000.0);
            ThoiGian3.Elapsed += 时间结束事件3;
            ThoiGian3.Enabled = true;
            ThoiGian3.AutoReset = false;
            num2 = 0;
            IL_022c:
            num2 = 8;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "PVP 时间结束事件2 出错：" + ex);
        }
    }

    public void 时间结束事件3(object sender, ElapsedEventArgs e)
    {
        try
        {
            var num2 = 0;
            var num3 = 0;
            IEnumerator<Players> enumerator = null;
            IEnumerator<Players> enumerator2 = null;
            IEnumerator<Players> enumerator3 = null;
            Players players = null;
            Players players2 = null;
            IEnumerator<Players> enumerator4 = null;
            IEnumerator<Players> enumerator5 = null;
            IEnumerator<Players> enumerator6 = null;
            Players players3 = null;
            Players players4 = null;
            IEnumerator<Players> enumerator7 = null;
            Players players5 = null;
            IEnumerator<Players> enumerator8 = null;
            Players players6 = null;
            IEnumerator<Players> enumerator9 = null;
            Players players7 = null;
            Players players8 = null;
            Players players9 = null;
            var num4 = (int)(World.PhiVaoCua_ToiThieu * 2 * (1.0 - World.SanTapTienThue_TiLePhanTram));
            num2 = 9;
            if (!PlayerA.Client.Running)
            {
                num2 = 25;
                num2 = 11;
                if (!PlayerB.Client.Running)
                {
                    num2 = 1;
                    enumerator8 = World.allConnectedChars.Values.GetEnumerator();
                    num2 = 15;
                    try
                    {
                        num2 = 2;
                        while (true)
                        {
                            num2 = 0;
                            if (!enumerator8.MoveNext()) break;
                            players5 = enumerator8.Current;
                            num2 = 3;
                            if (!players5.Client.TreoMay)
                            {
                                num2 = 1;
                                players5.HeThongNhacNho(
                                    "BõÒi viÌ song phýõng giýÞa trâòn rõÌi khoÒi, tranh taÌi kết thúc", 7, ":");
                                num2 = 5;
                            }
                        }

                        num2 = 4;
                        num2 = 6;
                    }
                    finally
                    {
                        num2 = 2;
                        while (true)
                        {
                            switch (num2)
                            {
                                case 1:
                                    break;
                                case 0:
                                    enumerator8.Dispose();
                                    num2 = 1;
                                    continue;
                                default:
                                    if (enumerator8 != null)
                                    {
                                        num2 = 0;
                                        continue;
                                    }

                                    break;
                            }

                            break;
                        }
                    }

                    DBA.ExeSqlCommand(string.Format(
                        "INSERT INTO TBL_XWWL_PVP (场地代号,A玩家,B玩家,A杀人数,B杀人数,A逃跑次数,B逃跑次数,A获得元宝,B获得元宝,比赛结果)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')",
                        90, PlayerA.UserName, PlayerB.UserName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVP逃跑次数,
                        PlayerB.PVP逃跑次数, 0, 0, "双方弃权"));
                    num2 = 4;
                    goto IL_1553;
                }
            }

            num2 = 13;
            if (!PlayerA.Client.Running)
            {
                num2 = 16;
                PlayerB.CheckTheNumberOfIngotsInBaibaoge();
                PlayerB.KiemSoatNguyenBao_SoLuong(num4, 1);
                RxjhClass.BachBaoCacRecord(PlayerB.Userid, PlayerB.UserName, 0.0, "90PVP胜利", 1, num4);
                PlayerB.Save_NguyenBaoData();
                enumerator7 = World.allConnectedChars.Values.GetEnumerator();
                num2 = 12;
                try
                {
                    num2 = 2;
                    while (true)
                    {
                        num2 = 4;
                        if (!enumerator7.MoveNext()) break;
                        players6 = enumerator7.Current;
                        num2 = 5;
                        if (!players6.Client.TreoMay)
                        {
                            num2 = 3;
                            players6.HeThongNhacNho(
                                "Thâòt ðaìng tiêìc! BõÒi viÌ [" + PlayerA.UserName + "] GiýÞa trâòn rõÌi khoÒi,[" +
                                PlayerB.UserName + "] Chiêìn thãìng, cao cấp thi ðâìu tranh taÌi kết thúc", 2,
                                "Hệ Thống thôìng");
                            num2 = 0;
                        }
                    }

                    num2 = 6;
                    num2 = 1;
                }
                finally
                {
                    num2 = 1;
                    while (true)
                    {
                        switch (num2)
                        {
                            case 2:
                                break;
                            case 0:
                                enumerator7.Dispose();
                                num2 = 2;
                                continue;
                            default:
                                if (enumerator7 != null)
                                {
                                    num2 = 0;
                                    continue;
                                }

                                break;
                        }

                        break;
                    }
                }

                DBA.ExeSqlCommand(string.Format(
                    "INSERT INTO TBL_XWWL_PVP (场地代号,A玩家,B玩家,A杀人数,B杀人数,A逃跑次数,B逃跑次数,A获得元宝,B获得元宝,比赛结果)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')",
                    90, PlayerA.UserName, PlayerB.UserName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVP逃跑次数,
                    PlayerB.PVP逃跑次数, 0, num4, PlayerA.UserName + " 退出比赛"));
                RxjhClass.Set_NguoiVinhDu_SoLieu(2, PlayerB.UserName, PlayerB.Player_Job, PlayerB.Player_Level,
                    PlayerB.Player_Zx, PlayerB.GangName, string.Empty, PlayerB.PVPScore);
                num2 = 27;
            }
            else
            {
                num2 = 20;
                if (!PlayerB.Client.Running)
                {
                    num2 = 28;
                    PlayerA.CheckTheNumberOfIngotsInBaibaoge();
                    PlayerA.KiemSoatNguyenBao_SoLuong(num4, 1);
                    RxjhClass.BachBaoCacRecord(PlayerA.Userid, PlayerA.UserName, 0.0, "PVP胜利", 1, num4);
                    PlayerA.Save_NguyenBaoData();
                    enumerator2 = World.allConnectedChars.Values.GetEnumerator();
                    num2 = 24;
                    try
                    {
                        num2 = 6;
                        while (true)
                        {
                            num2 = 1;
                            if (!enumerator2.MoveNext()) break;
                            players = enumerator2.Current;
                            num2 = 0;
                            if (!players.Client.TreoMay)
                            {
                                num2 = 2;
                                players.HeThongNhacNho(
                                    "Thâòt ðaìng tiêìc! BõÒi viÌ [" + PlayerA.UserName + "] GiýÞa trâòn rõÌi khoÒi,[" +
                                    PlayerB.UserName + "] Chiêìn thãìng, cao cấp thi ðâìu tranh taÌi kết thúc", 2, ":");
                                num2 = 3;
                            }
                        }

                        num2 = 4;
                        num2 = 5;
                    }
                    finally
                    {
                        num2 = 0;
                        while (true)
                        {
                            switch (num2)
                            {
                                case 2:
                                    break;
                                case 1:
                                    enumerator2.Dispose();
                                    num2 = 2;
                                    continue;
                                default:
                                    if (enumerator2 != null)
                                    {
                                        num2 = 1;
                                        continue;
                                    }

                                    break;
                            }

                            break;
                        }
                    }

                    DBA.ExeSqlCommand(string.Format(
                        "INSERT INTO TBL_XWWL_PVP (场地代号,A玩家,B玩家,A杀人数,B杀人数,A逃跑次数,B逃跑次数,A获得元宝,B获得元宝,比赛结果)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')",
                        90, PlayerA.UserName, PlayerB.UserName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVP逃跑次数,
                        PlayerB.PVP逃跑次数, num4, 0, PlayerB.UserName + " 退出比赛"));
                    RxjhClass.Set_NguoiVinhDu_SoLieu(2, PlayerA.UserName, PlayerA.Player_Job, PlayerA.Player_Level,
                        PlayerA.Player_Zx, PlayerA.GangName, string.Empty, PlayerA.PVPScore);
                    num2 = 18;
                }
                else
                {
                    num2 = 30;
                    if (PlayerA.PVPScore > PlayerB.PVPScore)
                    {
                        num2 = 7;
                        PlayerA.CheckTheNumberOfIngotsInBaibaoge();
                        PlayerA.KiemSoatNguyenBao_SoLuong(num4, 1);
                        RxjhClass.BachBaoCacRecord(PlayerA.Userid, PlayerA.UserName, 0.0, "90PVP胜利", 1, num4);
                        PlayerA.Save_NguyenBaoData();
                        enumerator4 = World.allConnectedChars.Values.GetEnumerator();
                        num2 = 22;
                        try
                        {
                            num2 = 1;
                            while (true)
                            {
                                num2 = 5;
                                if (!enumerator4.MoveNext()) break;
                                players2 = enumerator4.Current;
                                num2 = 6;
                                if (!players2.Client.TreoMay)
                                {
                                    num2 = 4;
                                    players2.HeThongNhacNho(
                                        "Cao cấp thi ðâìu tranh taÌi kết thúc,[" + PlayerA.UserName + "][" +
                                        PlayerA.PVPScore + "] VS [" + PlayerB.UserName + "][" + PlayerB.PVPScore +
                                        "] Chiêìn thãìng phýõng thu hoaòch ðýõòc" + num4 + "Nguyên baÒo", 2, ":");
                                    num2 = 0;
                                }
                            }

                            num2 = 2;
                            num2 = 3;
                        }
                        finally
                        {
                            num2 = 0;
                            while (true)
                            {
                                switch (num2)
                                {
                                    case 1:
                                        break;
                                    default:
                                        if (enumerator4 != null)
                                        {
                                            num2 = 2;
                                            continue;
                                        }

                                        break;
                                    case 2:
                                        enumerator4.Dispose();
                                        num2 = 1;
                                        continue;
                                }

                                break;
                            }
                        }

                        PlayerA.FLD_PVP_Piont++;
                        DBA.ExeSqlCommand(string.Format(
                            "INSERT INTO TBL_XWWL_PVP (场地代号,A玩家,B玩家,A杀人数,B杀人数,A逃跑次数,B逃跑次数,A获得元宝,B获得元宝,比赛结果)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')",
                            90, PlayerA.UserName, PlayerB.UserName, PlayerA.PVPScore, PlayerB.PVPScore, PlayerA.PVP逃跑次数,
                            PlayerB.PVP逃跑次数, num4, 0, PlayerA.UserName + " 获胜"));
                        RxjhClass.Set_NguoiVinhDu_SoLieu(2, PlayerA.UserName, PlayerA.Player_Job, PlayerA.Player_Level,
                            PlayerA.Player_Zx, PlayerA.GangName, string.Empty, PlayerA.PVPScore);
                        num2 = 23;
                    }
                    else
                    {
                        num2 = 8;
                        if (PlayerA.PVPScore == PlayerB.PVPScore)
                        {
                            num2 = 14;
                            num2 = 32;
                            if (PlayerA.PVP逃跑次数 > PlayerB.PVP逃跑次数)
                            {
                                num2 = 26;
                                PlayerB.CheckTheNumberOfIngotsInBaibaoge();
                                PlayerB.KiemSoatNguyenBao_SoLuong(num4, 1);
                                RxjhClass.BachBaoCacRecord(PlayerB.Userid, PlayerB.UserName, 0.0, "90PVP胜利", 1, num4);
                                PlayerB.Save_NguyenBaoData();
                                enumerator3 = World.allConnectedChars.Values.GetEnumerator();
                                num2 = 5;
                                try
                                {
                                    num2 = 2;
                                    while (true)
                                    {
                                        num2 = 3;
                                        if (!enumerator3.MoveNext()) break;
                                        players8 = enumerator3.Current;
                                        num2 = 1;
                                        if (!players8.Client.TreoMay)
                                        {
                                            num2 = 6;
                                            players8.HeThongNhacNho(
                                                "Quyêìt ðâìu châìm dýì" + PlayerA.UserName + "ðaÞ baòi trâòn," +
                                                PlayerB.UserName + " chiêìn thãìng, Nhận được:" + num4 + "Point！", 2,
                                                "PVP");
                                            num2 = 5;
                                        }
                                    }

                                    num2 = 4;
                                    num2 = 0;
                                }
                                finally
                                {
                                    num2 = 1;
                                    while (true)
                                    {
                                        switch (num2)
                                        {
                                            case 0:
                                                break;
                                            default:
                                                if (enumerator3 != null)
                                                {
                                                    num2 = 2;
                                                    continue;
                                                }

                                                break;
                                            case 2:
                                                enumerator3.Dispose();
                                                num2 = 0;
                                                continue;
                                        }

                                        break;
                                    }
                                }

                                PlayerB.FLD_PVP_Piont++;
                                DBA.ExeSqlCommand(string.Format(
                                    "INSERT INTO TBL_XWWL_PVP (场地代号,A玩家,B玩家,A杀人数,B杀人数,A逃跑次数,B逃跑次数,A获得元宝,B获得元宝,比赛结果)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')",
                                    90, PlayerA.UserName, PlayerB.UserName, PlayerA.PVPScore, PlayerB.PVPScore,
                                    PlayerA.PVP逃跑次数, PlayerB.PVP逃跑次数, 0, num4, "分数相同," + PlayerB.UserName + " 获胜"));
                                RxjhClass.Set_NguoiVinhDu_SoLieu(2, PlayerB.UserName, PlayerB.Player_Job,
                                    PlayerB.Player_Level, PlayerB.Player_Zx, PlayerB.GangName, string.Empty,
                                    PlayerB.PVPScore);
                                num2 = 29;
                            }
                            else
                            {
                                num2 = 21;
                                if (PlayerA.PVP逃跑次数 == PlayerB.PVP逃跑次数)
                                {
                                    num2 = 3;
                                    PlayerA.CheckTheNumberOfIngotsInBaibaoge();
                                    PlayerA.KiemSoatNguyenBao_SoLuong(
                                        (int)(World.PhiVaoCua_ToiThieu * (1.0 - World.SanTapTienThue_TiLePhanTram)), 1);
                                    RxjhClass.BachBaoCacRecord(PlayerA.Userid, PlayerA.UserName, 0.0, "90PVP返还", 1,
                                        (int)(World.PhiVaoCua_ToiThieu * (1.0 - World.SanTapTienThue_TiLePhanTram)));
                                    PlayerA.Save_NguyenBaoData();
                                    PlayerB.CheckTheNumberOfIngotsInBaibaoge();
                                    PlayerB.KiemSoatNguyenBao_SoLuong(
                                        (int)(World.PhiVaoCua_ToiThieu * (1.0 - World.SanTapTienThue_TiLePhanTram)), 1);
                                    RxjhClass.BachBaoCacRecord(PlayerB.Userid, PlayerB.UserName, 0.0, "90PVP返还", 1,
                                        (int)(World.PhiVaoCua_ToiThieu * (1.0 - World.SanTapTienThue_TiLePhanTram)));
                                    PlayerB.Save_NguyenBaoData();
                                    enumerator6 = World.allConnectedChars.Values.GetEnumerator();
                                    num2 = 19;
                                    try
                                    {
                                        num2 = 2;
                                        while (true)
                                        {
                                            num2 = 5;
                                            if (!enumerator6.MoveNext()) break;
                                            players3 = enumerator6.Current;
                                            num2 = 3;
                                            if (!players3.Client.TreoMay)
                                            {
                                                num2 = 4;
                                                players3.HeThongNhacNho(
                                                    "Quyêìt ðâìu châìm dýìt，Bâìt Phân Thãìng BaòilâÌn sau taìi ðâìu！",
                                                    2, "PVP");
                                                num2 = 6;
                                            }
                                        }

                                        num2 = 1;
                                        num2 = 0;
                                    }
                                    finally
                                    {
                                        num2 = 1;
                                        while (true)
                                        {
                                            switch (num2)
                                            {
                                                case 0:
                                                    break;
                                                default:
                                                    if (enumerator6 != null)
                                                    {
                                                        num2 = 2;
                                                        continue;
                                                    }

                                                    break;
                                                case 2:
                                                    enumerator6.Dispose();
                                                    num2 = 0;
                                                    continue;
                                            }

                                            break;
                                        }
                                    }

                                    DBA.ExeSqlCommand(string.Format(
                                        "INSERT INTO TBL_XWWL_PVP (场地代号,A玩家,B玩家,A杀人数,B杀人数,A逃跑次数,B逃跑次数,A获得元宝,B获得元宝,比赛结果)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')",
                                        90, PlayerA.UserName, PlayerB.UserName, PlayerA.PVPScore, PlayerB.PVPScore,
                                        PlayerA.PVP逃跑次数, PlayerB.PVP逃跑次数,
                                        (int)(World.PhiVaoCua_ToiThieu * (1.0 - World.SanTapTienThue_TiLePhanTram)),
                                        (int)(World.PhiVaoCua_ToiThieu * (1.0 - World.SanTapTienThue_TiLePhanTram)),
                                        "平局"));
                                    RxjhClass.Set_NguoiVinhDu_SoLieu(2, PlayerA.UserName, PlayerA.Player_Job,
                                        PlayerA.Player_Level, PlayerA.Player_Zx, PlayerA.GangName, string.Empty,
                                        PlayerA.PVPScore);
                                    RxjhClass.Set_NguoiVinhDu_SoLieu(2, PlayerB.UserName, PlayerB.Player_Job,
                                        PlayerB.Player_Level, PlayerB.Player_Zx, PlayerB.GangName, string.Empty,
                                        PlayerB.PVPScore);
                                    num2 = 10;
                                }
                                else
                                {
                                    PlayerA.CheckTheNumberOfIngotsInBaibaoge();
                                    PlayerA.KiemSoatNguyenBao_SoLuong(num4, 1);
                                    RxjhClass.BachBaoCacRecord(PlayerA.Userid, PlayerA.UserName, 0.0, "90PVP获得", 1,
                                        num4);
                                    PlayerA.Save_NguyenBaoData();
                                    enumerator5 = World.allConnectedChars.Values.GetEnumerator();
                                    num2 = 17;
                                    try
                                    {
                                        num2 = 6;
                                        while (true)
                                        {
                                            num2 = 0;
                                            if (!enumerator5.MoveNext()) break;
                                            players4 = enumerator5.Current;
                                            num2 = 1;
                                            if (!players4.Client.TreoMay)
                                            {
                                                num2 = 4;
                                                players4.HeThongNhacNho(
                                                    "Quyêìt ðâìu châìm dýìtCao ThuÒ:" + PlayerB.UserName +
                                                    "ðaÞ baòi trâòn," + PlayerA.UserName +
                                                    "daÌnh chiêìn thãìng, Nhận được" + num4 + "Point！", 2, "PVP");
                                                num2 = 3;
                                            }
                                        }

                                        num2 = 2;
                                        num2 = 5;
                                    }
                                    finally
                                    {
                                        num2 = 2;
                                        while (true)
                                        {
                                            switch (num2)
                                            {
                                                case 1:
                                                    break;
                                                case 0:
                                                    enumerator5.Dispose();
                                                    num2 = 1;
                                                    continue;
                                                default:
                                                    if (enumerator5 != null)
                                                    {
                                                        num2 = 0;
                                                        continue;
                                                    }

                                                    break;
                                            }

                                            break;
                                        }
                                    }

                                    PlayerA.FLD_PVP_Piont++;
                                    DBA.ExeSqlCommand(string.Format(
                                        "INSERT INTO TBL_XWWL_PVP (场地代号,A玩家,B玩家,A杀人数,B杀人数,A逃跑次数,B逃跑次数,A获得元宝,B获得元宝,比赛结果)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')",
                                        90, PlayerA.UserName, PlayerB.UserName, PlayerA.PVPScore, PlayerB.PVPScore,
                                        PlayerA.PVP逃跑次数, PlayerB.PVP逃跑次数, num4, 0, "分数相同," + PlayerA.UserName + " 获胜"));
                                    RxjhClass.Set_NguoiVinhDu_SoLieu(2, PlayerA.UserName, PlayerA.Player_Job,
                                        PlayerA.Player_Level, PlayerA.Player_Zx, PlayerA.GangName, string.Empty,
                                        PlayerA.PVPScore);
                                    num2 = 31;
                                }
                            }
                        }
                        else
                        {
                            PlayerB.CheckTheNumberOfIngotsInBaibaoge();
                            PlayerB.KiemSoatNguyenBao_SoLuong(num4, 1);
                            RxjhClass.BachBaoCacRecord(PlayerB.Userid, PlayerB.UserName, 0.0, "90PVP获得", 1, num4);
                            PlayerB.Save_NguyenBaoData();
                            enumerator9 = World.allConnectedChars.Values.GetEnumerator();
                            num2 = 6;
                            try
                            {
                                num2 = 2;
                                while (true)
                                {
                                    num2 = 6;
                                    if (!enumerator9.MoveNext()) break;
                                    players7 = enumerator9.Current;
                                    num2 = 5;
                                    if (!players7.Client.TreoMay)
                                    {
                                        num2 = 1;
                                        players7.HeThongNhacNho(
                                            "Quyêìt ðâìu châìm dýìt,[" + PlayerB.UserName + "][" + PlayerB.PVPScore +
                                            "] VS [" + PlayerA.UserName + "][" + PlayerA.PVPScore +
                                            "] Thãìng lõòi Nhận được" + num4 + "Point", 2, "PVP");
                                        num2 = 4;
                                    }
                                }

                                num2 = 3;
                                num2 = 0;
                            }
                            finally
                            {
                                num2 = 1;
                                while (true)
                                {
                                    switch (num2)
                                    {
                                        case 0:
                                            break;
                                        default:
                                            if (enumerator9 != null)
                                            {
                                                num2 = 2;
                                                continue;
                                            }

                                            break;
                                        case 2:
                                            enumerator9.Dispose();
                                            num2 = 0;
                                            continue;
                                    }

                                    break;
                                }
                            }

                            PlayerB.FLD_PVP_Piont++;
                            DBA.ExeSqlCommand(string.Format(
                                "INSERT INTO TBL_XWWL_PVP (场地代号,A玩家,B玩家,A杀人数,B杀人数,A逃跑次数,B逃跑次数,A获得元宝,B获得元宝,比赛结果)values({0},'{1}','{2}',{3},{4},{5},{6},{7},{8},'{9}')",
                                90, PlayerA.UserName, PlayerB.UserName, PlayerA.PVPScore, PlayerB.PVPScore,
                                PlayerA.PVP逃跑次数, PlayerB.PVP逃跑次数, 0, num4, PlayerB.UserName + " 获胜"));
                            RxjhClass.Set_NguoiVinhDu_SoLieu(2, PlayerB.UserName, PlayerB.Player_Job,
                                PlayerB.Player_Level, PlayerB.Player_Zx, PlayerB.GangName, string.Empty,
                                PlayerB.PVPScore);
                            num2 = 33;
                        }
                    }
                }
            }

            IL_1553:
            num3 = World.TinhToan_NguoiChoiDatCuoc_KetQua(PlayerA.PVPScore, PlayerB.PVPScore, 90);
            enumerator = World.allConnectedChars.Values.GetEnumerator();
            num2 = 0;
            try
            {
                num2 = 1;
                while (true)
                {
                    num2 = 3;
                    if (!enumerator.MoveNext()) break;
                    players9 = enumerator.Current;
                    num2 = 6;
                    if (!players9.Client.TreoMay)
                    {
                        num2 = 0;
                        players9.HeThongNhacNho(
                            "Quyêìt ðâìu châìm dýìt võìi tyÒ Số chung cuôòc: " + num3 + string.Empty, 3, "PVP Score");
                        num2 = 4;
                    }
                }

                num2 = 2;
                num2 = 5;
            }
            finally
            {
                num2 = 2;
                while (true)
                {
                    switch (num2)
                    {
                        case 0:
                            break;
                        default:
                            if (enumerator != null)
                            {
                                num2 = 1;
                                continue;
                            }

                            break;
                        case 1:
                            enumerator.Dispose();
                            num2 = 0;
                            continue;
                    }

                    break;
                }
            }

            World.Eve90Progress = 5;
            kssjgj = DateTime.Now.AddMinutes(1.0);
            ThoiGian3.Enabled = false;
            ThoiGian3.Close();
            ThoiGian3.Dispose();
            ThoiGian4 = new System.Timers.Timer(30000.0);
            ThoiGian4.Elapsed += 时间结束事件4;
            ThoiGian4.Enabled = true;
            ThoiGian4.AutoReset = true;
            ThoiGian5 = new System.Timers.Timer(30000.0);
            ThoiGian5.Elapsed += 时间结束事件5;
            ThoiGian5.Enabled = true;
            ThoiGian5.AutoReset = true;
            ThoiGian6.Enabled = false;
            ThoiGian6.Close();
            ThoiGian6.Dispose();
            num2 = 2;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "PVP Thời gian kết thúc sự kiện 333 Phạm sai lầm: " + ex);
            Dispose();
        }
    }

    public void 时间结束事件4(object sender, ElapsedEventArgs e)
    {
        try
        {
            if ((World.Eve90_ThoiGian = (int)kssjgj.Subtract(DateTime.Now).TotalSeconds) <= 0 ||
                !PlayerA.Client.Running || !PlayerB.Client.Running)
            {
                ThoiGian4.Enabled = false;
                ThoiGian4.Close();
                ThoiGian4.Dispose();
                World.Eve90Progress = 6;
                Dispose();
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "PVP Thời gian kết thúc sự kiện 444 Phạm sai lầm: " + ex);
        }
    }

    public void 时间结束事件5(object sender, ElapsedEventArgs e)
    {
        try
        {
            ThoiGian5.Enabled = false;
            ThoiGian5.Close();
            ThoiGian5.Dispose();
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "PVP Thời gian kết thúc sự kiện 555 Phạm sai lầm: " + ex);
        }
    }

    public void 时间结束事件6(object sender, ElapsedEventArgs e)
    {
        try
        {
            var num2 = 44;
            IEnumerator<Players> enumerator = null;
            Players players = null;
            if (PlayerA.Client != null)
            {
                num2 = 37;
                num2 = 21;
                if (PlayerA.Client.Running)
                {
                    num2 = 22;
                    num2 = 20;
                    if (PlayerB.Client != null)
                    {
                        num2 = 34;
                        num2 = 27;
                        if (PlayerB.Client.Running)
                        {
                            num2 = 0;
                            num2 = 14;
                            if (PlayerA.CheckIfThePlayerIsInTheDuelZone(PlayerA))
                            {
                                num2 = 4;
                                num2 = 50;
                                if (PlayerA.CharacterPKMode != 2)
                                {
                                    num2 = 39;
                                    PlayerA.SwitchPkMode(2);
                                    num2 = 10;
                                }
                            }
                            else
                            {
                                num2 = 30;
                                if (PlayerA.PVP逃跑次数 > World.SoLanTronThoat_ChoPhep)
                                {
                                    num2 = 16;
                                    num2 = 40;
                                    if (PlayerA.PVPScore > 0)
                                    {
                                        num2 = 52;
                                        PlayerA.PVPScore--;
                                        PlayerA.HeThongNhacNho(
                                            "你已累计逃跑" + PlayerA.PVP逃跑次数 + "次,超过系统限制的" + World.SoLanTronThoat_ChoPhep +
                                            "次,扣除一分！", 13, "系统警告");
                                        num2 = 5;
                                    }
                                    else
                                    {
                                        num2 = 18;
                                        if (PlayerA.PVPScore == 0)
                                        {
                                            num2 = 43;
                                            PlayerA.CheckTheNumberOfIngotsInBaibaoge();
                                            num2 = 47;
                                            if (PlayerA.FLD_RXPIONT >= World.NguyenBaoBiTru_SauKhiTruDiem)
                                            {
                                                num2 = 7;
                                                PlayerA.KiemSoatNguyenBao_SoLuong(World.NguyenBaoBiTru_SauKhiTruDiem,
                                                    0);
                                                RxjhClass.BachBaoCacRecord(PlayerA.Userid, PlayerA.UserName, 0.0,
                                                    "90PVP逃跑扣除", 1, World.NguyenBaoBiTru_SauKhiTruDiem);
                                                PlayerA.Save_NguyenBaoData();
                                                PlayerA.HeThongNhacNho(
                                                    "你当前分数为0且已累计逃跑" + PlayerA.PVP逃跑次数 + "次,超过系统限制的" +
                                                    World.SoLanTronThoat_ChoPhep + "次,扣除" +
                                                    World.NguyenBaoBiTru_SauKhiTruDiem + "元宝！", 13, "系统警告");
                                                num2 = 33;
                                            }
                                            else
                                            {
                                                num2 = 6;
                                                if (PlayerA.Player_Money >= World.TienBiTru_SauKhiTruDiem)
                                                {
                                                    num2 = 48;
                                                    PlayerA.Player_Money -= World.TienBiTru_SauKhiTruDiem;
                                                    PlayerA.UpdateMoneyAndWeight();
                                                    PlayerA.HeThongNhacNho(
                                                        "你当前分数为0且已累计逃跑" + PlayerA.PVP逃跑次数 + "次,超过系统限制的" +
                                                        World.SoLanTronThoat_ChoPhep + "次,扣除" +
                                                        World.TienBiTru_SauKhiTruDiem + "Gold", 13, "系统警告");
                                                    num2 = 49;
                                                }
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    num2 = 15;
                                    if (PlayerA.PVP逃跑次数 >= 5)
                                    {
                                        num2 = 19;
                                        PlayerA.HeThongNhacNho(
                                            "你已累计逃跑" + PlayerA.PVP逃跑次数 + "次,超过" + World.SoLanTronThoat_ChoPhep +
                                            "次后每次逃跑将扣除一分分数为0将扣除元宝或金钱！", 13, "系统警告");
                                        num2 = 29;
                                    }
                                }

                                PlayerA.PVP逃跑次数++;
                                PlayerA.Mobile(120f, 0f, 15f, 2301);
                                num2 = 3;
                            }

                            num2 = 9;
                            if (PlayerB.CheckIfThePlayerIsInTheDuelZone(PlayerB))
                            {
                                num2 = 45;
                                num2 = 32;
                                if (PlayerB.CharacterPKMode != 2)
                                {
                                    num2 = 51;
                                    PlayerB.SwitchPkMode(2);
                                    num2 = 38;
                                }
                            }
                            else
                            {
                                num2 = 25;
                                if (PlayerB.PVP逃跑次数 > World.SoLanTronThoat_ChoPhep)
                                {
                                    num2 = 41;
                                    num2 = 11;
                                    if (PlayerB.PVPScore > 0)
                                    {
                                        num2 = 13;
                                        PlayerB.PVPScore--;
                                        PlayerB.HeThongNhacNho(
                                            "你已累计逃跑" + PlayerB.PVP逃跑次数 + "次,超过系统限制的" + World.SoLanTronThoat_ChoPhep +
                                            "次,扣除一分！", 13, "警告");
                                        num2 = 28;
                                    }
                                    else
                                    {
                                        num2 = 46;
                                        if (PlayerB.PVPScore == 0)
                                        {
                                            num2 = 31;
                                            PlayerB.CheckTheNumberOfIngotsInBaibaoge();
                                            num2 = 26;
                                            if (PlayerB.FLD_RXPIONT >= World.NguyenBaoBiTru_SauKhiTruDiem)
                                            {
                                                num2 = 17;
                                                PlayerB.KiemSoatNguyenBao_SoLuong(World.NguyenBaoBiTru_SauKhiTruDiem,
                                                    0);
                                                RxjhClass.BachBaoCacRecord(PlayerB.Userid, PlayerB.UserName, 0.0,
                                                    "90PVP逃跑扣除", 1, World.NguyenBaoBiTru_SauKhiTruDiem);
                                                PlayerB.Save_NguyenBaoData();
                                                PlayerB.HeThongNhacNho(
                                                    "你当前分数为0且已累计逃跑" + PlayerB.PVP逃跑次数 + "次,超过系统限制的" +
                                                    World.SoLanTronThoat_ChoPhep + "次,扣除" +
                                                    World.NguyenBaoBiTru_SauKhiTruDiem + "元宝！", 13, "警告");
                                                num2 = 23;
                                            }
                                            else
                                            {
                                                num2 = 2;
                                                if (PlayerB.Player_Money >= World.TienBiTru_SauKhiTruDiem)
                                                {
                                                    num2 = 1;
                                                    PlayerB.Player_Money -= World.TienBiTru_SauKhiTruDiem;
                                                    PlayerB.UpdateMoneyAndWeight();
                                                    PlayerB.HeThongNhacNho(
                                                        "你当前分数为0且已累计逃跑" + PlayerB.PVP逃跑次数 + "次,超过系统限制的" +
                                                        World.SoLanTronThoat_ChoPhep + "次,扣除" +
                                                        World.TienBiTru_SauKhiTruDiem + "Gold", 13, "警告");
                                                    num2 = 53;
                                                }
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    num2 = 12;
                                    if (PlayerB.PVP逃跑次数 >= 5)
                                    {
                                        num2 = 8;
                                        PlayerB.HeThongNhacNho(
                                            "你已累计逃跑" + PlayerB.PVP逃跑次数 + "次,超过" + World.SoLanTronThoat_ChoPhep +
                                            "次后每次逃跑将扣除一分分数为0将扣除元宝或金钱！", 13, "警告");
                                        num2 = 42;
                                    }
                                }

                                PlayerB.PVP逃跑次数++;
                                PlayerB.Mobile(120f, 0f, 15f, 2301);
                                num2 = 24;
                            }

                            enumerator = World.allConnectedChars.Values.GetEnumerator();
                            num2 = 35;
                            try
                            {
                                num2 = 10;
                                while (true)
                                {
                                    num2 = 4;
                                    if (!enumerator.MoveNext()) break;
                                    players = enumerator.Current;
                                    num2 = 2;
                                    if (players.NhanVatToaDo_BanDo != 2301) continue;
                                    num2 = 8;
                                    num2 = 6;
                                    if (players.CharacterFullServerID != PlayerA.CharacterFullServerID)
                                    {
                                        num2 = 7;
                                        num2 = 0;
                                        if (players.CharacterFullServerID != PlayerB.CharacterFullServerID)
                                        {
                                            num2 = 1;
                                            players.Mobile(529f, 1528f, 15f, 101);
                                            num2 = 3;
                                        }
                                    }
                                }

                                num2 = 5;
                                num2 = 9;
                            }
                            finally
                            {
                                num2 = 2;
                                while (true)
                                {
                                    switch (num2)
                                    {
                                        case 1:
                                            break;
                                        case 0:
                                            enumerator.Dispose();
                                            num2 = 1;
                                            continue;
                                        default:
                                            if (enumerator != null)
                                            {
                                                num2 = 0;
                                                continue;
                                            }

                                            break;
                                    }

                                    break;
                                }
                            }
                        }
                    }
                }
            }

            num2 = 36;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "PVP Thời gian kết thúc sự kiện 666 Phạm sai lầm: " + ex);
            Dispose();
        }
    }
}