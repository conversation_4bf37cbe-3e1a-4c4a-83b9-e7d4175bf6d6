using System;

namespace RxjhServer;

public class X_Character_Template_Class
{
    public byte[] 人物模板_byte;

    public X_Character_Template_Class(byte[] byte_0)
    {
        人物模板_byte = byte_0;
    }

    public byte 脸型1
    {
        get => 人物模板_byte[0];
        set => 人物模板_byte[0] = value;
    }

    public short MauToc
    {
        get => BitConverter.ToInt16(人物模板_byte, 1);
        set => System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 人物模板_byte, 1, 2);
    }

    public byte 发型11
    {
        get => 人物模板_byte[3];
        set => 人物模板_byte[3] = value;
    }

    public byte AmThanh
    {
        get => 人物模板_byte[4];
        set => 人物模板_byte[4] = value;
    }

    public byte GioiTinh
    {
        get => 人物模板_byte[5];
        set => 人物模板_byte[5] = value;
    }

    public short KieuToc
    {
        get => BitConverter.ToInt16(人物模板_byte, 6);
        set => System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 人物模板_byte, 6, 2);
    }

    public short KhuonMat
    {
        get => BitConverter.ToInt16(人物模板_byte, 8);
        set => System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, 人物模板_byte, 8, 2);
    }
}