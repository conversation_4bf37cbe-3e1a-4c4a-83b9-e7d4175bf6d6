using System;
using System.Collections.Generic;

namespace RxjhServer;

public class RandomOrbOptionsClass
{
    public int ID { get; set; }

    public string FLD_OptionName { get; set; }

    public int FLD_Option { get; set; }

    public int FLD_Orb_Lv { get; set; }

    public int FLD_Orb_LvX { get; set; }

    public int FLD_Orb_VuKhi { get; set; }

    public int FLD_Orb_Ao { get; set; }

    public int FLD_Orb_Giap { get; set; }

    public int FLD_Orb_Tay { get; set; }

    public int FLD_Orb_Chan { get; set; }

    public int FLD_Line { get; set; }

    public int FLD_PP { get; set; }

    public static int Get_OrbOptions(int orblv, int reside2, int line)
    {
        var List = new List<RandomOrbOptionsClass>();
        foreach (var value in World.List_OrbOption.Values)
        {
            var flag = 0;
            if (orblv < value.FLD_Orb_Lv || orblv > value.FLD_Orb_LvX) continue;
            if (value.FLD_Line == 0 || value.FLD_Line == line)
            {
                if (value.FLD_Orb_VuKhi != 0 && reside2 == 32)
                    flag = 1;
                else if (value.FLD_Orb_Ao != 0 && reside2 == 33)
                    flag = 1;
                else if (value.FLD_Orb_Giap != 0 && reside2 == 34)
                    flag = 1;
                else if (value.FLD_Orb_Tay != 0 && reside2 == 35)
                    flag = 1;
                else if (value.FLD_Orb_Chan != 0 && reside2 == 36) flag = 1;
                var rate = new Random(World.GetRandomSeed()).Next(0, 10000);
                if (flag == 1 && rate > value.FLD_PP) flag = 0;
            }

            if (flag == 1) List.Add(value);
        }

        if (List.Count > 0)
        {
            var index = new Random(World.GetRandomSeed()).Next(0, List.Count);
            return List[index].FLD_Option;
        }

        return 0;
    }
}