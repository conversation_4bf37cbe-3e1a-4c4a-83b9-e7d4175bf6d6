using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Timers;
using RxjhServer.DbClss;

namespace RxjhServer;

public class X_Bang_Phai_Chien_MonChien
{
    private object AsyncLock;

    private DateTime dateTime_0;

    private DateTime dateTime_1;

    private DateTime dateTime_2;

    private readonly Dictionary<string, int> Diem_so_liet_bieu;

    private readonly System.Timers.Timer timer_0;

    private System.Timers.Timer timer_1;

    private System.Timers.Timer timer_2;

    private System.Timers.Timer timer_3;

    public X_Bang_Phai_Chien_MonChien()
    {
        AsyncLock = new object();
        Diem_so_liet_bieu = new Dictionary<string, int>();
        try
        {
            World.MonChien_ProgressNew = 1;
            dateTime_0 = DateTime.Now.AddMinutes(3.0);
            timer_0 = new System.Timers.Timer(60000.0);
            timer_0.Elapsed += 门主申请记时器结束事件;
            timer_0.Enabled = true;
            timer_0.AutoReset = true;
            foreach (var value in World.allConnectedChars.Values) value.RollingAnnouncement(4500);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Máu bang chiến Chuẩn bị nhớ lúc khí kết thúc sự kiệnPhạm sai lầm: " + ex);
        }
    }

    public void 门主申请记时器结束事件(object sender, ElapsedEventArgs e)
    {
        try
        {
            var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
            if (num > 0) return;
            timer_0.Enabled = false;
            timer_0.Close();
            timer_0.Dispose();
            foreach (var value2 in World.HelpList.Values)
                using (new Lock(value2.DanhSachUngVien, "帮战申请人物列表"))
                {
                    foreach (var value3 in value2.DanhSachUngVien.Values)
                        value3.SystemNotification("Bang Chiến gheìp ðôi thaÌnh công, sau 10 giây seÞ bắt đầu!");
                }

            dateTime_1 = DateTime.Now.AddSeconds(10.0);
            timer_1 = new System.Timers.Timer(5000.0);
            timer_1.Elapsed += 准备记时器结束事件;
            timer_1.Enabled = true;
            timer_1.AutoReset = true;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Bang chiến Xin nhớ lúc khí kết thúc sự kiệnPhạm sai lầm:" + ex);
        }
    }

    public void 准备记时器结束事件(object sender, ElapsedEventArgs e)
    {
        try
        {
            if ((int)dateTime_1.Subtract(DateTime.Now).TotalSeconds > 0) return;
            foreach (var value in World.HelpList.Values)
            foreach (var value2 in value.DanhSachUngVien.Values)
            {
                if (value2.Player_Level >= 100 && value2.NhanVatToaDo_BanDo == 1201 && value2.NhanVat_HP > 0 &&
                    !value2.PlayerTuVong && !value2.Exiting) continue;
                Form1.WriteLine(88,
                    "Bang phái[" + value.申请帮派名称 + "]Member[" + value2.UserName + "]Request|Lv:" + value2.Player_Level +
                    "|Map:" + value2.NhanVatToaDo_BanDo + "|HP:" + value2.NhanVat_HP + "|Exit:" + value2.Exiting +
                    "|Store:" + value2.InTheShop + "|Death:" + value2.PlayerTuVong);
                value.DanhSachUngVien.Remove(value2.CharacterFullServerID);
                if (value.DanhSachUngVien.Count >= 0) continue;
                Form1.WriteLine(88,
                    "Bang phái[" + value.申请帮派名称 +
                    "] Tham chiến nhân số nhỏ hơn 5 Người bị thủ tiêu tham chiến tư cách.");
                using (new Lock(World.HelpList, "帮战list"))
                {
                    World.HelpList.Remove(value.DangKy_BangPhaiID);
                    if (World.HelpNameList != null && World.HelpNameList.Count > 0 &&
                        World.HelpNameList.ContainsKey(value.DangKy_BangPhaiID))
                        World.HelpNameList.Remove(value.DangKy_BangPhaiID);
                }
            }

            if (World.HelpList.Count < 2)
            {
                Form1.WriteLine(88, "Tham gia bang chiến môn phái số nhỏ hơn 2 Môn phái, bang chiến hủy bỏ.");
                Form1.WriteLine(88, "Tổng số bang tham gia:" + World.HelpList.Count);
                foreach (var value3 in World.HelpList.Values)
                foreach (var value4 in value3.DanhSachUngVien.Values)
                {
                    value4.SafeMode = 0;
                    if (value4.GangCharacterLevel == 6)
                    {
                        value4.HeThongNhacNho(
                            "Không tìm được đối thủ, Hệ Thống trả lại:" + World.HeThong_MonChien_CanNguyenBao +
                            "Point。", 6, ":");
                        value4.CheckTheNumberOfIngotsInBaibaoge();
                        value4.KiemSoatNguyenBao_SoLuong(World.HeThong_MonChien_CanNguyenBao, 1);
                        value4.Save_NguyenBaoData();
                        Form1.WriteLine(88,
                            "Trở về bang phái bang chủ [" + value4.UserName + "]" +
                            World.HeThong_MonChien_CanNguyenBao + "Point");
                    }
                    else
                    {
                        value4.HeThongNhacNho("Bang hội cấp < 6 không thể tham gia tranh đấu,", 6, ":");
                    }
                }

                World.BangChienThang_ID = 0;
                if (World.HelpNameList != null && World.HelpNameList.Count > 0) World.HelpNameList.Clear();
                Dispose();
                return;
            }

            World.MonChien_ProgressNew = 2;
            World.CoMoRa_HeThong_MonChien = 0;
            var thoigianwar = 5;
            timer_1.Enabled = false;
            timer_1.Close();
            timer_1.Dispose();
            dateTime_2 = DateTime.Now.AddMinutes(thoigianwar);
            timer_2 = new System.Timers.Timer(60000.0);
            timer_2.Elapsed += 开始对战记时器结束事件;
            timer_2.Enabled = true;
            timer_2.AutoReset = true;
            var num = 0;
            var toa_do_X = 0;
            var toa_do_Y = 0;
            foreach (var value5 in World.HelpList.Values)
            {
                switch (num)
                {
                    case 0:
                        toa_do_X = 0;
                        toa_do_Y = 300;
                        break;
                    case 1:
                        toa_do_X = -300;
                        toa_do_Y = 0;
                        break;
                    case 2:
                        toa_do_X = 300;
                        toa_do_Y = 0;
                        break;
                    case 3:
                        toa_do_X = 0;
                        toa_do_Y = -300;
                        break;
                }

                using (new Lock(value5.DanhSachUngVien, "帮战申请人物列表"))
                {
                    foreach (var value6 in value5.DanhSachUngVien.Values)
                    {
                        value6.Mobile(toa_do_X, toa_do_Y, 15f, 7301);
                        value6.HelpStartPrompt(0, thoigianwar * 60);
                        value6.SystemNotification("Bang Chiến bãìt ðâu, haÞy côì hêìt sýìng ðêÒ thãng cấp bang phaìi！");
                        value6.SwitchPkMode(2);
                        value6.SafeMode = 0;
                    }
                }

                num++;
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Bang chiến Chuẩn bị nhớ lúc khí kết thúc sự kiệnPhạm sai lầm:" + ex);
        }
    }

    public void 开始对战记时器结束事件(object sender, ElapsedEventArgs e)
    {
        try
        {
            Diem_so_liet_bieu.Clear();
            var num = (int)dateTime_2.Subtract(DateTime.Now).TotalSeconds;
            foreach (var value in World.HelpList.Values) Diem_so_liet_bieu.Add(value.申请帮派名称, value.DiemSoHienTai);
            var num2 = 0;
            var text = string.Empty;
            foreach (var item in Diem_so_liet_bieu.OrderByDescending(keyValuePair_0 => keyValuePair_0.Value))
            {
                if (num2 >= 3) break;
                text = text + item.Key + ":" + item.Value + " ";
                num2++;
            }

            foreach (var value2 in World.HelpList.Values)
            foreach (var value3 in value2.DanhSachUngVien.Values)
                if (value3.NhanVatToaDo_BanDo == 7301)
                    value3.HeThongNhacNho(text, 7, "Điểm số:");
            if (num <= 60)
                foreach (var value4 in World.HelpList.Values)
                foreach (var value5 in value4.DanhSachUngVien.Values)
                    if (value5.NhanVatToaDo_BanDo == 7301)
                        value5.HeThongNhacNho(" thời gian còn lại" + num + " giây", 6, "Bang Chiến");
            if (num > 0) return;
            World.MonChien_ProgressNew = 3;
            World.CoMoRa_HeThong_MonChien = 0;
            foreach (var value6 in World.HelpList.Values)
            foreach (var value7 in value6.DanhSachUngVien.Values)
                if (value7.NhanVatToaDo_BanDo == 7301)
                {
                    value7.SafeMode = 1;
                    value7.HeThongNhacNho("Kết thúc, sau 10 giây sẽ có kết quả!", 6, "Bang Chiến");
                }

            timer_2.Enabled = false;
            timer_2.Close();
            timer_2.Dispose();
            timer_3 = new System.Timers.Timer(10000.0);
            timer_3.Elapsed += 时间结束事件3;
            timer_3.Enabled = true;
            timer_3.AutoReset = false;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(100, "Bang chiến Bắt đầu đối chiến nhớ lúc khí kết thúc sự kiệnPhạm sai lầm:" + ex);
        }
    }

    public void 时间结束事件3(object sender, ElapsedEventArgs e)
    {
        try
        {
            var Score2 = 0;
            var Guild_Id = 0;
            var Score1 = 0;
            foreach (var value in World.HelpList.Values)
                if (value.DiemSoHienTai > Score1)
                {
                    Score1 = value.DiemSoHienTai;
                    Guild_Id = value.DangKy_BangPhaiID;
                    Score2 = value.DiemSoHienTai;
                }

            if (Score2 > 0)
            {
                var num4 = 0;
                foreach (var value2 in World.HelpList.Values)
                    if (value2.DiemSoHienTai == Score2)
                        num4++;
                if (num4 == 1)
                {
                    foreach (var value3 in World.HelpList.Values)
                    {
                        if (value3.DangKy_BangPhaiID == Guild_Id)
                        {
                            DBA.ExeSqlCommand(
                                $"UPDATE TBL_XWWL_Guild SET 门派武勋=门派武勋+5000,名声=名声+3,胜=胜+1 WHERE ID='{value3.DangKy_BangPhaiID}'");
                            var players = World.KiemTra_Ten_NguoiChoi(value3.帮派门主);
                            if (players != null)
                            {
                                if (players.NhanVatToaDo_BanDo == 7301)
                                {
                                    try
                                    {
                                        var player_Job = players.Player_Job;
                                        var player_Zx = players.Player_Zx;
                                        players.UpdateBangPhai_Level(players.UserName);
                                        RxjhClass.SetBangPhaiVinhDuSoLieu(value3.申请帮派名称, value3.帮派门主, value3.等级,
                                            player_Job, player_Zx, 1);
                                        players.HeThongNhacNho(" Chiến thắng, Win[+1] Cống Hiến[+5000] Danh Vọng[+3]",
                                            6, "Bang Chiến");
                                        foreach (var value4 in value3.DanhSachUngVien.Values)
                                            if (value4.NhanVatToaDo_BanDo == 7301)
                                                jlwp(value4, true);
                                        World.BangChienThang_ID = value3.DangKy_BangPhaiID;
                                        World.GuiThongBao("[" + value3.申请帮派名称 +
                                                          "] chiến thắng Bang Chiến，Côìng Hiêìn tãng 5000, xin chuìc mýÌng");
                                    }
                                    catch (Exception ex)
                                    {
                                        Form1.WriteLine(1, "Bang chiến set Vinh Dự" + ex);
                                        Form1.WriteLine(100, "Bang chiến set Vinh Dự：" + ex);
                                    }

                                    continue;
                                }

                                foreach (var value5 in value3.DanhSachUngVien.Values)
                                    if (value5.NhanVatToaDo_BanDo == 7301)
                                        value5.HeThongNhacNho(" kết thúc, Bang Chủ rời khỏi bản đồ, huỷ bỏ phần thưởng",
                                            6, "Bang Chiến");
                                if (World.HelpNameList != null && World.HelpNameList.Count > 0 &&
                                    World.HelpNameList.ContainsKey(value3.DangKy_BangPhaiID))
                                    World.HelpNameList.Remove(value3.DangKy_BangPhaiID);
                                World.BangChienThang_ID = 0;
                                continue;
                            }

                            foreach (var value6 in value3.DanhSachUngVien.Values)
                                if (value6.NhanVatToaDo_BanDo == 7301)
                                    value6.HeThongNhacNho(" kết thúc, Bang Chủ rời khỏi bản đồ, huỷ bỏ phần thưởng", 6,
                                        "Bang Chiến");
                            if (World.HelpNameList != null && World.HelpNameList.Count > 0 &&
                                World.HelpNameList.ContainsKey(value3.DangKy_BangPhaiID))
                                World.HelpNameList.Remove(value3.DangKy_BangPhaiID);
                            World.BangChienThang_ID = 0;
                            continue;
                        }

                        DBA.ExeSqlCommand(
                            $"UPDATE TBL_XWWL_Guild SET 门派武勋=门派武勋+500,败=败+1 WHERE ID='{value3.DangKy_BangPhaiID}'");
                        foreach (var value7 in value3.DanhSachUngVien.Values) jlwp(value7, false);
                        if (World.HelpNameList != null && World.HelpNameList.Count > 0 &&
                            World.HelpNameList.ContainsKey(value3.DangKy_BangPhaiID))
                            World.HelpNameList.Remove(value3.DangKy_BangPhaiID);
                    }
                }
                else
                {
                    World.BangChienThang_ID = 0;
                    World.GuiThongBao("Bang Chiến kết thúc, hai bên hoaÌ nhau!");
                    if (World.HelpNameList != null && World.HelpNameList.Count > 0) World.HelpNameList.Clear();
                }
            }
            else
            {
                World.BangChienThang_ID = 0;
                World.GuiThongBao("Bang Chiến kết thúc, hai bên hoaÌ nhau!");
                if (World.HelpNameList != null && World.HelpNameList.Count > 0) World.HelpNameList.Clear();
            }

            if (timer_3 != null)
            {
                timer_3.Enabled = false;
                timer_3.Close();
                timer_3.Dispose();
            }

            Dispose();
        }
        catch
        {
            World.BangChienThang_ID = 0;
            if (World.HelpNameList != null && World.HelpNameList.Count > 0) World.HelpNameList.Clear();
            if (timer_3 != null)
            {
                timer_3.Enabled = false;
                timer_3.Close();
                timer_3.Dispose();
            }

            Dispose();
        }
        finally
        {
            Dispose();
        }
    }

    public void jlwp(Players players_0, bool bool_0)
    {
        try
        {
            var num2 = 0;
            byte[] array = null;
            var num3 = 0;
            byte[] array2 = null;
            byte[] array3 = null;
            if (players_0 == null) return;
            array = Buffer.GetBytes(1008000389);
            if (bool_0) array = Buffer.GetBytes(1008000388);
            array2 = Buffer.GetBytes(1);
            array3 = Buffer.GetBytes(RxjhClass.GetDBItmeId());
            num2 = players_0.GetParcelVacancy(players_0);
            if (num2 != -1)
            {
                players_0.AddItems(array3, array, num2, array2, new byte[56]);
                players_0.HeThongNhacNho("Nhận được vâòt phâÒm BUFF EXP", 6, ":");
            }

            if (World.VatPhamThuong_MonChienID != 0 && bool_0)
            {
                num3 = players_0.GetParcelVacancy(players_0);
                if (num3 != -1)
                    players_0.AddItems(Buffer.GetBytes(RxjhClass.GetDBItmeId()),
                        Buffer.GetBytes(World.VatPhamThuong_MonChienID), num3, array2, new byte[56]);
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(100, "帮战 发放奖励物品 出错：" + ex);
        }
    }

    public void Dispose()
    {
        try
        {
            World.MonChien_ProgressNew = 0;
            World.CoMoRa_HeThong_MonChien = 0;
            if (World.HelpList.Count > 0)
                foreach (var value in World.HelpList.Values)
                foreach (var value2 in value.DanhSachUngVien.Values)
                {
                    value2.SystemCountdown(0, 0);
                    value2.Mobile(value2.NhanVatToaDo_X, value2.NhanVatToaDo_Y, value2.NhanVatToaDo_Z, 1201);
                    value2.SafeMode = 0;
                }

            if (timer_0 != null)
            {
                timer_0.Enabled = false;
                timer_0.Close();
                timer_0.Dispose();
            }

            if (timer_1 != null)
            {
                timer_1.Enabled = false;
                timer_1.Close();
                timer_1.Dispose();
            }

            if (timer_2 != null)
            {
                timer_2.Enabled = false;
                timer_2.Close();
                timer_2.Dispose();
            }

            if (timer_3 != null)
            {
                timer_3.Enabled = false;
                timer_3.Close();
                timer_3.Dispose();
            }

            if (World.HelpList.Count > 0) World.HelpList.Clear();
            World.BangChien = null;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Bang chiến DisposePhạm sai lầm:：" + ex);
        }
        finally
        {
            World.MonChien_ProgressNew = 0;
            World.CoMoRa_HeThong_MonChien = 0;
            if (timer_0 != null)
            {
                timer_0.Enabled = false;
                timer_0.Close();
                timer_0.Dispose();
            }

            if (timer_1 != null)
            {
                timer_1.Enabled = false;
                timer_1.Close();
                timer_1.Dispose();
            }

            if (timer_2 != null)
            {
                timer_2.Enabled = false;
                timer_2.Close();
                timer_2.Dispose();
            }

            if (timer_3 != null)
            {
                timer_3.Enabled = false;
                timer_3.Close();
                timer_3.Dispose();
            }

            if (World.HelpList.Count > 0) World.HelpList.Clear();
            World.BangChien = null;
        }
    }

    [Serializable]
    [CompilerGenerated]
    private sealed class Class0
    {
        public static readonly Class0 class0_0 = new();

        public static Func<KeyValuePair<string, int>, int> func_0;

        internal int method_0(KeyValuePair<string, int> keyValuePair_0)
        {
            return keyValuePair_0.Value;
        }
    }
}