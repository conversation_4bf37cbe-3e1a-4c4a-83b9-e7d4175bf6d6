using System.Collections.Generic;

namespace RxjhServer;

public class MapClass
{
    public Dictionary<int, NpcClass> npcTemplate = new();

    public Dictionary<int, Players> PlayerTemplte = new();

    public int MapID { get; set; }

    public static int GetNpcConn()
    {
        var num = 0;
        foreach (var value in World.Map.Values) num += value.npcTemplate.Count;
        return num;
    }

    public static Dictionary<int, NpcClass> GetnpcTemplate(int int_1)
    {
        if (World.Map.TryGetValue(int_1, out var value)) return value.npcTemplate;
        return new Dictionary<int, NpcClass>();
    }

    public static NpcClass GetNpc(int int_1, int int_2)
    {
        if (!World.Map.TryGetValue(int_1, out var value)) return null;
        if (value.npcTemplate.TryGetValue(int_2, out var value2)) return value2;
        return null;
    }

    public static Players GetNhanVat(int int_1, int int_2)
    {
        if (!World.Map.TryGetValue(int_1, out var value)) return null;
        if (value.PlayerTemplte.TryGetValue(int_2, out var value2)) return value2;
        return null;
    }

    public static void delnpc(int int_1, int int_2)
    {
        //int num = 1;
        //MapClass value = null;
        //while (true)
        //{
        //	switch (num)
        //	{
        //	case 0:
        //		return;
        //	case 2:
        //		value.method_0(int_2);
        //		num = 0;
        //		continue;
        //	}
        //	if (World.Map.TryGetValue(int_1, out value))
        //	{
        //		num = 2;
        //		continue;
        //	}
        //	return;
        //}
        MapClass value;
        if (World.Map.TryGetValue(int_1, out value)) value.method_0(int_2);
    }

    public void method_0(int int_1)
    {
        //Lock @lock = new Lock(npcTemplate, "MapClass-del");
        //try
        //{
        //	npcTemplate.Remove(int_1);
        //}
        //finally
        //{
        //	int num = 1;
        //	while (true)
        //	{
        //		switch (num)
        //		{
        //		case 0:
        //			break;
        //		default:
        //			if (@lock != null)
        //			{
        //				num = 2;
        //				continue;
        //			}
        //			break;
        //		case 2:
        //			((IDisposable)@lock).Dispose();
        //			num = 0;
        //			continue;
        //		}
        //		break;
        //	}
        //}
        using (new Lock(npcTemplate, "MapClass-del"))
        {
            npcTemplate.Remove(int_1);
        }
    }

    public void add(NpcClass npcClass_0)
    {
        for (int i = 10000; i < 30000; i++)
        {
            if (!npcTemplate.ContainsKey(i))
            {
                npcClass_0.FLD_INDEX = i;
                if (!npcTemplate.ContainsKey(npcClass_0.FLD_INDEX))
                {
                    npcTemplate.Add(npcClass_0.FLD_INDEX, npcClass_0);
                }
                break;
            }
        }
        if (World.allConnectedChars != null && World.allConnectedChars.Count > 0)
        {
            foreach(Players c in World.allConnectedChars.Values)
            {
                //c.HeThongNhacNho("npcClass_0.FLD_INDEX: " + npcClass_0.FLD_INDEX, 7);
            }
        }
        if (!npcTemplate.ContainsKey(npcClass_0.FLD_INDEX))
        {
            npcTemplate.Add(npcClass_0.FLD_INDEX, npcClass_0);
        }
    }
}