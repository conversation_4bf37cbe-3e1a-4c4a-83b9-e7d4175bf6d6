using System;
using System.Management;

namespace RxjhServer;

internal class HardwareInfo
{
    public static string GetProcessorId()
    {
        var mc = new ManagementClass("win32_processor");
        var moc = mc.GetInstances();
        var Id = string.Empty;
        using (var managementObjectEnumerator = moc.GetEnumerator())
        {
            if (managementObjectEnumerator.MoveNext())
            {
                var mo = (ManagementObject)managementObjectEnumerator.Current;
                Id = mo.Properties["processorID"].Value.ToString();
            }
        }

        return Id;
    }

    public static string GetHDDSerialNo()
    {
        var mangnmt = new ManagementClass("Win32_LogicalDisk");
        var mcol = mangnmt.GetInstances();
        var result = "";
        foreach (ManagementObject strt in mcol) result += Convert.ToString(strt["VolumeSerialNumber"]);
        return result;
    }

    public static string GetMACAddress()
    {
        var mc = new ManagementClass("Win32_NetworkAdapterConfiguration");
        var moc = mc.GetInstances();
        var MACAddress = string.Empty;
        foreach (ManagementObject mo in moc)
        {
            if (MACAddress == string.Empty && (bool)mo["IPEnabled"]) MACAddress = mo["MacAddress"].ToString();
            mo.Dispose();
        }

        return MACAddress.Replace(":", "");
    }

    public static string GetBoardMaker()
    {
        var searcher = new ManagementObjectSearcher("root\\CIMV2", "SELECT * FROM Win32_BaseBoard");
        foreach (ManagementObject wmi in searcher.Get())
            try
            {
                return wmi.GetPropertyValue("Manufacturer").ToString();
            }
            catch
            {
            }

        return "Board Maker: Unknown";
    }

    public static string GetBoardProductId()
    {
        var searcher = new ManagementObjectSearcher("root\\CIMV2", "SELECT * FROM Win32_BaseBoard");
        foreach (ManagementObject wmi in searcher.Get())
            try
            {
                return wmi.GetPropertyValue("Product").ToString();
            }
            catch
            {
            }

        return "Product: Unknown";
    }

    public static string GetCdRomDrive()
    {
        var searcher = new ManagementObjectSearcher("root\\CIMV2", "SELECT * FROM Win32_CDROMDrive");
        foreach (ManagementObject wmi in searcher.Get())
            try
            {
                return wmi.GetPropertyValue("Drive").ToString();
            }
            catch
            {
            }

        return "CD ROM Drive Letter: Unknown";
    }

    public static string GetBIOSmaker()
    {
        var searcher = new ManagementObjectSearcher("root\\CIMV2", "SELECT * FROM Win32_BIOS");
        foreach (ManagementObject wmi in searcher.Get())
            try
            {
                return wmi.GetPropertyValue("Manufacturer").ToString();
            }
            catch
            {
            }

        return "BIOS Maker: Unknown";
    }

    public static string GetBIOSserNo()
    {
        var searcher = new ManagementObjectSearcher("root\\CIMV2", "SELECT * FROM Win32_BIOS");
        foreach (ManagementObject wmi in searcher.Get())
            try
            {
                return wmi.GetPropertyValue("SerialNumber").ToString();
            }
            catch
            {
            }

        return "BIOS Serial Number: Unknown";
    }

    public static string GetBIOScaption()
    {
        var searcher = new ManagementObjectSearcher("root\\CIMV2", "SELECT * FROM Win32_BIOS");
        foreach (ManagementObject wmi in searcher.Get())
            try
            {
                return wmi.GetPropertyValue("Caption").ToString();
            }
            catch
            {
            }

        return "BIOS Caption: Unknown";
    }

    public static string GetAccountName()
    {
        var searcher = new ManagementObjectSearcher("root\\CIMV2", "SELECT * FROM Win32_UserAccount");
        foreach (ManagementObject wmi in searcher.Get())
            try
            {
                return wmi.GetPropertyValue("Name").ToString();
            }
            catch
            {
            }

        return "User Account Name: Unknown";
    }

    public static string GetPhysicalMemory()
    {
        var oMs = new ManagementScope();
        var oQuery = new ObjectQuery("SELECT Capacity FROM Win32_PhysicalMemory");
        var oSearcher = new ManagementObjectSearcher(oMs, oQuery);
        var oCollection = oSearcher.Get();
        var MemSize = 0L;
        var mCap = 0L;
        foreach (ManagementObject obj in oCollection)
        {
            mCap = Convert.ToInt64(obj["Capacity"]);
            MemSize += mCap;
        }

        return MemSize / 1024 / 1024 + "MB";
    }

    public static string GetNoRamSlots()
    {
        var MemSlots = 0;
        var oMs = new ManagementScope();
        var oQuery2 = new ObjectQuery("SELECT MemoryDevices FROM Win32_PhysicalMemoryArray");
        var oSearcher2 = new ManagementObjectSearcher(oMs, oQuery2);
        var oCollection2 = oSearcher2.Get();
        foreach (ManagementObject obj in oCollection2) MemSlots = Convert.ToInt32(obj["MemoryDevices"]);
        return MemSlots.ToString();
    }

    public static string GetCPUManufacturer()
    {
        var cpuMan = string.Empty;
        var mgmt = new ManagementClass("Win32_Processor");
        var objCol = mgmt.GetInstances();
        foreach (ManagementObject obj in objCol)
            if (cpuMan == string.Empty)
                cpuMan = obj.Properties["Manufacturer"].Value.ToString();
        return cpuMan;
    }

    public static int GetCPUCurrentClockSpeed()
    {
        var cpuClockSpeed = 0;
        var mgmt = new ManagementClass("Win32_Processor");
        var objCol = mgmt.GetInstances();
        foreach (ManagementObject obj in objCol)
            if (cpuClockSpeed == 0)
                cpuClockSpeed = Convert.ToInt32(obj.Properties["CurrentClockSpeed"].Value.ToString());
        return cpuClockSpeed;
    }

    public static string GetDefaultIPGateway()
    {
        var mgmt = new ManagementClass("Win32_NetworkAdapterConfiguration");
        var objCol = mgmt.GetInstances();
        var gateway = string.Empty;
        foreach (ManagementObject obj in objCol)
        {
            if (gateway == string.Empty && (bool)obj["IPEnabled"]) gateway = obj["DefaultIPGateway"].ToString();
            obj.Dispose();
        }

        return gateway.Replace(":", "");
    }

    public static double? GetCpuSpeedInGHz()
    {
        double? GHz = null;
        using (var mc = new ManagementClass("Win32_Processor"))
        {
            using var managementObjectEnumerator = mc.GetInstances().GetEnumerator();
            if (managementObjectEnumerator.MoveNext())
            {
                var mo = (ManagementObject)managementObjectEnumerator.Current;
                GHz = 0.001 * (uint)mo.Properties["CurrentClockSpeed"].Value;
                return GHz;
            }
        }

        return GHz;
    }

    public static string GetCurrentLanguage()
    {
        var searcher = new ManagementObjectSearcher("root\\CIMV2", "SELECT * FROM Win32_BIOS");
        foreach (ManagementObject wmi in searcher.Get())
            try
            {
                return wmi.GetPropertyValue("CurrentLanguage").ToString();
            }
            catch
            {
            }

        return "BIOS Maker: Unknown";
    }

    public static string GetOSInformation()
    {
        var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem");
        foreach (ManagementObject wmi in searcher.Get())
            try
            {
                return ((string)wmi["Caption"]).Trim() + ", " + (string)wmi["Version"] + ", " +
                       (string)wmi["OSArchitecture"];
            }
            catch
            {
            }

        return "BIOS Maker: Unknown";
    }

    public static string GetProcessorInformation()
    {
        var mc = new ManagementClass("win32_processor");
        var moc = mc.GetInstances();
        var info = string.Empty;
        foreach (ManagementObject mo in moc)
        {
            var name = (string)mo["Name"];
            name = name.Replace("(TM)", "™").Replace("(tm)", "™").Replace("(R)", "®")
                .Replace("(r)", "®")
                .Replace("(C)", "©")
                .Replace("(c)", "©")
                .Replace("    ", " ")
                .Replace("  ", " ");
            info = name + ", " + (string)mo["Caption"] + ", " + (string)mo["SocketDesignation"];
        }

        return info;
    }

    public static string GetComputerName()
    {
        var mc = new ManagementClass("Win32_ComputerSystem");
        var moc = mc.GetInstances();
        var info = string.Empty;
        foreach (ManagementObject mo in moc) info = (string)mo["Name"];
        return info;
    }
}