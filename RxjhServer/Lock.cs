using System;
using System.Threading;

namespace RxjhServer;

public class Lock : IDisposable
{
    public static int DefaultMillisecondsTimeout = 1000;

    private object _obj;

    public Lock(object object_0)
    {
        TryGet(object_0, 1000, true, string.Empty);
    }

    public Lock(object object_0, string string_0)
    {
        TryGet(object_0, 1000, true, string_0);
    }

    public Lock(object object_0, int millisecondsTimeout, bool throwTimeoutException, string string_0)
    {
        TryGet(object_0, millisecondsTimeout, throwTimeoutException, string_0);
    }

    public bool IsTimeout => _obj == null;

    public void Dispose()
    {
        if (_obj != null)
        {
            Monitor.Exit(_obj);
            _obj = null;
        }
    }

    private void TryGet(object object_0, int millisecondsTimeout, bool throwTimeoutException, string string_0)
    {
        if (Monitor.TryEnter(object_0, millisecondsTimeout))
            _obj = object_0;
        else if (throwTimeoutException) Form1.WriteLine(100, "Lock Timeout:" + string_0 + " " + object_0.GetType());
    }
}