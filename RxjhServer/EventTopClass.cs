namespace RxjhServer;

public class EventTopClass
{
    public string <PERSON><PERSON><PERSON>;

    public int DangCap;

    public int DiemSo;

    public int GietNguoiSoLuong;

    public int NgheNghiep;

    public Players NguoiChoi;

    public int Player_EvRandom_Team;
    public string TenNhanVat;

    public string TheLuc;

    public int TongDiem;

    public int TuVongSoLuong;

    public string UserIP;

    public static bool Check_LimitedUser(string UserIP, string username)
    {
        var i = 0;
        foreach (var value in World.EventTop.Values)
        {
            if (value.TenNhanVat == username) return true;
            if (value.UserIP == UserIP) i++;
        }

        if (i >= World.TheLucChien_GioiHanClone) return false;
        return true;
    }

    public static int TinhToan_TheLucChienDiemSo(int Kill_Level, int Dead_level)
    {
        var diem = 0;
        var chenhlech_lv = Kill_Level - Dead_level;
        if (chenhlech_lv >= -5 && chenhlech_lv <= 5) return 10;
        if (chenhlech_lv >= 6)
        {
            diem = 10 - (chenhlech_lv - 5);
            if (diem < 5) diem = 5;
            return diem;
        }

        if (chenhlech_lv < -5)
        {
            diem = 10 + (chenhlech_lv * -1 - 5);
            if (diem > 15) diem = 15;
            return diem;
        }

        return diem;
    }

    public static bool Check_GioiHanTuVong(Players play)
    {
        if (World.EventTop.TryGetValue(play.UserName, out var value) &&
            value.GietNguoiSoLuong - value.TuVongSoLuong <= -111) return true;
        return false;
    }
}