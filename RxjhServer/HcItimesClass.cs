using System;

namespace RxjhServer;

public class HcItimesClass
{
    private byte[] _VatPham_id;

    public int Position { get; set; }

    public byte[] VatPham { get; set; }

    public byte[] VatPham_id
    {
        get => DatDuocVatPhamId();
        set => _VatPham_id = value;
    }

    public byte[] ItemGlobal_ID => DatDuocGlobal_ID();

    public byte[] VatPhamSoLuong
    {
        get => DatDuocVatPhamSoLuong();
        set => ThietLap_VatPhamSoLuong(value);
    }

    public byte[] VatPham_ThuocTinh => DatDuocVatPham_ThuocTinh();

    public int CuongHoaLoaiHinh { get; set; }

    public int CuongHoaSoLuong { get; set; }

    public int GiaiDoanLoaiHinh { get; set; }

    public int KhiCongThuocTinhLoaiHinh { get; set; }

    public int GiaiDoanSoLuong { get; set; }

    public Itimesx ThuocTinh1 { get; set; }

    public Itimesx ThuocTinh2 { get; set; }

    public Itimesx ThuocTinh3 { get; set; }

    public Itimesx ThuocTinh4 { get; set; }

    public int FLD_FJ_ThucTinh
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham, 62, array, 0, 4);
            return Buffer.ToInt32(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham, 62, 4);
    }

    public int FLD_FJ_SucManh4ViThan
    {
        get
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham, 71, array, 0, 1);
            return Buffer.ToInt32(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham, 71, 1);
    }

    public int FLD_FJ_NJ
    {
        get
        {
            var array = new byte[2];
            System.Buffer.BlockCopy(VatPham, 60, array, 0, 2);
            return Buffer.ToInt16(array, 0);
        }
        set => System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham, 60, 2);
    }

    public int FLD_FJ_TrungCapPhuHon
    {
        get
        {
            var array = new byte[2];
            System.Buffer.BlockCopy(VatPham, 40, array, 0, 2);
            return Buffer.ToInt16(array, 0);
        }
        set
        {
            if (value > 0) System.Buffer.BlockCopy(Buffer.GetBytes(1), 0, VatPham, 38, 2);
            System.Buffer.BlockCopy(Buffer.GetBytes(value), 0, VatPham, 40, 2);
        }
    }

    public byte[] DatDuocVatPham_ThuocTinh()
    {
        var array = new byte[56];
        try
        {
            System.Buffer.BlockCopy(VatPham, 16, array, 0, 56);
            return array;
        }
        catch
        {
            return array;
        }
    }

    public byte[] DatDuocVatPhamId()
    {
        var array = new byte[4];
        try
        {
            System.Buffer.BlockCopy(VatPham, 8, array, 0, 4);
            return array;
        }
        catch
        {
            return array;
        }
    }

    public byte[] DatDuocVatPhamSoLuong()
    {
        var array = new byte[4];
        try
        {
            System.Buffer.BlockCopy(VatPham, 12, array, 0, 4);
            return array;
        }
        catch
        {
            return array;
        }
    }

    public void ThietLap_VatPhamSoLuong(byte[] byte_0)
    {
        System.Buffer.BlockCopy(byte_0, 0, VatPham, 12, 4);
    }

    public void ThietLap_ThuocTinh()
    {
        try
        {
            var s = "00000000";
            var s2 = "00000000";
            var s3 = "00000000";
            var s4 = "00000000";
            if (World.CoHoTro_MoRongChuSo_VatPham_ThuocTinhHayKhong == 0)
            {
                if (ThuocTinh1.ThuocTinhSoLuong != 0)
                    s = ThuocTinh1.ThuocTinhSoLuong < 10
                        ?
                        ThuocTinh1.ThuocTinhLoaiHinh != 8 || ThuocTinh1.KhiCongThuocTinhLoaiHinh == 0
                            ?
                            ThuocTinh1.ThuocTinhLoaiHinh + "000000" + ThuocTinh1.ThuocTinhSoLuong
                            : ThuocTinh1.KhiCongThuocTinhLoaiHinh.ToString().Length > 2
                                ? ThuocTinh1.ThuocTinhLoaiHinh + "00" + ThuocTinh1.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh1.ThuocTinhSoLuong
                                : ThuocTinh1.ThuocTinhLoaiHinh + "000" + ThuocTinh1.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh1.ThuocTinhSoLuong
                        : ThuocTinh1.ThuocTinhLoaiHinh != 8 || ThuocTinh1.KhiCongThuocTinhLoaiHinh == 0
                            ? ThuocTinh1.ThuocTinhLoaiHinh + "00000" + ThuocTinh1.ThuocTinhSoLuong
                            : ThuocTinh1.KhiCongThuocTinhLoaiHinh.ToString().Length > 2
                                ? ThuocTinh1.ThuocTinhLoaiHinh + "00" + ThuocTinh1.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh1.ThuocTinhSoLuong
                                : ThuocTinh1.ThuocTinhLoaiHinh + "000" + ThuocTinh1.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh1.ThuocTinhSoLuong;
                if (ThuocTinh2.ThuocTinhSoLuong != 0)
                    s2 = ThuocTinh2.ThuocTinhSoLuong < 10
                        ?
                        ThuocTinh2.ThuocTinhLoaiHinh != 8 || ThuocTinh2.KhiCongThuocTinhLoaiHinh == 0
                            ?
                            ThuocTinh2.ThuocTinhLoaiHinh + "000000" + ThuocTinh2.ThuocTinhSoLuong
                            : ThuocTinh2.KhiCongThuocTinhLoaiHinh.ToString().Length > 2
                                ? ThuocTinh2.ThuocTinhLoaiHinh + "00" + ThuocTinh2.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh2.ThuocTinhSoLuong
                                : ThuocTinh2.ThuocTinhLoaiHinh + "000" + ThuocTinh2.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh2.ThuocTinhSoLuong
                        : ThuocTinh2.ThuocTinhLoaiHinh != 8 || ThuocTinh2.KhiCongThuocTinhLoaiHinh == 0
                            ? ThuocTinh2.ThuocTinhLoaiHinh + "00000" + ThuocTinh2.ThuocTinhSoLuong
                            : ThuocTinh2.KhiCongThuocTinhLoaiHinh.ToString().Length > 2
                                ? ThuocTinh2.ThuocTinhLoaiHinh + "00" + ThuocTinh2.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh2.ThuocTinhSoLuong
                                : ThuocTinh2.ThuocTinhLoaiHinh + "000" + ThuocTinh2.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh2.ThuocTinhSoLuong;
                if (ThuocTinh3.ThuocTinhSoLuong != 0)
                    s3 = ThuocTinh3.ThuocTinhSoLuong < 10
                        ?
                        ThuocTinh3.ThuocTinhLoaiHinh != 8 || ThuocTinh3.KhiCongThuocTinhLoaiHinh == 0
                            ?
                            ThuocTinh3.ThuocTinhLoaiHinh + "000000" + ThuocTinh3.ThuocTinhSoLuong
                            : ThuocTinh3.KhiCongThuocTinhLoaiHinh.ToString().Length > 2
                                ? ThuocTinh3.ThuocTinhLoaiHinh + "00" + ThuocTinh3.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh3.ThuocTinhSoLuong
                                : ThuocTinh3.ThuocTinhLoaiHinh + "000" + ThuocTinh3.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh3.ThuocTinhSoLuong
                        : ThuocTinh3.ThuocTinhLoaiHinh != 8 || ThuocTinh3.KhiCongThuocTinhLoaiHinh == 0
                            ? ThuocTinh3.ThuocTinhLoaiHinh + "00000" + ThuocTinh3.ThuocTinhSoLuong
                            : ThuocTinh3.KhiCongThuocTinhLoaiHinh.ToString().Length > 2
                                ? ThuocTinh3.ThuocTinhLoaiHinh + "00" + ThuocTinh3.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh3.ThuocTinhSoLuong
                                : ThuocTinh3.ThuocTinhLoaiHinh + "000" + ThuocTinh3.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh3.ThuocTinhSoLuong;
                if (ThuocTinh4.ThuocTinhSoLuong != 0)
                    s4 = ThuocTinh4.ThuocTinhSoLuong < 10
                        ?
                        ThuocTinh4.ThuocTinhLoaiHinh != 8 || ThuocTinh4.KhiCongThuocTinhLoaiHinh == 0
                            ?
                            ThuocTinh4.ThuocTinhLoaiHinh + "000000" + ThuocTinh4.ThuocTinhSoLuong
                            : ThuocTinh4.KhiCongThuocTinhLoaiHinh.ToString().Length > 2
                                ? ThuocTinh4.ThuocTinhLoaiHinh + "00" + ThuocTinh4.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh4.ThuocTinhSoLuong
                                : ThuocTinh4.ThuocTinhLoaiHinh + "000" + ThuocTinh4.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh4.ThuocTinhSoLuong
                        : ThuocTinh4.ThuocTinhLoaiHinh != 8 || ThuocTinh4.KhiCongThuocTinhLoaiHinh == 0
                            ? ThuocTinh4.ThuocTinhLoaiHinh + "00000" + ThuocTinh4.ThuocTinhSoLuong
                            : ThuocTinh4.KhiCongThuocTinhLoaiHinh.ToString().Length > 2
                                ? ThuocTinh4.ThuocTinhLoaiHinh + "00" + ThuocTinh4.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh4.ThuocTinhSoLuong
                                : ThuocTinh4.ThuocTinhLoaiHinh + "000" + ThuocTinh4.KhiCongThuocTinhLoaiHinh + "0" +
                                  ThuocTinh4.ThuocTinhSoLuong;
            }
            else
            {
                if (ThuocTinh1.ThuocTinhSoLuong != 0)
                    switch (ThuocTinh1.ThuocTinhSoLuong.ToString().Length)
                    {
                        case 1:
                            s = ThuocTinh1.ThuocTinhLoaiHinh != 8 || ThuocTinh1.KhiCongThuocTinhLoaiHinh == 0
                                ?
                                ThuocTinh1.ThuocTinhLoaiHinh + "000000" + ThuocTinh1.ThuocTinhSoLuong
                                : ThuocTinh1.KhiCongThuocTinhLoaiHinh.ToString().Length > 2
                                    ? ThuocTinh1.ThuocTinhLoaiHinh + "00" + ThuocTinh1.KhiCongThuocTinhLoaiHinh + "0" +
                                      ThuocTinh1.ThuocTinhSoLuong
                                    : ThuocTinh1.ThuocTinhLoaiHinh + "000" + ThuocTinh1.KhiCongThuocTinhLoaiHinh + "0" +
                                      ThuocTinh1.ThuocTinhSoLuong;
                            break;
                        case 2:
                            s = ThuocTinh1.ThuocTinhLoaiHinh + "00000" + ThuocTinh1.ThuocTinhSoLuong;
                            break;
                        case 3:
                            s = ThuocTinh1.ThuocTinhLoaiHinh + "0000" + ThuocTinh1.ThuocTinhSoLuong;
                            break;
                        case 4:
                            s = ThuocTinh1.ThuocTinhLoaiHinh + "000" + ThuocTinh1.ThuocTinhSoLuong;
                            break;
                        case 5:
                            s = ThuocTinh1.ThuocTinhLoaiHinh + "00" + ThuocTinh1.ThuocTinhSoLuong;
                            break;
                    }

                if (ThuocTinh2.ThuocTinhSoLuong != 0)
                    switch (ThuocTinh2.ThuocTinhSoLuong.ToString().Length)
                    {
                        case 1:
                            s2 = ThuocTinh2.ThuocTinhLoaiHinh != 8 || ThuocTinh2.KhiCongThuocTinhLoaiHinh == 0
                                ?
                                ThuocTinh2.ThuocTinhLoaiHinh + "000000" + ThuocTinh2.ThuocTinhSoLuong
                                : ThuocTinh2.KhiCongThuocTinhLoaiHinh.ToString().Length > 2
                                    ? ThuocTinh2.ThuocTinhLoaiHinh + "00" + ThuocTinh2.KhiCongThuocTinhLoaiHinh + "0" +
                                      ThuocTinh2.ThuocTinhSoLuong
                                    : ThuocTinh2.ThuocTinhLoaiHinh + "000" + ThuocTinh2.KhiCongThuocTinhLoaiHinh + "0" +
                                      ThuocTinh2.ThuocTinhSoLuong;
                            break;
                        case 2:
                            s2 = ThuocTinh2.ThuocTinhLoaiHinh + "00000" + ThuocTinh2.ThuocTinhSoLuong;
                            break;
                        case 3:
                            s2 = ThuocTinh2.ThuocTinhLoaiHinh + "0000" + ThuocTinh2.ThuocTinhSoLuong;
                            break;
                        case 4:
                            s2 = ThuocTinh2.ThuocTinhLoaiHinh + "000" + ThuocTinh2.ThuocTinhSoLuong;
                            break;
                        case 5:
                            s2 = ThuocTinh2.ThuocTinhLoaiHinh + "00" + ThuocTinh2.ThuocTinhSoLuong;
                            break;
                    }

                if (ThuocTinh3.ThuocTinhSoLuong != 0)
                    switch (ThuocTinh3.ThuocTinhSoLuong.ToString().Length)
                    {
                        case 1:
                            s3 = ThuocTinh3.ThuocTinhLoaiHinh != 8 || ThuocTinh3.KhiCongThuocTinhLoaiHinh == 0
                                ?
                                ThuocTinh3.ThuocTinhLoaiHinh + "000000" + ThuocTinh3.ThuocTinhSoLuong
                                : ThuocTinh3.KhiCongThuocTinhLoaiHinh.ToString().Length > 2
                                    ? ThuocTinh3.ThuocTinhLoaiHinh + "00" + ThuocTinh3.KhiCongThuocTinhLoaiHinh + "0" +
                                      ThuocTinh3.ThuocTinhSoLuong
                                    : ThuocTinh3.ThuocTinhLoaiHinh + "000" + ThuocTinh3.KhiCongThuocTinhLoaiHinh + "0" +
                                      ThuocTinh3.ThuocTinhSoLuong;
                            break;
                        case 2:
                            s3 = ThuocTinh3.ThuocTinhLoaiHinh + "00000" + ThuocTinh3.ThuocTinhSoLuong;
                            break;
                        case 3:
                            s3 = ThuocTinh3.ThuocTinhLoaiHinh + "0000" + ThuocTinh3.ThuocTinhSoLuong;
                            break;
                        case 4:
                            s3 = ThuocTinh3.ThuocTinhLoaiHinh + "000" + ThuocTinh3.ThuocTinhSoLuong;
                            break;
                        case 5:
                            s3 = ThuocTinh3.ThuocTinhLoaiHinh + "00" + ThuocTinh3.ThuocTinhSoLuong;
                            break;
                    }

                if (ThuocTinh4.ThuocTinhSoLuong != 0)
                    switch (ThuocTinh4.ThuocTinhSoLuong.ToString().Length)
                    {
                        case 1:
                            s4 = ThuocTinh4.ThuocTinhLoaiHinh != 8 || ThuocTinh4.KhiCongThuocTinhLoaiHinh == 0
                                ?
                                ThuocTinh4.ThuocTinhLoaiHinh + "000000" + ThuocTinh4.ThuocTinhSoLuong
                                : ThuocTinh4.KhiCongThuocTinhLoaiHinh.ToString().Length > 2
                                    ? ThuocTinh4.ThuocTinhLoaiHinh + "00" + ThuocTinh4.KhiCongThuocTinhLoaiHinh + "0" +
                                      ThuocTinh4.ThuocTinhSoLuong
                                    : ThuocTinh4.ThuocTinhLoaiHinh + "000" + ThuocTinh4.KhiCongThuocTinhLoaiHinh + "0" +
                                      ThuocTinh4.ThuocTinhSoLuong;
                            break;
                        case 2:
                            s4 = ThuocTinh4.ThuocTinhLoaiHinh + "00000" + ThuocTinh4.ThuocTinhSoLuong;
                            break;
                        case 3:
                            s4 = ThuocTinh4.ThuocTinhLoaiHinh + "0000" + ThuocTinh4.ThuocTinhSoLuong;
                            break;
                        case 4:
                            s4 = ThuocTinh4.ThuocTinhLoaiHinh + "000" + ThuocTinh4.ThuocTinhSoLuong;
                            break;
                        case 5:
                            s4 = ThuocTinh4.ThuocTinhLoaiHinh + "00" + ThuocTinh4.ThuocTinhSoLuong;
                            break;
                    }
            }

            var bytes = Buffer.GetBytes(int.Parse(s));
            var bytes2 = Buffer.GetBytes(int.Parse(s2));
            var bytes3 = Buffer.GetBytes(int.Parse(s3));
            var bytes4 = Buffer.GetBytes(int.Parse(s4));
            var VatPham = this.VatPham;
            System.Buffer.BlockCopy(bytes, 0, VatPham, 20, 4);
            System.Buffer.BlockCopy(bytes2, 0, this.VatPham, 24, 4);
            System.Buffer.BlockCopy(bytes3, 0, this.VatPham, 28, 4);
            System.Buffer.BlockCopy(bytes4, 0, this.VatPham, 32, 4);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "合成   ThietLap_ThuocTinh   error：" + ex);
        }
    }

    public void DatDuocThuocTinh()
    {
        try
        {
            var array = new byte[4];
            var array2 = new byte[4];
            var array3 = new byte[4];
            var array4 = new byte[4];
            System.Buffer.BlockCopy(VatPham, 20, array, 0, 4);
            System.Buffer.BlockCopy(VatPham, 24, array2, 0, 4);
            System.Buffer.BlockCopy(VatPham, 28, array3, 0, 4);
            System.Buffer.BlockCopy(VatPham, 32, array4, 0, 4);
            ThuocTinh1 = new Itimesx(array);
            ThuocTinh2 = new Itimesx(array2);
            ThuocTinh3 = new Itimesx(array3);
            ThuocTinh4 = new Itimesx(array4);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Hợp thành Đạt được thuộc tính Phạm sai lầm: " + ex);
        }
    }

    public void ThietLap_GiaiDoanThuocTinh()
    {
        try
        {
            string text = null;
            var s = "00000000";
            var s2 = "0000000000";
            if (CuongHoaSoLuong != 0)
            {
                if (CuongHoaSoLuong >= 10)
                {
                    var str = CuongHoaLoaiHinh.ToString();
                    var str2 = CuongHoaSoLuong.ToString();
                    text = str + "00000" + str2;
                }
                else
                {
                    var str3 = CuongHoaLoaiHinh.ToString();
                    var str4 = CuongHoaSoLuong.ToString();
                    text = str3 + "000000" + str4;
                }

                s = text;
            }

            if (GiaiDoanSoLuong != 0)
            {
                GiaiDoanSoLuong--;
                var str5 = GiaiDoanLoaiHinh.ToString();
                var str6 = GiaiDoanSoLuong.ToString();
                s2 = "100000" + str5 + str6 + "00";
            }

            System.Buffer.BlockCopy(Buffer.GetBytes(int.Parse(s) + int.Parse(s2)), 0, VatPham, 16, 4);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Hợp thành Thiết trí giai đoạn thuộc tính Phạm sai lầm: " + ex);
        }
    }

    public byte[] DatDuocGlobal_ID()
    {
        var array = new byte[8];
        try
        {
            System.Buffer.BlockCopy(VatPham, 0, array, 0, 8);
            return array;
        }
        catch
        {
            return array;
        }
    }

    public void CuongHoaThuocTinhGiaiDoan()
    {
        try
        {
            var array = new byte[4];
            System.Buffer.BlockCopy(VatPham, 16, array, 0, 4);
            var text = Buffer.ToInt32(array, 0).ToString();
            switch (text.Length)
            {
                case 3:
                case 4:
                case 5:
                    break;
                case 2:
                    GiaiDoanSoLuong = int.Parse(text.Substring(0, 2));
                    break;
                case 6:
                    GiaiDoanLoaiHinh = int.Parse(text.Substring(0, 1));
                    if (GiaiDoanLoaiHinh == 8)
                    {
                        KhiCongThuocTinhLoaiHinh = int.Parse(text.Substring(1, 3));
                        GiaiDoanSoLuong = int.Parse(text.Substring(4, 2));
                    }
                    else if (World.CoHoTro_MoRongChuSo_VatPham_ThuocTinhHayKhong == 0)
                    {
                        GiaiDoanSoLuong = int.Parse(text.Substring(4, 2));
                    }
                    else
                    {
                        GiaiDoanSoLuong = int.Parse(text) - int.Parse(text.Substring(0, 1)) * 100000;
                    }

                    break;
                case 7:
                    GiaiDoanLoaiHinh = int.Parse(text.Substring(0, 1));
                    if (GiaiDoanLoaiHinh == 2)
                    {
                        GiaiDoanLoaiHinh = int.Parse(text.Substring(3, 1));
                        break;
                    }

                    GiaiDoanLoaiHinh = int.Parse(text.Substring(0, 2));
                    if (World.CoHoTro_MoRongChuSo_VatPham_ThuocTinhHayKhong == 0)
                        GiaiDoanSoLuong = int.Parse(text.Substring(5, 2));
                    else
                        GiaiDoanSoLuong = int.Parse(text) - int.Parse(text.Substring(0, 2)) * 100000;
                    break;
                case 8:
                    CuongHoaLoaiHinh = int.Parse(text.Substring(0, 1));
                    CuongHoaSoLuong = int.Parse(text.Substring(text.Length - 2, 2));
                    break;
                case 9:
                    CuongHoaLoaiHinh = int.Parse(text.Substring(0, 2));
                    CuongHoaSoLuong = int.Parse(text.Substring(text.Length - 2, 2));
                    break;
                case 10:
                    GiaiDoanLoaiHinh = int.Parse(text.Substring(6, 1));
                    GiaiDoanSoLuong = int.Parse(text.Substring(7, 1)) + 1;
                    CuongHoaLoaiHinh = int.Parse(text.Substring(2, 1));
                    CuongHoaSoLuong = int.Parse(text.Substring(text.Length - 2, 2));
                    break;
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "合成   CuongHoaThuocTinhGiaiDoan   error：" + ex);
        }
    }
}