using System;
using System.Collections.Generic;

namespace RxjhServer;

public class RandomPositionClass
{
    public int ID { get; set; }

    public int FLD_MAP { get; set; }

    public float FLD_X { get; set; }

    public float FLD_Y { get; set; }

    public int FLD_Type { get; set; }

    public int FLD_BossMap { get; set; }

    public string FLD_NAME { get; set; }

    public static RandomPositionClass GetRandomPosition(int map, int type = 0)
    {
        var list = new List<RandomPositionClass>();
        foreach (var Value in World.List_RandomPosition.Values)
        {
            if (Value.FLD_MAP != map) continue;
            if (type != 0)
            {
                if (type == Value.FLD_Type) list.Add(Value);
            }
            else
            {
                list.Add(Value);
            }
        }

        if (list.Count > 0)
        {
            var random = new Random(World.GetRandomSeed()).Next(0, list.Count);
            return list[random];
        }

        return null;
    }

    public static void GetBossMapPosition(int map, int soluong)
    {
        var list = new List<RandomPositionClass>();
        foreach (var Value in World.List_RandomPosition.Values)
            if (Value.FLD_MAP == map && Value.FLD_BossMap == 1)
                list.Add(Value);
        for (var i = 0; i < soluong; i++)
            if (list.Count > 0)
            {
                var random = new Random(World.GetRandomSeed()).Next(0, list.Count);
                World.List_BossMapPosition.Add(i, list[random]);
                list.Remove(list[random]);
            }
    }
}