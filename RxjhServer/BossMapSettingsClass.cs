using System;
using System.Collections.Generic;

namespace RxjhServer;

public class BossMapSettingsClass
{
    public int ID { get; set; }

    public string FLD_BossName { get; set; }

    public int FLD_BossID { get; set; }

    public float FLD_X { get; set; }

    public float FLD_Y { get; set; }

    public int FLD_MAP { get; set; }

    public int FLD_Amount { get; set; }

    public int FLD_TYPE { get; set; }

    public string FLD_Time { get; set; }

    public static List<BossMapSettingsClass> GetBossMapByTime(string time, int amount)
    {
        var List = new List<BossMapSettingsClass>();
        var List2 = new List<BossMapSettingsClass>();
        foreach (var value in World.List_BossMapSettings.Values)
            if (value.FLD_Time == time)
                List.Add(value);
        if (List != null)
        {
            for (var sl = 0; sl < amount; sl++)
                if (List.Count > 0)
                {
                    var random = new Random(World.GetRandomSeed()).Next(0, List.Count);
                    List2.Add(List[random]);
                    List.Remove(List[random]);
                }

            return List2;
        }

        return null;
    }
}