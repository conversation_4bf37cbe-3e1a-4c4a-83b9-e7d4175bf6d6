using System;

namespace RxjhServer;

public class OpenChestClass
{
    public int ID { get; set; }

    public int CHEST_ID { get; set; }

    public string CHEST_NAME { get; set; }

    public string ITEM_NAME { get; set; }

    public string FLD_ITEM { get; set; }

    public string FLD_LEVEL { get; set; }

    public string FLD_JOB { get; set; }

    public int FLD_JOB_LEVEL { get; set; }

    public int FLD_GENDER { get; set; }

    public int FLD_FORCE { get; set; }

    public int FLD_PP { get; set; }

    public static void Get_ItemChest(int ChestId, Players player)
    {
        var num2 = 0;
        try
        {
            if (player.List_Item_Chest.Count > 0) player.List_Item_Chest.Clear();
            num2 = 1;
            foreach (var Chest in World.List_OpenChest.Values)
            {
                num2 = 2;
                while (true)
                {
                    num2 = 3;
                    var num = 0;
                    if (Chest.CHEST_ID != ChestId) break;
                    num = 1;
                    if (Chest.FLD_JOB != "")
                    {
                        num = 0;
                        var arrJob = Chest.FLD_JOB.Split(',');
                        for (var i = 0; i < arrJob.Length; i++)
                            if (int.Parse(arrJob[i]) == player.Player_Job)
                                num = 1;
                        if (num == 0) break;
                    }

                    if (Chest.FLD_JOB_LEVEL != 0)
                    {
                        num = 0;
                        if (player.Player_Job_level >= Chest.FLD_JOB_LEVEL) num = 1;
                        if (num == 0) break;
                    }

                    if (Chest.FLD_LEVEL != "")
                    {
                        num = 0;
                        var arrLV = Chest.FLD_LEVEL.Split(',');
                        if (player.Player_Level >= int.Parse(arrLV[0]) &&
                            player.Player_Level <= int.Parse(arrLV[1])) num = 1;
                        if (num == 0) break;
                    }

                    if (Chest.FLD_GENDER != 0)
                    {
                        num = 0;
                        if (player.Player_Sex == Chest.FLD_GENDER) num = 1;
                        if (num == 0) break;
                    }

                    if (Chest.FLD_FORCE != 0)
                    {
                        num = 0;
                        if (player.Player_Zx == Chest.FLD_FORCE) num = 1;
                        if (num == 0) break;
                    }

                    if (Chest.FLD_PP != 0)
                    {
                        num = 0;
                        var rate = RNG.Next(0, 10000);
                        if (rate <= Chest.FLD_PP) num = 1;
                        if (num == 0) break;
                    }

                    if (num == 1)
                    {
                        player.List_Item_Chest.Add(Chest.ID + player.CharacterFullServerID, Chest);
                        num2 = 4;
                        break;
                    }

                    num2 += 100;
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Get_OpenChest Error : " + num2 + "|" + ex);
        }
    }
}