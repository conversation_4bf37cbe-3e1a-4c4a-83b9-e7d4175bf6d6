using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using RxjhServer.DbClss;
using RxjhServer.Network;

namespace RxjhServer;

public class X_Linh_Thu_Loai : IDisposable
{
    public bool _死亡;

    public List<X_Cong_Kich_Loai> AttackList;

    private NetState client;

    public int FullServiceID;

    public Players Playe;
    private readonly object thisLock;

    public X_Vat_Pham_Loai[] ThuCung_Thanh_TrangBi;

    public X_Vat_Pham_Loai[] ThuCungVaTrangBi;

    public X_Vo_Cong_Loai[,] VoCongMoi;

    public X_Linh_Thu_Loai(long long_0, NetState clien, DataTable table2, Players Playe)
    {
        thisLock = new object();
        VoCongMoi = new X_Vo_Cong_Loai[2, 17];
        this.Playe = Playe;
        client = clien;
        Id = long_0;
        ZrName = table2.Rows[0]["ZrName"].ToString();
        Name = table2.Rows[0]["Name"].ToString();
        FLD_ZCD = (int)table2.Rows[0]["FLD_ZCD"];
        FLD_EXP = long.Parse(table2.Rows[0]["FLD_EXP"].ToString());
        FLD_LEVEL = (int)table2.Rows[0]["FLD_LEVEL"];
        FLD_JOB = (int)table2.Rows[0]["FLD_JOB"];
        FLD_JOB_LEVEL = (int)table2.Rows[0]["FLD_JOB_LEVEL"];
        FLD_HP = (int)table2.Rows[0]["FLD_HP"];
        FLD_MP = (int)table2.Rows[0]["FLD_MP"];
        Bs = (int)table2.Rows[0]["FLD_BS"];
        FLD_MAGIC1 = (int)table2.Rows[0]["FLD_MAGIC1"];
        FLD_MAGIC2 = (int)table2.Rows[0]["FLD_MAGIC2"];
        FLD_MAGIC3 = (int)table2.Rows[0]["FLD_MAGIC3"];
        FLD_MAGIC4 = (int)table2.Rows[0]["FLD_MAGIC4"];
        FLD_MAGIC5 = (int)table2.Rows[0]["FLD_MAGIC5"];
        FLD_EXP_MAX = 100000L;
        FLD_CongKich = 1000;
        FLD_PhongNgu = 1000;
        FLD_TrungDich = 1000;
        FLD_NeTranh = 1000;
        FLD_TrongLuong = 0;
        FLD_TrongLuong_MAX = 100;
        FLD_VatPham_ThemVao_HP = 0;
        FLD_VatPham_ThemVao_MP = 0;
        FLD_TrangBi_ThemVao_CongKich = 0;
        FLD_TrangBi_ThemVao_PhongNgu = 0;
        FLD_TrangBi_ThemVao_TrungDich = 0;
        FLD_TrangBi_ThemVao_NeTranh = 0;
        FLD_TrangBi_ThemVao_HP = 0;
        FLD_TrangBi_ThemVao_MP = 0;
        FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
        FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
        FLD_ThemVaoTiLePhanTram_TrungDich = 0.0;
        FLD_ThemVaoTiLePhanTram_NeTranh = 0.0;
        FLD_ThemVaoTiLePhanTram_CongKich = 0.0;
        FLD_ThemVaoTiLePhanTram_PhongNgu = 0.0;
        FLD_ThemVaoTiLePhanTram_HPCaoNhat = 0.0;
        FLD_ThemVaoTiLePhanTram_MPCaoNhat = 0.0;
        FLD_SpiritBeast_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
        FLD_SpiritBeast_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
        FLD_SpiritBeast_ThuDuocPhanTram_TangKinhNghiem = 0.0;
        ThuCung_Thanh_TrangBi = new X_Vat_Pham_Loai[16];
        ThuCungVaTrangBi = new X_Vat_Pham_Loai[5];
        VoCongMoi = new X_Vo_Cong_Loai[2, 16];
        AttackList = new List<X_Cong_Kich_Loai>();
        var src = (byte[])table2.Rows[0]["FLD_ITEM"];
        for (var i = 0; i < 16; i++)
        {
            var array = new byte[World.Item_Db_Byte_Length];
            try
            {
                System.Buffer.BlockCopy(src, i * World.Item_Db_Byte_Length, array, 0,
                    World.Item_Db_Byte_Length);
            }
            catch (Exception value)
            {
                Console.WriteLine(value);
            }

            ThuCung_Thanh_TrangBi[i] = new X_Vat_Pham_Loai(array, i);
            FLD_TrongLuong += ThuCung_Thanh_TrangBi[i].VatPham_TongTrongLuong;
        }

        var src2 = (byte[])table2.Rows[0]["FLD_WEARITEM"];
        for (var j = 0; j < 5; j++)
        {
            var array2 = new byte[World.Item_Db_Byte_Length];
            try
            {
                System.Buffer.BlockCopy(src2, j * World.Item_Db_Byte_Length, array2, 0,
                    World.Item_Db_Byte_Length);
            }
            catch (Exception value2)
            {
                Console.WriteLine(value2);
            }

            ThuCungVaTrangBi[j] = new X_Vat_Pham_Loai(array2, j);
            FLD_TrongLuong += ThuCungVaTrangBi[j].VatPham_TongTrongLuong;
        }

        var array3 = (byte[])table2.Rows[0]["FLD_KONGFU"];
        for (var k = 0; k < 32; k++)
        {
            var array4 = new byte[4];
            try
            {
                if (array3.Length < k * 4 + 4) break;
                System.Buffer.BlockCopy(array3, k * 4, array4, 0, 4);
                var num2 = Buffer.ToInt32(array4, 0);
                if (num2 == 0) continue;
                var VoCongClass = new X_Vo_Cong_Loai(num2);
                if (VoCongClass.FLD_JOB != 0)
                    switch (VoCongClass.FLD_JOB)
                    {
                        case 7:
                            if (FLD_JOB != 1) continue;
                            break;
                        case 8:
                            if (FLD_JOB != 2) continue;
                            break;
                        case 9:
                            if (FLD_JOB != 3) continue;
                            break;
                        case 10:
                            if (FLD_JOB != 4) continue;
                            break;
                    }

                if (FLD_JOB_LEVEL >= VoCongClass.FLD_JOBLEVEL && FLD_LEVEL >= VoCongClass.FLD_LEVEL)
                    VoCongMoi[VoCongClass.FLD_VoCongLoaiHinh, VoCongClass.FLD_INDEX] = VoCongClass;
            }
            catch (Exception value3)
            {
                Console.WriteLine(value3);
            }
        }

        CalculateBasicData();
        TinhToan_ThuCung_TrangBi_SoLieu();
    }

    public float NhanVatToaDo_X { get; set; }

    public float NhanVatToaDo_Y { get; set; }

    public float NhanVatToaDo_Z { get; set; }

    public int NhanVatToaDo_MAP { get; set; }

    public long Id { get; set; }

    public int Bs { get; set; }

    public string ZrName { get; set; }

    public string Name { get; set; }

    public int FLD_ZCD { get; set; }

    public long FLD_EXP { get; set; }

    public long LonNhatKinhNghiem { get; set; }

    public long FLD_EXP_MAX { get; set; }

    public int FLD_LEVEL { get; set; }

    public int FLD_JOB { get; set; }

    public int FLD_JOB_LEVEL { get; set; }

    public int FLD_HP { get; set; }

    public int FLD_HP_MAX { get; set; }

    public int FLD_MP { get; set; }

    public int FLD_MP_MAX { get; set; }

    public int FLD_VatPham_ThemVao_HP { get; set; }

    public int FLD_VatPham_ThemVao_MP { get; set; }

    public int 灵兽基本最大_HP => (int)((FLD_HP_MAX + FLD_VatPham_ThemVao_HP + FLD_TrangBi_ThemVao_HP) *
                                  (1.0 + FLD_ThemVaoTiLePhanTram_HPCaoNhat));

    public int 灵兽基本最大_MP => (int)((FLD_MP_MAX + FLD_VatPham_ThemVao_MP + FLD_TrangBi_ThemVao_MP) *
                                  (1.0 + FLD_ThemVaoTiLePhanTram_MPCaoNhat));

    public int SpiritBeastCoBanCongKich =>
        (int)((FLD_CongKich + FLD_TrangBi_ThemVao_CongKich) * (1.0 + FLD_ThemVaoTiLePhanTram_CongKich));

    public int SpiritBeastCoBanPhongNgu =>
        (int)((FLD_PhongNgu + FLD_TrangBi_ThemVao_PhongNgu) * (1.0 + FLD_ThemVaoTiLePhanTram_PhongNgu));

    public int SpiritBeastCoBanTrungDich =>
        (int)((FLD_TrungDich + FLD_TrangBi_ThemVao_TrungDich) * (1.0 + FLD_ThemVaoTiLePhanTram_TrungDich));

    public int 灵兽基本回避 => (int)((FLD_NeTranh + FLD_TrangBi_ThemVao_NeTranh) * (1.0 + FLD_ThemVaoTiLePhanTram_NeTranh));

    public int FLD_TrongLuong { get; set; }

    public int FLD_TrongLuong_MAX { get; set; }

    public int FLD_CongKich { get; set; }

    public int FLD_PhongNgu { get; set; }

    public int FLD_TrungDich { get; set; }

    public int FLD_NeTranh { get; set; }

    public int CuoiThu { get; set; }

    public int ThuBay { get; set; }

    public int FLD_TrangBi_ThemVao_CongKich { get; set; }

    public int FLD_TrangBi_ThemVao_PhongNgu { get; set; }

    public int FLD_TrangBi_ThemVao_TrungDich { get; set; }

    public int FLD_TrangBi_ThemVao_NeTranh { get; set; }

    public int FLD_TrangBi_ThemVao_HP { get; set; }

    public int FLD_TrangBi_ThemVao_MP { get; set; }

    public double FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram { get; set; }

    public double FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram { get; set; }

    public double FLD_ThemVaoTiLePhanTram_CongKich { get; set; }

    public double FLD_ThemVaoTiLePhanTram_PhongNgu { get; set; }

    public double FLD_ThemVaoTiLePhanTram_TrungDich { get; set; }

    public double FLD_ThemVaoTiLePhanTram_NeTranh { get; set; }

    public double FLD_ThemVaoTiLePhanTram_HPCaoNhat { get; set; }

    public double FLD_ThemVaoTiLePhanTram_MPCaoNhat { get; set; }

    public double FLD_SpiritBeast_LucCongKichVoCongGiaTang_TiLePhanTram { get; set; }

    public double FLD_SpiritBeast_VoCong_LucPhongNgu_GiaTangTiLePhanTram { get; set; }

    public double FLD_SpiritBeast_ThuDuocPhanTram_TangKinhNghiem { get; set; }

    public int FLD_MAGIC1 { get; set; }

    public int FLD_MAGIC2 { get; set; }

    public int FLD_MAGIC3 { get; set; }

    public int FLD_MAGIC4 { get; set; }

    public int FLD_MAGIC5 { get; set; }

    public bool TuVong
    {
        get
        {
            var num = 1;
            while (true)
            {
                switch (num)
                {
                    default:
                        if (FLD_HP <= 0)
                        {
                            num = 2;
                            continue;
                        }

                        break;
                    case 2:
                        _死亡 = false;
                        num = 0;
                        continue;
                    case 0:
                        break;
                }

                break;
            }

            return _死亡;
        }
        set => _死亡 = value;
    }

    public void Dispose()
    {
        Playe = null;
        client = null;
    }

    public void addFLD_追加百分比_Cong_kich(double double_0)
    {
        var @lock = new Lock(thisLock, "addFLD_追加百分比_攻击");
        try
        {
            FLD_ThemVaoTiLePhanTram_CongKich += double_0;
        }
        finally
        {
            var num = 1;
            while (true)
            {
                switch (num)
                {
                    case 0:
                        break;
                    default:
                        if (@lock != null)
                        {
                            num = 2;
                            continue;
                        }

                        break;
                    case 2:
                        ((IDisposable)@lock).Dispose();
                        num = 0;
                        continue;
                }

                break;
            }
        }
    }

    public void dllFLD_ThemVaoTiLePhanTram_Attack(double double_0)
    {
        var @lock = new Lock(thisLock, "dllFLD_追加百分比_攻击");
        try
        {
            FLD_ThemVaoTiLePhanTram_CongKich -= double_0;
        }
        finally
        {
            var num = 1;
            while (true)
            {
                switch (num)
                {
                    case 2:
                        break;
                    case 0:
                        ((IDisposable)@lock).Dispose();
                        num = 2;
                        continue;
                    default:
                        if (@lock != null)
                        {
                            num = 0;
                            continue;
                        }

                        break;
                }

                break;
            }
        }
    }

    public void addFLD_Them_vao_ti_le_phan_tram_Phong_ngu(double double_0)
    {
        var @lock = new Lock(thisLock, "addFLD_追加百分比_防御");
        try
        {
            FLD_ThemVaoTiLePhanTram_PhongNgu += double_0;
        }
        finally
        {
            var num = 1;
            while (true)
            {
                switch (num)
                {
                    case 2:
                        break;
                    case 0:
                        ((IDisposable)@lock).Dispose();
                        num = 2;
                        continue;
                    default:
                        if (@lock != null)
                        {
                            num = 0;
                            continue;
                        }

                        break;
                }

                break;
            }
        }
    }

    public void dllFLD_ThemVaoTiLePhanTram_PhongNgu(double double_0)
    {
        var @lock = new Lock(thisLock, "dllFLD_追加百分比_防御");
        try
        {
            FLD_ThemVaoTiLePhanTram_PhongNgu -= double_0;
        }
        finally
        {
            var num = 1;
            while (true)
            {
                switch (num)
                {
                    case 2:
                        break;
                    case 0:
                        ((IDisposable)@lock).Dispose();
                        num = 2;
                        continue;
                    default:
                        if (@lock != null)
                        {
                            num = 0;
                            continue;
                        }

                        break;
                }

                break;
            }
        }
    }

    ~X_Linh_Thu_Loai()
    {
    }

    public bool LookInNpc(int far_, NpcClass Npc)
    {
        if (Npc.Rxjh_Map != NhanVatToaDo_MAP) return false;
        var num = Npc.Rxjh_X - NhanVatToaDo_X;
        var num2 = Npc.Rxjh_Y - NhanVatToaDo_Y;
        return (int)Math.Sqrt(num * (double)num + num2 * (double)num2) <= (double)far_;
    }

    public void CalculateBasicData()
    {
        if (FLD_LEVEL > 99) FLD_LEVEL = 99;
        LonNhatKinhNghiem = (long)World.Level[FLD_LEVEL];
        var fLD_LEVEL = FLD_LEVEL;
        while (FLD_LEVEL < 99)
        {
            if (client == null || !client.Running) return;
            int fLD_LEVEL2;
            if (FLD_EXP < LonNhatKinhNghiem)
            {
                if (FLD_LEVEL != 1 && FLD_EXP - Convert.ToInt64(World.Level[FLD_LEVEL - 1]) < 1.0)
                {
                    fLD_LEVEL2 = FLD_LEVEL - 1;
                    FLD_LEVEL = fLD_LEVEL2;
                    LonNhatKinhNghiem = (long)World.Level[FLD_LEVEL];
                }

                break;
            }

            fLD_LEVEL2 = FLD_LEVEL + 1;
            FLD_LEVEL = fLD_LEVEL2;
            LonNhatKinhNghiem = (long)World.Level[FLD_LEVEL];
        }

        if (FLD_LEVEL - fLD_LEVEL != 0)
        {
            if (FLD_LEVEL - fLD_LEVEL > 0 && client.Player.CharacterBeast != null)
                client.Player.TipsAfterTheUpgradeOfTheSpiritBeast();
            if (client.Player.CharacterBeast != null)
            {
                client.Player.UpdateSpiritBeastHP_MP_SP();
                client.Player.UpdateSpiritBeastMartialArtsAndStatus();
            }
        }

        FLD_TrongLuong_MAX = 500 + 20 * FLD_LEVEL;
        switch (FLD_JOB)
        {
            case 1:
            {
                FLD_HP_MAX = 133 + FLD_LEVEL * 12;
                FLD_MP_MAX = 114 + FLD_LEVEL * 2;
                FLD_TrungDich = 8 + FLD_LEVEL;
                FLD_NeTranh = 8 + FLD_LEVEL;
                FLD_CongKich = 9;
                FLD_PhongNgu = 16;
                for (var j = 2; j <= FLD_LEVEL; j++)
                {
                    if (j % 2 == 0)
                    {
                        FLD_CongKich += 2;
                        FLD_PhongNgu += 2;
                        continue;
                    }

                    var fLD_LEVEL2 = FLD_CongKich + 1;
                    FLD_CongKich = fLD_LEVEL2;
                    fLD_LEVEL2 = FLD_PhongNgu + 1;
                    FLD_PhongNgu = fLD_LEVEL2;
                }

                break;
            }
            case 2:
            {
                FLD_HP_MAX = 133 + FLD_LEVEL * 12;
                FLD_MP_MAX = 114 + FLD_LEVEL * 2;
                FLD_TrungDich = 8 + FLD_LEVEL;
                FLD_NeTranh = 8 + FLD_LEVEL;
                FLD_CongKich = 9;
                FLD_PhongNgu = 16;
                for (var l = 2; l <= FLD_LEVEL; l++)
                {
                    if (l % 2 == 0)
                    {
                        FLD_CongKich += 2;
                        FLD_PhongNgu += 2;
                        continue;
                    }

                    var fLD_LEVEL2 = FLD_CongKich + 1;
                    FLD_CongKich = fLD_LEVEL2;
                    fLD_LEVEL2 = FLD_PhongNgu + 1;
                    FLD_PhongNgu = fLD_LEVEL2;
                }

                break;
            }
            case 3:
            {
                FLD_HP_MAX = 133 + FLD_LEVEL * 12;
                FLD_MP_MAX = 114 + FLD_LEVEL * 2;
                FLD_TrungDich = 8 + FLD_LEVEL;
                FLD_NeTranh = 8 + FLD_LEVEL;
                FLD_CongKich = 9;
                FLD_PhongNgu = 16;
                for (var m = 2; m <= FLD_LEVEL; m++)
                {
                    if (m % 2 == 0)
                    {
                        FLD_CongKich += 2;
                        FLD_PhongNgu += 2;
                        continue;
                    }

                    var fLD_LEVEL2 = FLD_CongKich + 1;
                    FLD_CongKich = fLD_LEVEL2;
                    fLD_LEVEL2 = FLD_PhongNgu + 1;
                    FLD_PhongNgu = fLD_LEVEL2;
                }

                break;
            }
            case 4:
            {
                FLD_HP_MAX = 133 + FLD_LEVEL * 12;
                FLD_MP_MAX = 114 + FLD_LEVEL * 2;
                FLD_TrungDich = 8 + FLD_LEVEL;
                FLD_NeTranh = 8 + FLD_LEVEL;
                FLD_CongKich = 9;
                FLD_PhongNgu = 16;
                for (var k = 2; k <= FLD_LEVEL; k++)
                {
                    if (k % 2 == 0)
                    {
                        FLD_CongKich += 2;
                        FLD_PhongNgu += 2;
                        continue;
                    }

                    var fLD_LEVEL2 = FLD_CongKich + 1;
                    FLD_CongKich = fLD_LEVEL2;
                    fLD_LEVEL2 = FLD_PhongNgu + 1;
                    FLD_PhongNgu = fLD_LEVEL2;
                }

                break;
            }
            case 5:
            {
                FLD_HP_MAX = 133 + FLD_LEVEL * 12;
                FLD_MP_MAX = 114 + FLD_LEVEL * 2;
                FLD_TrungDich = 8 + FLD_LEVEL;
                FLD_NeTranh = 8 + FLD_LEVEL;
                FLD_CongKich = 9;
                FLD_PhongNgu = 16;
                for (var i = 2; i <= FLD_LEVEL; i++)
                {
                    if (i % 2 == 0)
                    {
                        FLD_CongKich += 2;
                        FLD_PhongNgu += 2;
                        continue;
                    }

                    var fLD_LEVEL2 = FLD_CongKich + 1;
                    FLD_CongKich = fLD_LEVEL2;
                    fLD_LEVEL2 = FLD_PhongNgu + 1;
                    FLD_PhongNgu = fLD_LEVEL2;
                }

                break;
            }
        }

        switch (FLD_JOB_LEVEL)
        {
            case 1:
                FLD_CongKich += 5;
                FLD_PhongNgu += 5;
                FLD_HP_MAX += 85;
                FLD_MP_MAX += 50;
                break;
            case 2:
                FLD_CongKich += 15;
                FLD_PhongNgu += 15;
                FLD_HP_MAX += 200;
                FLD_MP_MAX += 150;
                break;
            case 3:
                FLD_CongKich += 35;
                FLD_PhongNgu += 35;
                FLD_HP_MAX += 400;
                FLD_MP_MAX += 350;
                break;
        }

        if (FLD_JOB == 1)
        {
            Playe.FLD_Pet_ThemVao_LonNhatHP = (FLD_LEVEL + 1) * 3;
        }
        else if (FLD_JOB == 5)
        {
            Playe.FLD_Pet_ThemVao_LonNhatHP = (FLD_LEVEL + 1) * 2;
            Playe.FLD_Pet_ThemVao_PhongNgu = (int)((FLD_LEVEL + 1) * 0.5);
            Playe.FLD_Pet_ThemVao_CongKich = (int)((FLD_LEVEL + 1) * 0.5);
        }
        else
        {
            Playe.FLD_Pet_ThemVao_LonNhatHP = (FLD_LEVEL + 1) * 2;
            Playe.FLD_Pet_ThemVao_PhongNgu = (int)((FLD_LEVEL + 1) * 0.5);
        }

        Playe.CapNhat_HP_MP_SP();
        Playe.UpdateMartialArtsAndStatus();
    }

    public void TinhToan_ThuCung_TrangBi_SoLieu()
    {
        using (new Lock(thisLock, "TinhToan_ThuCung_TrangBi_SoLieu"))
        {
            FLD_TrangBi_ThemVao_CongKich = 0;
            FLD_TrangBi_ThemVao_PhongNgu = 0;
            FLD_TrangBi_ThemVao_TrungDich = 0;
            FLD_TrangBi_ThemVao_NeTranh = 0;
            FLD_TrangBi_ThemVao_HP = 0;
            FLD_TrangBi_ThemVao_MP = 0;
            FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram = 0.0;
            FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram = 0.0;
            FLD_SpiritBeast_ThuDuocPhanTram_TangKinhNghiem = 0.0;
            for (var i = 0; i < 4; i++)
                if (ThuCungVaTrangBi[i].GetVatPham_ID != 0)
                {
                    ThuCungVaTrangBi[i].DatDuocVatPham_ThuocTinhPhuongThuc(0, 0);
                    FLD_TrangBi_ThemVao_CongKich += (ThuCungVaTrangBi[i].Vat_Pham_Luc_Cong_Kich +
                                                     ThuCungVaTrangBi[i].Vat_Pham_Luc_Cong_KichMAX) / 2;
                    FLD_TrangBi_ThemVao_PhongNgu += ThuCungVaTrangBi[i].Vat_Pham_Luc_Phong_Ngu;
                    FLD_TrangBi_ThemVao_TrungDich += ThuCungVaTrangBi[i].VatPham_ThuocTinh_TiLeChinhXac_GiaTang;
                    FLD_TrangBi_ThemVao_NeTranh += ThuCungVaTrangBi[i].VatPham_ThuocTinh_NeTranh_Suat_GiaTang;
                    FLD_TrangBi_ThemVao_HP += ThuCungVaTrangBi[i].VatPham_ThuocTinh_SinhMenhLuc_GiaTang;
                    FLD_TrangBi_ThemVao_MP += ThuCungVaTrangBi[i].VatPham_ThuocTinh_NoiCong_Luc_GiaTang;
                    double num = ThuCungVaTrangBi[i].VatPham_ThuocTinh_VoCong_LucCongKich;
                    var num2 = ThuCungVaTrangBi[i].VatPham_ThuocTinh_VoCong_LucPhongNgu_GiaTang *
                               (1.0 - World.TyLePhanTram_PhongNguVoCong);
                    FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += num * 0.01;
                    FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += num2 * 0.01;
                }

            DatDuocThuLinhDanThuocTinh(FLD_MAGIC1.ToString());
            DatDuocThuLinhDanThuocTinh(FLD_MAGIC2.ToString());
            DatDuocThuLinhDanThuocTinh(FLD_MAGIC3.ToString());
            DatDuocThuLinhDanThuocTinh(FLD_MAGIC4.ToString());
            DatDuocThuLinhDanThuocTinh(FLD_MAGIC5.ToString());
        }
    }

    public void DatDuocThuLinhDanThuocTinh(string ysqh)
    {
        try
        {
            var num2 = 0;
            string text = null;
            if (!Buffer.IsEquals(ysqh, "0"))
            {
                switch (ysqh.Length)
                {
                    default:
                        return;
                    case 9:
                        text = ysqh.Substring(0, 2);
                        break;
                    case 8:
                        text = ysqh.Substring(0, 1);
                        break;
                }

                num2 = int.Parse(ysqh.Substring(ysqh.Length - 2, 2));
                switch (int.Parse(text))
                {
                    case 8:
                    case 9:
                    case 10:
                    case 12:
                    case 13:
                    case 14:
                        break;
                    case 1:
                        FLD_TrangBi_ThemVao_CongKich += num2;
                        break;
                    case 2:
                        FLD_TrangBi_ThemVao_PhongNgu += num2;
                        break;
                    case 3:
                        FLD_TrangBi_ThemVao_HP += num2;
                        break;
                    case 4:
                        FLD_TrangBi_ThemVao_MP += num2;
                        break;
                    case 5:
                        FLD_TrangBi_ThemVao_TrungDich += num2;
                        break;
                    case 6:
                        FLD_TrangBi_ThemVao_NeTranh += num2;
                        break;
                    case 7:
                        FLD_TrangBi_LucCongKichVoCongGiaTang_TiLePhanTram += num2 * 0.01;
                        break;
                    case 11:
                        FLD_TrangBi_VoCong_LucPhongNgu_GiaTangTiLePhanTram += num2 * 0.01;
                        break;
                    case 15:
                        FLD_SpiritBeast_ThuDuocPhanTram_TangKinhNghiem += num2 * 0.01;
                        break;
                }
            }
        }
        catch
        {
        }
    }

    public void SaveSoLieu()
    {
        var prams = new SqlParameter[18]
        {
            SqlDBA.MakeInParam("@id", SqlDbType.VarChar, 20, Id.ToString()),
            SqlDBA.MakeInParam("@name", SqlDbType.VarChar, 20, Name),
            SqlDBA.MakeInParam("@level", SqlDbType.Int, 0, FLD_LEVEL),
            SqlDBA.MakeInParam("@zcd", SqlDbType.Int, 10, FLD_ZCD),
            SqlDBA.MakeInParam("@job", SqlDbType.Int, 0, FLD_JOB),
            SqlDBA.MakeInParam("@job_level", SqlDbType.Int, 0, FLD_JOB_LEVEL),
            SqlDBA.MakeInParam("@exp", SqlDbType.VarChar, 50, FLD_EXP.ToString()),
            SqlDBA.MakeInParam("@hp", SqlDbType.Int, 0, FLD_HP),
            SqlDBA.MakeInParam("@mp", SqlDbType.Int, 0, FLD_MP),
            SqlDBA.MakeInParam("@strWearitem", SqlDbType.VarBinary, 385, GetWEARITEMCodes()),
            SqlDBA.MakeInParam("@strItem", SqlDbType.VarBinary, 1232, GetFLD_ITEMCodes()),
            SqlDBA.MakeInParam("@strKongfu", SqlDbType.VarBinary, 128, GetFLD_KONGFUCodes()),
            SqlDBA.MakeInParam("@bs", SqlDbType.Int, 0, Bs),
            SqlDBA.MakeInParam("@MAGIC1", SqlDbType.Int, 0, FLD_MAGIC1),
            SqlDBA.MakeInParam("@MAGIC2", SqlDbType.Int, 0, FLD_MAGIC2),
            SqlDBA.MakeInParam("@MAGIC3", SqlDbType.Int, 0, FLD_MAGIC3),
            SqlDBA.MakeInParam("@MAGIC4", SqlDbType.Int, 0, FLD_MAGIC4),
            SqlDBA.MakeInParam("@MAGIC5", SqlDbType.Int, 0, FLD_MAGIC5)
        };
        var sqlPool = World.SqlPool;
        var dbPoolClass = new DbPoolClass();
        dbPoolClass.Conn = DBA.getstrConnection(null);
        dbPoolClass.Prams = prams;
        dbPoolClass.Sql = "XWWL_UPDATE_Cw_DATA";
        sqlPool.Enqueue(dbPoolClass);
    }

    public byte[] GetWEARITEMCodes()
    {
        var array = new byte[World.Item_Db_Byte_Length * 5];
        for (var i = 0; i < 5; i++)
        {
            byte[] src;
            try
            {
                src = ThuCungVaTrangBi[i].VatPham_byte;
            }
            catch
            {
                src = new byte[World.Item_Db_Byte_Length];
            }

            System.Buffer.BlockCopy(src, 0, array, i * World.Item_Db_Byte_Length,
                World.Item_Db_Byte_Length);
        }

        return array;
    }

    public byte[] GetFLD_ITEMCodes()
    {
        var array = new byte[World.Item_Db_Byte_Length * 16];
        for (var i = 0; i < 16; i++)
        {
            byte[] src;
            try
            {
                src = ThuCung_Thanh_TrangBi[i].VatPham_byte;
            }
            catch
            {
                src = new byte[World.Item_Db_Byte_Length];
            }

            System.Buffer.BlockCopy(src, 0, array, i * World.Item_Db_Byte_Length,
                World.Item_Db_Byte_Length);
        }

        return array;
    }

    public byte[] GetFLD_KONGFUCodes()
    {
        var SendingClass = new SendingClass();
        try
        {
            for (var i = 0; i < 2; i++)
            for (var j = 0; j < 16; j++)
                if (VoCongMoi[i, j] != null)
                    SendingClass.Write4(VoCongMoi[i, j].FLD_PID);
        }
        catch
        {
        }

        return SendingClass.ToArray3();
    }
}