using System;
using System.Collections.Generic;

namespace RxjhServer;

public class X_Giao_Dich_Loai : IDisposable
{
    public bool GiaoDichBenTrong;

    public long GiaoDichTien;
    public Dictionary<long, X_Vat_Pham_Giao_Dich_Loai> GiaoDichVatPham1;

    public X_Giao_Dich_Loai()
    {
    }

    public X_Giao_Dich_Loai(Players NguoiGiaoDich_)
    {
        NguoiGiaoDich = NguoiGiaoDich_;
        GiaoDich_TiepNhan = false;
        GiaoDichTien = 0L;
        GiaoDichVatPham1 = new Dictionary<long, X_Vat_Pham_Giao_Dich_Loai>();
    }

    public bool GiaoDich_TiepNhan { get; set; }

    public Players NguoiGiaoDich { get; set; }

    public void Dispose()
    {
        try
        {
            GiaoDichBenTrong = false;
            if (NguoiGiaoDich != null)
            {
                NguoiGiaoDich.OpenWarehouse = false;
                NguoiGiaoDich.GiaoDich.NguoiGiaoDich.OpenWarehouse = false;
                NguoiGiaoDich.GiaoDich.GiaoDichBenTrong = false;
                NguoiGiaoDich.GiaoDich.NguoiGiaoDich = null;
                NguoiGiaoDich = null;
            }
        }
        catch (Exception)
        {
        }
    }

    ~X_Giao_Dich_Loai()
    {
    }

    public void CloseTransaction()
    {
        Dispose();
    }
}