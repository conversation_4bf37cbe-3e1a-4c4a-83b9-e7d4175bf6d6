using System;
using System.Timers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class X_Than_Nu_Di_Thuong_Trang_Thai_Loai : IDisposable
{
    public NpcClass Npc;

    public int NpcPlayId;
    public System.Timers.Timer npcyd;

    public Players Play;

    public DateTime time;

    public System.Timers.Timer yczt;

    public double ycztsl;

    public X_Than_Nu_Di_Thuong_Trang_Thai_Loai(Players Play_, int ThoiGian, int DiThuong_ID, double DiThuong_SoLuong,
        double TonThuong)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường trạng thái loại -NEW 444");
        FLD_PID = DiThuong_ID;
        FLD_NUM = DiThuong_SoLuong;
        this.TonThuong = TonThuong;
        time = DateTime.Now;
        time = time.AddMilliseconds(ThoiGian);
        Play = Play_;
        npcyd = new System.Timers.Timer(ThoiGian);
        npcyd.Elapsed += TimeEndEvent1;
        npcyd.Enabled = true;
        npcyd.AutoReset = false;
        Play.StatusEffect(FLD_PID, 1, (int)DiThuong_SoLuong, ThoiGian / 1000);
    }

    public X_Than_Nu_Di_Thuong_Trang_Thai_Loai(Players Play_, int ThoiGian, int DiThuong_ID, double DiThuong_SoLuong,
        double TonThuong, int Skill_ID = 0, int Level = 1)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "TrangThai_BatThuongClass-NEW");
        FLD_PID = DiThuong_ID;
        FLD_NUM = DiThuong_SoLuong;
        this.TonThuong = TonThuong;
        time = DateTime.Now;
        time = time.AddMilliseconds(ThoiGian);
        Play = Play_;
        npcyd = new System.Timers.Timer(ThoiGian);
        npcyd.Elapsed += TimeEndEvent1;
        npcyd.Enabled = true;
        npcyd.AutoReset = false;
        StatusEffect(FLD_PID, 1, (int)DiThuong_SoLuong, ThoiGian / 1000);
    }

    public X_Than_Nu_Di_Thuong_Trang_Thai_Loai(NpcClass NPC_, int ThoiGian, int DiThuong_ID, double DiThuong_SoLuong,
        double TonThuong, int Skill_ID = 0, int Level = 1)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "TrangThai_BatThuongClass-NPC");
        FLD_PID = DiThuong_ID;
        FLD_NUM = DiThuong_SoLuong;
        this.TonThuong = TonThuong;
        time = DateTime.Now;
        time = time.AddMilliseconds(ThoiGian);
        Npc = NPC_;
        npcyd = new System.Timers.Timer(ThoiGian);
        npcyd.Elapsed += TimeEndEvent1;
        npcyd.Enabled = true;
        npcyd.AutoReset = false;
        StatusEffect(FLD_PID, 1, (int)DiThuong_SoLuong, ThoiGian / 1000);
    }

    public int FLD_PID { get; set; }

    public double FLD_NUM { get; set; }

    private double TonThuong { get; set; }

    public void Dispose()
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "Dị thường trạng thái loại -Dispose 666");
        if (npcyd != null)
        {
            npcyd.Enabled = false;
            npcyd.Close();
            npcyd.Dispose();
            npcyd = null;
        }

        if (yczt != null)
        {
            yczt.Enabled = false;
            yczt.Close();
            yczt.Dispose();
            yczt = null;
        }

        Play = null;
        Npc = null;
    }

    public void TimeEndEvent1(object sender, ElapsedEventArgs e)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "TimeEndEvent1");
        ThoiGianKetThucSuKien();
    }

    public void ThoiGianKetThucSuKien()
    {
        if (npcyd != null)
        {
            npcyd.Enabled = false;
            npcyd.Close();
            npcyd.Dispose();
            npcyd = null;
        }

        if (Npc != null)
            try
            {
                var fLD_PID = FLD_PID;
                var num = fLD_PID;
                if (num == 44) Npc.DiemVuongBao_ThucThi();
                if (Npc.TrangThai_BatThuong_ThanNu != null && Npc.TrangThai_BatThuong_ThanNu.ContainsKey(FLD_PID))
                    Npc.TrangThai_BatThuong_ThanNu.Remove(FLD_PID);
                TrangThaiHieuQua2(FLD_PID, 0, 0, 0);
                Dispose();
                return;
            }
            catch (Exception ex)
            {
                Form1.WriteLine(1, "TrangThai_BatThuongClass ThoiGianKetThucSuKien  error：[" + FLD_PID + "]" + ex);
                return;
            }
            finally
            {
                Dispose();
            }

        if (Play != null)
        {
            if (!Play.Exiting && Play.Client.Running)
                try
                {
                    switch (FLD_PID)
                    {
                        case 52:
                            Play.TNDebuff_ThemVao_PhanTramCongKich = 0.0;
                            Play.TNDebuff_ThemVao_PhanTramPhongThu = 0.0;
                            Play.UpdateMartialArtsAndStatus();
                            break;
                        case 35:
                            Play.TNDebuff_GiamBot_PhanTramCongKich = 0.0;
                            Play.TNDebuff_GiamBot_PhanTramPhongThu = 0.0;
                            Play.UpdateMartialArtsAndStatus();
                            break;
                        case 34:
                            Play.TNDebuff_GiamBot_PhanTramCongKich = 0.0;
                            Play.TNDebuff_GiamBot_PhanTramPhongThu = 0.0;
                            Play.UpdateMartialArtsAndStatus();
                            break;
                        default:
                            Play.UpdateMartialArtsAndStatus();
                            break;
                    }

                    Play.ThanNuTrangThai_BatThuong.Remove(FLD_PID);
                    TrangThaiHieuQua2(FLD_PID, 0, 0, 0);
                    Dispose();
                    return;
                }
                catch (Exception ex2)
                {
                    Form1.WriteLine(1, "TrangThai_BatThuongClass ThoiGianKetThucSuKien  error：[" + FLD_PID + "]" + ex2);
                    return;
                }
                finally
                {
                    Dispose();
                }

            Play.ThanNuTrangThai_BatThuong?.Clear();
            Dispose();
        }
        else
        {
            Dispose();
        }
    }

    public void TrangThaiHieuQua2(int DiThuong_ID, int SwitchOnOff, int DiThuong_SoLuong, int ThoiGian)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "TrangThai_BatThuongClass-TrangThaiHieuQua");
        var array = Converter.HexStringToByte(
            "AA553E00250040153800000000002500000034000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
        System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 18, 4);
        if (Play != null)
        {
            System.Buffer.BlockCopy(BitConverter.GetBytes(Play.CharacterFullServerID), 0, array, 14, 2);
            System.Buffer.BlockCopy(BitConverter.GetBytes(Play.CharacterFullServerID), 0, array, 4, 2);
            if (Play.Client != null) Play.Client.Send_Map_Data(array, array.Length);
            Play.SendCurrentRangeBroadcastData(array, array.Length);
        }
        else if (Npc != null)
        {
            System.Buffer.BlockCopy(BitConverter.GetBytes(Npc.FLD_INDEX), 0, array, 14, 2);
            System.Buffer.BlockCopy(BitConverter.GetBytes(Npc.FLD_INDEX), 0, array, 4, 2);
            Npc.QuangBaSoLieu(array, array.Length);
        }
    }

    public void StatusEffect(int DiThuong_ID, int SwitchOnOff, int DiThuong_SoLuong, int ThoiGian)
    {
        if (World.jlMsg == 1) Form1.WriteLine(0, "TrangThai_BatThuongClass-TrangThaiHieuQua");
        var array = Converter.HexStringToByte(
            "AA5546003527401538008C0300002C0100000900000001000000000000006016A2496016A2492600000014000000000000008C030000E80300000900000001000000000000000000000055AA");
        System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 18, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 58, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(SwitchOnOff), 0, array, 62, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(ThoiGian), 0, array, 38, 4);
        System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_SoLuong), 0, array, 42, 4);
        if (Play != null)
        {
            System.Buffer.BlockCopy(BitConverter.GetBytes(Play.CharacterFullServerID), 0, array, 14, 2);
            System.Buffer.BlockCopy(BitConverter.GetBytes(Play.CharacterFullServerID), 0, array, 4, 2);
            Play.Client?.SendMultiplePackage(array, array.Length);
            Play.SendMultiplePacketsOfCurrentRangeBroadcastData(array, array.Length);
        }
        else if (Npc != null)
        {
            System.Buffer.BlockCopy(BitConverter.GetBytes(Npc.FLD_INDEX), 0, array, 14, 2);
            System.Buffer.BlockCopy(BitConverter.GetBytes(Npc.FLD_INDEX), 0, array, 4, 2);
            Npc.QuangBaSoLieu(array, array.Length);
        }
    }
}