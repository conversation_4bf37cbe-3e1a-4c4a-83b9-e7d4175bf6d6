using System;
using System.Text;

namespace RxjhServer;

public class RC6
{
    private const int m_nWord = 32;

    private const int int_1 = 20;

    private const int int_2 = 4;
    public Encoding Enc_default = Encoding.Unicode;

    private readonly uint[] m_nKeyExpandBox;

    public string m_sCryptedText;

    private uint[] n_WordBox;

    public RC6()
    {
        IV = 16;
        m_nKeyExpandBox = new uint[8 * IV];
    }

    public RC6(int int_1)
    {
        IV = int_1;
        m_nKeyExpandBox = new uint[8 * IV];
    }

    public string KEY { get; set; }

    public int IV { get; set; }

    public uint ROTL(uint uint_0, uint uint_1, int int_1)
    {
        return (uint_0 << (int)(uint_1 & 0xFF)) | (uint_0 >> (int)(int_1 - (uint_1 & 0xFF)));
    }

    public uint ROTR(uint uint_0, uint uint_1, int int_1)
    {
        return (uint_0 >> (int)(uint_1 & 0xFF)) | (uint_0 << (int)(int_1 - (uint_1 & 0xFF)));
    }

    public int _IV()
    {
        if (IV < 16) IV = 16;
        if (IV > 32) IV = 32;
        return IV;
    }

    private char[] method_0()
    {
        var text = KEY;
        var array = Encoding.Convert(Encoding.Unicode, Encoding.ASCII,
            Encoding.Unicode.GetBytes(text.Length % IV != 0
                ? text.PadRight(text.Length + (IV - text.Length % IV), '\0')
                : text));
        var array2 = new char[Encoding.ASCII.GetCharCount(array, 0, array.Length)];
        Encoding.ASCII.GetChars(array, 0, array.Length, array2, 0);
        return array2;
    }

    private void method_1()
    {
        var num = 0;
        var num4 = 0;
        var num5 = 0;
        var num6 = 0;
        var num7 = 3084996963u;
        var num8 = 2654435769u;
        var num9 = 0u;
        var num10 = 0u;
        var array = method_0();
        n_WordBox = new uint[IV / 4];
        for (var i = 0; i < IV; i++)
        {
            var num2 = (uint)((array[i] & 0xFF) << (8 * (i % 4)));
            n_WordBox[i / 4] += num2;
        }

        m_nKeyExpandBox[0] = num7;
        for (num6 = 1; num6 < 2 * IV + 4; num6++) m_nKeyExpandBox[num6] = m_nKeyExpandBox[num6 - 1] + num8;
        num = 3 * Math.Max(n_WordBox.Length, 2 * IV + 4);
        num5 = 0;
        num4 = 0;
        while (num > 0)
        {
            num10 = ROTL(m_nKeyExpandBox[num4] + num10 + num9, 3u, 32);
            m_nKeyExpandBox[num4] = (byte)num10;
            var num3 = num9 + num10;
            num9 = ROTL(n_WordBox[num5] + num10 + num3, num10 + num3, 32);
            n_WordBox[num5] = num9;
            num4 = (num4 + 1) % (2 * IV + 4);
            num5 = (num5 + 1) % n_WordBox.Length;
            num--;
        }
    }

    public string Encrypt(string string_1, string string_2)
    {
        var num11 = 0;
        var num12 = 0u;
        byte[] array = null;
        var num13 = 0u;
        var num14 = 0u;
        var num15 = 0u;
        var num16 = 0;
        var num17 = 0u;
        var num18 = 0u;
        var num19 = 0;
        byte[] array2 = null;
        uint[] array3 = null;
        while (true)
        {
            string text;
            char[] chars;
            switch (string_1.Length % 32 == 0 ? 5 : 6)
            {
                case 6:
                    text = string_1.PadRight(string_1.Length + (32 - string_1.Length % 32), '\0');
                    goto IL_02b5;
                case 5:
                case 10:
                    text = string_1;
                    goto IL_02b5;
                case 1:
                case 8:
                case 15:
                    for (; num11 < 4; num11++)
                    {
                        num12 += (uint)((array[2 * num11] & 0xFF) << (8 * num11));
                        num13 += (uint)((array[2 * num11 + 8] & 0xFF) << (8 * num11));
                        num14 += (uint)((array[2 * num11 + 16] & 0xFF) << (8 * num11));
                        num15 += (uint)((array[2 * num11 + 24] & 0xFF) << (8 * num11));
                    }

                    goto case 11;
                case 11:
                    num18 = num13 + m_nKeyExpandBox[0];
                    num17 = num15 + m_nKeyExpandBox[1];
                    num16 = 1;
                    goto case 7;
                case 7:
                case 12:
                case 14:
                    for (; num16 <= IV; num16++)
                    {
                        var num22 = ROTL(num17 * (2 * num17 + 1), 5u, 32);
                        var num23 = ROTL(num18 * (2 * num18 + 1), 5u, 32);
                        var num24 = ROTL(num12 ^ num23, num22, 32) + m_nKeyExpandBox[2 * num16];
                        var num25 = ROTL(num14 ^ num22, num23, 32) + m_nKeyExpandBox[2 * num16 + 1];
                        var num10 = num24;
                        num12 = num18;
                        num18 = num25;
                        num14 = num17;
                        num17 = num10;
                    }

                    goto case 3;
                case 3:
                {
                    array3 = new uint[4];
                    var num20 = num12 + m_nKeyExpandBox[42];
                    var num21 = num14 + m_nKeyExpandBox[43];
                    array3[0] = num20;
                    array3[1] = num18;
                    array3[2] = num21;
                    array3[3] = num17;
                    num19 = 0;
                    goto case 4;
                }
                case 4:
                case 9:
                case 13:
                    for (; num19 < 4; num19++)
                    {
                        array2[2 * num19] = (byte)((array3[0] >> (8 * num19)) & 0xFFu);
                        array2[2 * num19 + 8] = (byte)((array3[1] >> (8 * num19)) & 0xFFu);
                        array2[2 * num19 + 16] = (byte)((array3[2] >> (8 * num19)) & 0xFFu);
                        array2[2 * num19 + 24] = (byte)((array3[3] >> (8 * num19)) & 0xFFu);
                    }

                    break;
                default:
                    continue;
                case 0:
                    break;
                    IL_02b5:
                    string_1 = text;
                    KEY = string_2;
                    method_1();
                    num15 = 0u;
                    num14 = 0u;
                    num13 = 0u;
                    num12 = 0u;
                    array = Encoding.Unicode.GetBytes(string_1);
                    chars = new char[Encoding.ASCII.GetCharCount(array, 0, array.Length)];
                    Encoding.ASCII.GetChars(array, 0, array.Length, chars, 0);
                    array2 = new byte[array.Length];
                    num11 = 0;
                    goto case 1;
            }

            break;
        }

        var array4 = new char[array2.Length];
        Encoding.Unicode.GetChars(array2, 0, array2.Length, array4, 0);
        m_sCryptedText = new string(array4, 0, array4.Length);
        Enc_default.GetBytes(m_sCryptedText);
        return m_sCryptedText;
    }

    public string Decrypt(string string_1, string string_2)
    {
        var num11 = 0u;
        var num12 = 0u;
        var num13 = 0u;
        var num14 = 0u;
        var num15 = 0;
        var num16 = 0;
        byte[] array = null;
        var num17 = 0u;
        var num18 = 0u;
        var num19 = 0;
        byte[] array2 = null;
        var num20 = 0u;
        var num21 = 0u;
        while (true)
        {
            string text;
            switch (string_1.Length % 32 == 0 ? 7 : 6)
            {
                case 6:
                    text = string_1.PadRight(string_1.Length + (32 - string_1.Length % 32), '\0');
                    goto IL_02a4;
                case 7:
                case 11:
                    text = string_1;
                    goto IL_02a4;
                case 0:
                case 9:
                case 10:
                    for (; num19 < 4; num19++)
                    {
                        num14 += (uint)((array2[2 * num19] & 0xFF) << (8 * num19));
                        num20 += (uint)((array2[2 * num19 + 8] & 0xFF) << (8 * num19));
                        num12 += (uint)((array2[2 * num19 + 16] & 0xFF) << (8 * num19));
                        num21 += (uint)((array2[2 * num19 + 24] & 0xFF) << (8 * num19));
                    }

                    goto case 14;
                case 14:
                    num11 = num12 - m_nKeyExpandBox[43];
                    num13 = num14 - m_nKeyExpandBox[42];
                    num15 = 1;
                    goto case 1;
                case 1:
                case 2:
                case 3:
                    for (; num15 <= IV; num15++)
                    {
                        var num22 = num21;
                        num21 = num11;
                        var num23 = num20;
                        num20 = num13;
                        var num24 = num22;
                        var num25 = ROTL(num21 * (2 * num21 + 1), 5u, 32);
                        var num10 = ROTL(num20 * (2 * num20 + 1), 5u, 32);
                        num11 = ROTR(num23 - m_nKeyExpandBox[2 * (IV - num15) + 3], num10, 32) ^ num25;
                        num13 = ROTR(num24 - m_nKeyExpandBox[2 * (IV - num15) + 2], num25, 32) ^ num10;
                    }

                    goto case 4;
                case 4:
                    num18 = num21 - m_nKeyExpandBox[1];
                    num17 = num20 - m_nKeyExpandBox[0];
                    num16 = 0;
                    goto case 5;
                case 5:
                case 8:
                case 12:
                    for (; num16 < 4; num16++)
                    {
                        array[2 * num16] = (byte)((num13 >> (8 * num16)) & 0xFFu);
                        array[2 * num16 + 8] = (byte)((num17 >> (8 * num16)) & 0xFFu);
                        array[2 * num16 + 16] = (byte)((num11 >> (8 * num16)) & 0xFFu);
                        array[2 * num16 + 24] = (byte)((num18 >> (8 * num16)) & 0xFFu);
                    }

                    break;
                default:
                    continue;
                case 13:
                    break;
                    IL_02a4:
                    string_1 = text;
                    KEY = string_2;
                    method_1();
                    num21 = 0u;
                    num12 = 0u;
                    num20 = 0u;
                    num14 = 0u;
                    array2 = Enc_default.GetBytes(string_1);
                    array = new byte[array2.Length];
                    num19 = 0;
                    goto case 0;
            }

            break;
        }

        var array3 = new char[Enc_default.GetCharCount(array, 0, array.Length)];
        Enc_default.GetChars(array, 0, array.Length, array3, 0);
        m_sCryptedText = new string(array3, 0, array3.Length);
        Enc_default.GetBytes(m_sCryptedText);
        return m_sCryptedText;
    }
}