namespace RxjhServer;

public class ItmeClass
{
    public int FLD_QUESTITEM { get; set; }

    public int FLD_PID { get; set; }

    public int FLD_RESIDE1 { get; set; }

    public int FLD_RESIDE2 { get; set; }

    public int FLD_SEX { get; set; }

    public int FLD_DF { get; set; }

    public int FLD_LEVEL { get; set; }

    public int FLD_JOB_LEVEL { get; set; }

    public int FLD_ZX { get; set; }

    public int FLD_AT { get; set; }

    public int FLD_AT_Max { get; set; }

    public int FLD_RECYCLE_MONEY { get; set; }

    public int FLD_SALE_MONEY { get; set; }

    public int FLD_SIDE { get; set; }

    public int FLD_TYPE { get; set; }

    public int FLD_WEIGHT { get; set; }

    public string ItmeNAME { get; set; }

    public int FLD_MAGIC0 { get; set; }

    public int FLD_MAGIC1 { get; set; }

    public int FLD_MAGIC2 { get; set; }

    public int FLD_MAGIC3 { get; set; }

    public int FLD_MAGIC4 { get; set; }

    public int FLD_NEED_MONEY { get; set; }

    public int FLD_NEED_FIGHTEXP { get; set; }

    public int FLD_INTEGRATION { get; set; }

    public int FLD_SERIES { get; set; }

    public int CoMoThongBao { get; internal set; }

    public int FLD_UP_LEVEL { get; set; }

    public int FLD_LOCK { get; set; }

    public int FLD_XW { get; set; }

    public int FLD_XWVoHoang { get; set; }

    public int FLD_XWJD { get; set; }

    public int FLD_NJ { get; set; }

    public int FLD_Chan { get; set; }

    public int FLD_BS { get; set; }

    public int FLD_PetNameType { get; set; }

    public string FLD_ITEMGROUP { get; set; }

    public int FLD_PetStage { get; set; }

    public static ItmeClass GetItme(string string_1)
    {
        using (var enumerator = World.Itme.Values.GetEnumerator())
        {
            ItmeClass itmeClass = null;
            while (enumerator.MoveNext())
            {
                itmeClass = enumerator.Current;
                if (itmeClass.ItmeNAME == string_1) return itmeClass;
            }
        }

        return null;
    }

    public int getOffsetItemChan()
    {
        return FLD_RESIDE1 switch
        {
            1 => 1,
            2 => 2,
            3 => 3,
            4 => 4,
            5 => 5,
            6 => 7,
            7 => 8,
            8 => -1,
            9 => -2,
            10 => 9,
            11 => -4,
            12 => -3,
            13 => -5,
            _ => 0
        };
    }

    public bool isTayChan()
    {
        if (FLD_PID == 501016 || FLD_PID == 501018 || FLD_PID == 501020 || FLD_PID == 501916 || FLD_PID == 502016 ||
            FLD_PID == 502018 || FLD_PID == 502020 || FLD_PID == 502916 || FLD_PID == 506016 || FLD_PID == 506018 ||
            FLD_PID == 507016 || FLD_PID == 507018) return true;
        if (FLD_PID == 500116 || FLD_PID == 500118 || FLD_PID == 500120 || FLD_PID == 502116 || FLD_PID == 502118 ||
            FLD_PID == 502120 || FLD_PID == 506016 || FLD_PID == 506018 || FLD_PID == 507016 ||
            FLD_PID == 507018) return true;
        var offset = getOffsetItemChan();
        if (offset > 0)
        {
            if (FLD_PID == offset * 100000000 + 503032 || FLD_PID == offset * 100000000 + 503034 ||
                FLD_PID == offset * 100000000 + 503036) return true;
        }
        else if (offset < 0)
        {
            offset *= -1;
            if (FLD_PID == offset * 100000000 + 503072 || FLD_PID == offset * 100000000 + 503074 ||
                FLD_PID == offset * 100000000 + 503076) return true;
        }

        return false;
    }

    public bool isChanChan()
    {
        if (FLD_PID == 801017 || FLD_PID == 801019 || FLD_PID == 801021 || FLD_PID == 801917 || FLD_PID == 802017 ||
            FLD_PID == 802019 || FLD_PID == 802021 || FLD_PID == 802917 || FLD_PID == 806017 || FLD_PID == 806019 ||
            FLD_PID == 807017 || FLD_PID == 807019) return true;
        if (FLD_PID == 800117 || FLD_PID == 800119 || FLD_PID == 800121 || FLD_PID == 802117 || FLD_PID == 802119 ||
            FLD_PID == 802121 || FLD_PID == 806017 || FLD_PID == 806019 || FLD_PID == 807017 ||
            FLD_PID == 807019) return true;
        var offset = getOffsetItemChan();
        if (offset > 0)
        {
            if (FLD_PID == offset * 100000000 + 803032 || FLD_PID == offset * 100000000 + 803034 ||
                FLD_PID == offset * 100000000 + 803036) return true;
        }
        else if (offset < 0)
        {
            offset *= -1;
            if (FLD_PID == offset * 100000000 + 803072 || FLD_PID == offset * 100000000 + 803074 ||
                FLD_PID == offset * 100000000 + 803076) return true;
        }

        return false;
    }

    public bool isGiapChan()
    {
        if (FLD_PID == 501016 || FLD_PID == 501018 || FLD_PID == 501020 || FLD_PID == 501916 || FLD_PID == 502016 ||
            FLD_PID == 502018 || FLD_PID == 502020 || FLD_PID == 502916 || FLD_PID == 506016 || FLD_PID == 506018 ||
            FLD_PID == 507016 || FLD_PID == 507018) return true;
        if (FLD_PID == 500116 || FLD_PID == 500118 || FLD_PID == 500120 || FLD_PID == 502116 || FLD_PID == 502118 ||
            FLD_PID == 502120 || FLD_PID == 506016 || FLD_PID == 506018 || FLD_PID == 507016 ||
            FLD_PID == 507018) return true;
        var offset = getOffsetItemChan();
        if (offset > 0)
        {
            if (FLD_PID == offset * 100000000 + 403033 || FLD_PID == offset * 100000000 + 403035 ||
                FLD_PID == offset * 100000000 + 403037) return true;
        }
        else if (offset < 0)
        {
            offset *= -1;
            if (FLD_PID == offset * 100000000 + 403073 || FLD_PID == offset * 100000000 + 403075 ||
                FLD_PID == offset * 100000000 + 403077) return true;
        }

        return false;
    }

    public bool isAoChan()
    {
        var offset = getOffsetItemChan();
        if (offset > 0)
        {
            if (FLD_PID == offset * 100000000 + 10301015 || FLD_PID == offset * 100000000 + 10301020 ||
                FLD_PID == offset * 100000000 + 10301023 || FLD_PID == offset * 100000000 + 10302015 ||
                FLD_PID == offset * 100000000 + 10302020 || FLD_PID == offset * 100000000 + 10302023 ||
                FLD_PID == offset * 100000000 + 20301015 || FLD_PID == offset * 100000000 + 20301020 ||
                FLD_PID == offset * 100000000 + 20301023 || FLD_PID == offset * 100000000 + 20302015 ||
                FLD_PID == offset * 100000000 + 20302020 || FLD_PID == offset * 100000000 + 20302023 ||
                FLD_PID == offset * 100000000 + 303032 || FLD_PID == offset * 100000000 + 303034 ||
                FLD_PID == offset * 100000000 + 303036) return true;
        }
        else if (offset < 0)
        {
            offset *= -1;
            if (FLD_PID == offset * 100000000 + 10304015 || FLD_PID == offset * 100000000 + 10304020 ||
                FLD_PID == offset * 100000000 + 10304023 || FLD_PID == offset * 100000000 + 20304015 ||
                FLD_PID == offset * 100000000 + 20304020 || FLD_PID == offset * 100000000 + 20304023 ||
                FLD_PID == offset * 100000000 + 303072 || FLD_PID == offset * 100000000 + 303074 ||
                FLD_PID == offset * 100000000 + 303076) return true;
        }

        return false;
    }

    public bool isVKChan()
    {
        var offset = getOffsetItemChan();
        if (offset > 0)
        {
            if (FLD_PID == offset * 100000000 + 201251 || FLD_PID == offset * 100000000 + 201254 ||
                FLD_PID == offset * 100000000 + 201262 || FLD_PID == offset * 100000000 + 202261 ||
                FLD_PID == offset * 100000000 + 202264 || FLD_PID == offset * 100000000 + 202272 ||
                FLD_PID == offset * 100000000 + 200321 || FLD_PID == offset * 100000000 + 200323 ||
                FLD_PID == offset * 100000000 + 200326) return true;
        }
        else if (offset < 0)
        {
            offset *= -1;
            if (FLD_PID == offset * 100000000 + 204010 || FLD_PID == offset * 100000000 + 204022 ||
                FLD_PID == offset * 100000000 + 204025 || FLD_PID == offset * 100000000 + 204027 ||
                FLD_PID == offset * 100000000 + 204032 || FLD_PID == offset * 100000000 + 204034 ||
                FLD_PID == offset * 100000000 + 200361 || FLD_PID == offset * 100000000 + 200363 ||
                FLD_PID == offset * 100000000 + 200366) return true;
        }

        return false;
    }

    public static string DatDuocVatPhamTen_XungHao2(int int_30)
    {
        if (World.Itme.TryGetValue(int_30, out var value)) return value.ItmeNAME = FontVietName.Convert(value.ItmeNAME);
        return string.Empty;
    }

    public static string DatDuocVatPhamTen_XungHao(int int_30)
    {
        if (World.Itme.TryGetValue(int_30, out var value)) return value.ItmeNAME;
        return string.Empty;
    }

    public static ItmeClass GetItmeID(int int_30)
    {
        if (World.Itme.TryGetValue(int_30, out var value)) return value;
        return null;
    }

    public static int Get_OrbLevel(long ItemID)
    {
        if (ItemID == 1000001390 || ItemID == 1000001391 || ItemID == 1000001392 || ItemID == 1000001393 ||
            ItemID == 1000001394 || ItemID == 1000001410 || ItemID == 1000001414 || ItemID == 1000001415 ||
            ItemID == 1000001416 || ItemID == 1000001417) return 1;
        if (ItemID == 1000001395 || ItemID == 1000001396 || ItemID == 1000001397 || ItemID == 1000001398 ||
            ItemID == 1000001399 || ItemID == 1000001411 || ItemID == 1000001418 || ItemID == 1000001419 ||
            ItemID == 1000001420 || ItemID == 1000001421) return 2;
        if (ItemID == 1000001400 || ItemID == 1000001401 || ItemID == 1000001402 || ItemID == 1000001403 ||
            ItemID == 1000001404 || ItemID == 1000001412 || ItemID == 1000001422 || ItemID == 1000001423 ||
            ItemID == 1000001424 || ItemID == 1000001425) return 3;
        if (ItemID == 1000001405 || ItemID == 1000001406 || ItemID == 1000001407 || ItemID == 1000001408 ||
            ItemID == 1000001409 || ItemID == 1000001413 || ItemID == 1000001426 || ItemID == 1000001427 ||
            ItemID == 1000001428 || ItemID == 1000001429) return 4;
        if (ItemID == 1000001430 || ItemID == 1000001431 || ItemID == 1000001432 || ItemID == 1000001433 ||
            ItemID == 1000001434) return 5;
        return 0;
    }

    public static bool ItemTayLuyenThanChau(long ItemID)
    {
        if (World.ItemTayLuyenThanChau1 != 0 && ItemID == World.ItemTayLuyenThanChau1) return true;
        if (World.ItemTayLuyenThanChau2 != 0 && ItemID == World.ItemTayLuyenThanChau2) return true;
        if (World.ItemTayLuyenThanChau3 != 0 && ItemID == World.ItemTayLuyenThanChau3) return true;
        if (World.ItemTayLuyenThanChau4 != 0 && ItemID == World.ItemTayLuyenThanChau4) return true;
        return false;
    }
}