using System;
using System.IO;

namespace RxjhServer;

public class logo
{
    public static void shopTxtLog(string string_0)
    {
        try
        {
            var num2 = 4;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 2;
                Directory.CreateDirectory("logs");
                num2 = 3;
            }

            streamWriter = new StreamWriter(new FileStream(
                "logs\\NPCShop_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write,
                FileShare.Read));
            num2 = 0;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 2;
                while (true)
                {
                    switch (num2)
                    {
                        case 0:
                            break;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 1;
                                continue;
                            }

                            break;
                        case 1:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 0;
                            continue;
                    }

                    break;
                }
            }

            num2 = 1;
        }
        catch
        {
        }
    }

    public static void checkitem(string Txt)
    {
        try
        {
            if (!Directory.Exists("Checkitem")) Directory.CreateDirectory("Checkitem");
            using var writer = new StreamWriter(new FileStream(
                "Checkitem\\LogCheckItem" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append,
                FileAccess.Write, FileShare.Read));
            writer.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
        }
        catch
        {
        }
    }

    public static void log_AddForceWarRewards(string Txt)
    {
        try
        {
            if (!Directory.Exists("LogAutoAddRewards")) Directory.CreateDirectory("LogAutoAddRewards");
            using var writer = new StreamWriter(new FileStream(
                "LogAutoAddRewards\\forcewar_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append,
                FileAccess.Write, FileShare.Read));
            writer.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
        }
        catch
        {
        }
    }

    public static void LogGuiDiTuVongSoLieu(string txt)
    {
        try
        {
            if (!Directory.Exists("LogError")) Directory.CreateDirectory("LogError");
            using var writer = new StreamWriter(new FileStream(
                "LogError\\log_TuVongSoLieu_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append,
                FileAccess.Write, FileShare.Read));
            writer.Write(string.Concat(DateTime.Now, " ", txt, "\r\n"));
        }
        catch
        {
        }
    }

    public static void LogNPC_Recover(string txt)
    {
        try
        {
            if (!Directory.Exists("logs")) Directory.CreateDirectory("logs");
            using var writer = new StreamWriter(new FileStream(
                "logs\\log_NPC_Recover_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append,
                FileAccess.Write, FileShare.Read));
            writer.Write(string.Concat(DateTime.Now, " ", txt, "\r\n"));
        }
        catch
        {
        }
    }

    public static void LogSellItemByCommand(string txt)
    {
        try
        {
            if (!Directory.Exists("logs")) Directory.CreateDirectory("logs");
            using var writer = new StreamWriter(new FileStream(
                "logs\\LogSellItemByCommand_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append,
                FileAccess.Write, FileShare.Read));
            writer.Write(string.Concat(DateTime.Now, " ", txt, "\r\n"));
        }
        catch
        {
        }
    }

    public static void LogPlayerMove(string txt)
    {
        try
        {
            if (!Directory.Exists("LogError")) Directory.CreateDirectory("LogError");
            using var writer = new StreamWriter(new FileStream(
                "LogError\\log_LogPlayerMove_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append,
                FileAccess.Write, FileShare.Read));
            writer.Write(string.Concat(DateTime.Now, " ", txt, "\r\n"));
        }
        catch
        {
        }
    }

    public static void logchangeskill(string Txt)
    {
        try
        {
            if (!Directory.Exists("LogBugGame")) Directory.CreateDirectory("LogBugGame");
            using var writer = new StreamWriter(new FileStream(
                "LogBugGame\\log_ChangeSkill_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append,
                FileAccess.Write, FileShare.Read));
            writer.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
        }
        catch
        {
        }
    }

    public static void kickid(string string_0)
    {
        try
        {
            var num2 = 4;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 2;
                Directory.CreateDirectory("logs");
                num2 = 3;
            }

            streamWriter = new StreamWriter(new FileStream(
                "logs\\KickID_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write,
                FileShare.Read));
            num2 = 0;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 2;
                while (true)
                {
                    switch (num2)
                    {
                        case 0:
                            break;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 1;
                                continue;
                            }

                            break;
                        case 1:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 0;
                            continue;
                    }

                    break;
                }
            }

            num2 = 1;
        }
        catch
        {
        }
    }

    public static void UseItemLog(string string_0)
    {
        try
        {
            var num2 = 3;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 1;
                Directory.CreateDirectory("logs");
                num2 = 2;
            }

            streamWriter = new StreamWriter(new FileStream(
                "logs\\VatPhamSuDung_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append,
                FileAccess.Write, FileShare.Read));
            num2 = 0;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 1;
                while (true)
                {
                    switch (num2)
                    {
                        case 2:
                            break;
                        case 0:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 2;
                            continue;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            num2 = 4;
        }
        catch
        {
        }
    }

    public static void gmtools11(string Txt, int Type)
    {
        try
        {
            if (Type != 321)
            {
                if (!Directory.Exists("log_GM")) Directory.CreateDirectory("log_GM");
                using var writer = new StreamWriter(new FileStream(
                    "log_GM\\GM_TOOLS_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append,
                    FileAccess.Write, FileShare.Read));
                writer.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
            }
        }
        catch
        {
        }
    }

    public static void GM_LogDis(int Txt, int Type)
    {
        try
        {
            if (Type != 321)
            {
                if (!Directory.Exists("log_GM")) Directory.CreateDirectory("log_GM");
                using var writer = new StreamWriter(new FileStream(
                    "log_GM\\GM_LogDis _" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append,
                    FileAccess.Write, FileShare.Read));
                writer.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
            }
        }
        catch
        {
        }
    }

    public static void Gm_LogPet(string Txt, int Type)
    {
        try
        {
            if (Type != 321)
            {
                if (!Directory.Exists("log_GM")) Directory.CreateDirectory("log_GM");
                using var writer = new StreamWriter(new FileStream(
                    "log_GM\\GM_ThanThu __ " + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append,
                    FileAccess.Write, FileShare.Read));
                writer.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
            }
        }
        catch
        {
        }
    }

    public static void Gm_LogHackSpeed(string Txt, int Type)
    {
        try
        {
            if (Type != 321)
            {
                if (!Directory.Exists("log_GM")) Directory.CreateDirectory("log_GM");
                using var writer = new StreamWriter(new FileStream(
                    "log_GM\\GM_LogHackSpeed __ " + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append,
                    FileAccess.Write, FileShare.Read));
                writer.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
            }
        }
        catch
        {
        }
    }

    public static void HCItemLog(string string_0)
    {
        try
        {
            var num2 = 3;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 1;
                Directory.CreateDirectory("logs");
                num2 = 2;
            }

            streamWriter = new StreamWriter(new FileStream(
                "logs\\HopThanhVatPham_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append,
                FileAccess.Write, FileShare.Read));
            num2 = 0;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 1;
                while (true)
                {
                    switch (num2)
                    {
                        case 2:
                            break;
                        case 0:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 2;
                            continue;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            num2 = 4;
        }
        catch
        {
        }
    }

    public static void WGTxtLog(string string_0)
    {
        try
        {
            var num2 = 3;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 1;
                Directory.CreateDirectory("logs");
                num2 = 2;
            }

            streamWriter = new StreamWriter(new FileStream(
                "logs\\KiemTraDoLuong_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append,
                FileAccess.Write, FileShare.Read));
            num2 = 0;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 1;
                while (true)
                {
                    switch (num2)
                    {
                        case 2:
                            break;
                        case 0:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 2;
                            continue;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            num2 = 4;
        }
        catch
        {
        }
    }

    public static void WritePacketLog(string ErrTxt)
    {
        try
        {
            if (!Directory.Exists("packet")) Directory.CreateDirectory("packet");
            using var writer = new StreamWriter(new FileStream(
                "packet\\PACKET_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write,
                FileShare.Read));
            writer.Write(string.Concat(DateTime.Now, " ", ErrTxt, "\r\n"));
        }
        catch
        {
        }
    }

    public static void FileTxtLog(string string_0)
    {
        try
        {
            var num2 = 2;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 4;
                Directory.CreateDirectory("logs");
                num2 = 3;
            }

            streamWriter = new StreamWriter(new FileStream(
                "logs\\ErrorLog_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write,
                FileShare.Read));
            num2 = 0;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 2;
                while (true)
                {
                    switch (num2)
                    {
                        case 0:
                            break;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 1;
                                continue;
                            }

                            break;
                        case 1:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 0;
                            continue;
                    }

                    break;
                }
            }

            num2 = 1;
        }
        catch
        {
        }
    }

    public static void RegLog(string string_0)
    {
        try
        {
            var num2 = 2;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 4;
                Directory.CreateDirectory("logs");
                num2 = 3;
            }

            streamWriter =
                new StreamWriter(new FileStream("logs\\硬件ID.txt", FileMode.Append, FileAccess.Write, FileShare.Read));
            num2 = 0;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 2;
                while (true)
                {
                    switch (num2)
                    {
                        case 0:
                            break;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 1;
                                continue;
                            }

                            break;
                        case 1:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 0;
                            continue;
                    }

                    break;
                }
            }

            num2 = 1;
        }
        catch
        {
        }
    }

    public static void pzTxtLog(string string_0)
    {
        try
        {
            var num2 = 2;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 4;
                Directory.CreateDirectory("logs");
                num2 = 3;
            }

            streamWriter = new StreamWriter(new FileStream(
                "logs\\BangChien_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write,
                FileShare.Read));
            num2 = 0;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 2;
                while (true)
                {
                    switch (num2)
                    {
                        case 0:
                            break;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 1;
                                continue;
                            }

                            break;
                        case 1:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 0;
                            continue;
                    }

                    break;
                }
            }

            num2 = 1;
        }
        catch
        {
        }
    }

    public static void cfzTxtLog(string string_0)
    {
        try
        {
            var num2 = 4;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 3;
                Directory.CreateDirectory("logs");
                num2 = 0;
            }

            streamWriter = new StreamWriter(new FileStream(
                "logs\\PhiPhapVatPham_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append,
                FileAccess.Write, FileShare.Read));
            num2 = 2;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 2;
                while (true)
                {
                    switch (num2)
                    {
                        case 1:
                            break;
                        case 0:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 1;
                            continue;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            num2 = 1;
        }
        catch
        {
        }
    }

    public static void zhtfTxtLog(string string_0)
    {
        try
        {
            var num2 = 4;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 3;
                Directory.CreateDirectory("logs");
                num2 = 0;
            }

            streamWriter = new StreamWriter(new FileStream(
                "logs\\DeleteChar_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write,
                FileShare.Read));
            num2 = 2;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 2;
                while (true)
                {
                    switch (num2)
                    {
                        case 1:
                            break;
                        case 0:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 1;
                            continue;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            num2 = 1;
        }
        catch
        {
        }
    }

    public static void FileCQTxtLog(string string_0)
    {
        try
        {
            var num2 = 4;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 3;
                Directory.CreateDirectory("logs");
                num2 = 0;
            }

            streamWriter = new StreamWriter(new FileStream(
                "logs\\CQLog_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write,
                FileShare.Read));
            num2 = 2;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 2;
                while (true)
                {
                    switch (num2)
                    {
                        case 1:
                            break;
                        case 0:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 1;
                            continue;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 0;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            num2 = 1;
        }
        catch
        {
        }
    }

    public static void FileLoninTxtLog(string string_0)
    {
        try
        {
            if (!Directory.Exists("logs")) Directory.CreateDirectory("logs");
            using var streamWriter = new StreamWriter(new FileStream(
                "logs\\Login_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write,
                FileShare.Read));
            streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
        }
        catch
        {
        }
    }

    public static void FileDropItmeTxtLog(string string_0)
    {
        try
        {
            if (!Directory.Exists("logs")) Directory.CreateDirectory("logs");
            using var streamWriter = new StreamWriter(new FileStream(
                "logs\\DropItme_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write,
                FileShare.Read));
            streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
        }
        catch
        {
        }
    }

    public static void BossLog(string string_0)
    {
        try
        {
            if (!Directory.Exists("logs")) Directory.CreateDirectory("logs");
            using var streamWriter = new StreamWriter(new FileStream(
                "logs\\BossSystem_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write,
                FileShare.Read));
            streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
        }
        catch
        {
        }
    }

    public static void FileItmeTxtLog(string string_0)
    {
        try
        {
            var num2 = 3;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 1;
                Directory.CreateDirectory("logs");
                num2 = 0;
            }

            streamWriter = new StreamWriter(new FileStream(
                "logs\\ItmeLog_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write,
                FileShare.Read));
            num2 = 4;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 1;
                while (true)
                {
                    switch (num2)
                    {
                        case 0:
                            break;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 2;
                                continue;
                            }

                            break;
                        case 2:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 0;
                            continue;
                    }

                    break;
                }
            }

            num2 = 2;
        }
        catch
        {
        }
    }

    public static void FileBugTxtLog(string string_0)
    {
        try
        {
            var num2 = 2;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 1;
                Directory.CreateDirectory("logs");
                num2 = 0;
            }

            streamWriter = new StreamWriter(new FileStream(
                "logs\\Loi_thoat_nhom_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append,
                FileAccess.Write, FileShare.Read));
            num2 = 3;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 0;
                while (true)
                {
                    switch (num2)
                    {
                        case 2:
                            break;
                        case 1:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 2;
                            continue;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 1;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            num2 = 4;
        }
        catch
        {
        }
    }

    public static void FilePakTxtLog(string string_0)
    {
        try
        {
            var num2 = 2;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 1;
                Directory.CreateDirectory("logs");
                num2 = 0;
            }

            streamWriter = new StreamWriter(new FileStream(
                "logs\\FilePakTxtLog_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append,
                FileAccess.Write, FileShare.Read));
            num2 = 3;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 0;
                while (true)
                {
                    switch (num2)
                    {
                        case 2:
                            break;
                        case 1:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 2;
                            continue;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 1;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            num2 = 4;
        }
        catch
        {
        }
    }

    public static void SeveTxtLog(string string_0)
    {
        try
        {
            var num2 = 2;
            StreamWriter streamWriter = null;
            if (!Directory.Exists("logs"))
            {
                num2 = 1;
                Directory.CreateDirectory("logs");
                num2 = 0;
            }

            streamWriter = new StreamWriter(new FileStream("logs\\Save_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt",
                FileMode.Append, FileAccess.Write, FileShare.Read));
            num2 = 3;
            try
            {
                streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
            }
            finally
            {
                num2 = 0;
                while (true)
                {
                    switch (num2)
                    {
                        case 2:
                            break;
                        case 1:
                            ((IDisposable)streamWriter).Dispose();
                            num2 = 2;
                            continue;
                        default:
                            if (streamWriter != null)
                            {
                                num2 = 1;
                                continue;
                            }

                            break;
                    }

                    break;
                }
            }

            num2 = 4;
        }
        catch
        {
        }
    }

    public static void MsGLog(string string_0)
    {
    }

    public static void gmtools(string Txt, int Type)
    {
        try
        {
            if (!Directory.Exists("log_GM")) Directory.CreateDirectory("log_GM");
            using var writer = new StreamWriter(new FileStream(
                "log_GM\\GM_TOOLS_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write,
                FileShare.Read));
            writer.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
        }
        catch
        {
        }
    }

    public static void logGD_Gold(string Txt)
    {
        try
        {
            if (!Directory.Exists("logs")) Directory.CreateDirectory("logs");
            using var writer = new StreamWriter(new FileStream(
                "logs\\logGD_Gold" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write,
                FileShare.Read));
            writer.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
        }
        catch
        {
        }
    }

    public static void errorLog(string Txt)
    {
        try
        {
            if (!Directory.Exists("log_GM")) Directory.CreateDirectory("log_GM");
            using var writer = new StreamWriter(new FileStream(
                "log_GM\\ErrorLog_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write,
                FileShare.Read));
            writer.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
        }
        catch
        {
        }
    }
}