using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer.DbClss;

namespace RxjhServer;

public class Form3 : Form
{
    private Button button1;

    private Button button2;

    private Button button43;

    private ColumnHeader columnHeader11;

    private ColumnHeader columnHeader12;

    private ColumnHeader columnHeader13;

    private ColumnHeader columnHeader14;

    private ColumnHeader columnHeader15;

    private ColumnHeader columnHeader16;

    private ColumnHeader columnHeader17;

    private ComboBox comboBox11;

    private ComboBox comboBox12;
    private IContainer components;

    private ContextMenuStrip contextMenuStrip1;

    private Label label1;

    private Label label14;

    private Label label2;

    private Label label3;

    private Label label4;

    private Label label40;

    private Label label5;

    private Label label6;

    private Label label7;

    private ListBox listBox3;

    private ListView listView2;

    private Panel panel1;

    private TextBox textBox1;

    private TextBox textBox2;

    private TextBox textBox3;

    private TextBox textBox4;

    private TextBox textBox5;

    private TextBox textBox6;

    private ToolStripMenuItem 删除ToolStripMenuItem;

    private ToolStripMenuItem 编辑ToolStripMenuItem;

    public Form3()
    {
        InitializeComponent();
        listView2.ContextMenuStrip = contextMenuStrip1;
        contextMenuStrip1.Closed += contextMenuStrip1_Closed;
        button1.Enabled = false;
        button2.Enabled = false;
    }

    public string s_id { get; set; }

    private void contextMenuStrip1_Closed(object sender, ToolStripDropDownClosedEventArgs e)
    {
        listView2.ContextMenuStrip = contextMenuStrip1;
    }

    private void button43_Click(object sender, EventArgs e)
    {
        刷新();
    }

    private void button1_Click(object sender, EventArgs e)
    {
        string text = null;
        string text2 = null;
        string text3 = null;
        string text4 = null;
        string text5 = null;
        string text6 = null;
        var flag = true;
        var flag2 = true;
        while (true)
        {
            if (!string.IsNullOrEmpty(s_id)) goto IL_0032;
            IL_0318:
            while (true)
            {
                IL_0318_2:
                text = textBox1.Text;
                text2 = textBox2.Text;
                text3 = textBox3.Text;
                text4 = textBox4.Text;
                text5 = textBox5.Text;
                text6 = textBox6.Text;
                var text7 = "";
                while (true)
                {
                    string text8;
                    ListView.SelectedIndexCollection selectedIndices;
                    switch (flag ? 5 : 3)
                    {
                        case 0:
                            break;
                        case 2:
                            goto IL_0032;
                        default:
                            flag = true;
                            break;
                        case 3:
                            text8 = string.Format(
                                "UPDATE tbl_xwwl_open SET FLD_pid='{1}',FLD_NAME='{2}', FLD_pidx='{3}',FLD_namex='{4}',FLD_number='{5}',FLD_pp='{6}'WHERE FLD_index='{0}'",
                                s_id, text, text2, text3, text4, text5, text6);
                            goto IL_0184;
                        case 5:
                        case 6:
                            text8 =
                                "INSERT INTO tbl_xwwl_open  (FLD_pid,FLD_NAME, FLD_pidx,FLD_namex,FLD_number,FLD_pp)VALUES(" +
                                text + ",'" + text2 + "'," + text3 + ",'" + text4 + "'," + text5 + "," + text6 + ")";
                            goto IL_0184;
                        case 1:
                            continue;
                        case 4:
                            goto IL_0318_2;
                            IL_0184:
                            text7 = text8;
                            DBA.ExeSqlCommand(text7, "PublicDb");
                            selectedIndices = listView2.SelectedIndices;
                            s_id = listView2.Items[selectedIndices[0]].SubItems[0].Text;
                            listView2.Items[selectedIndices[0]].SubItems[1].Text = textBox1.Text;
                            listView2.Items[selectedIndices[0]].SubItems[2].Text = textBox2.Text;
                            listView2.Items[selectedIndices[0]].SubItems[3].Text = textBox3.Text;
                            listView2.Items[selectedIndices[0]].SubItems[4].Text = textBox4.Text;
                            listView2.Items[selectedIndices[0]].SubItems[5].Text = textBox5.Text;
                            listView2.Items[selectedIndices[0]].SubItems[6].Text = textBox6.Text;
                            return;
                    }

                    break;
                }

                break;
            }

            continue;
            IL_0032:
            flag = false;
            goto IL_0318;
        }
    }

    private void listView2_MouseClick(object sender, MouseEventArgs e)
    {
        if (listView2.SelectedIndices != null && listView2.SelectedIndices.Count > 0)
        {
            var selectedIndices = listView2.SelectedIndices;
            s_id = listView2.Items[selectedIndices[0]].SubItems[0].Text;
            textBox1.Text = listView2.Items[selectedIndices[0]].SubItems[1].Text;
            textBox2.Text = listView2.Items[selectedIndices[0]].SubItems[2].Text;
            textBox3.Text = listView2.Items[selectedIndices[0]].SubItems[3].Text;
            textBox4.Text = listView2.Items[selectedIndices[0]].SubItems[4].Text;
            textBox5.Text = listView2.Items[selectedIndices[0]].SubItems[5].Text;
            textBox6.Text = listView2.Items[selectedIndices[0]].SubItems[6].Text;
            if (!button1.Enabled) button1.Enabled = true;
            if (!button2.Enabled) button2.Enabled = true;
        }
    }

    private void 编辑ToolStripMenuItem_Click(object sender, EventArgs e)
    {
        if (listView2.SelectedIndices != null && listView2.SelectedIndices.Count > 0)
        {
            var selectedIndices = listView2.SelectedIndices;
            s_id = listView2.Items[selectedIndices[0]].SubItems[0].Text;
            textBox1.Text = listView2.Items[selectedIndices[0]].SubItems[1].Text;
            textBox2.Text = listView2.Items[selectedIndices[0]].SubItems[2].Text;
            textBox3.Text = listView2.Items[selectedIndices[0]].SubItems[3].Text;
            textBox4.Text = listView2.Items[selectedIndices[0]].SubItems[4].Text;
            textBox5.Text = listView2.Items[selectedIndices[0]].SubItems[5].Text;
            textBox6.Text = listView2.Items[selectedIndices[0]].SubItems[6].Text;
        }
    }

    private void 删除ToolStripMenuItem_Click(object sender, EventArgs e)
    {
        if (listView2.SelectedIndices != null && listView2.SelectedIndices.Count > 0)
        {
            var selectedIndices = listView2.SelectedIndices;
            var text = listView2.Items[selectedIndices[0]].Text;
            var string_ = "delete from tbl_xwwl_open where FLD_index =" + text;
            DBA.ExeSqlCommand(string_, "PublicDb");
            刷新();
        }
    }

    public void 刷新()
    {
        listView2.Items.Clear();
        var string_ = "select * from tbl_xwwl_open";
        var dBToDataTable = DBA.GetDBToDataTable(string_, "PublicDb");
        for (var i = 0; i < dBToDataTable.Rows.Count; i++)
        {
            var listViewItem = new ListViewItem();
            listViewItem.SubItems.Clear();
            listViewItem.SubItems[0].Text = dBToDataTable.Rows[i]["FLD_index"].ToString();
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_pid"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_NAME"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_pidx"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_namex"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_number"].ToString());
            listViewItem.SubItems.Add(dBToDataTable.Rows[i]["FLD_pp"].ToString());
            listView2.Items.Add(listViewItem);
        }
    }

    internal static uint ComputeStringHash(string string_0)
    {
        var num = 0;
        var num2 = 0u;
        if (string_0 != null)
        {
            num2 = 2166136261u;
            for (num = 0; num < string_0.Length; num++) num2 = (string_0[num] ^ num2) * 16777619;
        }

        return num2;
    }

    private void comboBox11_SelectedIndexChanged(object sender, EventArgs e)
    {
        DataTable dataTable = null;
        var num2 = 0;
        var arg = "";
        var text = comboBox11.Text;
        switch (ComputeStringHash(text))
        {
            case 963180387u:
                if (text == "武器") arg = "4";
                break;
            case 867671693u:
                if (text == "衣服") arg = "1";
                break;
            case 477010303u:
                if (text == "门甲") arg = "14";
                break;
            case 1895148392u:
                if (text == "护手") arg = "2";
                break;
            case 1618293597u:
                if (text == "弓箭") arg = "13";
                break;
            case 1502406078u:
                if (text == "项链") arg = "7";
                break;
            case 3393768302u:
                if (text == "鞋子") arg = "5";
                break;
            case 3322318871u:
                if (text == "耳环") arg = "8";
                break;
            case 2913798006u:
                if (text == "戒指") arg = "10";
                break;
            case 4183737334u:
                if (text == "内甲") arg = "6";
                break;
            case 3578152463u:
                if (text == "宝宝") arg = "15";
                break;
            case 3558870252u:
                if (text == "披风") arg = "12";
                break;
        }

        listBox3.Items.Clear();
        comboBox12.Items.Clear();
        var string_ = "select * from TBL_XWWL_ITEM where FLD_RESIDE2='" + arg + "'";
        dataTable = DBA.GetDBToDataTable(string_, "PublicDb");
        for (num2 = 0; num2 < dataTable.Rows.Count; num2++)
        {
            listBox3.Items.Add(dataTable.Rows[num2]["FLD_NAME"].ToString());
            comboBox12.Items.Add(dataTable.Rows[num2]["FLD_PID"].ToString());
        }

        dataTable.Dispose();
    }

    private void listBox3_SelectedIndexChanged(object sender, EventArgs e)
    {
        comboBox12.SelectedIndex = listBox3.SelectedIndex;
        textBox3.Text = comboBox12.Text;
        textBox4.Text = listBox3.Text;
    }

    private void button2_Click(object sender, EventArgs e)
    {
        var text = textBox1.Text;
        var text2 = textBox2.Text;
        var text3 = textBox3.Text;
        var text4 = textBox4.Text;
        var text5 = textBox5.Text;
        var text6 = textBox6.Text;
        var text7 = "";
        text7 = "INSERT INTO tbl_xwwl_open  (FLD_pid,FLD_NAME, FLD_pidx,FLD_namex,FLD_number,FLD_pp)VALUES(" + text +
                ",'" + text2 + "'," + text3 + ",'" + text4 + "'," + text5 + "," + text6 + ")";
        DBA.ExeSqlCommand(text7, "PublicDb");
        刷新();
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        components = new Container();
        listView2 = new ListView();
        columnHeader11 = new ColumnHeader();
        columnHeader12 = new ColumnHeader();
        columnHeader13 = new ColumnHeader();
        columnHeader14 = new ColumnHeader();
        columnHeader15 = new ColumnHeader();
        columnHeader16 = new ColumnHeader();
        columnHeader17 = new ColumnHeader();
        button43 = new Button();
        panel1 = new Panel();
        button2 = new Button();
        button1 = new Button();
        textBox6 = new TextBox();
        label6 = new Label();
        textBox5 = new TextBox();
        label2 = new Label();
        textBox4 = new TextBox();
        label5 = new Label();
        textBox3 = new TextBox();
        label4 = new Label();
        textBox2 = new TextBox();
        label3 = new Label();
        textBox1 = new TextBox();
        label1 = new Label();
        contextMenuStrip1 = new ContextMenuStrip(components);
        编辑ToolStripMenuItem = new ToolStripMenuItem();
        删除ToolStripMenuItem = new ToolStripMenuItem();
        label7 = new Label();
        label14 = new Label();
        comboBox12 = new ComboBox();
        comboBox11 = new ComboBox();
        label40 = new Label();
        listBox3 = new ListBox();
        panel1.SuspendLayout();
        contextMenuStrip1.SuspendLayout();
        SuspendLayout();
        listView2.Columns.AddRange(new ColumnHeader[7]
        {
            columnHeader11, columnHeader12, columnHeader13, columnHeader14, columnHeader15, columnHeader16,
            columnHeader17
        });
        listView2.FullRowSelect = true;
        listView2.GridLines = true;
        listView2.Location = new Point(12, 12);
        listView2.Name = "listView2";
        listView2.Size = new Size(503, 342);
        listView2.TabIndex = 1;
        listView2.UseCompatibleStateImageBehavior = false;
        listView2.View = View.Details;
        listView2.MouseClick += listView2_MouseClick;
        columnHeader11.Text = "id";
        columnHeader11.Width = 40;
        columnHeader12.Text = "盒子PID";
        columnHeader12.Width = 84;
        columnHeader13.Text = "盒子名称";
        columnHeader13.Width = 72;
        columnHeader14.Text = "开出物品PID";
        columnHeader14.Width = 84;
        columnHeader15.Text = "开出物品名字";
        columnHeader15.Width = 102;
        columnHeader16.Text = "数量";
        columnHeader16.Width = 50;
        columnHeader17.Text = "开出几率";
        columnHeader17.Width = 67;
        button43.Location = new Point(363, 361);
        button43.Name = "button43";
        button43.Size = new Size(103, 23);
        button43.TabIndex = 3;
        button43.Text = "查看";
        button43.UseVisualStyleBackColor = true;
        button43.Click += button43_Click;
        panel1.Controls.Add(button2);
        panel1.Controls.Add(button1);
        panel1.Controls.Add(textBox6);
        panel1.Controls.Add(label6);
        panel1.Controls.Add(textBox5);
        panel1.Controls.Add(label2);
        panel1.Controls.Add(textBox4);
        panel1.Controls.Add(label5);
        panel1.Controls.Add(textBox3);
        panel1.Controls.Add(label4);
        panel1.Controls.Add(textBox2);
        panel1.Controls.Add(label3);
        panel1.Controls.Add(textBox1);
        panel1.Controls.Add(label1);
        panel1.Location = new Point(240, 398);
        panel1.Name = "panel1";
        panel1.Size = new Size(266, 233);
        panel1.TabIndex = 4;
        button2.Location = new Point(167, 187);
        button2.Name = "button2";
        button2.Size = new Size(75, 23);
        button2.TabIndex = 14;
        button2.Text = "增加";
        button2.UseVisualStyleBackColor = true;
        button2.Click += button2_Click;
        button1.Location = new Point(45, 187);
        button1.Name = "button1";
        button1.Size = new Size(75, 23);
        button1.TabIndex = 13;
        button1.Text = "保存修改";
        button1.UseVisualStyleBackColor = true;
        button1.Click += button1_Click;
        textBox6.Location = new Point(113, 155);
        textBox6.Name = "textBox6";
        textBox6.Size = new Size(129, 21);
        textBox6.TabIndex = 12;
        label6.AutoSize = true;
        label6.Location = new Point(54, 158);
        label6.Name = "label6";
        label6.Size = new Size(53, 12);
        label6.TabIndex = 11;
        label6.Text = "开出几率";
        textBox5.Location = new Point(113, 128);
        textBox5.Name = "textBox5";
        textBox5.Size = new Size(129, 21);
        textBox5.TabIndex = 10;
        label2.AutoSize = true;
        label2.Location = new Point(78, 131);
        label2.Name = "label2";
        label2.Size = new Size(29, 12);
        label2.TabIndex = 9;
        label2.Text = "数量";
        textBox4.Location = new Point(113, 101);
        textBox4.Name = "textBox4";
        textBox4.Size = new Size(129, 21);
        textBox4.TabIndex = 8;
        label5.AutoSize = true;
        label5.Location = new Point(30, 104);
        label5.Name = "label5";
        label5.Size = new Size(77, 12);
        label5.TabIndex = 7;
        label5.Text = "开出物品名称";
        textBox3.Location = new Point(113, 74);
        textBox3.Name = "textBox3";
        textBox3.Size = new Size(129, 21);
        textBox3.TabIndex = 6;
        label4.AutoSize = true;
        label4.Location = new Point(36, 77);
        label4.Name = "label4";
        label4.Size = new Size(71, 12);
        label4.TabIndex = 5;
        label4.Text = "开出物品PID";
        textBox2.Location = new Point(113, 47);
        textBox2.Name = "textBox2";
        textBox2.Size = new Size(129, 21);
        textBox2.TabIndex = 4;
        label3.AutoSize = true;
        label3.Location = new Point(54, 50);
        label3.Name = "label3";
        label3.Size = new Size(53, 12);
        label3.TabIndex = 3;
        label3.Text = "盒子名称";
        textBox1.Location = new Point(113, 20);
        textBox1.Name = "textBox1";
        textBox1.Size = new Size(129, 21);
        textBox1.TabIndex = 1;
        label1.AutoSize = true;
        label1.Location = new Point(60, 23);
        label1.Name = "label1";
        label1.Size = new Size(47, 12);
        label1.TabIndex = 0;
        label1.Text = "盒子PID";
        contextMenuStrip1.Items.AddRange(new ToolStripItem[2] { 编辑ToolStripMenuItem, 删除ToolStripMenuItem });
        contextMenuStrip1.Name = "contextMenuStrip1";
        contextMenuStrip1.Size = new Size(99, 48);
        编辑ToolStripMenuItem.Name = "编辑ToolStripMenuItem";
        编辑ToolStripMenuItem.Size = new Size(98, 22);
        编辑ToolStripMenuItem.Text = "编辑";
        编辑ToolStripMenuItem.Click += 编辑ToolStripMenuItem_Click;
        删除ToolStripMenuItem.Name = "删除ToolStripMenuItem";
        删除ToolStripMenuItem.Size = new Size(98, 22);
        删除ToolStripMenuItem.Text = "删除";
        删除ToolStripMenuItem.Click += 删除ToolStripMenuItem_Click;
        label7.Font = new Font("宋体", 12f, FontStyle.Bold, GraphicsUnit.Point, 134);
        label7.ForeColor = Color.Red;
        label7.Location = new Point(56, 361);
        label7.Name = "label7";
        label7.Size = new Size(222, 23);
        label7.TabIndex = 6;
        label7.Text = "修改后请在菜单中重新加载.";
        label14.AutoSize = true;
        label14.Location = new Point(8, 424);
        label14.Name = "label14";
        label14.Size = new Size(65, 12);
        label14.TabIndex = 42;
        label14.Text = "物品代码ID";
        comboBox12.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox12.FormattingEnabled = true;
        comboBox12.Location = new Point(79, 421);
        comboBox12.Name = "comboBox12";
        comboBox12.Size = new Size(141, 20);
        comboBox12.TabIndex = 41;
        comboBox11.DropDownStyle = ComboBoxStyle.DropDownList;
        comboBox11.FormattingEnabled = true;
        comboBox11.Items.AddRange(new object[13]
        {
            "衣服", "护手", "鞋子", "武器", "内甲", "耳环", "项链", "戒指", "披风", "门甲",
            "宝宝", "弓箭", "其他"
        });
        comboBox11.Location = new Point(79, 398);
        comboBox11.MaxDropDownItems = 20;
        comboBox11.Name = "comboBox11";
        comboBox11.Size = new Size(141, 20);
        comboBox11.TabIndex = 40;
        comboBox11.SelectedIndexChanged += comboBox11_SelectedIndexChanged;
        label40.AutoSize = true;
        label40.Location = new Point(20, 401);
        label40.Name = "label40";
        label40.Size = new Size(53, 12);
        label40.TabIndex = 39;
        label40.Text = "物品列表";
        listBox3.FormattingEnabled = true;
        listBox3.ItemHeight = 12;
        listBox3.Location = new Point(79, 445);
        listBox3.Name = "listBox3";
        listBox3.Size = new Size(141, 184);
        listBox3.TabIndex = 38;
        listBox3.SelectedIndexChanged += listBox3_SelectedIndexChanged;
        AutoScaleDimensions = new SizeF(6f, 12f);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(527, 650);
        Controls.Add(label14);
        Controls.Add(comboBox12);
        Controls.Add(comboBox11);
        Controls.Add(label40);
        Controls.Add(listBox3);
        Controls.Add(label7);
        Controls.Add(listView2);
        Controls.Add(panel1);
        Controls.Add(button43);
        MinimizeBox = false;
        Name = "Form3";
        Text = "盒子数据修改";
        panel1.ResumeLayout(false);
        panel1.PerformLayout();
        contextMenuStrip1.ResumeLayout(false);
        ResumeLayout(false);
        PerformLayout();
    }
}