using System;

namespace RxjhServer;

public class AccountOfflineClass
{
    public X_Vat_Pham_Loai[] Packet_PublicStore_Offline;

    public int ID { get; set; }

    public string UserID { get; set; }

    public int Online { get; set; }

    public byte[] Item_PublicStore_Offline { get; set; }

    public void Set_Packet_PublicStore_Offline()
    {
        if (Item_PublicStore_Offline == null) return;
        Packet_PublicStore_Offline = new X_Vat_Pham_Loai[66];
        var buffer_item = Item_PublicStore_Offline;
        for (var numIndex = 0; numIndex < 60; numIndex++)
        {
            var buffer22 = new byte[World.Item_Db_Byte_Length];
            if (buffer_item.Length >= numIndex * World.Item_Db_Byte_Length +
                World.Item_Db_Byte_Length)
            {
                try
                {
                    System.Buffer.BlockCopy(buffer_item, numIndex * World.Item_Db_Byte_Length, buffer22,
                        0, World.Item_Db_Byte_Length);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(numIndex + " " + ex);
                }

                Packet_PublicStore_Offline[numIndex] = new X_Vat_Pham_Loai(buffer22, numIndex);
                var buffer23 = new byte[4];
                System.Buffer.BlockCopy(Packet_PublicStore_Offline[numIndex].VatPham_byte, 56, buffer23, 0, 4);
            }
            else
            {
                Packet_PublicStore_Offline[numIndex] = new X_Vat_Pham_Loai(buffer22, numIndex);
            }
        }
    }
}