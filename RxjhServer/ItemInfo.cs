using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace RxjhServer;

public class ItemInfo : Form
{
    private Button btnDelete;

    private Button btnDong;

    private readonly IContainer components = null;

    private readonly int CuongHoa;

    private readonly int DangCap;

    private readonly int index;
    private int ItemID;

    private readonly string ItemName = "";

    private readonly int Khoa;

    private Label lblItem;

    private Label lblKhoa;

    private Label lblLevel;

    private Label lblOption1;

    private Label lblOption2;

    private Label lblOption3;

    private Label lblOption4;

    private Label lblThuocTinh;

    private Label lblTinhNgo;

    private Label lblTrungCap;

    private Label lblZx;

    private readonly int LoaiKho;

    private readonly int Option1Level;

    private readonly int Option1Type;

    private readonly int Option2Level;

    private readonly int Option2Type;

    private readonly int Option3Level;

    private readonly int Option3Type;

    private readonly int Option4Level;

    private readonly int Option4Type;

    private readonly int PhamChat;

    private readonly Players Player;

    private readonly int TheLuc;

    private readonly int ThuocTinh;

    private readonly int ThuocTinhLevel;

    private readonly int TinhNgo;

    private readonly int TrungCap;

    public ItemInfo(int ItemID, long ItemSeri, int Amount, string ItemName, int PhamChat, int CuongHoa, int DangCap,
        int TheLuc, int Gender, int Option1Type, int Option1Level, int Option2Type, int Option2Level, int Option3Type,
        int Option3Level, int Option4Type, int Option4Level, int ThuocTinh, int ThuocTinhLevel, int TinhNgo,
        int TrungCap, int Khoa, int LoaiKho, int index, Players Player)
    {
        InitializeComponent();
        this.ItemID = ItemID;
        this.ItemName = ItemName;
        this.PhamChat = PhamChat;
        this.CuongHoa = CuongHoa;
        this.DangCap = DangCap;
        this.TheLuc = TheLuc;
        this.Option1Type = Option1Type;
        this.Option1Level = Option1Level;
        this.Option2Type = Option2Type;
        this.Option2Level = Option2Level;
        this.Option3Type = Option3Type;
        this.Option3Level = Option3Level;
        this.Option4Type = Option4Type;
        this.Option4Level = Option4Level;
        this.ThuocTinh = ThuocTinh;
        this.ThuocTinhLevel = ThuocTinhLevel;
        this.TinhNgo = TinhNgo;
        this.TrungCap = TrungCap;
        this.Khoa = Khoa;
        this.LoaiKho = LoaiKho;
        this.Player = Player;
        this.index = index;
        HienThi();
    }

    private void HienThi()
    {
        HienThiTen();
        HienThiDangCap();
        HienThiTheLuc();
        HienThiOption1();
        HienThiOption2();
        HienThiOption3();
        HienThiOption4();
        HienThiThuocTinh();
        HienThiTinhNgo();
        HienThiTrungCap();
    }

    private void HienThiTen()
    {
        var TenTrangBi = ItemName;
        switch (PhamChat)
        {
            case 1:
                TenTrangBi += " (Kha\u0301 tốt) ";
                break;
            case 2:
                TenTrangBi += " (Cao cấp) ";
                break;
        }

        if (CuongHoa > 0) TenTrangBi = TenTrangBi + "[" + CuongHoa + "]";
        if (TrungCap > 0) lblItem.ForeColor = Color.Aqua;
        if (TrungCap == 0 && CuongHoa > 0) lblItem.ForeColor = Color.Blue;
        if (TrungCap == 0 && CuongHoa == 0) lblItem.ForeColor = Color.White;
        lblItem.Text = TenTrangBi;
    }

    private void HienThiDangCap()
    {
        lblLevel.Text = "Đă\u0309ng cấp:" + DangCap;
    }

    private void HienThiTheLuc()
    {
        var TheLuc = "Thê\u0301 lư\u0323c: ";
        switch (this.TheLuc)
        {
            case 0:
                TheLuc += "Không";
                break;
            case 1:
                TheLuc += "Chi\u0301nh pha\u0301i";
                break;
            case 2:
                TheLuc += "Ta\u0300 pha\u0301i";
                break;
        }

        lblZx.Text = TheLuc;
    }

    private void HienThiOption1()
    {
        var Option1 = "";
        switch (Option1Type)
        {
            case 0:
                lblOption1.Visible = false;
                break;
            case 1:
                Option1 = Option1 + "Sư\u0301c tâ\u0301n công " + Option1Level + " Tăng";
                break;
            case 2:
                Option1 = Option1 + "Sư\u0301c pho\u0300ng ngư\u0323 " + Option1Level + " Tăng";
                break;
            case 3:
                Option1 = Option1 + "Điê\u0309m Hp " + Option1Level + " Tăng";
                break;
            case 4:
                Option1 = Option1 + "Mp " + Option1Level + " Tăng";
                break;
            case 5:
                Option1 = Option1 + "Ti\u0309 lê\u0323 chi\u0301nh xa\u0301c " + Option1Level + " Tăng";
                break;
            case 6:
                Option1 = Option1 + "Ti\u0309 lê\u0323 tra\u0301nh ne\u0301 " + Option1Level + " Tăng";
                break;
            case 7:
                Option1 = Option1 + "Sư\u0301c tâ\u0301n công cu\u0309a vo\u0303 công " + Option1Level + " % Tăng";
                break;
            case 8:
                Option1 = Option1 + "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công " + Option1Level + " Tăng";
                break;
            case 9:
                Option1 = Option1 + "X.suâ\u0301t hơ\u0323p tha\u0300nh cươ\u0300ng ho\u0301a tha\u0300nh công " +
                          Option1Level + " % Tăng";
                break;
            case 10:
                Option1 = Option1 + "Thêm điê\u0309m đa\u0309 ki\u0301ch " + Option1Level + " Tăng";
                break;
            case 11:
                Option1 = Option1 + "Sư\u0301c pho\u0300ng ngư\u0323 cu\u0309a vo\u0303 công " + Option1Level + " Tăng";
                break;
            case 12:
                Option1 = Option1 + "X.suâ\u0301t nhâ\u0323n tiê\u0300n " + Option1Level + " % Tăng";
                break;
            case 13:
                Option1 = Option1 + "Điê\u0309m Exp tô\u0309n thâ\u0301t khi chê\u0301t " + Option1Level + " % Tăng";
                break;
        }

        lblOption1.Text = Option1;
    }

    private void HienThiOption2()
    {
        var Option2 = "";
        switch (Option2Type)
        {
            case 0:
                lblOption2.Visible = false;
                break;
            case 1:
                Option2 = Option2 + "Sư\u0301c tâ\u0301n công " + Option2Level + " Tăng";
                break;
            case 2:
                Option2 = Option2 + "Sư\u0301c pho\u0300ng ngư\u0323 " + Option2Level + " Tăng";
                break;
            case 3:
                Option2 = Option2 + "Điê\u0309m Hp " + Option2Level + " Tăng";
                break;
            case 4:
                Option2 = Option2 + "Mp " + Option2Level + " Tăng";
                break;
            case 5:
                Option2 = Option2 + "Ti\u0309 lê\u0323 chi\u0301nh xa\u0301c " + Option2Level + " Tăng";
                break;
            case 6:
                Option2 = Option2 + "Ti\u0309 lê\u0323 tra\u0301nh ne\u0301 " + Option2Level + " Tăng";
                break;
            case 7:
                Option2 = Option2 + "Sư\u0301c tâ\u0301n công cu\u0309a vo\u0303 công " + Option2Level + " % Tăng";
                break;
            case 8:
                Option2 = Option2 + "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công " + Option2Level + " Tăng";
                break;
            case 9:
                Option2 = Option2 + "X.suâ\u0301t hơ\u0323p tha\u0300nh cươ\u0300ng ho\u0301a tha\u0300nh công " +
                          Option2Level + " % Tăng";
                break;
            case 10:
                Option2 = Option2 + "Thêm điê\u0309m đa\u0309 ki\u0301ch " + Option2Level + " Tăng";
                break;
            case 11:
                Option2 = Option2 + "Sư\u0301c pho\u0300ng ngư\u0323 cu\u0309a vo\u0303 công " + Option2Level + " Tăng";
                break;
            case 12:
                Option2 = Option2 + "X.suâ\u0301t nhâ\u0323n tiê\u0300n " + Option2Level + " % Tăng";
                break;
            case 13:
                Option2 = Option2 + "Điê\u0309m Exp tô\u0309n thâ\u0301t khi chê\u0301t " + Option2Level + " % Tăng";
                break;
        }

        lblOption2.Text = Option2;
    }

    private void HienThiOption3()
    {
        var Option3 = "";
        switch (Option3Type)
        {
            case 0:
                lblOption3.Visible = false;
                break;
            case 1:
                Option3 = Option3 + "Sư\u0301c tâ\u0301n công " + Option3Level + " Tăng";
                break;
            case 2:
                Option3 = Option3 + "Sư\u0301c pho\u0300ng ngư\u0323 " + Option3Level + " Tăng";
                break;
            case 3:
                Option3 = Option3 + "Điê\u0309m Hp " + Option3Level + " Tăng";
                break;
            case 4:
                Option3 = Option3 + "Mp " + Option3Level + " Tăng";
                break;
            case 5:
                Option3 = Option3 + "Ti\u0309 lê\u0323 chi\u0301nh xa\u0301c " + Option3Level + " Tăng";
                break;
            case 6:
                Option3 = Option3 + "Ti\u0309 lê\u0323 tra\u0301nh ne\u0301 " + Option3Level + " Tăng";
                break;
            case 7:
                Option3 = Option3 + "Sư\u0301c tâ\u0301n công cu\u0309a vo\u0303 công " + Option3Level + " % Tăng";
                break;
            case 8:
                Option3 = Option3 + "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công " + Option3Level + " Tăng";
                break;
            case 9:
                Option3 = Option3 + "X.suâ\u0301t hơ\u0323p tha\u0300nh cươ\u0300ng ho\u0301a tha\u0300nh công " +
                          Option3Level + " % Tăng";
                break;
            case 10:
                Option3 = Option3 + "Thêm điê\u0309m đa\u0309 ki\u0301ch " + Option3Level + " Tăng";
                break;
            case 11:
                Option3 = Option3 + "Sư\u0301c pho\u0300ng ngư\u0323 cu\u0309a vo\u0303 công " + Option3Level + " Tăng";
                break;
            case 12:
                Option3 = Option3 + "X.suâ\u0301t nhâ\u0323n tiê\u0300n  " + Option3Level + " % Tăng";
                break;
            case 13:
                Option3 = Option3 + "Điê\u0309m Exp tô\u0309n thâ\u0301t khi chê\u0301t " + Option3Level + " % Tăng";
                break;
        }

        lblOption3.Text = Option3;
    }

    private void HienThiOption4()
    {
        var Option4 = "";
        switch (Option4Type)
        {
            case 0:
                lblOption4.Visible = false;
                break;
            case 1:
                Option4 = Option4 + "Sư\u0301c tâ\u0301n công " + Option4Level + " Tăng";
                break;
            case 2:
                Option4 = Option4 + "Sư\u0301c pho\u0300ng ngư\u0323 " + Option4Level + " Tăng";
                break;
            case 3:
                Option4 = Option4 + "Điê\u0309m Hp " + Option4Level + "Tăng";
                break;
            case 4:
                Option4 = Option4 + "Mp " + Option4Level + " Tăng";
                break;
            case 5:
                Option4 = Option4 + "Ti\u0309 lê\u0323 chi\u0301nh xa\u0301c " + Option4Level + " Tăng";
                break;
            case 6:
                Option4 = Option4 + "Ti\u0309 lê\u0323 tra\u0301nh ne\u0301" + Option4Level + " Tăng";
                break;
            case 7:
                Option4 = Option4 + "Sư\u0301c tâ\u0301n công cu\u0309a vo\u0303 công " + Option4Level + " % Tăng";
                break;
            case 8:
                Option4 = Option4 + "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công " + Option4Level + " Tăng";
                break;
            case 9:
                Option4 = Option4 + "X.suâ\u0301t hơ\u0323p tha\u0300nh cươ\u0300ng ho\u0301a tha\u0300nh công " +
                          Option4Level + " % Tăng";
                break;
            case 10:
                Option4 = Option4 + "Thêm điê\u0309m đa\u0309 ki\u0301ch " + Option4Level + " Tăng";
                break;
            case 11:
                Option4 = Option4 + "Sư\u0301c pho\u0300ng ngư\u0323 cu\u0309a vo\u0303 công " + Option4Level + " Tăng";
                break;
            case 12:
                Option4 = Option4 + "X.suâ\u0301t nhâ\u0323n tiê\u0300n " + Option4Level + " % Tăng";
                break;
            case 13:
                Option4 = Option4 + "Điê\u0309m Exp tô\u0309n thâ\u0301t khi chê\u0301t " + Option4Level + " % Tăng";
                break;
        }

        lblOption4.Text = Option4;
    }

    private void HienThiThuocTinh()
    {
        var ThuocTinh = "Ti\u0301nh ";
        switch (this.ThuocTinh)
        {
            case 0:
                lblThuocTinh.Visible = false;
                break;
            case 1:
                ThuocTinh += " Hỏa ";
                break;
            case 2:
                ThuocTinh += " Thủy ";
                break;
            case 3:
                ThuocTinh += " Phong ";
                break;
            case 4:
                ThuocTinh += " Nội ";
                break;
            case 5:
                ThuocTinh += " Ngoại ";
                break;
            case 6:
                ThuocTinh += " Độc ";
                break;
        }

        var obj = ThuocTinh;
        var thuocTinhLevel = ThuocTinhLevel;
        ThuocTinh = obj + thuocTinhLevel + "Giai đoạn";
        lblThuocTinh.Text = ThuocTinh;
    }

    private void HienThiTinhNgo()
    {
        var TinhNgo = "Ti\u0301nh ngô\u0323 cấp: " + this.TinhNgo + " Giai đoạn";
        if (this.TinhNgo == 0) lblTinhNgo.Visible = false;
        lblTinhNgo.Text = TinhNgo;
    }

    private void HienThiTrungCap()
    {
        var TrungCap = "[Trung cấp] ";
        var TrungCapLevel = 0;
        if (this.TrungCap <= 51 && this.TrungCap >= 47)
            TrungCap = TrungCap + "Hô\u0303n nguyên  " + (this.TrungCap - 46) + "%";
        else if (this.TrungCap <= 46 && this.TrungCap >= 42)
            TrungCap = TrungCap + "Hô\u0323 thê\u0309" + (this.TrungCap - 41) + "%";
        else if (this.TrungCap <= 41 && this.TrungCap >= 37)
            TrungCap = TrungCap + "Di tinh " + (this.TrungCap - 36) + "%";
        else if (this.TrungCap <= 36 && this.TrungCap >= 34)
            TrungCap = TrungCap + "Phâ\u0303n nô\u0323" + (this.TrungCap - 33) + "%";
        else if (this.TrungCap <= 33 && this.TrungCap >= 31)
            TrungCap = TrungCap + "Ky\u0300 duyên " + (this.TrungCap - 30) + "%";
        else if (this.TrungCap <= 30 && this.TrungCap >= 28)
            TrungCap = TrungCap + "Hâ\u0301p hô\u0300n " + (this.TrungCap - 30) + "%";
        else if (this.TrungCap <= 27 && this.TrungCap >= 23)
            TrungCap = TrungCap + "Phu\u0323c cư\u0300u " + (this.TrungCap - 22) + "%";
        else if (this.TrungCap == 0) lblTrungCap.Visible = false;
        lblTrungCap.Text = TrungCap;
    }

    private void HienThiKhoa()
    {
        if (Khoa == 0)
            lblKhoa.Visible = false;
        else
            lblKhoa.Visible = true;
    }

    private void btnDong_Click(object sender, EventArgs e)
    {
        Close();
    }

    private void lblOption1_Click(object sender, EventArgs e)
    {
    }

    private void btnDelete_Click(object sender, EventArgs e)
    {
        var dr = MessageBox.Show("Bạn có chắc muốn xóa item này?", "Hêò Thôìng", MessageBoxButtons.YesNo,
            MessageBoxIcon.Exclamation);
        if (dr != DialogResult.Yes) return;
        switch (LoaiKho)
        {
            case 0:
                Player.Item_Wear[index].VatPham_byte = new byte[World.Item_Db_Byte_Length];
                Player.HeThongNhacNho("ÐaÞ xoìa môòt vâòt phâÒm trên ngýõÌi cuÒa baòn .", 10, "Hêò thôìng");
                break;
            case 1:
                Player.ItemMinusTheNumberOfAttributes(index, 1);
                Player.HeThongNhacNho("ÐaÞ xoìa item õÒ ô thýì " + index + 1 + " trong tuìi ðôÌ cuÒa baòn", 10,
                    "Hêò thôìng");
                if (!Player.Item_In_Bag[index].Vat_Pham_Khoa_Lai && Player.Item_In_Bag[index].GetVatPham_ID != 0)
                    Player.Item_In_Bag[index].VatPham_byte = new byte[World.Item_Db_Byte_Length];
                break;
            case 2:
                Player.PersonalWarehouse[index].VatPham_byte = new byte[World.Item_Db_Byte_Length];
                Player.OpenPersonalWarehouse();
                Player.OpenTheComprehensiveWarehouse();
                Player.HeThongNhacNho("ÐaÞ xoìa item õÒ ô thýì " + index + 1 + " trong kho riêng cuÒa baòn", 10,
                    "Hêò thôìng");
                break;
            case 3:
                Player.PublicWarehouse[index].VatPham_byte = new byte[World.Item_Db_Byte_Length];
                Player.OpenPersonalWarehouse();
                Player.OpenTheComprehensiveWarehouse();
                Player.HeThongNhacNho("ÐaÞ xoìa item õÒ ô thýì " + index + 1 + " trong kho chung cuÒa baòn", 10,
                    "Hêò thôìng");
                break;
        }

        Player.UpdateEquipmentEffects();
        Player.LoadCharacterWearItem();
        Player.UpdateMoneyAndWeight();
        Close();
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing && components != null) components.Dispose();
        base.Dispose(disposing);
    }

    private void InitializeComponent()
    {
        btnDong = new Button();
        lblKhoa = new Label();
        lblTrungCap = new Label();
        lblTinhNgo = new Label();
        lblThuocTinh = new Label();
        lblOption4 = new Label();
        lblOption3 = new Label();
        lblOption2 = new Label();
        lblOption1 = new Label();
        lblZx = new Label();
        lblLevel = new Label();
        lblItem = new Label();
        btnDelete = new Button();
        SuspendLayout();
        btnDong.Anchor = AnchorStyles.Bottom;
        btnDong.BackColor = Color.White;
        btnDong.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Bold, GraphicsUnit.Point, 0);
        btnDong.Location = new Point(75, 268);
        btnDong.Name = "btnDong";
        btnDong.Size = new Size(75, 23);
        btnDong.TabIndex = 31;
        btnDong.Text = "Đo\u0301ng";
        btnDong.UseVisualStyleBackColor = false;
        btnDong.Click += btnDong_Click;
        lblKhoa.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
        lblKhoa.BackColor = Color.Transparent;
        lblKhoa.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Bold);
        lblKhoa.ForeColor = Color.White;
        lblKhoa.Location = new Point(2, 247);
        lblKhoa.Name = "lblKhoa";
        lblKhoa.Size = new Size(323, 29);
        lblKhoa.TabIndex = 30;
        lblKhoa.Text = "Đa\u0323o cu\u0323 sư\u0309 du\u0323ng di\u0323ch vu\u0323 tro\u0301i buô\u0323c\r\n";
        lblKhoa.TextAlign = ContentAlignment.MiddleCenter;
        lblTrungCap.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
        lblTrungCap.BackColor = Color.Transparent;
        lblTrungCap.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
        lblTrungCap.ForeColor = Color.Aqua;
        lblTrungCap.Location = new Point(2, 226);
        lblTrungCap.Name = "lblTrungCap";
        lblTrungCap.Size = new Size(320, 21);
        lblTrungCap.TabIndex = 29;
        lblTrungCap.Text = "[Trung câ\u0301p] Ky\u0300 duyên 3%";
        lblTrungCap.TextAlign = ContentAlignment.MiddleCenter;
        lblTinhNgo.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
        lblTinhNgo.BackColor = Color.Transparent;
        lblTinhNgo.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
        lblTinhNgo.ForeColor = Color.FromArgb(192, 255, 255);
        lblTinhNgo.Location = new Point(3, 205);
        lblTinhNgo.Name = "lblTinhNgo";
        lblTinhNgo.Size = new Size(323, 21);
        lblTinhNgo.TabIndex = 28;
        lblTinhNgo.Text = "Ti\u0301nh ngô\u0323 câ\u0301p: 10Giai đoa\u0323n";
        lblTinhNgo.TextAlign = ContentAlignment.MiddleCenter;
        lblThuocTinh.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
        lblThuocTinh.BackColor = Color.Transparent;
        lblThuocTinh.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
        lblThuocTinh.ForeColor = Color.Magenta;
        lblThuocTinh.Location = new Point(6, 184);
        lblThuocTinh.Name = "lblThuocTinh";
        lblThuocTinh.Size = new Size(323, 21);
        lblThuocTinh.TabIndex = 27;
        lblThuocTinh.Text = "Ti\u0301nh Ho\u0309a 10Giai đoa\u0323n";
        lblThuocTinh.TextAlign = ContentAlignment.MiddleCenter;
        lblOption4.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
        lblOption4.BackColor = Color.Transparent;
        lblOption4.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
        lblOption4.ForeColor = Color.Orange;
        lblOption4.Location = new Point(4, 154);
        lblOption4.Name = "lblOption4";
        lblOption4.Size = new Size(323, 30);
        lblOption4.TabIndex = 26;
        lblOption4.Text = "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công 3 Tăng ";
        lblOption4.TextAlign = ContentAlignment.MiddleCenter;
        lblOption3.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
        lblOption3.BackColor = Color.Transparent;
        lblOption3.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
        lblOption3.ForeColor = Color.Orange;
        lblOption3.Location = new Point(4, 124);
        lblOption3.Name = "lblOption3";
        lblOption3.Size = new Size(323, 30);
        lblOption3.TabIndex = 25;
        lblOption3.Text = "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công 3 Tăng ";
        lblOption3.TextAlign = ContentAlignment.MiddleCenter;
        lblOption2.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
        lblOption2.BackColor = Color.Transparent;
        lblOption2.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
        lblOption2.ForeColor = Color.Orange;
        lblOption2.Location = new Point(7, 94);
        lblOption2.Name = "lblOption2";
        lblOption2.Size = new Size(323, 30);
        lblOption2.TabIndex = 24;
        lblOption2.Text = "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công 3 Tăng ";
        lblOption2.TextAlign = ContentAlignment.MiddleCenter;
        lblOption1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
        lblOption1.BackColor = Color.Transparent;
        lblOption1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
        lblOption1.ForeColor = Color.Orange;
        lblOption1.Location = new Point(6, 64);
        lblOption1.Name = "lblOption1";
        lblOption1.Size = new Size(323, 30);
        lblOption1.TabIndex = 23;
        lblOption1.Text = "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công 3 Tăng ";
        lblOption1.TextAlign = ContentAlignment.MiddleCenter;
        lblOption1.Click += lblOption1_Click;
        lblZx.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
        lblZx.BackColor = Color.Transparent;
        lblZx.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Bold);
        lblZx.ForeColor = Color.White;
        lblZx.Location = new Point(4, 43);
        lblZx.Name = "lblZx";
        lblZx.Size = new Size(326, 21);
        lblZx.TabIndex = 22;
        lblZx.Text = "Thê\u0301 lư\u0323c: Ta\u0300 pha\u0301i";
        lblZx.TextAlign = ContentAlignment.MiddleCenter;
        lblLevel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
        lblLevel.BackColor = Color.Transparent;
        lblLevel.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Bold);
        lblLevel.ForeColor = Color.White;
        lblLevel.Location = new Point(3, 22);
        lblLevel.Name = "lblLevel";
        lblLevel.Size = new Size(326, 21);
        lblLevel.TabIndex = 21;
        lblLevel.Text = "Đă\u0309ng câ\u0301p: 140";
        lblLevel.TextAlign = ContentAlignment.MiddleCenter;
        lblItem.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
        lblItem.BackColor = Color.Transparent;
        lblItem.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
        lblItem.ForeColor = Color.Aqua;
        lblItem.Location = new Point(4, -7);
        lblItem.Name = "lblItem";
        lblItem.Size = new Size(326, 29);
        lblItem.TabIndex = 20;
        lblItem.Text = "Pha\u0301 Hoa\u0323ch Thâ\u0300n Thương (Cao Câ\u0301p) [15]";
        lblItem.TextAlign = ContentAlignment.MiddleCenter;
        btnDelete.Anchor = AnchorStyles.Bottom;
        btnDelete.BackColor = Color.White;
        btnDelete.Font = new Font("Microsoft Sans Serif", 9f, FontStyle.Bold, GraphicsUnit.Point, 0);
        btnDelete.Location = new Point(170, 268);
        btnDelete.Name = "btnDelete";
        btnDelete.Size = new Size(75, 23);
        btnDelete.TabIndex = 32;
        btnDelete.Text = "Xóa item";
        btnDelete.UseVisualStyleBackColor = false;
        btnDelete.Click += btnDelete_Click;
        AutoScaleDimensions = new SizeF(6f, 13f);
        AutoScaleMode = AutoScaleMode.Font;
        BackColor = Color.FromArgb(64, 64, 64);
        ClientSize = new Size(334, 303);
        Controls.Add(btnDelete);
        Controls.Add(btnDong);
        Controls.Add(lblKhoa);
        Controls.Add(lblTrungCap);
        Controls.Add(lblTinhNgo);
        Controls.Add(lblThuocTinh);
        Controls.Add(lblOption4);
        Controls.Add(lblOption3);
        Controls.Add(lblOption2);
        Controls.Add(lblOption1);
        Controls.Add(lblZx);
        Controls.Add(lblLevel);
        Controls.Add(lblItem);
        ForeColor = SystemColors.ControlText;
        FormBorderStyle = FormBorderStyle.None;
        Name = "ItemInfo";
        Opacity = 0.8;
        Text = "ItemInfo";
        ResumeLayout(false);
    }
}