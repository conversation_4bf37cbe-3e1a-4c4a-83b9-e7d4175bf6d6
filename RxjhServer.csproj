<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">
  <PropertyGroup>
    <AssemblyName>RxjhServer</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <OutputType>WinExe</OutputType>
    <UseWindowsForms>True</UseWindowsForms>
    <TargetFramework>net481</TargetFramework>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup>
    <LangVersion>11.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>app.ico</ApplicationIcon>
    <RootNamespace />
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLua" Version="1.7.3" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Design" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="NLua">
      <HintPath>..\GS_RELOAD\bin\x86\Release\net472\NLua.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\GS_RELOAD\bin\x86\Release\net472\Newtonsoft.Json.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>