using System.ComponentModel.DataAnnotations;

namespace YulgangServer.RxjhServer.YGZodiac.Entity;

public class YZ_EvGiftDetail
{
	public int ID { get; set; }

	public int? IdGift { get; set; }

	public int? IdItem { get; set; }

	public int? CuongHoa { get; set; }

	public int? magic0 { get; set; }

	public int? magic1 { get; set; }

	public int? magic2 { get; set; }

	public int? magic3 { get; set; }

	public int? HSD { get; set; }

	public int? Lock { get; set; }

	public bool? ItemGop { get; set; }

	public int? SoLuong { get; set; }

	[StringLength(250)]
	public string Description { get; set; }
}
