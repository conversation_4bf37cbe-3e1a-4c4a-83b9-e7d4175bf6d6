using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using RxjhServer;

namespace YulgangServer.RxjhServer;

public class ItemInfo : Form
{
	private int ItemID = 0;

	private long ItemSeri = 0L;

	private string ItemName = "";

	private int Amount = 0;

	private int PhamChat = 0;

	private int CuongHoa = 0;

	private int DangCap = 0;

	private int TheLuc = 0;

	private int Gender = 0;

	private int Option1Type = 0;

	private int Option1Level = 0;

	private int Option2Type = 0;

	private int Option2Level = 0;

	private int Option3Type = 0;

	private int Option3Level = 0;

	private int Option4Type = 0;

	private int Option4Level = 0;

	private int ThuocTinh = 0;

	private int ThuocTinhLevel = 0;

	private int TinhNgo = 0;

	private int TrungCap = 0;

	private int Khoa = 0;

	private int LoaiKho = 0;

	private Players Player;

	private int index = 0;

	private IContainer components = null;

	private Button btnDong;

	private Label lblKhoa;

	private Label lblTrungCap;

	private Label lblTinhNgo;

	private Label lblThuocTinh;

	private Label lblOption4;

	private Label lblOption3;

	private Label lblOption2;

	private Label lblOption1;

	private Label lblZx;

	private Label lblLevel;

	private Label lblItem;

	private Button btnDelete;

	private Label lblPID;

	private Label lblItemSeri;

	private Label lblGender;

	private Label lblAmount;

	private Button btn_changepid;

	private TextBox tbox_changepid;

	public ItemInfo(int ItemID, long ItemSeri, int Amount, string ItemName, int PhamChat, int CuongHoa, int DangCap, int TheLuc, int Gender, int Option1Type, int Option1Level, int Option2Type, int Option2Level, int Option3Type, int Option3Level, int Option4Type, int Option4Level, int ThuocTinh, int ThuocTinhLevel, int TinhNgo, int TrungCap, int Khoa, int LoaiKho, int index, Players Player)
	{
		InitializeComponent();
		this.ItemID = ItemID;
		this.ItemSeri = ItemSeri;
		this.ItemName = ItemName;
		this.Amount = Amount;
		this.PhamChat = PhamChat;
		this.CuongHoa = CuongHoa;
		this.DangCap = DangCap;
		this.TheLuc = TheLuc;
		this.Gender = Gender;
		this.Option1Type = Option1Type;
		this.Option1Level = Option1Level;
		this.Option2Type = Option2Type;
		this.Option2Level = Option2Level;
		this.Option3Type = Option3Type;
		this.Option3Level = Option3Level;
		this.Option4Type = Option4Type;
		this.Option4Level = Option4Level;
		this.ThuocTinh = ThuocTinh;
		this.ThuocTinhLevel = ThuocTinhLevel;
		this.TinhNgo = TinhNgo;
		this.TrungCap = TrungCap;
		this.Khoa = Khoa;
		this.LoaiKho = LoaiKho;
		this.Player = Player;
		this.index = index;
		tbox_changepid.Text = "";
		tbox_changepid.Visible = false;
		btn_changepid.Text = "Đổi PID";
		HienThi();
	}

	private void HienThi()
	{
		HienThiTen();
		HienThiPID();
		HienThiAmount();
		HienThiDangCap();
		HienThiTheLuc();
		HienThiGender();
		HienThiOption1();
		HienThiOption2();
		HienThiOption3();
		HienThiOption4();
		HienThiThuocTinh();
		HienThiTinhNgo();
		HienThiTrungCap();
		HienThiItemKhoa();
		HienThiItemSeri();
	}

	private void HienThiTen()
	{
		string TenTrangBi = ItemName;
		switch (PhamChat)
		{
		case 1:
			TenTrangBi += " (Kha\u0301 tô\u0301t) ";
			break;
		case 2:
			TenTrangBi += " (Cao câ\u0301p) ";
			break;
		}
		if (CuongHoa > 0)
		{
			TenTrangBi = TenTrangBi + "[" + CuongHoa + "]";
		}
		if (TrungCap > 0)
		{
			lblItem.ForeColor = Color.Aqua;
		}
		if (TrungCap == 0 && CuongHoa > 0)
		{
			lblItem.ForeColor = Color.Blue;
		}
		if (TrungCap == 0 && CuongHoa == 0)
		{
			lblItem.ForeColor = Color.White;
		}
		lblItem.Text = TenTrangBi;
	}

	private void HienThiPID()
	{
		lblPID.Text = "PID : " + ItemID;
	}

	private void HienThiAmount()
	{
		lblAmount.Text = "Amount : " + Amount;
	}

	private void HienThiDangCap()
	{
		lblLevel.Text = "Đă\u0309ng câ\u0301p:" + DangCap;
	}

	private void HienThiTheLuc()
	{
		string TheLuc = "Thê\u0301 lư\u0323c: ";
		switch (this.TheLuc)
		{
		case 0:
			TheLuc += "Không";
			break;
		case 1:
			TheLuc += "Chi\u0301nh pha\u0301i";
			break;
		case 2:
			TheLuc += "Ta\u0300 pha\u0301i";
			break;
		}
		lblZx.Text = TheLuc;
	}

	private void HienThiItemKhoa()
	{
		string ItemLock = "";
		switch (Khoa)
		{
		case 0:
			ItemLock += "Vật Phẩm Không Khóa";
			lblKhoa.ForeColor = Color.Aquamarine;
			break;
		case 1:
			ItemLock += "Đa\u0323o cu\u0323 sư\u0309 du\u0323ng di\u0323ch vu\u0323 tro\u0301i buô\u0323c";
			lblKhoa.ForeColor = Color.Red;
			break;
		}
		lblKhoa.Text = ItemLock;
	}

	private void HienThiGender()
	{
		string Gender = "Gender: ";
		switch (this.Gender)
		{
		case 0:
			Gender += "Không";
			break;
		case 1:
			Gender += "Nam";
			break;
		case 2:
			Gender += "Nữ";
			break;
		}
		lblGender.Text = Gender;
	}

	private void HienThiOption1()
	{
		string Option1 = "";
		switch (Option1Type)
		{
		case 0:
			lblOption1.Visible = false;
			break;
		case 1:
			Option1 = Option1 + "Sư\u0301c tâ\u0301n công " + Option1Level + " Tăng";
			break;
		case 2:
			Option1 = Option1 + "Sư\u0301c pho\u0300ng ngư\u0323" + Option1Level + " Tăng";
			break;
		case 3:
			Option1 = Option1 + "Điê\u0309m Hp " + Option1Level + " Tăng";
			break;
		case 4:
			Option1 = Option1 + "Mp " + Option1Level + " Tăng";
			break;
		case 5:
			Option1 = Option1 + "Ti\u0309 lê\u0323 chi\u0301nh xa\u0301c " + Option1Level + " Tăng";
			break;
		case 6:
			Option1 = Option1 + "Ti\u0309 lê\u0323 tra\u0301nh ne\u0301" + Option1Level + " Tăng";
			break;
		case 7:
			Option1 = Option1 + "Sư\u0301c tâ\u0301n công cu\u0309a vo\u0303 công " + Option1Level + " % Tăng";
			break;
		case 8:
			Option1 = Option1 + "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công " + Option1Level + " Tăng";
			break;
		case 9:
			Option1 = Option1 + "X.suâ\u0301t hơ\u0323p tha\u0300nh cươ\u0300ng ho\u0301a tha\u0300nh công " + Option1Level + " % Tăng";
			break;
		case 10:
			Option1 = Option1 + "Thêm điê\u0309m đa\u0309 ki\u0301ch " + Option1Level + " Tăng";
			break;
		case 11:
			Option1 = Option1 + "Sư\u0301c pho\u0300ng ngư\u0323 cu\u0309a vo\u0303 công " + Option1Level + " Tăng";
			break;
		case 12:
			Option1 = Option1 + "X.suâ\u0301t nhâ\u0323n tiê\u0300n " + Option1Level + " % Tăng";
			break;
		case 13:
			Option1 = Option1 + "Điê\u0309m Exp tô\u0309n thâ\u0301t khi chê\u0301t " + Option1Level + " % Tăng";
			break;
		}
		lblOption1.Text = Option1;
	}

	private void HienThiOption2()
	{
		string Option2 = "";
		switch (Option2Type)
		{
		case 0:
			lblOption2.Visible = false;
			break;
		case 1:
			Option2 = Option2 + "Sư\u0301c tâ\u0301n công " + Option2Level + " Tăng";
			break;
		case 2:
			Option2 = Option2 + "Sư\u0301c pho\u0300ng ngư\u0323 " + Option2Level + " Tăng";
			break;
		case 3:
			Option2 = Option2 + "Điê\u0309m Hp " + Option2Level + " Tăng";
			break;
		case 4:
			Option2 = Option2 + "Mp " + Option2Level + " Tăng";
			break;
		case 5:
			Option2 = Option2 + "Ti\u0309 lê\u0323 chi\u0301nh xa\u0301c " + Option2Level + " Tăng";
			break;
		case 6:
			Option2 = Option2 + "Ti\u0309 lê\u0323 tra\u0301nh ne\u0301 " + Option2Level + " Tăng";
			break;
		case 7:
			Option2 = Option2 + "Sư\u0301c tâ\u0301n công cu\u0309a vo\u0303 công " + Option2Level + " % Tăng";
			break;
		case 8:
			Option2 = Option2 + "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công " + Option2Level + " Tăng";
			break;
		case 9:
			Option2 = Option2 + "X.suâ\u0301t hơ\u0323p tha\u0300nh cươ\u0300ng ho\u0301a tha\u0300nh công " + Option2Level + " % Tăng";
			break;
		case 10:
			Option2 = Option2 + "Thêm điê\u0309m đa\u0309 ki\u0301ch " + Option2Level + " Tăng";
			break;
		case 11:
			Option2 = Option2 + "Sư\u0301c pho\u0300ng ngư\u0323 cu\u0309a vo\u0303 công " + Option2Level + " Tăng";
			break;
		case 12:
			Option2 = Option2 + "X.suâ\u0301t nhâ\u0323n tiê\u0300n " + Option2Level + " % Tăng";
			break;
		case 13:
			Option2 = Option2 + "Điê\u0309m Exp tô\u0309n thâ\u0301t khi chê\u0301t " + Option2Level + " % Tăng";
			break;
		}
		lblOption2.Text = Option2;
	}

	private void HienThiOption3()
	{
		string Option3 = "";
		switch (Option3Type)
		{
		case 0:
			lblOption3.Visible = false;
			break;
		case 1:
			Option3 = Option3 + "Sư\u0301c tâ\u0301n công " + Option3Level + " Tăng";
			break;
		case 2:
			Option3 = Option3 + "Sư\u0301c pho\u0300ng ngư\u0323 " + Option3Level + " Tăng";
			break;
		case 3:
			Option3 = Option3 + "Điê\u0309m Hp " + Option3Level + " Tăng";
			break;
		case 4:
			Option3 = Option3 + "Mp " + Option3Level + " Tăng";
			break;
		case 5:
			Option3 = Option3 + "Ti\u0309 lê\u0323 chi\u0301nh xa\u0301c " + Option3Level + " Tăng";
			break;
		case 6:
			Option3 = Option3 + "Ti\u0309 lê\u0323 tra\u0301nh ne\u0301 " + Option3Level + " Tăng";
			break;
		case 7:
			Option3 = Option3 + "Sư\u0301c tâ\u0301n công cu\u0309a vo\u0303 công " + Option3Level + " % Tăng";
			break;
		case 8:
			Option3 = Option3 + "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công " + Option3Level + " Tăng";
			break;
		case 9:
			Option3 = Option3 + "X.suâ\u0301t hơ\u0323p tha\u0300nh cươ\u0300ng ho\u0301a tha\u0300nh công " + Option3Level + " % Tăng";
			break;
		case 10:
			Option3 = Option3 + "Thêm điê\u0309m đa\u0309 ki\u0301ch " + Option3Level + " Tăng";
			break;
		case 11:
			Option3 = Option3 + "Sư\u0301c pho\u0300ng ngư\u0323 cu\u0309a vo\u0303 công " + Option3Level + " Tăng";
			break;
		case 12:
			Option3 = Option3 + "X.suâ\u0301t nhâ\u0323n tiê\u0300n  " + Option3Level + " % Tăng";
			break;
		case 13:
			Option3 = Option3 + "Điê\u0309m Exp tô\u0309n thâ\u0301t khi chê\u0301t " + Option3Level + " % Tăng";
			break;
		}
		lblOption3.Text = Option3;
	}

	private void HienThiOption4()
	{
		string Option4 = "";
		switch (Option4Type)
		{
		case 0:
			lblOption4.Visible = false;
			break;
		case 1:
			Option4 = Option4 + "Sư\u0301c tâ\u0301n công " + Option4Level + " Tăng";
			break;
		case 2:
			Option4 = Option4 + "Sư\u0301c pho\u0300ng ngư\u0323 " + Option4Level + " Tăng";
			break;
		case 3:
			Option4 = Option4 + "Điê\u0309m Hp " + Option4Level + "Tăng";
			break;
		case 4:
			Option4 = Option4 + "Mp " + Option4Level + " Tăng";
			break;
		case 5:
			Option4 = Option4 + "Ti\u0309 lê\u0323 chi\u0301nh xa\u0301c " + Option4Level + " Tăng";
			break;
		case 6:
			Option4 = Option4 + "Ti\u0309 lê\u0323 tra\u0301nh ne\u0301" + Option4Level + " Tăng";
			break;
		case 7:
			Option4 = Option4 + "Sư\u0301c tâ\u0301n công cu\u0309a vo\u0303 công " + Option4Level + " % Tăng";
			break;
		case 8:
			Option4 = Option4 + "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công " + Option4Level + " Tăng";
			break;
		case 9:
			Option4 = Option4 + "X.suâ\u0301t hơ\u0323p tha\u0300nh cươ\u0300ng ho\u0301a tha\u0300nh công " + Option4Level + " % Tăng";
			break;
		case 10:
			Option4 = Option4 + "Thêm điê\u0309m đa\u0309 ki\u0301ch " + Option4Level + " Tăng";
			break;
		case 11:
			Option4 = Option4 + "Sư\u0301c pho\u0300ng ngư\u0323 cu\u0309a vo\u0303 công " + Option4Level + " Tăng";
			break;
		case 12:
			Option4 = Option4 + "X.suâ\u0301t nhâ\u0323n tiê\u0300n " + Option4Level + " % Tăng";
			break;
		case 13:
			Option4 = Option4 + "Điê\u0309m Exp tô\u0309n thâ\u0301t khi chê\u0301t " + Option4Level + " % Tăng";
			break;
		}
		lblOption4.Text = Option4;
	}

	private void HienThiThuocTinh()
	{
		string ThuocTinh = "Ti\u0301nh ";
		switch (this.ThuocTinh)
		{
		case 0:
			lblThuocTinh.Visible = false;
			break;
		case 1:
			ThuocTinh += " Ho\u0309a ";
			break;
		case 2:
			ThuocTinh += " Thu\u0309y ";
			break;
		case 3:
			ThuocTinh += " Phong ";
			break;
		case 4:
			ThuocTinh += " Nô\u0323i ";
			break;
		case 5:
			ThuocTinh += " Ngoa\u0323i ";
			break;
		case 6:
			ThuocTinh += " Đô\u0323c ";
			break;
		}
		string obj = ThuocTinh;
		int thuocTinhLevel = ThuocTinhLevel;
		ThuocTinh = obj + thuocTinhLevel + "Giai đoa\u0323n";
		lblThuocTinh.Text = ThuocTinh;
	}

	private void HienThiTinhNgo()
	{
		string TinhNgo = "Ti\u0301nh ngô\u0323 câ\u0301p: " + this.TinhNgo + " Giai đoa\u0323n";
		if (this.TinhNgo == 0)
		{
			lblTinhNgo.Visible = false;
		}
		lblTinhNgo.Text = TinhNgo;
	}

	private void HienThiTrungCap()
	{
		string TrungCap = "[Trung câ\u0301p] ";
		int TrungCapLevel = 0;
		if (this.TrungCap <= 51 && this.TrungCap >= 47)
		{
			TrungCap = TrungCap + "Hô\u0303n nguyên  " + (this.TrungCap - 46) + "%";
		}
		else if (this.TrungCap <= 46 && this.TrungCap >= 42)
		{
			TrungCap = TrungCap + "Hô\u0323 thê\u0309" + (this.TrungCap - 41) + "%";
		}
		else if (this.TrungCap <= 41 && this.TrungCap >= 37)
		{
			TrungCap = TrungCap + "Di tinh " + (this.TrungCap - 36) + "%";
		}
		else if (this.TrungCap <= 36 && this.TrungCap >= 34)
		{
			TrungCap = TrungCap + "Phâ\u0303n nô\u0323" + (this.TrungCap - 33) + "%";
		}
		else if (this.TrungCap <= 33 && this.TrungCap >= 31)
		{
			TrungCap = TrungCap + "Ky\u0300 duyên " + (this.TrungCap - 30) + "%";
		}
		else if (this.TrungCap <= 30 && this.TrungCap >= 28)
		{
			TrungCap = TrungCap + "Hâ\u0301p hô\u0300n " + (this.TrungCap - 30) + "%";
		}
		else if (this.TrungCap <= 27 && this.TrungCap >= 23)
		{
			TrungCap = TrungCap + "Phu\u0323c cư\u0300u " + (this.TrungCap - 22) + "%";
		}
		else if (this.TrungCap == 0)
		{
			lblTrungCap.Visible = false;
		}
		lblTrungCap.Text = TrungCap;
	}

	private void HienThiItemSeri()
	{
		lblItemSeri.Text = "Item Serial : " + ItemSeri;
	}

	private void HienThiKhoa()
	{
		if (Khoa == 0)
		{
			lblKhoa.Visible = false;
		}
		else
		{
			lblKhoa.Visible = true;
		}
	}

	private void btnDong_Click(object sender, EventArgs e)
	{
		Close();
	}

	private void lblOption1_Click(object sender, EventArgs e)
	{
	}

	private void btnDelete_Click(object sender, EventArgs e)
	{
		DialogResult dr = MessageBox.Show("Bạn có chắc muốn xóa item này?", "Hệ thống", MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation);
		if (dr == DialogResult.Yes)
		{
			switch (LoaiKho)
			{
			case 0:
				Player.Item_Wear[index].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				Player.HeThongNhacNho("Đã xoá một vật phẩm trên người của bạn.", 10, "Hêò thôìng");
				break;
			case 1:
				Player.Item_In_Bag[index].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				Player.HeThongNhacNho("Đã xoá vật phẩm thứ " + (index + 1) + " trong trong túi đồ của bạn");
				break;
			case 2:
				Player.WarehousePackageItemOperation(38144, 5, (int)Player.PersonalWarehouse[index].GetVatPham_ID, 0L, Player.PersonalWarehouse[index], index, 0);
				Player.PersonalWarehouse[index].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				Player.HeThongNhacNho("Đã xoá item ô thứ " + (index + 1) + " trong kho riêng của bạn", 10);
				break;
			case 3:
				Player.WarehousePackageItemOperation(38144, 6, (int)Player.PublicWarehouse[index].GetVatPham_ID, 0L, Player.PublicWarehouse[index], index, 0);
				Player.PublicWarehouse[index].VatPham_byte = new byte[World.Item_Db_Byte_Length];
				Player.HeThongNhacNho("Ðã xoá item ô thứ " + (index + 1) + "trong kho chung của bạn", 10);
				break;
			}
			Player.LoadCharacterWearItem();
			Player.UpdateEquipmentEffects();
			Player.Init_Item_In_Bag();
			Player.UpdateMoneyAndWeight();
			Close();
		}
	}

	private void lblItem_Click(object sender, EventArgs e)
	{
	}

	private void lblPID_Click(object sender, EventArgs e)
	{
	}

	private void button1_Click(object sender, EventArgs e)
	{
		if (tbox_changepid.Visible)
		{
			if (tbox_changepid.Text != "")
			{
				int pid = 0;
				try
				{
					pid = int.Parse(tbox_changepid.Text);
				}
				catch
				{
					MessageBox.Show("Mã sản phẩm không hợp lệ!");
				}
				if (World.Itme.TryGetValue(pid, out var _))
				{
					if (LoaiKho == 0)
					{
						Player.Item_Wear[index].VatPham_ID = BitConverter.GetBytes(pid);
						Player.LoadCharacterWearItem();
						Player.UpdateEquipmentEffects();
						Player.UpdateCharacterData(Player);
					}
					else if (LoaiKho == 1)
					{
						Player.Item_In_Bag[index].VatPham_ID = BitConverter.GetBytes(pid);
						Player.Update_Item_In_Bag();
					}
					else
					{
						MessageBox.Show("Chỉ có thể thay đổi vật phẩm trên người!");
					}
				}
				else
				{
					MessageBox.Show("Vật phẩm không tồn tại trong dữ liệu!");
				}
			}
			else
			{
				btn_changepid.Text = "Đổi PID";
				tbox_changepid.Text = "";
				tbox_changepid.Visible = false;
			}
		}
		else
		{
			tbox_changepid.Visible = true;
			btn_changepid.Text = "Đổi";
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.btnDong = new System.Windows.Forms.Button();
		this.lblKhoa = new System.Windows.Forms.Label();
		this.lblTrungCap = new System.Windows.Forms.Label();
		this.lblTinhNgo = new System.Windows.Forms.Label();
		this.lblThuocTinh = new System.Windows.Forms.Label();
		this.lblOption4 = new System.Windows.Forms.Label();
		this.lblOption3 = new System.Windows.Forms.Label();
		this.lblOption2 = new System.Windows.Forms.Label();
		this.lblOption1 = new System.Windows.Forms.Label();
		this.lblZx = new System.Windows.Forms.Label();
		this.lblLevel = new System.Windows.Forms.Label();
		this.lblItem = new System.Windows.Forms.Label();
		this.btnDelete = new System.Windows.Forms.Button();
		this.lblPID = new System.Windows.Forms.Label();
		this.lblItemSeri = new System.Windows.Forms.Label();
		this.lblGender = new System.Windows.Forms.Label();
		this.lblAmount = new System.Windows.Forms.Label();
		this.btn_changepid = new System.Windows.Forms.Button();
		this.tbox_changepid = new System.Windows.Forms.TextBox();
		base.SuspendLayout();
		this.btnDong.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
		this.btnDong.BackColor = System.Drawing.Color.White;
		this.btnDong.Font = new System.Drawing.Font("Microsoft Sans Serif", 9f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.btnDong.Location = new System.Drawing.Point(167, 335);
		this.btnDong.Name = "btnDong";
		this.btnDong.Size = new System.Drawing.Size(75, 23);
		this.btnDong.TabIndex = 31;
		this.btnDong.Text = "Đo\u0301ng";
		this.btnDong.UseVisualStyleBackColor = false;
		this.btnDong.Click += new System.EventHandler(btnDong_Click);
		this.lblKhoa.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblKhoa.BackColor = System.Drawing.Color.Transparent;
		this.lblKhoa.Font = new System.Drawing.Font("Microsoft Sans Serif", 9f, System.Drawing.FontStyle.Bold);
		this.lblKhoa.ForeColor = System.Drawing.Color.Red;
		this.lblKhoa.Location = new System.Drawing.Point(2, 265);
		this.lblKhoa.Name = "lblKhoa";
		this.lblKhoa.Size = new System.Drawing.Size(323, 17);
		this.lblKhoa.TabIndex = 30;
		this.lblKhoa.Text = "Đa\u0323o cu\u0323 sư\u0309 du\u0323ng di\u0323ch vu\u0323 tro\u0301i buô\u0323c\r\n";
		this.lblKhoa.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.lblTrungCap.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblTrungCap.BackColor = System.Drawing.Color.Transparent;
		this.lblTrungCap.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.lblTrungCap.ForeColor = System.Drawing.Color.Aqua;
		this.lblTrungCap.Location = new System.Drawing.Point(2, 240);
		this.lblTrungCap.Name = "lblTrungCap";
		this.lblTrungCap.Size = new System.Drawing.Size(320, 21);
		this.lblTrungCap.TabIndex = 29;
		this.lblTrungCap.Text = "[Trung câ\u0301p] Ky\u0300 duyên 3%";
		this.lblTrungCap.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.lblTinhNgo.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblTinhNgo.BackColor = System.Drawing.Color.Transparent;
		this.lblTinhNgo.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.lblTinhNgo.ForeColor = System.Drawing.Color.FromArgb(192, 255, 255);
		this.lblTinhNgo.Location = new System.Drawing.Point(3, 219);
		this.lblTinhNgo.Name = "lblTinhNgo";
		this.lblTinhNgo.Size = new System.Drawing.Size(323, 21);
		this.lblTinhNgo.TabIndex = 28;
		this.lblTinhNgo.Text = "Ti\u0301nh ngô\u0323 câ\u0301p: 10Giai đoa\u0323n";
		this.lblTinhNgo.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.lblThuocTinh.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblThuocTinh.BackColor = System.Drawing.Color.Transparent;
		this.lblThuocTinh.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.lblThuocTinh.ForeColor = System.Drawing.Color.Magenta;
		this.lblThuocTinh.Location = new System.Drawing.Point(6, 198);
		this.lblThuocTinh.Name = "lblThuocTinh";
		this.lblThuocTinh.Size = new System.Drawing.Size(323, 21);
		this.lblThuocTinh.TabIndex = 27;
		this.lblThuocTinh.Text = "Ti\u0301nh Ho\u0309a 10Giai đoa\u0323n";
		this.lblThuocTinh.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.lblOption4.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblOption4.BackColor = System.Drawing.Color.Transparent;
		this.lblOption4.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.lblOption4.ForeColor = System.Drawing.Color.Orange;
		this.lblOption4.Location = new System.Drawing.Point(4, 168);
		this.lblOption4.Name = "lblOption4";
		this.lblOption4.Size = new System.Drawing.Size(323, 30);
		this.lblOption4.TabIndex = 26;
		this.lblOption4.Text = "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công 3 Tăng ";
		this.lblOption4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.lblOption3.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblOption3.BackColor = System.Drawing.Color.Transparent;
		this.lblOption3.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.lblOption3.ForeColor = System.Drawing.Color.Orange;
		this.lblOption3.Location = new System.Drawing.Point(4, 138);
		this.lblOption3.Name = "lblOption3";
		this.lblOption3.Size = new System.Drawing.Size(323, 30);
		this.lblOption3.TabIndex = 25;
		this.lblOption3.Text = "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công 3 Tăng ";
		this.lblOption3.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.lblOption2.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblOption2.BackColor = System.Drawing.Color.Transparent;
		this.lblOption2.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.lblOption2.ForeColor = System.Drawing.Color.Orange;
		this.lblOption2.Location = new System.Drawing.Point(7, 108);
		this.lblOption2.Name = "lblOption2";
		this.lblOption2.Size = new System.Drawing.Size(323, 30);
		this.lblOption2.TabIndex = 24;
		this.lblOption2.Text = "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công 3 Tăng ";
		this.lblOption2.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.lblOption1.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblOption1.BackColor = System.Drawing.Color.Transparent;
		this.lblOption1.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.lblOption1.ForeColor = System.Drawing.Color.Orange;
		this.lblOption1.Location = new System.Drawing.Point(6, 78);
		this.lblOption1.Name = "lblOption1";
		this.lblOption1.Size = new System.Drawing.Size(323, 30);
		this.lblOption1.TabIndex = 23;
		this.lblOption1.Text = "Tâ\u0301t ca\u0309 đă\u0309ng câ\u0301p khi\u0301 công 3 Tăng ";
		this.lblOption1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.lblOption1.Click += new System.EventHandler(lblOption1_Click);
		this.lblZx.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblZx.BackColor = System.Drawing.Color.Transparent;
		this.lblZx.Font = new System.Drawing.Font("Microsoft Sans Serif", 9f, System.Drawing.FontStyle.Bold);
		this.lblZx.ForeColor = System.Drawing.Color.White;
		this.lblZx.Location = new System.Drawing.Point(213, 57);
		this.lblZx.Name = "lblZx";
		this.lblZx.Size = new System.Drawing.Size(118, 21);
		this.lblZx.TabIndex = 22;
		this.lblZx.Text = "| Thê\u0301 lư\u0323c: Ta\u0300 pha\u0301i";
		this.lblZx.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.lblLevel.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblLevel.BackColor = System.Drawing.Color.Transparent;
		this.lblLevel.Font = new System.Drawing.Font("Microsoft Sans Serif", 9f, System.Drawing.FontStyle.Bold);
		this.lblLevel.ForeColor = System.Drawing.Color.White;
		this.lblLevel.Location = new System.Drawing.Point(-4, 57);
		this.lblLevel.Name = "lblLevel";
		this.lblLevel.Size = new System.Drawing.Size(111, 21);
		this.lblLevel.TabIndex = 21;
		this.lblLevel.Text = "Đă\u0309ng câ\u0301p: 140";
		this.lblLevel.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.lblItem.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblItem.BackColor = System.Drawing.Color.Transparent;
		this.lblItem.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.lblItem.ForeColor = System.Drawing.Color.Aqua;
		this.lblItem.Location = new System.Drawing.Point(4, -5);
		this.lblItem.Name = "lblItem";
		this.lblItem.Size = new System.Drawing.Size(326, 29);
		this.lblItem.TabIndex = 20;
		this.lblItem.Text = "Pha\u0301 Hoa\u0323ch Thâ\u0300n Thương (Cao Câ\u0301p) [15]";
		this.lblItem.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.lblItem.Click += new System.EventHandler(lblItem_Click);
		this.btnDelete.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
		this.btnDelete.BackColor = System.Drawing.Color.White;
		this.btnDelete.Font = new System.Drawing.Font("Microsoft Sans Serif", 9f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.btnDelete.Location = new System.Drawing.Point(69, 335);
		this.btnDelete.Name = "btnDelete";
		this.btnDelete.Size = new System.Drawing.Size(75, 23);
		this.btnDelete.TabIndex = 32;
		this.btnDelete.Text = "Xóa item";
		this.btnDelete.UseVisualStyleBackColor = false;
		this.btnDelete.Click += new System.EventHandler(btnDelete_Click);
		this.lblPID.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblPID.BackColor = System.Drawing.Color.Transparent;
		this.lblPID.Font = new System.Drawing.Font("Microsoft Sans Serif", 9f, System.Drawing.FontStyle.Bold);
		this.lblPID.ForeColor = System.Drawing.Color.ForestGreen;
		this.lblPID.Location = new System.Drawing.Point(4, 17);
		this.lblPID.Name = "lblPID";
		this.lblPID.Size = new System.Drawing.Size(326, 21);
		this.lblPID.TabIndex = 33;
		this.lblPID.Text = "FLD_PID : 1008000048";
		this.lblPID.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.lblPID.Click += new System.EventHandler(lblPID_Click);
		this.lblItemSeri.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblItemSeri.BackColor = System.Drawing.Color.Transparent;
		this.lblItemSeri.Font = new System.Drawing.Font("Microsoft Sans Serif", 9f, System.Drawing.FontStyle.Bold);
		this.lblItemSeri.ForeColor = System.Drawing.Color.RoyalBlue;
		this.lblItemSeri.Location = new System.Drawing.Point(2, 282);
		this.lblItemSeri.Name = "lblItemSeri";
		this.lblItemSeri.Size = new System.Drawing.Size(326, 21);
		this.lblItemSeri.TabIndex = 34;
		this.lblItemSeri.Text = "Item Serial : 14357198";
		this.lblItemSeri.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.lblGender.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblGender.BackColor = System.Drawing.Color.Transparent;
		this.lblGender.Font = new System.Drawing.Font("Microsoft Sans Serif", 9f, System.Drawing.FontStyle.Bold);
		this.lblGender.ForeColor = System.Drawing.Color.White;
		this.lblGender.Location = new System.Drawing.Point(101, 57);
		this.lblGender.Name = "lblGender";
		this.lblGender.Size = new System.Drawing.Size(116, 21);
		this.lblGender.TabIndex = 35;
		this.lblGender.Text = "| Giới Tính : Nam";
		this.lblGender.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.lblAmount.Anchor = System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right;
		this.lblAmount.BackColor = System.Drawing.Color.Transparent;
		this.lblAmount.Font = new System.Drawing.Font("Microsoft Sans Serif", 9f, System.Drawing.FontStyle.Bold);
		this.lblAmount.ForeColor = System.Drawing.Color.OrangeRed;
		this.lblAmount.Location = new System.Drawing.Point(4, 36);
		this.lblAmount.Name = "lblAmount";
		this.lblAmount.Size = new System.Drawing.Size(326, 21);
		this.lblAmount.TabIndex = 36;
		this.lblAmount.Text = "Amount : 9999";
		this.lblAmount.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
		this.btn_changepid.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
		this.btn_changepid.BackColor = System.Drawing.Color.White;
		this.btn_changepid.Font = new System.Drawing.Font("Microsoft Sans Serif", 9f, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, 0);
		this.btn_changepid.Location = new System.Drawing.Point(69, 306);
		this.btn_changepid.Name = "btn_changepid";
		this.btn_changepid.Size = new System.Drawing.Size(75, 23);
		this.btn_changepid.TabIndex = 37;
		this.btn_changepid.Text = "Đổi PID";
		this.btn_changepid.UseVisualStyleBackColor = false;
		this.btn_changepid.Click += new System.EventHandler(button1_Click);
		this.tbox_changepid.Location = new System.Drawing.Point(160, 306);
		this.tbox_changepid.Name = "tbox_changepid";
		this.tbox_changepid.Size = new System.Drawing.Size(91, 20);
		this.tbox_changepid.TabIndex = 38;
		this.tbox_changepid.Visible = false;
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		this.BackColor = System.Drawing.Color.FromArgb(64, 64, 64);
		base.ClientSize = new System.Drawing.Size(334, 363);
		base.Controls.Add(this.tbox_changepid);
		base.Controls.Add(this.btn_changepid);
		base.Controls.Add(this.lblAmount);
		base.Controls.Add(this.lblGender);
		base.Controls.Add(this.lblItemSeri);
		base.Controls.Add(this.lblPID);
		base.Controls.Add(this.btnDelete);
		base.Controls.Add(this.btnDong);
		base.Controls.Add(this.lblKhoa);
		base.Controls.Add(this.lblTrungCap);
		base.Controls.Add(this.lblTinhNgo);
		base.Controls.Add(this.lblThuocTinh);
		base.Controls.Add(this.lblOption4);
		base.Controls.Add(this.lblOption3);
		base.Controls.Add(this.lblOption2);
		base.Controls.Add(this.lblOption1);
		base.Controls.Add(this.lblZx);
		base.Controls.Add(this.lblLevel);
		base.Controls.Add(this.lblItem);
		this.ForeColor = System.Drawing.SystemColors.ControlText;
		base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
		base.Name = "ItemInfo";
		base.Opacity = 0.8;
		this.Text = "ItemInfo";
		base.ResumeLayout(false);
		base.PerformLayout();
	}
}
