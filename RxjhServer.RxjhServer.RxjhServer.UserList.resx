﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
<resheader name="resmimetype"><value>text/microsoft-resx</value></resheader><resheader name="version"><value>1.3</value></resheader><resheader name="reader"><value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></resheader><resheader name="writer"><value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></resheader><data name="&gt;&gt;columnHeader5.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader5.Name" xml:space="preserve"><value>columnHeader5</value></data>
  <data name="&gt;&gt;columnHeader4.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader4.Name" xml:space="preserve"><value>columnHeader4</value></data>
  <data name="&gt;&gt;columnHeader7.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader7.Name" xml:space="preserve"><value>columnHeader7</value></data>
  <data name="&gt;&gt;columnHeader6.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader6.Name" xml:space="preserve"><value>columnHeader6</value></data>
  <data name="&gt;&gt;columnHeader1.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader1.Name" xml:space="preserve"><value>columnHeader1</value></data>
  <data name="&gt;&gt;columnHeader3.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader3.Name" xml:space="preserve"><value>columnHeader3</value></data>
  <data name="&gt;&gt;columnHeader2.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader2.Name" xml:space="preserve"><value>columnHeader2</value></data>
  <data name="columnHeader4.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>113</value></data>
  <data name="$this.Text" xml:space="preserve"><value>UserList</value></data>
  <data name="columnHeader17.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>37</value></data>
  <data name="columnHeader18.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>113</value></data>
  <data name="menuItem2.Text" xml:space="preserve"><value>退人ID</value></data>
  <data name="menuItem3.Text" xml:space="preserve"><value>封IP</value></data>
  <data name="columnHeader9.Text" xml:space="preserve"><value>HP</value></data>
  <data name="columnHeader8.Text" xml:space="preserve"><value>Y</value></data>
  <data name="menuItem1.Text" xml:space="preserve"><value>退人</value></data>
  <data name="$this.ClientSize" mimetype="application/x-microsoft.net.object.binary.base64"><value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABNTeXN0ZW0uRHJhd2luZy5TaXplAgAAAAV3aWR0aAZoZWlnaHQAAAgIAgAAAAwEAAB0AQAACw==</value></data><data name="menuItem6.Text" xml:space="preserve"><value>禁/解言</value></data>
  <data name="listView1.Location" mimetype="application/x-microsoft.net.object.binary.base64"><value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABRTeXN0ZW0uRHJhd2luZy5Qb2ludAIAAAABeAF5AAAICAIAAAAAAAAAAAAAAAs=</value></data><data name="menuItem7.Text" xml:space="preserve"><value>1天</value></data>
  <data name="columnHeader5.Text" xml:space="preserve"><value>序号</value></data>
  <data name="menuItem4.Text" xml:space="preserve"><value>封ID</value></data>
  <data name="columnHeader4.Text" xml:space="preserve"><value>IP</value></data>
  <data name="menuItem5.Text" xml:space="preserve"><value>查数据</value></data>
  <data name="columnHeader7.Text" xml:space="preserve"><value>X</value></data>
  <data name="columnHeader6.Text" xml:space="preserve"><value>地图</value></data>
  <data name="columnHeader1.Text" xml:space="preserve"><value>ID</value></data>
  <data name="menuItem8.Text" xml:space="preserve"><value>3天</value></data>
  <data name="columnHeader13.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>39</value></data>
  <data name="menuItem9.Text" xml:space="preserve"><value>7天</value></data>
  <data name="columnHeader3.Text" xml:space="preserve"><value>等级</value></data>
  <data name="columnHeader14.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>41</value></data>
  <data name="columnHeader2.Text" xml:space="preserve"><value>名字</value></data>
  <data name="listView1.Size" mimetype="application/x-microsoft.net.object.binary.base64"><value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABNTeXN0ZW0uRHJhd2luZy5TaXplAgAAAAV3aWR0aAZoZWlnaHQAAAgIAgAAAAwEAAB0AQAACw==</value></data><data name="listView1.Dock" mimetype="application/x-microsoft.net.object.binary.base64"><value>AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAAB5TeXN0ZW0uV2luZG93cy5Gb3Jtcy5Eb2NrU3R5bGUBAAAAB3ZhbHVlX18ACAIAAAAFAAAACw==</value></data><data name="$this.AutoScaleBaseSize" mimetype="application/x-microsoft.net.object.binary.base64"><value>AAEAAAD/////AQAAAAAAAAAMAgAAAFFTeXN0ZW0uRHJhd2luZywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWIwM2Y1ZjdmMTFkNTBhM2EFAQAAABNTeXN0ZW0uRHJhd2luZy5TaXplAgAAAAV3aWR0aAZoZWlnaHQAAAgIAgAAAAYAAAAOAAAACw==</value></data><data name="columnHeader7.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>61</value></data>
  <data name="&gt;&gt;menuItem2.Type" xml:space="preserve"><value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;menuItem2.Name" xml:space="preserve"><value>menuItem2</value></data>
  <data name="columnHeader10.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>36</value></data>
  <data name="&gt;&gt;menuItem3.Type" xml:space="preserve"><value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;menuItem3.Name" xml:space="preserve"><value>menuItem3</value></data>
  <data name="columnHeader19.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>42</value></data>
  <data name="&gt;&gt;menuItem1.Type" xml:space="preserve"><value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;menuItem1.Name" xml:space="preserve"><value>menuItem1</value></data>
  <data name="&gt;&gt;menuItem6.Type" xml:space="preserve"><value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;menuItem6.Name" xml:space="preserve"><value>menuItem6</value></data>
  <data name="&gt;&gt;menuItem7.Type" xml:space="preserve"><value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;menuItem7.Name" xml:space="preserve"><value>menuItem7</value></data>
  <data name="&gt;&gt;menuItem4.Type" xml:space="preserve"><value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;menuItem4.Name" xml:space="preserve"><value>menuItem4</value></data>
  <data name="&gt;&gt;menuItem5.Type" xml:space="preserve"><value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;menuItem5.Name" xml:space="preserve"><value>menuItem5</value></data>
  <data name="&gt;&gt;listView1.Parent" xml:space="preserve"><value>$this</value></data>
  <data name="&gt;&gt;menuItem8.Type" xml:space="preserve"><value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;menuItem8.Name" xml:space="preserve"><value>menuItem8</value></data>
  <data name="&gt;&gt;menuItem9.Type" xml:space="preserve"><value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;menuItem9.Name" xml:space="preserve"><value>menuItem9</value></data>
  <data name="columnHeader3.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>38</value></data>
  <data name="&gt;&gt;listView1.ZOrder" xml:space="preserve"><value>0</value></data>
  <data name="&gt;&gt;menuItem10.Name" xml:space="preserve"><value>menuItem10</value></data>
  <data name="&gt;&gt;menuItem10.Type" xml:space="preserve"><value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="columnHeader15.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>39</value></data>
  <data name="&gt;&gt;menuItem11.Name" xml:space="preserve"><value>menuItem11</value></data>
  <data name="&gt;&gt;menuItem11.Type" xml:space="preserve"><value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader18.Name" xml:space="preserve"><value>columnHeader18</value></data>
  <data name="&gt;&gt;columnHeader18.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader19.Name" xml:space="preserve"><value>columnHeader19</value></data>
  <data name="&gt;&gt;columnHeader19.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="columnHeader6.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>45</value></data>
  <data name="columnHeader9.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>47</value></data>
  <data name="columnHeader11.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>37</value></data>
  <data name="&gt;&gt;columnHeader14.Name" xml:space="preserve"><value>columnHeader14</value></data>
  <data name="&gt;&gt;columnHeader14.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader15.Name" xml:space="preserve"><value>columnHeader15</value></data>
  <data name="&gt;&gt;columnHeader15.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader16.Name" xml:space="preserve"><value>columnHeader16</value></data>
  <data name="&gt;&gt;columnHeader16.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader17.Name" xml:space="preserve"><value>columnHeader17</value></data>
  <data name="&gt;&gt;columnHeader17.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader10.Name" xml:space="preserve"><value>columnHeader10</value></data>
  <data name="&gt;&gt;columnHeader10.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader11.Name" xml:space="preserve"><value>columnHeader11</value></data>
  <data name="&gt;&gt;columnHeader11.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader12.Name" xml:space="preserve"><value>columnHeader12</value></data>
  <data name="&gt;&gt;columnHeader12.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader13.Name" xml:space="preserve"><value>columnHeader13</value></data>
  <data name="&gt;&gt;columnHeader13.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve"><value>UserList</value></data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve"><value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="columnHeader2.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>98</value></data>
  <data name="columnHeader5.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>36</value></data>
  <data name="menuItem10.Text" xml:space="preserve"><value>1月</value></data>
  <data name="columnHeader18.Text" xml:space="preserve"><value>绑定帐号</value></data>
  <data name="$this.StartPosition" mimetype="application/x-microsoft.net.object.binary.base64"><value>AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0ZW0uV2luZG93cy5Gb3Jtcy5Gb3JtU3RhcnRQb3NpdGlvbgEAAAAHdmFsdWVfXwAIAgAAAAEAAAAL</value></data><data name="columnHeader16.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>39</value></data>
  <data name="columnHeader19.Text" xml:space="preserve"><value>挂机</value></data>
  <data name="menuItem11.Text" xml:space="preserve"><value>永久</value></data>
  <data name="listView1.TabIndex" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>1</value></data>
  <data name="columnHeader14.Text" xml:space="preserve"><value>攻加</value></data>
  <data name="columnHeader15.Text" xml:space="preserve"><value>防加</value></data>
  <data name="columnHeader16.Text" xml:space="preserve"><value>攻强</value></data>
  <data name="columnHeader17.Text" xml:space="preserve"><value>防强</value></data>
  <data name="columnHeader10.Text" xml:space="preserve"><value>攻</value></data>
  <data name="columnHeader11.Text" xml:space="preserve"><value>防</value></data>
  <data name="&gt;&gt;contextMenu1.Type" xml:space="preserve"><value>System.Windows.Forms.ContextMenu, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;contextMenu1.Name" xml:space="preserve"><value>contextMenu1</value></data>
  <data name="columnHeader12.Text" xml:space="preserve"><value>ping</value></data>
  <data name="columnHeader13.Text" xml:space="preserve"><value>气</value></data>
  <data name="&gt;&gt;listView1.Type" xml:space="preserve"><value>System.Windows.Forms.ListView, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;listView1.Name" xml:space="preserve"><value>listView1</value></data>
  <data name="columnHeader1.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>66</value></data>
  <data name="columnHeader8.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>61</value></data>
  <data name="columnHeader12.Width" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"><value>39</value></data>
  <data name="&gt;&gt;columnHeader9.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader9.Name" xml:space="preserve"><value>columnHeader9</value></data>
  <data name="&gt;&gt;columnHeader8.Type" xml:space="preserve"><value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></data>
  <data name="&gt;&gt;columnHeader8.Name" xml:space="preserve"><value>columnHeader8</value></data>
  </root>