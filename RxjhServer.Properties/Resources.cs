using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace RxjhServer.Properties;

[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
[DebuggerNonUserCode]
[CompilerGenerated]
internal class Resources
{
	private static ResourceManager resourceMan;

	private static CultureInfo resourceCulture;

	[EditorBrowsable(EditorBrowsableState.Advanced)]
	internal static ResourceManager ResourceManager
	{
		get
		{
			if (resourceMan == null)
			{
				ResourceManager temp = new ResourceManager("RxjhServer.Properties.Resources", typeof(Resources).Assembly);
				resourceMan = temp;
			}
			return resourceMan;
		}
	}

	[EditorBrowsable(EditorBrowsableState.Advanced)]
	internal static CultureInfo Culture
	{
		get
		{
			return resourceCulture;
		}
		set
		{
			resourceCulture = value;
		}
	}

	internal static Bitmap npc_window_store01
	{
		get
		{
			object obj = ResourceManager.GetObject("npc_window_store01", resourceCulture);
			return (Bitmap)obj;
		}
	}

	internal static Bitmap npc_window_store02
	{
		get
		{
			object obj = ResourceManager.GetObject("npc_window_store02", resourceCulture);
			return (Bitmap)obj;
		}
	}

	internal static Bitmap window_inventory_bag
	{
		get
		{
			object obj = ResourceManager.GetObject("window_inventory_bag", resourceCulture);
			return (Bitmap)obj;
		}
	}

	internal static Bitmap window_inventory_sub_bag
	{
		get
		{
			object obj = ResourceManager.GetObject("window_inventory_sub_bag", resourceCulture);
			return (Bitmap)obj;
		}
	}

	internal static Bitmap window_inventory_sub_bag1
	{
		get
		{
			object obj = ResourceManager.GetObject("window_inventory_sub_bag1", resourceCulture);
			return (Bitmap)obj;
		}
	}

	internal static Bitmap window_pet_inven
	{
		get
		{
			object obj = ResourceManager.GetObject("window_pet_inven", resourceCulture);
			return (Bitmap)obj;
		}
	}

	internal Resources()
	{
	}
}
