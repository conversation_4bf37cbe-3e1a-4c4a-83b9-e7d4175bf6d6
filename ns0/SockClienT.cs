using System;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using RxjhServer;
using RxjhServer.HelperTools;

namespace ns0;

public class SockClienT
{
	public Socket clientSocket;

	private byte[] dataReceive;

	private bool bool_0;

	private MemoryStream memoryStream_0;

	private RemoveClientDelegateE removeClientDelegateE_0;

	[CompilerGenerated]
	private MessageeDelegaterE messageeDelegaterE_0;

	public bool Disposed => bool_0;

	public IPAddress IP
	{
		get
		{
			try
			{
				IPAddress result = null;
				while (true)
				{
					object obj;
					switch (bool_0 ? 1 : 4)
					{
					case 1:
						obj = null;
						goto IL_0052;
					case 2:
					case 4:
						obj = ((IPEndPoint)clientSocket.RemoteEndPoint).Address;
						goto IL_0052;
					default:
						continue;
					case 3:
						break;
						IL_0052:
						result = (IPAddress)obj;
						break;
					}
					break;
				}
				return result;
			}
			catch
			{
				Dispose();
			}
			return null;
		}
	}

	public event MessageeDelegaterE Event_0
	{
		[CompilerGenerated]
		add
		{
			MessageeDelegaterE messageeDelegaterE = null;
			MessageeDelegaterE messageeDelegaterE2 = messageeDelegaterE_0;
			do
			{
				messageeDelegaterE = messageeDelegaterE2;
				MessageeDelegaterE value2 = (MessageeDelegaterE)Delegate.Combine(messageeDelegaterE, value);
				messageeDelegaterE2 = Interlocked.CompareExchange(ref messageeDelegaterE_0, value2, messageeDelegaterE);
			}
			while ((object)messageeDelegaterE2 != messageeDelegaterE);
		}
		[CompilerGenerated]
		remove
		{
			MessageeDelegaterE messageeDelegaterE = null;
			MessageeDelegaterE messageeDelegaterE2 = messageeDelegaterE_0;
			do
			{
				messageeDelegaterE = messageeDelegaterE2;
				MessageeDelegaterE value2 = (MessageeDelegaterE)Delegate.Remove(messageeDelegaterE, value);
				messageeDelegaterE2 = Interlocked.CompareExchange(ref messageeDelegaterE_0, value2, messageeDelegaterE);
			}
			while ((object)messageeDelegaterE2 != messageeDelegaterE);
		}
	}

	public SockClienT(Socket socket_0, RemoveClientDelegateE removeClientDelegateE_1)
	{
		dataReceive = new byte[1500];
		bool_0 = false;
		memoryStream_0 = new MemoryStream();
		removeClientDelegateE_0 = removeClientDelegateE_1;
		clientSocket = socket_0;
	}

	public void Dispose()
	{
		if (!bool_0)
		{
			bool_0 = true;
			try
			{
				removeClientDelegateE_0?.Invoke(this);
				clientSocket.Shutdown(SocketShutdown.Both);
			}
			catch
			{
			}
			clientSocket?.Close();
			clientSocket = null;
		}
	}

	public virtual void OnReceiveData(IAsyncResult iasyncResult_0)
	{
		try
		{
			int num = 0;
			if (!bool_0)
			{
				num = clientSocket.EndReceive(iasyncResult_0);
				if (num <= 0)
				{
					Dispose();
					return;
				}
				ProcessDataReceived(dataReceive, num);
				Dispose();
			}
		}
		catch (Exception)
		{
			Dispose();
		}
	}

	public void OnSended(IAsyncResult iasyncResult_0)
	{
		try
		{
			if (!bool_0)
			{
				clientSocket.EndSend(iasyncResult_0);
				clientSocket.BeginReceive((iasyncResult_0.AsyncState as SockClienT).dataReceive, 0, (iasyncResult_0.AsyncState as SockClienT).dataReceive.Length, SocketFlags.None, OnReceiveData, iasyncResult_0.AsyncState);
			}
		}
		catch
		{
			Dispose();
		}
	}

	public void OnSended2(IAsyncResult iasyncResult_0)
	{
		try
		{
			if (!bool_0)
			{
				clientSocket.EndSend(iasyncResult_0);
			}
		}
		catch (Exception)
		{
			Dispose();
		}
	}

	public virtual void ProcessDataReceived(byte[] byte_0, int int_0)
	{
	}

	public virtual void Sendd(string string_0)
	{
		byte[] bytes = Encoding.Default.GetBytes(string_0);
		Send(bytes, bytes.Length);
	}

	public virtual void Send(string string_0)
	{
		try
		{
			int int_ = 0;
			if (!bool_0)
			{
				byte[] array = new byte[string_0.Length];
				Converter.ToBytes(string_0, array, ref int_);
				clientSocket.BeginSend(array, 0, int_, SocketFlags.None, OnSended2, this);
			}
		}
		catch (Exception)
		{
			Dispose();
		}
	}

	public virtual void Send(byte[] byte_0, int int_0)
	{
		try
		{
			if (!bool_0)
			{
				byte[] array = new byte[int_0 + 6];
				array[0] = 170;
				array[1] = 102;
				System.Buffer.BlockCopy(BitConverter.GetBytes(int_0), 0, array, 2, 4);
				System.Buffer.BlockCopy(byte_0, 0, array, 6, int_0);
				clientSocket.BeginSend(array, 0, array.Length, SocketFlags.None, OnSended2, this);
			}
		}
		catch (Exception)
		{
			Dispose();
		}
	}

	public virtual void Send(byte[] byte_0, int int_0, int int_1)
	{
		try
		{
			byte[] array = null;
			if (!bool_0)
			{
				array = new byte[int_1];
				System.Buffer.BlockCopy(byte_0, int_0, array, 0, int_1);
				if (!bool_0)
				{
					clientSocket.BeginSend(array, 0, int_1, SocketFlags.None, OnSended2, this);
				}
			}
		}
		catch (Exception)
		{
			Dispose();
		}
	}

	public void Start()
	{
		clientSocket.BeginReceive(dataReceive, 0, dataReceive.Length, SocketFlags.None, OnReceiveData, this);
	}

	public void RaiseMessageEvent(string string_0)
	{
		int num = 0;
		while (true)
		{
			switch (num)
			{
			case 1:
				return;
			case 2:
				messageeDelegaterE_0(string_0, this);
				num = 1;
				continue;
			}
			if (messageeDelegaterE_0 != null)
			{
				num = 2;
				continue;
			}
			return;
		}
	}
}
