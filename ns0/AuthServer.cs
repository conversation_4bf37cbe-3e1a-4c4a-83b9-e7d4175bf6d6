using System;
using System.Collections;
using System.Net;
using System.Net.Sockets;
using System.Runtime.CompilerServices;
using System.Threading;
using RxjhServer;

namespace ns0;

public class AuthServer
{
	public static ArrayList clients = new ArrayList();

	private string string_0;

	private Socket socket_0;

	private int int_0;

	public int totalReceive;

	public int totalSend;

	[CompilerGenerated]
	private MessageeDelegaterE messageeDelegaterE_0;

	public event MessageeDelegaterE Event_0
	{
		[CompilerGenerated]
		add
		{
			MessageeDelegaterE messageeDelegaterE = null;
			MessageeDelegaterE messageeDelegaterE2 = messageeDelegaterE_0;
			do
			{
				messageeDelegaterE = messageeDelegaterE2;
				MessageeDelegaterE value2 = (MessageeDelegaterE)Delegate.Combine(messageeDelegaterE, value);
				messageeDelegaterE2 = Interlocked.CompareExchange(ref messageeDelegaterE_0, value2, messageeDelegaterE);
			}
			while ((object)messageeDelegaterE2 != messageeDelegaterE);
		}
		[CompilerGenerated]
		remove
		{
			MessageeDelegaterE messageeDelegaterE = null;
			MessageeDelegaterE messageeDelegaterE2 = messageeDelegaterE_0;
			do
			{
				messageeDelegaterE = messageeDelegaterE2;
				MessageeDelegaterE value2 = (MessageeDelegaterE)Delegate.Remove(messageeDelegaterE, value);
				messageeDelegaterE2 = Interlocked.CompareExchange(ref messageeDelegaterE_0, value2, messageeDelegaterE);
			}
			while ((object)messageeDelegaterE2 != messageeDelegaterE);
		}
	}

	public AuthServer(string string_1, int int_1)
	{
		totalSend = 0;
		totalReceive = 0;
		string_0 = string_1;
		int_0 = int_1;
		Start();
	}

	public void CloseServer()
	{
		socket_0.Close();
	}

	public void Dispose()
	{
		while (clients.Count > 0)
		{
			((SockClienT)clients[0]).Dispose();
		}
		try
		{
			socket_0.Shutdown(SocketShutdown.Both);
		}
		catch
		{
		}
		socket_0?.Close();
	}

	public virtual void OnAccept(IAsyncResult iasyncResult_0)
	{
		try
		{
			Socket socket = socket_0.EndAccept(iasyncResult_0);
			if (socket != null)
			{
				ClientConnection clientConnection = new ClientConnection(socket, RemoveClient);
				clients.Add(clientConnection);
				clientConnection.Event_0 += method_0;
				clientConnection.Start();
			}
		}
		catch (Exception arg)
		{
			Console.WriteLine("{0}", arg);
		}
		try
		{
			socket_0.BeginAccept(OnAccept, socket_0);
		}
		catch
		{
			Dispose();
		}
	}

	public void RemoveClient(SockClienT sockClienT_0)
	{
		Lock @lock = new Lock(clients, "RemoveClient");
		try
		{
			clients.Remove(sockClienT_0);
		}
		finally
		{
			int num = 1;
			while (true)
			{
				switch (num)
				{
				case 2:
					break;
				case 0:
					((IDisposable)@lock).Dispose();
					num = 2;
					continue;
				default:
					if (@lock != null)
					{
						num = 0;
						continue;
					}
					break;
				}
				break;
			}
		}
	}

	public bool Start()
	{
		try
		{
			socket_0 = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
			socket_0.Bind(new IPEndPoint(IPAddress.Any, int_0));
			Form1.WriteLine(6, "Bắt đầu nghe lén Bách Bảo cảng " + int_0 + " IP " + string_0.ToString());
			socket_0.Listen(10);
			socket_0.BeginAccept(OnAccept, socket_0);
		}
		catch (Exception ex)
		{
			Console.WriteLine("Failled to list on port {0}\n{1}", int_0, ex.Message);
			Form1.WriteLine(1, "Nghe lén Bách Bảo cảng phạm sai lầm " + int_0 + " " + ex.Message);
			socket_0 = null;
			return false;
		}
		return true;
	}

	private void method_0(object object_0, SockClienT sockClienT_0)
	{
		method_1(object_0, sockClienT_0);
	}

	private void method_1(object object_0, SockClienT sockClienT_0)
	{
		int num = 1;
		while (true)
		{
			switch (num)
			{
			case 2:
				return;
			case 0:
				messageeDelegaterE_0(object_0, sockClienT_0);
				num = 2;
				continue;
			}
			if (messageeDelegaterE_0 != null)
			{
				num = 0;
				continue;
			}
			return;
		}
	}
}
