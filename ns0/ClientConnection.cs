using System;
using System.Net.Sockets;
using System.Text;
using RxjhServer;

namespace ns0;

public class ClientConnection : SockClienT
{
	public ClientConnection(Socket socket_0, RemoveClientDelegateE removeClientDelegateE_1)
		: base(socket_0, removeClientDelegateE_1)
	{
	}

	public override void ProcessDataReceived(byte[] byte_0, int int_0)
	{
		int num2 = 0;
		byte[] array = null;
		int num3 = 0;
		if (170 == byte_0[0] && 102 == byte_0[1])
		{
			array = new byte[4];
			System.Buffer.BlockCopy(byte_0, 2, array, 0, 4);
			num2 = BitConverter.ToInt32(array, 0);
			if (int_0 < num2 + 6)
			{
				return;
			}
			while (true)
			{
				if (World.jlMsg == 1)
				{
					Form1.WriteLine(0, "Xử lý dữ liệu đã nhận");
				}
				byte[] array2 = new byte[num2];
				System.Buffer.BlockCopy(byte_0, num3 + 6, array2, 0, num2);
				num3 += num2 + 6;
				DataReceived(array2, num2);
				if (num3 < int_0 && byte_0[num3] == 170 && byte_0[num3 + 1] == 102)
				{
					System.Buffer.BlockCopy(byte_0, num3 + 2, array, 0, 4);
					num2 = BitConverter.ToInt16(array, 0);
					continue;
				}
				break;
			}
		}
		else
		{
			Form1.WriteLine(1, "Sai bao: " + byte_0[0] + " " + byte_0[1]);
		}
	}

	public void DataReceived(byte[] byte_1, int int_0)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "Xử lý dữ liệu đã nhận");
		}
		try
		{
			byte[] array = new byte[int_0];
			for (int i = 0; i < int_0; i++)
			{
				array[i] = byte_1[i];
			}
			string @string = Encoding.Default.GetString(array);
			string string_ = "-1";
			string[] array2 = @string.Split(',');
			string text = array2[0];
			if (text == null)
			{
				goto IL_0291;
			}
			if (text == "UserLoginSuperAbnormaljc")
			{
				string_ = ((World.KiemTraNguoiChoi(array2[1]) != null) ? "LandedSuccessfully" : "FailedToLogin");
			}
			else if (text != "QuerySuperVariableBaibao")
			{
				if (!(text == "BuyUltraChangeableTreasures"))
				{
					goto IL_0291;
				}
				Players players = World.KiemTraNguoiChoi(array2[1]);
				if (long.Parse(array2[4]) >= 0 && int.Parse(array2[3]) >= 1)
				{
					X_Bach_Bao_Cac_Loai value;
					if (players == null)
					{
						string_ = "-1";
					}
					else if (World.BachBaoCat_ThuocTinhVatPhamClassList.TryGetValue(int.Parse(array2[2]), out value))
					{
						string_ = players.PakTreasureCourtBuyAndSellThings(int.Parse(array2[2]), int.Parse(array2[3]), int.Parse(array2[4]), int.Parse(array2[5]), value.MAGIC0, value.MAGIC1, value.MAGIC2, value.MAGIC3, value.MAGIC4, value.TrungCapHon, value.ThucTinh, value.TienHoa, value.KhoaLai, value.NgaySuDung);
					}
				}
				else
				{
					string_ = "-1";
					players.Dispose();
				}
			}
			else
			{
				Players players2 = World.KiemTraNguoiChoi(array2[1]);
				if (players2 == null)
				{
					string_ = "-1";
				}
				else if (array2[2] == "cash")
				{
					players2.CheckTheNumberOfIngotsInBaibaoge();
					string_ = players2.FLD_RXPIONT.ToString();
				}
				else if (array2[2] == "cash2")
				{
					players2.CheckTheNumberOfIngotsInBaibaoge();
					string_ = players2.FLD_RXPIONTX.ToString();
				}
				else if (array2[2] == "empty")
				{
					string_ = players2.GetParcelVacancyNumber().ToString();
				}
			}
			goto IL_0286;
			IL_0291:
			string_ = "-1";
			goto IL_0286;
			IL_0286:
			Sendd(string_);
		}
		catch (Exception)
		{
			Dispose();
		}
	}
}
