using System;
using System.IO;
using System.Text;

namespace RxjhServer.Network;

public class PacketReader
{
	private byte[] m_Data;

	private int int_0;

	private int int_1;

	public byte[] Buffer => m_Data;

	public int Size => int_0;

	public PacketReader(byte[] byte_0, int int_2, bool bool_0)
	{
		m_Data = byte_0;
		int_0 = int_2;
		int_1 = (bool_0 ? 1 : 3);
	}

	public void Trace(NetState netState_0)
	{
		try
		{
			StreamWriter streamWriter = new StreamWriter("Packets.log", append: true);
			try
			{
				MemoryStream memoryStream = null;
				byte[] data = m_Data;
				int num2 = 4;
				if (data.Length != 0)
				{
					num2 = 1;
					streamWriter.WriteLine("Client: {0}: Unhandled 包ID 0x{1:X2}{2:X2} 包长{3} 时间{4}", netState_0, data[8], data[7], data.Length, DateTime.Now);
					num2 = 0;
				}
				memoryStream = new MemoryStream(data);
				num2 = 2;
				try
				{
					Utility.FormatBuffer(streamWriter, memoryStream, data.Length);
				}
				finally
				{
					num2 = 2;
					while (true)
					{
						switch (num2)
						{
						case 1:
							break;
						case 0:
							((IDisposable)memoryStream).Dispose();
							num2 = 1;
							continue;
						default:
							if (memoryStream != null)
							{
								num2 = 0;
								continue;
							}
							break;
						}
						break;
					}
				}
				streamWriter.WriteLine();
				streamWriter.WriteLine();
				num2 = 3;
			}
			finally
			{
				int num3 = 2;
				while (true)
				{
					switch (num3)
					{
					case 1:
						break;
					case 0:
						((IDisposable)streamWriter).Dispose();
						num3 = 1;
						continue;
					default:
						if (streamWriter != null)
						{
							num3 = 0;
							continue;
						}
						break;
					}
					break;
				}
			}
		}
		catch
		{
		}
	}

	public int Seek(int int_2, SeekOrigin seekOrigin_0)
	{
		int num = 5;
		while (true)
		{
			switch (num)
			{
			case 0:
				num = 3;
				continue;
			case 1:
			case 2:
			case 3:
			case 4:
				return int_1;
			}
			switch (seekOrigin_0)
			{
			default:
				num = 0;
				break;
			case SeekOrigin.Begin:
				int_1 = int_2;
				num = 1;
				break;
			case SeekOrigin.Current:
				int_1 += int_2;
				num = 2;
				break;
			case SeekOrigin.End:
				int_1 = int_0 - int_2;
				num = 4;
				break;
			}
		}
	}

	public int ReadInt32()
	{
		if (int_1 + 4 > int_0)
		{
			return 0;
		}
		return m_Data[int_1++] | (m_Data[int_1++] << 8) | (m_Data[int_1++] << 16) | (m_Data[int_1++] << 24);
	}

	public int ReadInt16()
	{
		if (int_1 + 2 > int_0)
		{
			return 0;
		}
		return m_Data[int_1++] | (m_Data[int_1++] << 8);
	}

	public int ReadInt8()
	{
		if (int_1 + 1 > int_0)
		{
			return 0;
		}
		return m_Data[int_1++];
	}

	public float Readfloat()
	{
		if (int_1 + 4 > int_0)
		{
			return 0f;
		}
		double num = BitConverter.ToSingle(m_Data, int_1);
		int_1 += 4;
		return (float)num;
	}

	public uint ReadUInt32()
	{
		if (int_1 + 4 > int_0)
		{
			return 0u;
		}
		return (uint)(m_Data[int_1++] | (m_Data[int_1++] << 8) | (m_Data[int_1++] << 16) | (m_Data[int_1++] << 24));
	}

	public ushort ReadUInt16()
	{
		if (int_1 + 2 > int_0)
		{
			return 0;
		}
		return (ushort)(m_Data[int_1++] | (m_Data[int_1++] << 8));
	}

	public byte ReadByte()
	{
		if (int_1 + 1 > int_0)
		{
			return 0;
		}
		return m_Data[int_1++];
	}

	public sbyte ReadSByte()
	{
		if (int_1 + 1 > int_0)
		{
			return 0;
		}
		return (sbyte)m_Data[int_1++];
	}

	public bool ReadBoolean()
	{
		if (int_1 + 1 <= int_0)
		{
			return m_Data[int_1++] > 0;
		}
		return false;
	}

	public string ReadUnicodeStringLE()
	{
		int num = 0;
		byte b = 0;
		int num2 = 0;
		StringBuilder stringBuilder = new StringBuilder();
		while (int_1 + 1 < int_0)
		{
			b = m_Data[int_1++];
			num2 = m_Data[int_1++] << 8;
			if ((num = b | num2) == 0)
			{
				break;
			}
			stringBuilder.Append((char)num);
		}
		return stringBuilder.ToString();
	}

	public string ReadUnicodeStringLESafe(int int_2)
	{
		StringBuilder stringBuilder = null;
		int num = 0;
		byte b = 0;
		int num2 = 0;
		int num3 = int_1 + (int_2 << 1);
		int num4 = num3;
		if (num3 > int_0)
		{
			num3 = int_0;
		}
		stringBuilder = new StringBuilder();
		while (int_1 + 1 < num3)
		{
			b = m_Data[int_1++];
			num2 = m_Data[int_1++] << 8;
			if ((num = b | num2) == 0)
			{
				break;
			}
			if (IsSafeChar(num))
			{
				stringBuilder.Append((char)num);
			}
		}
		int_1 = num4;
		return stringBuilder.ToString();
	}

	public string ReadUnicodeStringLESafe()
	{
		int num = 0;
		byte b = 0;
		int num2 = 0;
		StringBuilder stringBuilder = new StringBuilder();
		while (int_1 + 1 < int_0)
		{
			b = m_Data[int_1++];
			num2 = m_Data[int_1++] << 8;
			if ((num = b | num2) == 0)
			{
				break;
			}
			if (IsSafeChar(num))
			{
				stringBuilder.Append((char)num);
			}
		}
		return stringBuilder.ToString();
	}

	public string ReadUnicodeStringSafe()
	{
		int num = 0;
		int num2 = 0;
		int num3 = 0;
		StringBuilder stringBuilder = new StringBuilder();
		while (int_1 + 1 < int_0)
		{
			num2 = m_Data[int_1++] << 8;
			num3 = m_Data[int_1++];
			if ((num = num2 | num3) == 0)
			{
				break;
			}
			if (IsSafeChar(num))
			{
				stringBuilder.Append((char)num);
			}
		}
		return stringBuilder.ToString();
	}

	public string ReadUnicodeString()
	{
		int num = 0;
		int num2 = 0;
		int num3 = 0;
		StringBuilder stringBuilder = new StringBuilder();
		while (int_1 + 1 < int_0)
		{
			num = m_Data[int_1++] << 8;
			num2 = m_Data[int_1++];
			if ((num3 = num | num2) == 0)
			{
				break;
			}
			stringBuilder.Append((char)num3);
		}
		return stringBuilder.ToString();
	}

	public bool IsSafeChar(int int_2)
	{
		if (int_2 >= 32)
		{
			return int_2 < 65534;
		}
		return false;
	}

	public string ReadUTF8StringSafe(int int_2)
	{
		int num3 = 0;
		string text = null;
		byte[] array = null;
		bool flag = false;
		int num4 = 0;
		int num5 = 0;
		int num6 = 0;
		int num7 = 0;
		int num8 = 0;
		StringBuilder stringBuilder = null;
		int num9 = 0;
		bool flag2 = true;
		while (int_1 < int_0)
		{
			num3 = int_1 + int_2;
			while (true)
			{
				IL_040f:
				if (num3 > int_0)
				{
					goto IL_0060;
				}
				goto IL_0406;
				IL_0406:
				while (true)
				{
					IL_0406_2:
					num7 = 0;
					num9 = int_1;
					num8 = int_1;
					while (true)
					{
						bool flag3 = true;
						while (true)
						{
							IL_03e0:
							bool flag4 = true;
							while (true)
							{
								IL_0097:
								if (num9 < num3)
								{
									goto IL_00ab;
								}
								goto IL_03d0;
								IL_03d0:
								while (true)
								{
									IL_03d0_2:
									num6 = 0;
									array = new byte[num7];
									while (true)
									{
										bool flag5 = true;
										int num2;
										while (true)
										{
											IL_03a4:
											bool flag6 = true;
											while (true)
											{
												IL_00f0:
												if (int_1 < num3)
												{
													goto IL_0108;
												}
												goto IL_0394;
												IL_0394:
												while (true)
												{
													IL_0394_2:
													text = Utility.UTF8.GetString(array);
													flag = true;
													num4 = 0;
													while (true)
													{
														bool flag7 = true;
														while (true)
														{
															IL_035f:
															bool flag8 = true;
															while (true)
															{
																IL_0163:
																if (flag)
																{
																	goto IL_0173;
																}
																goto IL_034f;
																IL_034f:
																while (true)
																{
																	IL_034f_2:
																	int_1 = num8 + int_2;
																	bool flag9 = true;
																	while (true)
																	{
																		IL_01a3:
																		if (!flag)
																		{
																			stringBuilder = new StringBuilder(text.Length);
																			num5 = 0;
																			bool flag10 = true;
																			while (true)
																			{
																				switch ((num5 >= text.Length) ? 11 : 27)
																				{
																				case 7:
																					goto IL_0060;
																				case 9:
																					goto IL_0097;
																				case 0:
																				case 8:
																					goto IL_00ab;
																				case 28:
																					goto IL_00f0;
																				case 3:
																				case 30:
																					goto IL_0108;
																				case 19:
																					goto IL_0163;
																				case 20:
																				case 29:
																					goto IL_0173;
																				case 1:
																					goto IL_01a3;
																				case 27:
																					if (IsSafeChar(text[num5]))
																					{
																						goto case 18;
																					}
																					goto case 4;
																				case 18:
																					stringBuilder.Append(text[num5]);
																					goto case 4;
																				case 4:
																					num5++;
																					continue;
																				case 11:
																					return stringBuilder.ToString();
																				case 13:
																				case 17:
																				case 21:
																					continue;
																				case 14:
																					goto IL_033c;
																				case 22:
																					goto IL_034f_2;
																				case 10:
																				case 24:
																					goto IL_035f;
																				case 2:
																					goto IL_0394_2;
																				case 23:
																				case 25:
																					goto IL_03a4;
																				case 26:
																					goto IL_03d0_2;
																				case 12:
																				case 31:
																					goto IL_03e0;
																				case 15:
																					goto IL_0406_2;
																				case 16:
																					goto IL_040f;
																				case 5:
																					goto end_IL_0024;
																				}
																				break;
																			}
																			break;
																		}
																		goto IL_033c;
																		IL_033c:
																		return text;
																	}
																	break;
																}
																break;
																IL_0173:
																if (num4 < text.Length)
																{
																	goto IL_0373;
																}
																goto IL_034f;
															}
															break;
														}
														break;
														IL_0373:
														flag = IsSafeChar(text[num4]);
														num4++;
													}
													break;
												}
												break;
												IL_0108:
												if ((num2 = m_Data[int_1++]) != 0)
												{
													goto IL_03b8;
												}
												goto IL_0394;
											}
											break;
										}
										break;
										IL_03b8:
										array[num6++] = (byte)num2;
									}
									break;
								}
								break;
								IL_00ab:
								if (m_Data[num9++] != 0)
								{
									goto IL_03f4;
								}
								goto IL_03d0;
							}
							break;
						}
						break;
						IL_03f4:
						num7++;
					}
					break;
				}
				break;
				IL_0060:
				num3 = int_0;
				goto IL_0406;
			}
			continue;
			end_IL_0024:
			break;
		}
		int_1 += int_2;
		return string.Empty;
	}

	public string ReadUTF8StringSafe()
	{
		int num2 = 0;
		string text = null;
		bool flag = false;
		byte[] array = null;
		int num3 = 0;
		StringBuilder stringBuilder = null;
		int num4 = 0;
		int num5 = 0;
		int num6 = 0;
		bool flag2 = true;
		while (int_1 < int_0)
		{
			num6 = 0;
			num4 = int_1;
			while (true)
			{
				bool flag3 = true;
				while (true)
				{
					IL_0386:
					bool flag4 = true;
					while (true)
					{
						IL_0054:
						if (num4 < int_0)
						{
							goto IL_006d;
						}
						goto IL_036d;
						IL_036d:
						while (true)
						{
							IL_036d_2:
							num5 = 0;
							array = new byte[num6];
							while (true)
							{
								bool flag5 = true;
								int num7;
								while (true)
								{
									IL_0341:
									bool flag6 = true;
									while (true)
									{
										IL_00b2:
										if (int_1 < int_0)
										{
											goto IL_00cf;
										}
										goto IL_0331;
										IL_0331:
										while (true)
										{
											IL_0331_2:
											text = Utility.UTF8.GetString(array);
											flag = true;
											num2 = 0;
											while (true)
											{
												bool flag7 = true;
												while (true)
												{
													IL_02ff:
													bool flag8 = true;
													while (true)
													{
														IL_0129:
														if (flag)
														{
															goto IL_0139;
														}
														goto IL_02ef;
														IL_02ef:
														while (true)
														{
															IL_02ef_2:
															bool flag9 = true;
															while (true)
															{
																IL_015e:
																if (!flag)
																{
																	stringBuilder = new StringBuilder(text.Length);
																	num3 = 0;
																	bool flag10 = true;
																	while (true)
																	{
																		switch ((num3 >= text.Length) ? 16 : 24)
																		{
																		case 14:
																			goto IL_0054;
																		case 1:
																		case 21:
																			goto IL_006d;
																		case 20:
																			goto IL_00b2;
																		case 7:
																		case 28:
																			goto IL_00cf;
																		case 8:
																			goto IL_0129;
																		case 4:
																		case 27:
																			goto IL_0139;
																		case 19:
																			goto IL_015e;
																		case 24:
																			if (IsSafeChar(text[num3]))
																			{
																				goto case 15;
																			}
																			goto case 9;
																		case 15:
																			stringBuilder.Append(text[num3]);
																			goto case 9;
																		case 9:
																			num3++;
																			continue;
																		case 16:
																			return stringBuilder.ToString();
																		case 2:
																		case 13:
																		case 18:
																			continue;
																		case 10:
																			goto IL_02dc;
																		case 12:
																			goto IL_02ef_2;
																		case 6:
																		case 11:
																			goto IL_02ff;
																		case 25:
																			goto IL_0331_2;
																		case 23:
																		case 26:
																			goto IL_0341;
																		case 5:
																			goto IL_036d_2;
																		case 17:
																		case 22:
																			goto IL_0386;
																		case 0:
																			goto end_IL_001e;
																		}
																		break;
																	}
																	break;
																}
																goto IL_02dc;
																IL_02dc:
																return text;
															}
															break;
														}
														break;
														IL_0139:
														if (num2 < text.Length)
														{
															goto IL_0313;
														}
														goto IL_02ef;
													}
													break;
												}
												break;
												IL_0313:
												flag = IsSafeChar(text[num2]);
												num2++;
											}
											break;
										}
										break;
										IL_00cf:
										if ((num7 = m_Data[int_1++]) != 0)
										{
											goto IL_0355;
										}
										goto IL_0331;
									}
									break;
								}
								break;
								IL_0355:
								array[num5++] = (byte)num7;
							}
							break;
						}
						break;
						IL_006d:
						if (m_Data[num4++] != 0)
						{
							goto IL_038e;
						}
						goto IL_036d;
					}
					break;
				}
				break;
				IL_038e:
				num6++;
			}
			continue;
			end_IL_001e:
			break;
		}
		return string.Empty;
	}

	public string ReadUTF8String()
	{
		int num = 0;
		int num2 = 0;
		int num3 = 0;
		byte[] array = null;
		if (int_1 >= int_0)
		{
			return string.Empty;
		}
		num2 = 0;
		num = int_1;
		while (num < int_0 && m_Data[num++] != 0)
		{
			num2++;
		}
		num3 = 0;
		array = new byte[num2];
		int num4;
		while (int_1 < int_0 && (num4 = m_Data[int_1++]) != 0)
		{
			array[num3++] = (byte)num4;
		}
		return Utility.UTF8.GetString(array);
	}

	public string ReadString()
	{
		StringBuilder stringBuilder = new StringBuilder();
		int num;
		while (int_1 < int_0 && (num = m_Data[int_1++]) != 0)
		{
			stringBuilder.Append((char)num);
		}
		return stringBuilder.ToString();
	}

	public string ReadStringSafe()
	{
		int num = 0;
		bool flag = true;
		while (true)
		{
			StringBuilder stringBuilder = new StringBuilder();
			while (true)
			{
				IL_00eb:
				bool flag2 = true;
				while (true)
				{
					IL_001c:
					if (int_1 < int_0)
					{
						bool flag3 = true;
						while (true)
						{
							switch (((num = m_Data[int_1++]) != 0) ? 3 : 0)
							{
							case 6:
								goto IL_001c;
							case 3:
								if (!IsSafeChar(num))
								{
									goto IL_00eb;
								}
								goto case 2;
							case 2:
								stringBuilder.Append((char)num);
								goto IL_00eb;
							case 1:
							case 5:
								continue;
							case 0:
								goto IL_00d7;
							case 4:
							case 7:
								goto IL_00eb;
							}
							break;
						}
						break;
					}
					goto IL_00d7;
					IL_00d7:
					return stringBuilder.ToString();
				}
				break;
			}
		}
	}

	public string ReadUnicodeStringSafe(int int_2)
	{
		int num = 0;
		StringBuilder stringBuilder = null;
		int num2 = 0;
		int num3 = 0;
		int num4 = int_1 + (int_2 << 1);
		int num5 = num4;
		if (num4 > int_0)
		{
			num4 = int_0;
		}
		stringBuilder = new StringBuilder();
		while (int_1 + 1 < num4)
		{
			num2 = m_Data[int_1++] << 8;
			num3 = m_Data[int_1++];
			if ((num = num2 | num3) == 0)
			{
				break;
			}
			if (IsSafeChar(num))
			{
				stringBuilder.Append((char)num);
			}
		}
		int_1 = num5;
		return stringBuilder.ToString();
	}

	public string ReadUnicodeString(int int_2)
	{
		StringBuilder stringBuilder = null;
		int num = 0;
		int num2 = 0;
		int num3 = 0;
		int num4 = int_1 + (int_2 << 1);
		int num5 = num4;
		if (num4 > int_0)
		{
			num4 = int_0;
		}
		stringBuilder = new StringBuilder();
		while (int_1 + 1 < num4)
		{
			num2 = m_Data[int_1++] << 8;
			num3 = m_Data[int_1++];
			if ((num = num2 | num3) == 0)
			{
				break;
			}
			stringBuilder.Append((char)num);
		}
		int_1 = num5;
		return stringBuilder.ToString();
	}

	public string ReadStringSafe(int int_2)
	{
		StringBuilder stringBuilder = null;
		int num2 = 0;
		bool flag = true;
		while (true)
		{
			int num3 = int_1 + int_2;
			int num4 = num3;
			while (true)
			{
				IL_0162:
				if (num3 > int_0)
				{
					goto IL_0031;
				}
				goto IL_0159;
				IL_0159:
				while (true)
				{
					IL_0159_2:
					stringBuilder = new StringBuilder();
					bool flag2 = true;
					while (true)
					{
						IL_0145:
						bool flag3 = true;
						while (true)
						{
							IL_0055:
							if (int_1 < num3)
							{
								bool flag4 = true;
								while (true)
								{
									switch (((num2 = m_Data[int_1++]) != 0) ? 1 : 5)
									{
									case 6:
										goto IL_0031;
									case 3:
										goto IL_0055;
									case 1:
										if (!IsSafeChar(num2))
										{
											goto IL_0145;
										}
										goto case 8;
									case 8:
										stringBuilder.Append((char)num2);
										goto IL_0145;
									case 4:
									case 7:
										continue;
									case 5:
										goto IL_0133;
									case 9:
									case 10:
										goto IL_0145;
									case 2:
										goto IL_0159_2;
									case 0:
										goto IL_0162;
									}
									break;
								}
								break;
							}
							goto IL_0133;
							IL_0133:
							int_1 = num4;
							return stringBuilder.ToString();
						}
						break;
					}
					break;
				}
				break;
				IL_0031:
				num3 = int_0;
				goto IL_0159;
			}
		}
	}

	public string ReadString(int int_2)
	{
		StringBuilder stringBuilder = null;
		int num = int_1 + int_2;
		int num2 = num;
		if (num > int_0)
		{
			num = int_0;
		}
		stringBuilder = new StringBuilder();
		int num3;
		while (int_1 < num && (num3 = m_Data[int_1++]) != 0)
		{
			stringBuilder.Append((char)num3);
		}
		int_1 = num2;
		return stringBuilder.ToString();
	}
}
