using RxjhServer.DbClss;
using System;
using System.Timers;

namespace RxjhServer.Network;

public class NetState : IDisposable
{
    public byte[] g_cur_key;

    public int Key2;

    public DateTime Ljtime;

    private IntPtr _ConnId;

    private string _BindAccount;

    private string _port;

    public bool Online;

    public bool Login;

    public bool Encryption;

    public bool Discconect;

    private TcpServer Server;

    private int _WorldId;

    private bool _挂机;

    private Players _Player;

    public ByteQueue m_Buffer;

    private bool m_Running;

    private string m_ToString;

    public bool VersionVerification;

    private System.Timers.Timer _自动断开;

    public int dwStop;

    public bool 退出;

    public IntPtr ConnId
    {
        get
        {
            return _ConnId;
        }
        set
        {
            _ConnId = value;
        }
    }

    public int WorldId
    {
        get
        {
            return _WorldId;
        }
        set
        {
            _WorldId = value;
        }
    }

    public bool TreoMay
    {
        get
        {
            return _挂机;
        }
        set
        {
            _挂机 = value;
        }
    }

    public string BindAccount
    {
        get
        {
            return _BindAccount;
        }
        set
        {
            _BindAccount = value;
        }
    }

    public Players Player
    {
        get
        {
            return _Player;
        }
        set
        {
            _Player = value;
        }
    }

    private ByteQueue mBuffer => m_Buffer;

    public bool Running
    {
        get
        {
            return m_Running;
        }
        set
        {
            m_Running = value;
        }
    }

    public System.Timers.Timer AutoDisconnect
    {
        get
        {
            return _自动断开;
        }
        set
        {
            _自动断开 = value;
        }
    }

    public string port
    {
        get
        {
            return _port;
        }
        set
        {
            _port = value;
        }
    }

    public override string ToString()
    {
        return m_ToString;
    }

    public void delWorldIdd(int WId)
    {
        try
        {
            if (World.InConnectList(WId))
            {
                World.list[WId] = null;
                World.list.Remove(WId);
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "delWorldIdd Phạm sai lầm" + WorldId + "|" + ToString() + "" + ex.Message); //Form1.WriteLine(1, "delWorldIdd()出错" + WorldId + "|" + ToString() + " " + ex.Message);
        }
    }

    ~NetState()
    {
    }

    public NetState(ClientInfo Ci)
    {
        g_cur_key = new byte[32]
        {
            4,
            55,
            194,
            8,
            236,
            235,
            63,
            151,
            52,
            131,
            91,
            17,
            67,
            248,
            106,
            144,
            193,
            231,
            211,
            232,
            92,
            237,
            241,
            255,
            89,
            139,
            13,
            129,
            51,
            65,
            89,
            137
        };
        Key2 = **********;
        Ljtime = DateTime.Now;
        _ConnId = IntPtr.Zero;
        _BindAccount = string.Empty;
        _port = string.Empty;
        try
        {
            ConnId = Ci.ConnId;
            Server = Ci.Server;
            Ljtime = DateTime.Now;
            TreoMay = false;
            m_Buffer = new ByteQueue();
            m_Running = false;
            Discconect = false;
            m_ToString = Ci.IpAddress;
            port = Ci.Port.ToString();
            //port = World.AtPort.ToString();
            WorldId = Ci.WorldId;
            //Form1.WriteLine(5, "Check NetState : " + WorldId);
            if (!World.InConnectList(WorldId))
            {
                World.list.Add(WorldId, this);
            }
            else
            {
                Form1.WriteLine(3, WorldId + "Cắt ra số thẻ kết nối " + m_ToString + ":" + port); //Form1.WriteLine(3, WorldId + " 断开卡号连接 " + m_ToString + ":" + port);
                Server.Disconnect(ConnId);
            }
            Player = new Players(this);
            AutoDisconnect = new System.Timers.Timer(3000.0);
            AutoDisconnect.Elapsed += AutoDisconnectEvent;
            AutoDisconnect.AutoReset = true;
            AutoDisconnect.Enabled = true;
            Form1.WriteLine(3, "Kết nối ID[" + ConnId.ToInt32() + "]- Toàn bộ server ID[" + WorldId + "]-IP Địa chỉ [" + m_ToString + ":" + port + "]"); //Form1.WriteLine(3, "连接 连接ID[" + ConnId.ToInt32() + "]-全服ID[" + WorldId + "]-IP地址[" + m_ToString + ":" + port + "]");
        }
        catch (Exception ex)
        {
            Server.Disconnect(ConnId);
            Form1.WriteLine(3, WorldId + "Ban đầu hóa hộ khách bưng kết nối phạm sai lầm " + m_ToString + ":" + port + "|" + ex.Message); //Form1.WriteLine(3, WorldId + " 初始化客户端连接出错 " + m_ToString + ":" + port + "|" + ex.Message);
        }
    }

    private void AutoDisconnectEvent(object sender, ElapsedEventArgs e)
    {
        if (!VersionVerification)
        {
            Form1.WriteLine(1, WorldId + "Tự động cắt ra sự kiện " + ToString()); //Form1.WriteLine(1, WorldId + " 自动断开事件 " + ToString());
            Dispose(true);
        }
        if (AutoDisconnect != null)
        {
            AutoDisconnect.Enabled = false;
            AutoDisconnect.Close();
            AutoDisconnect.Dispose();
            AutoDisconnect = null;
        }
    }

    private void reflashClient()
    {
        var server = Server;
        var connId = ConnId;
        var clientInfo = new ClientInfo();
        clientInfo.ConnId = ConnId;
        clientInfo.IpAddress = ToString();
        clientInfo.Port = ushort.Parse(port);
        clientInfo.Server = Server;
        clientInfo.WorldId = WorldId;
        clientInfo.Client = this;
        server.SetConnectionExtra(connId, clientInfo);
    }

    public Players Start()
    {
        var num = 2;
        while (true)
        {
            switch (num)
            {
                case 1:
                    return null;
                case 0:
                    m_Running = true;
                    reflashClient();
                    num = 1;
                    continue;
            }
            if (Player != null)
            {
                num = 0;
                continue;
            }
            return Player;
        }
    }

    public void IDout()
    {
        try
        {
            if (Player != null)
            {
                World.conn.Transmit("用户登出|" + Player.Userid + "|" + World.ServerID);
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "IDout Phạm sai lầm" + WorldId + "|" + ToString() + " " + ex.Message); //Form1.WriteLine(1, "IDout()出错" + WorldId + "|" + ToString() + " " + ex.Message);
        }
    }

    public void HandleReceive(NetState netState_0)
    {
        try
        {
            if (mBuffer != null && mBuffer.Length > 0)
            {
                using (new Lock(mBuffer, "HandleReceive"))
                {
                    int length = mBuffer.Length;
                    while (true)
                    {
                        if (length <= 0 || !Running || length <= 4)
                        {
                            return;
                        }
                        int num = Buffer.ToInt16(mBuffer.GetPacketID(), 0);
                        if (num <= 0)
                        {
                            mBuffer.Clear();
                            return;
                        }
                        int num2 = num + 6;
                        if (length < num2)
                        {
                            break;
                        }
                        byte[] array = new byte[num2];
                        mBuffer.Dequeue(array, 0, num2);
                        length = mBuffer.Length;
                        if (170 != array[0] || 85 != array[1])
                        {
                            mBuffer.Clear();
                            return;
                        }
                        if (array[num2 - 2] != 85 || array[num2 - 1] != 170)
                        {
                            mBuffer.Clear();
                            return;
                        }
                        Player.ManagePacket(array, array.Length);
                    }
                    if (170 != mBuffer.m_Buffer[0] && 85 != mBuffer.m_Buffer[1])
                    {
                        mBuffer.Clear();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "HandleReceive()error" + WorldId + "|" + ToString() + " " + ex?.ToString());
            Server.Disconnect(ConnId);
        }
    }

    public void Send_Map_Data(byte[] byte_0, int int_1)
    {
        byte[] array = new byte[int_1 + 2];
        array[0] = 170;
        array[1] = 85;
        System.Buffer.BlockCopy(Buffer.GetBytes(int_1 - 4), 0, array, 2, 2);
        System.Buffer.BlockCopy(byte_0, 4, array, 4, 2);
        System.Buffer.BlockCopy(byte_0, 6, array, 8, int_1 - 8);
        array[array.Length - 2] = 85;
        array[array.Length - 1] = 170;
        Send(array, array.Length);
    }

    public void Send(byte[] bytes, int int_0)
    {
        if (m_Running/* && !TreoMay*/)
        {
            Server.Send(ConnId, bytes, int_0);
        }
    }





    public void Send单包(byte[] toSendBuff, int int_0)
    {
        var array = new byte[Buffer.ToInt16(toSendBuff, 9) + 7];
        System.Buffer.BlockCopy(toSendBuff, 5, array, 0, array.Length);
        Send单包封装发送(array, array.Length);
    }

    private void Send单包封装发送(byte[] toSendBuff, int length)
    {
        var array = new byte[length + 15];
        array[0] = 170;
        array[1] = 85;
        System.Buffer.BlockCopy(Buffer.GetBytes(length + 9), 0, array, 2, 2);
        System.Buffer.BlockCopy(toSendBuff, 0, array, 5, length);
        array[array.Length - 2] = 85;
        array[array.Length - 1] = 170;
        Send_Map_Data(array, array.Length);
    }

    public void SendTest(byte[] toSendBuff, int int_0)// new
    {
        var array = new byte[Buffer.ToInt16(toSendBuff, 8) + 6];
        System.Buffer.BlockCopy(toSendBuff, 4, array, 0, array.Length);
        SendMultiplePackageEncryption(array, array.Length, 1);
    }

    public void SendMultiplePackage(byte[] toSendBuff, int int_0)// this
    {
         Send_Map_Data(toSendBuff, int_0);
    }

    //private void SendMultiplePackageEncryption(byte[] toSendBuff, int length, int int_0)
    //{
    //	int num;
    //	num = 4;
    //	try
    //	{
    //		int num2;
    //		byte[] packetBuffer;
    //		num2 = SendDuopak3(toSendBuff, length, out packetBuffer);
    //		byte[] array;
    //		array = new byte[num2 + 8];
    //		System.Buffer.BlockCopy(Buffer.GetBytes(192), 0, array, 0, 2);
    //		System.Buffer.BlockCopy(Buffer.GetBytes(num2 + 4), 0, array, 2, 2);
    //		System.Buffer.BlockCopy(Buffer.GetBytes(num2), 0, array, 4, 2);
    //		System.Buffer.BlockCopy(Buffer.GetBytes(length), 0, array, 6, 2);
    //		System.Buffer.BlockCopy(packetBuffer, 0, array, 8, num2);
    //		SendMultiplePackage_PackageTransmit(array, array.Length, int_0);
    //	}
    //	catch (Exception ex)
    //	{
    //		Form1.WriteLine(1, "Send_Send Nhiều bao mã hóa gửi đi sai lầm" + WorldId + "|" + ex.Message); //Form1.WriteLine(1, "Send()_Send多包加密发送错误" + WorldId + "|" + ex.Message);
    //	}
    //}

    private void SendMultiplePackageEncryption(byte[] toSendBuff, int length, int xl)
    {

        try
        {
            int num = SendDuopak3(toSendBuff, length, out byte[] packetBuffer);
            byte[] array = new byte[num + 8];

            Buffer.BlockCopy(BitConverter.GetBytes(588), 0, array, 0, 2);
            Buffer.BlockCopy(BitConverter.GetBytes(num + 4), 0, array, 2, 2);
            Buffer.BlockCopy(BitConverter.GetBytes(num), 0, array, 4, 2);
            Buffer.BlockCopy(BitConverter.GetBytes(length), 0, array, 6, 2);
            Buffer.BlockCopy(packetBuffer, 0, array, 8, num);

            SendMultiplePackage_PackageTransmit(array, array.Length, xl);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, $"Send()_SendMultiplePackageEncryptionTransmit Error {WorldId} | {ex.Message}");
        }
    }
    /* private void SendMultiplePackageEncryption(byte[] toSendBuff, int length, int xl)
     {
         try
         {
             string bytejoin = "";
             for (int i = 0; i < toSendBuff.Length; i++)
             {
                 bytejoin = bytejoin + " " + toSendBuff[i];
             }
             byte[] packetBuffer;
             int num = SendDuopak3(toSendBuff, length, out packetBuffer);
             string string11 = HelperTools.Converter.ToString(toSendBuff);
             string string111 = HelperTools.Converter.ToString(packetBuffer);
             byte[] array = new byte[num + 8];
             System.Buffer.BlockCopy(Buffer.GetBytes(588), 0, array, 0, 2);
             System.Buffer.BlockCopy(Buffer.GetBytes(num + 4), 0, array, 2, 2);
             System.Buffer.BlockCopy(Buffer.GetBytes(num), 0, array, 4, 2);
             System.Buffer.BlockCopy(Buffer.GetBytes(length), 0, array, 6, 2);
             System.Buffer.BlockCopy(packetBuffer, 0, array, 8, num);
             string string1111 = HelperTools.Converter.ToString(array);
             SendMultiplePackage_PackageTransmit(array, array.Length, xl);
             //logo.CheckData("SendMultiplePackageEncryption [" + array.Length + "] : " + Converter.ToString(array));
         }
         catch (Exception ex)
         {
             Form1.WriteLine(1, "Send()_SendMultiplePackageEncryptionTransmit错误" + WorldId + "|" + ex.Message);
         }
     }
    */

    private void SendMultiplePackage_PackageTransmit(byte[] toSendBuff, int length, int int_0)
    {
        var array = new byte[length + 16];
        array[0] = 170;
        array[1] = 85;
        System.Buffer.BlockCopy(Buffer.GetBytes(length + 10), 0, array, 2, 2);
        System.Buffer.BlockCopy(Buffer.GetBytes(int_0), 0, array, 4, 2);
        System.Buffer.BlockCopy(toSendBuff, 0, array, 6, length);
        array[array.Length - 2] = 85;
        array[array.Length - 1] = 170;
        Send_Map_Data(array, array.Length);
    }

    public void Offline()
    {
        if (Server.Disconnect(ConnId))
        {
            退出 = true;
            TreoMay = true;
            World.OffLine_SoLuong++;
            Form1.WriteLine(3, WorldId + " Offline treo máy" + ToString()); //Form1.WriteLine(3, WorldId + " TreoMay " + ToString());
        }
        else
        {
            Form1.WriteLine(3, WorldId + "Offline treo máy sai lầm " + ToString()); //Form1.WriteLine(3, WorldId + " TreoMay错误 " + ToString());
        }
    }

    public void DisposedOffline()
    {
        try
        {
            int num = ConnId.ToInt32();
            int worldId = WorldId;
            string text = ToString();
            TreoMay = false;
            退出 = true;
            m_Running = false;
            if (m_Buffer != null)
            {
                m_Buffer.Dispose();
                m_Buffer = null;
            }
            if (AutoDisconnect != null)
            {
                AutoDisconnect.Enabled = false;
                AutoDisconnect.Close();
                AutoDisconnect.Dispose();
                AutoDisconnect = null;
            }
            IDout();
            if (Player != null)
            {
                Player.Logout();
                Player.Dispose();
            }
            Players value;
            if (World.allConnectedChars.TryGetValue(WorldId, out value))
            {
                World.allConnectedChars.Remove(WorldId);
            }
            Player = null;
            delWorldIdd(WorldId);
            int num2 = 18;
            Form1.WriteLine(3, "退出TreoMay 连接ID[" + num + "]CharacterFullServerID[" + worldId + "] IP地址[" + text + "] ExceStep:" + num2);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, " ProcessDisposedOffline()error" + WorldId + "|" + ToString() + " " + ex.Message);
        }
    }

    public void logout(SocketOperation enOperation, int errorCode)
    {
        var num2 = 0;
        string _userid = "";
        try
        {
            var num3 = 0;
            var num4 = 0;
            string text = null;
            num2 = 1;
            NetState value;
            if (!World.list.TryGetValue(WorldId, out value))
            {
                return;
            }
            num2 = 2;
            if (TreoMay)
            {
                return;
            }
            num2 = 3;
            num3 = ConnId.ToInt32();
            num4 = WorldId;
            text = ToString() + ":" + port;
            TreoMay = false;
            退出 = true;
            m_Running = false;
            num2 = 4;
            if (m_Buffer != null)
            {
                m_Buffer.Dispose();
                m_Buffer = null;
            }
            num2 = 5;
            if (AutoDisconnect != null)
            {
                AutoDisconnect.Enabled = false;
                AutoDisconnect.Close();
                AutoDisconnect.Dispose();
                AutoDisconnect = null;
            }
            num2 = 6;
            if (Online)
            {
                IDout();
                num2 = 7;
            }
            num2 = 8;
            if (Player != null)
            {
                _userid = Player.Userid;
                num2 = 9;
                if (!Player.Exiting)
                {
                    num2 = 10;
                    Player.Logout();
                    num2 = 11;
                }
                num2 = 12;
                Player.Dispose();
                num2 = 13;
            }
            num2 = 14;
            Players value2;
            if (World.allConnectedChars.TryGetValue(WorldId, out value2))
            {
                num2 = 15;
                World.allConnectedChars.Remove(WorldId);
                num2 = 16;
            }
            num2 = 17;
            Player = null;
            delWorldIdd(WorldId);
            num2 = 18;
            RxjhClass.DeleteUserLogin(_userid);
            Form1.WriteLine(3, "Rời khỏi Kết nối ID[" + num3 + "] Nhân vật toàn bộ server ID[" + num4 + "] IP Địa chỉ [" + text + "]ExceStep:" + 18); //Form1.WriteLine(3, "退出 连接ID[" + num3 + "]人物全服ID[" + num4 + "] IP地址[" + text + "] ExceStep:" + 18);
        }
        catch
        {
            Form1.WriteLine(3, "Rời khỏi sai lầm ExceStep:" + num2); //Form1.WriteLine(3, "退出错误 ExceStep:" + num2);
        }
    }

    public void Dispose()
    {
        Dispose(true);
    }

    private void Dispose(bool flush)
    {
        try
        {
            if (!Discconect)
            {
                Discconect = true;
                Server.Disconnect(ConnId);
            }
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Dispose Phạm sai lầm" + WorldId + "|" + ToString() + " " + ex.Message); //Form1.WriteLine(1, " Dispose()出错" + WorldId + "|" + ToString() + " " + ex.Message);
        }
    }

    public void SendPak11(SendingClass 发包类_0, int int_0, int wordid)
    {
        try
        {
            var array = 发包类_0.ToArray2(int_0, wordid);
            SendMultiplePackageEncryption(array, array.Length, 1);
        }
        catch (Exception)
        {
        }
    }

    public void SendPak(SendingClass 发包类_0, int int_0, int wordid)
    {
        try
        {
            byte[] array = 发包类_0.ToArray2(int_0, wordid);
            SendMultiplePackageEncryption(array, array.Length, 1);
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, $"Error in SendPak: {ex.Message}");
        }
    }

    private int SendDuopak3(byte[] 进包, int 进包长, out byte[] packetBuffer)
    {
        packetBuffer = new byte[进包长 + 进包长 / 32 + 1];
        var 出包长 = 0;
        var 进包处理长 = 0;
        var i = 6;
        var num = 5;
        var j = 0;
        var num2 = 0;
        var num3 = 0;
        try
        {
            for (; i <= 进包长; i++)
            {
                while (num > 1 && i - num < 255)
                {
                    for (; j < 255 && num + j < 进包长 && i + j < 进包长; j++)
                    {
                        if (进包[i + j] != 进包[num + j])
                        {
                            if (num2 >= 3)
                            {
                                if (进包[i] == 进包[num - 1] && i - num < 254)
                                {
                                    num3 = 1;
                                    break;
                                }
                                OneByte(i, num, j, num2, 进包, ref 进包处理长, ref 出包长, ref packetBuffer);
                                i = 进包处理长;
                                num = 进包处理长;
                                num3 = 0;
                                num2 = 0;
                            }
                            else
                            {
                                num2 = 0;
                            }
                            break;
                        }
                        num2++;
                    }
                    if (num2 >= 3 && (进包[i] != 进包[num - 1] || num3 == 0))
                    {
                        OneByte(i, num, j, num2, 进包, ref 进包处理长, ref 出包长, ref packetBuffer);
                        i = 进包处理长;
                        num = 进包处理长;
                        num3 = 0;
                    }
                    j = 0;
                    num2 = 0;
                    num--;
                }
                num = i;
            }
            if (进包处理长 >= 进包长)
            {
                return 出包长;
            }

            var num4 = 进包长 - 进包处理长;
            if (num4 <= 32)
            {
                if (num4 <= 0)
                {
                    return 出包长;
                }
                packetBuffer[出包长++] = (byte)(进包长 - 进包处理长 - 1);
                System.Buffer.BlockCopy(进包, 进包处理长, packetBuffer, 出包长, 进包长 - 进包处理长);
                出包长 += 进包长 - 进包处理长;
                return 出包长;
            }

            var outlength = 0;
            var array = new byte[num4];
            System.Buffer.BlockCopy(进包, 进包处理长, array, 0, array.Length);
            var array2 = SendDuopak(array, array.Length, out outlength);
            if (出包长 == 0)
            {
                packetBuffer = new byte[array2.Length];
            }
            System.Buffer.BlockCopy(array2, 0, packetBuffer, 出包长, outlength);
            出包长 += outlength;
            return 出包长;
        }
        catch (Exception value)
        {
            Console.WriteLine(value);
            return 出包长;
        }
    }

    private void OneByte(int j, int i, int n, int x, byte[] 进包, ref int 进包处理长, ref int 出包长, ref byte[] packetBuffer)
    {
        var num = j - 进包处理长;
        if (num > 32)
        {
            var outlength = 0;
            var array = new byte[num];
            System.Buffer.BlockCopy(进包, 进包处理长, array, 0, array.Length);
            System.Buffer.BlockCopy(SendDuopak(array, array.Length, out outlength), 0, packetBuffer, 出包长, outlength);
            出包长 += outlength;
            进包处理长 += num;
        }
        else if (num > 0)
        {
            packetBuffer[出包长++] = (byte)(num - 1);
            System.Buffer.BlockCopy(进包, 进包处理长, packetBuffer, 出包长, j - 进包处理长);
            出包长 += j - 进包处理长;
            进包处理长 += j - 进包处理长;
        }

        var num2 = x - 2;
        if (num2 < 7)
        {
            packetBuffer[出包长++] = (byte)(num2 << 5);
            var num3 = j - i - 1;
            packetBuffer[出包长++] = (byte)num3;
        }
        else
        {
            packetBuffer[出包长++] = 224;
            var num4 = x - 2 - 7;
            packetBuffer[出包长++] = (byte)num4;
            var num5 = j - i - 1;
            packetBuffer[出包长++] = (byte)num5;
        }
        进包处理长 += x;
    }

    private byte[] SendDuopak(byte[] toSendBuff, int length, out int outlength)
    {
        try
        {
            var num = 10;
            var num2 = length / 10;
            while (length - num2 * num > 0 && length - num2 * num < 2)
            {
                num++;
                num2 = length / num;
            }
            if (length % num > 0)
            {
                num2++;
            }

            var array = new byte[length + num2];
            var num3 = 0;
            var num4 = 0;
            var num5 = num;
            do
            {
                if (num3 + num5 < length)
                {
                    array[num4] = (byte)(num5 - 1);
                    System.Buffer.BlockCopy(toSendBuff, num3, array, num4 + 1, num5);
                    num3 += num5;
                    num4 += num5 + 1;
                    num5 = num;
                }
                else
                {
                    num5 = length - num3;
                    array[num4] = (byte)(num5 - 1);
                    System.Buffer.BlockCopy(toSendBuff, num3, array, num4 + 1, num5);
                    num3 += num5;
                    num4 += num5 + 1;
                }
            }
            while (num3 < length);
            outlength = num4;
            return array;
        }
        catch (Exception ex)
        {
            Form1.WriteLine(1, "Send Send Duopak" + WorldId + "|" + ex.Message); //Form1.WriteLine(1, "Send()SendDuopak" + WorldId + "|" + ex.Message);
            outlength = length;
            return toSendBuff;
        }
    }
}