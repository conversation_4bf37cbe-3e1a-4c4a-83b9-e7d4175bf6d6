using System;

namespace RxjhServer.Network;

public class ByteQueue : IDisposable
{
	private int m_Head;

	private int m_Tail;

	private int m_Size;

	public byte[] m_Buffer;

	public int Length => m_Size;

	public ByteQueue()
	{
		m_Buffer = new byte[2048];
	}

	public void Dispose()
	{
		m_Buffer = null;
	}

	public void Clear()
	{
		m_Head = 0;
		m_Tail = 0;
		m_Size = 0;
	}

	private void SetCapacity(int capacity)
	{
		byte[] array = new byte[capacity];
		if (m_Size > 0)
		{
			if (m_Head < m_Tail)
			{
				System.Buffer.BlockCopy(m_Buffer, m_Head, array, 0, m_Size);
			}
			else
			{
				System.Buffer.BlockCopy(m_Buffer, m_Head, array, 0, m_Buffer.Length - m_Head);
				System.Buffer.BlockCopy(m_Buffer, 0, array, m_Buffer.Length - m_Head, m_Tail);
			}
		}
		m_Head = 0;
		m_Tail = m_Size;
		m_Buffer = array;
	}

	public byte[] GetPacketID()
	{
		byte[] array = new byte[2];
		try
		{
			System.Buffer.BlockCopy(m_Buffer, m_Head + 2, array, 0, 2);
			return array;
		}
		catch (Exception)
		{
			return array;
		}
	}

	public byte GetPacketIDd()
	{
		if (m_Size >= 1)
		{
			return m_Buffer[m_Head];
		}
		return byte.MaxValue;
	}

	public int GetPacketLength()
	{
		if (m_Size >= 3)
		{
			return (m_Buffer[(m_Head + 1) % m_Buffer.Length] << 8) | m_Buffer[(m_Head + 2) % m_Buffer.Length];
		}
		return 0;
	}

	public int Dequeue(byte[] buffer, int offset, int size)
	{
		int num2 = 0;
		bool flag = true;
		while (true)
		{
			if (size > m_Size)
			{
				goto IL_001c;
			}
			goto IL_018c;
			IL_018c:
			while (true)
			{
				IL_018c_2:
				bool flag2 = true;
				while (true)
				{
					switch ((size == 0) ? 8 : 2)
					{
					case 11:
						goto IL_001c;
					case 2:
						if (m_Head < m_Tail)
						{
							goto case 10;
						}
						num2 = m_Buffer.Length - m_Head;
						goto case 9;
					case 8:
						return 0;
					case 9:
						if (num2 >= size)
						{
							goto case 4;
						}
						System.Buffer.BlockCopy(m_Buffer, m_Head, buffer, offset, num2);
						System.Buffer.BlockCopy(m_Buffer, 0, buffer, offset + num2, size - num2);
						goto case 1;
					case 4:
						System.Buffer.BlockCopy(m_Buffer, m_Head, buffer, offset, size);
						goto case 1;
					case 10:
						System.Buffer.BlockCopy(m_Buffer, m_Head, buffer, offset, size);
						goto case 1;
					case 1:
					case 3:
					case 5:
						m_Head = (m_Head + size) % m_Buffer.Length;
						m_Size -= size;
						goto case 0;
					case 0:
						if (m_Size == 0)
						{
							goto case 6;
						}
						goto case 13;
					case 6:
						m_Head = 0;
						m_Tail = 0;
						goto case 13;
					case 13:
						return size;
					case 7:
						continue;
					case 14:
						goto IL_018c_2;
					}
					break;
				}
				break;
			}
			continue;
			IL_001c:
			size = m_Size;
			goto IL_018c;
		}
	}

	public void Enqueue(byte[] buffer, int offset, int size)
	{
		int num = 0;
		if (m_Size + size > m_Buffer.Length)
		{
			SetCapacity((m_Size + size + 2047) & -2048);
		}
		if (m_Head < m_Tail)
		{
			num = m_Buffer.Length - m_Tail;
			if (num >= size)
			{
				System.Buffer.BlockCopy(buffer, offset, m_Buffer, m_Tail, size);
			}
			else
			{
				System.Buffer.BlockCopy(buffer, offset, m_Buffer, m_Tail, num);
				System.Buffer.BlockCopy(buffer, offset + num, m_Buffer, 0, size - num);
			}
		}
		else
		{
			System.Buffer.BlockCopy(buffer, offset, m_Buffer, m_Tail, size);
		}
		m_Tail = (m_Tail + size) % m_Buffer.Length;
		m_Size += size;
	}
}
