using System;
using System.IO;
using System.Runtime.InteropServices.ComTypes;
using System.Text;

namespace RxjhServer.Network;

public class SendingClass : IDisposable
{
	private byte[] _mBuffer = new byte[8];

	private MemoryStream _mStream = new MemoryStream();

	private MemoryStream _zStream;

	public int InfoType { get; set; }

	public int Length => (int)(_mStream.Length + 6);

	public void Dispose()
	{
		_mStream = null;
		_zStream = null;
	}

	public void Write(byte[] value)
	{
		_mStream.Write(value, 0, value.Length);
	}

	public void Write(float value)
	{
		_mBuffer = BitConverter.GetBytes(value);
		_mStream.Write(_mBuffer, 0, 4);
	}

	public void Write(int value)
	{
		_mStream.WriteByte((byte)((uint)value & 0xFFu));
	}

	public void Write1(int value)
	{
		_mStream.WriteByte((byte)((uint)value & 0xFFu));
	}

	public void Write2(int value)
	{
		_mBuffer[0] = (byte)value;
		_mBuffer[1] = (byte)(value >> 8);
		_mStream.Write(_mBuffer, 0, 2);
	}

	public void WriteInt(int value)
	{
		_mBuffer[0] = (byte)value;
		_mBuffer[1] = (byte)(value >> 8);
		_mStream.Write(_mBuffer, 0, 2);
	}

	public void WriteShort(int value, int loop = 1)
	{
		while (true)
		{
			_mBuffer[0] = (byte)value;
			_mBuffer[1] = (byte)(value >> 8);
			_mStream.Write(_mBuffer, 0, 2);
			if (loop > 1)
			{
				loop--;
				continue;
			}
			break;
		}
	}

	public void Write4(int value)
	{
		_mBuffer[0] = (byte)value;
		_mBuffer[1] = (byte)(value >> 8);
		_mBuffer[2] = (byte)(value >> 16);
		_mBuffer[3] = (byte)(value >> 24);
		_mStream.Write(_mBuffer, 0, 4);
	}

	public void Write4(long value)
	{
		_mBuffer[0] = (byte)value;
		_mBuffer[1] = (byte)(value >> 8);
		_mBuffer[2] = (byte)(value >> 16);
		_mBuffer[3] = (byte)(value >> 24);
		_mStream.Write(_mBuffer, 0, 4);
	}

	public void WriteByte17(int value, int loop = 1)
	{
		_mStream.WriteByte((byte)((uint)value & 0xFFu));
		if (loop > 1)
		{
			WriteByte17(value, loop - 1);
		}
	}

	public void Write4_2(int value, int loop = 1)
	{
		_mBuffer[0] = (byte)value;
		_mBuffer[1] = (byte)(value >> 8);
		_mBuffer[2] = (byte)(value >> 16);
		_mBuffer[3] = (byte)(value >> 24);
		_mStream.Write(_mBuffer, 0, 4);
		if (loop > 1)
		{
			Write4_2(value, loop - 1);
		}
	}

	public void Write4(uint value)
	{
		_mBuffer[0] = (byte)value;
		_mBuffer[1] = (byte)(value >> 8);
		_mBuffer[2] = (byte)(value >> 16);
		_mBuffer[3] = (byte)(value >> 24);
		_mStream.Write(_mBuffer, 0, 4);
	}

	public void Write8(long value)
	{
		_mBuffer[0] = (byte)value;
		_mBuffer[1] = (byte)(value >> 8);
		_mBuffer[2] = (byte)(value >> 16);
		_mBuffer[3] = (byte)(value >> 24);
		_mBuffer[4] = (byte)(value >> 32);
		_mBuffer[5] = (byte)(value >> 40);
		_mBuffer[6] = (byte)(value >> 48);
		_mBuffer[7] = (byte)(value >> 56);
		_mStream.Write(_mBuffer, 0, 8);
	}

	public void Write(long value)
	{
		_mBuffer[0] = (byte)value;
		_mBuffer[1] = (byte)(value >> 8);
		_mBuffer[2] = (byte)(value >> 16);
		_mBuffer[3] = (byte)(value >> 24);
		_mBuffer[4] = (byte)(value >> 32);
		_mBuffer[5] = (byte)(value >> 40);
		_mBuffer[6] = (byte)(value >> 48);
		_mBuffer[7] = (byte)(value >> 56);
		_mStream.Write(_mBuffer, 0, 8);
	}

	public void Write(byte[] buffer, int offset, int size)
	{
		_mStream.Write(buffer, offset, size);
	}

	public void WriteAsciiFixed(string value)
	{
		int num2 = 1;
		while (true)
		{
			switch (num2)
			{
			case 0:
				Console.WriteLine("Network: Attempted to WriteAsciiFixed() with null value");
				value = string.Empty;
				num2 = 2;
				continue;
			default:
				if (value == null)
				{
					num2 = 0;
					continue;
				}
				break;
			case 2:
				break;
			}
			break;
		}
		byte[] bytes = Encoding.Default.GetBytes(value);
		WriteInt((byte)bytes.Length);
		_mStream.Write(bytes, 0, bytes.Length);
	}

	
    public void WriteString(string value, int SoLuong)
    {
        byte[] array = null;
        if (value == null)
        {
            Console.WriteLine("Network: Attempted to WriteAsciiFixed() with null value");
            value = string.Empty;
        }

        var array2 = new byte[SoLuong];
        array = Encoding.GetEncoding(World.Language_Charset).GetBytes(value);
        System.Buffer.BlockCopy(array, 0, array2, 0, array.Length);
        _mStream.Write(array2, 0, array2.Length);
    }

	public void WriteName(string value)
	{
		byte[] array = null;
		byte[] array2 = null;
		if (value == null)
		{
			value = string.Empty;
		}
		array = Encoding.Default.GetBytes(value);
		array2 = new byte[15];
		if (array.Length <= 15)
		{
			System.Buffer.BlockCopy(array, 0, array2, 0, array.Length);
		}
		else
		{
			System.Buffer.BlockCopy(array, 0, array2, 0, 15);
		}
		_mStream.Write(array2, 0, array2.Length);
	}
	 public string TruncateToByteLength(string input, int maxLength, Encoding encoding)
    {
        var bytes = encoding.GetBytes(input);
        if (bytes.Length <= maxLength)
            return input; // The input is already within the length limit

        // Truncate the string until the byte array fits the maxLength
        var cutLength = input.Length;
        while (bytes.Length > maxLength && cutLength > 0)
        {
            cutLength--;
            bytes = encoding.GetBytes(input.Substring(0, cutLength));
        }

        return input.Substring(0, cutLength);
    }
	    public void WriteStringCut(string value, int SoLuong)
    {
        value ??= string.Empty; // Ensure we do not deal with a null value further down

        var truncatedValue = TruncateToByteLength(value, SoLuong, Encoding.GetEncoding(World.Language_Charset));

        // Encode the truncated string into bytes
        var bytes = Encoding.GetEncoding(World.Language_Charset).GetBytes(truncatedValue);

        // Ensure the byte array is exactly the required length
        if (bytes.Length < SoLuong) Array.Resize(ref bytes, SoLuong); // Resize and pad with zeros if necessary

        // Write the bytes to the stream; no need to check for overflow as truncation handles it
        _mStream.Write(bytes, 0, SoLuong);
    }
	public void WriteStringCut(string value, int SoLuong, Encoding encoding)
    {
        value ??= string.Empty; // Ensure we do not deal with a null value further down

        // Truncate the string to fit the exact byte length allowed
        var truncatedValue = TruncateToByteLength(value, SoLuong, encoding);

        // Encode the truncated string into bytes
        var bytes = encoding.GetBytes(truncatedValue);

        // Ensure the byte array is exactly the required length
        if (bytes.Length < SoLuong) Array.Resize(ref bytes, SoLuong); // Resize and pad with zeros if necessary

        // Write the bytes to the stream; no need to check for overflow as truncation handles it
        _mStream.Write(bytes, 0, SoLuong);
    }

	public void WriteName1258(string value)
	{
		byte[] array = null;
		byte[] array2 = null;
		if (value == null)
		{
			value = string.Empty;
		}
		array = Encoding.GetEncoding(1252).GetBytes(value);
		array2 = new byte[15];
		if (array.Length <= 15)
		{
			System.Buffer.BlockCopy(array, 0, array2, 0, array.Length);
		}
		else
		{
			System.Buffer.BlockCopy(array, 0, array2, 0, 15);
		}
		_mStream.Write(array2, 0, array2.Length);
	}

	public byte[] ToArray3()
	{
		return _mStream.ToArray();
	}

	public byte[] ToArray2(int int0, int wordid)
	{
		int estimatedSize = 8 + (int)_mStream.Length;
		using MemoryStream zStream = new MemoryStream(estimatedSize);
		_mBuffer[0] = (byte)wordid;
		_mBuffer[1] = (byte)(wordid >> 8);
		_mBuffer[2] = 0;
		_mBuffer[3] = 0;
		zStream.Write(_mBuffer, 0, 4);
		_mBuffer[0] = (byte)(int0 >> 8);
		_mBuffer[1] = (byte)int0;
		zStream.Write(_mBuffer, 0, 2);
		_mBuffer[0] = (byte)_mStream.Length;
		_mBuffer[1] = (byte)(_mStream.Length >> 8);
		zStream.Write(_mBuffer, 0, 2);
		_mStream.WriteTo(zStream);
		return zStream.ToArray();
	}

	public byte[] ToArray(int int0, int wordid)
	{
		int streamLength = (int)_mStream.Length;
		int totalLength = streamLength + 21;
		using MemoryStream zStream = new MemoryStream(totalLength);
		zStream.WriteByte(170);
		zStream.WriteByte(85);
		_mBuffer[0] = (byte)(streamLength + 7);
		_mBuffer[1] = (byte)(streamLength + 7 >> 8);
		zStream.Write(_mBuffer, 0, 2);
		zStream.WriteByte(0);
		_mBuffer[0] = (byte)wordid;
		_mBuffer[1] = (byte)(wordid >> 8);
		zStream.Write(_mBuffer, 0, 2);
		_mBuffer[0] = (byte)(int0 >> 8);
		_mBuffer[1] = (byte)int0;
		zStream.Write(_mBuffer, 0, 2);
		_mBuffer[0] = (byte)(streamLength - 8);
		_mBuffer[1] = (byte)(streamLength - 8 >> 8);
		zStream.Write(_mBuffer, 0, 2);
		zStream.Write(new byte[8], 0, 8);
		_mStream.WriteTo(zStream);
		zStream.WriteByte(85);
		zStream.WriteByte(170);
		return zStream.ToArray();
	}
}
