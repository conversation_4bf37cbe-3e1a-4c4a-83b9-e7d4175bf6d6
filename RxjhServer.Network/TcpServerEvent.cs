using System;

namespace RxjhServer.Network;

public class TcpServerEvent
{
	public delegate <PERSON><PERSON><PERSON><PERSON>ult OnSendEventHandler(IntPtr intptr_0, byte[] byte_0);

	public delegate <PERSON><PERSON><PERSON><PERSON><PERSON> OnReceiveEventHandler(IntPtr intptr_0, byte[] byte_0);

	public delegate <PERSON><PERSON><PERSON><PERSON><PERSON> OnCloseEventHandler(IntPtr intptr_0, SocketOperation socketOperation_0, int int_0);

	public delegate <PERSON><PERSON><PERSON><PERSON><PERSON> OnShutdownEventHandler();

	public delegate <PERSON><PERSON><PERSON><PERSON><PERSON> OnPrepareListenEventHandler(IntPtr intptr_0);

	public delegate <PERSON><PERSON><PERSON><PERSON><PERSON> OnAcceptEventHandler(IntPtr intptr_0, IntPtr intptr_1);
}
