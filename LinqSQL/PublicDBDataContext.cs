using System.Data;
using System.Data.Linq;
using System.Data.Linq.Mapping;

namespace LinqSQL;

[Database(Name = "publicdb")]
public class PublicDBDataContext : DataContext
{
	private static MappingSource mappingSource = new AttributeMappingSource();

	public Table<TBL_QUESTDROP> TBL_QUESTDROPs => GetTable<TBL_QUESTDROP>();

	public PublicDBDataContext(IDbConnection connection)
		: base(connection, mappingSource)
	{
	}

	public PublicDBDataContext(string connection)
		: base(connection, mappingSource)
	{
	}

	public PublicDBDataContext(IDbConnection connection, MappingSource mappingSource)
		: base(connection, mappingSource)
	{
	}

	public PublicDBDataContext(string connection, MappingSource mappingSource)
		: base(connection, mappingSource)
	{
	}
}
