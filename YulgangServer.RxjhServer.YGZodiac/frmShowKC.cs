using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;

namespace YulgangServer.RxjhServer.YGZodiac;

public class frmShowKC : Form
{
	private static List<QiGongModel> mlstQiGong = new List<QiGongModel>();

	private IContainer components = null;

	private DataGridView grvData;

	private GroupBox groupBox1;

	private TextBox txtSearch;

	private Button btnSearch;

	private Label label1;

	public frmShowKC()
	{
		InitializeComponent();
	}

	private void frmShowKC_Load(object sender, EventArgs e)
	{
		LoadData();
		grvData.DataSource = Enumerable.OrderBy<QiGongModel, int>((IEnumerable<QiGongModel>)mlstQiGong, (Func<QiGongModel, int>)((QiGongModel m) => m.Class)).ToList();
	}

	private void LoadData()
	{
		mlstQiGong.Add(new QiGongModel(10, "Lực phách hoa sơn", 1, "<PERSON><PERSON>"));
		mlstQiGong.Add(new QiGongModel(11, "Nhiếp hồn nhất kích", 1, "Đao Khách"));
		mlstQiGong.Add(new QiGongModel(12, "Liên Hoàn phi vũ", 1, "Đao Khách"));
		mlstQiGong.Add(new QiGongModel(13, "Hỏa long chi hỏa", 1, "Đao Khách"));
		mlstQiGong.Add(new QiGongModel(14, "Cuồng phong vạn phá", 1, "Đao Khách"));
		mlstQiGong.Add(new QiGongModel(15, "Tử lưỡng thiên kim", 1, "Đao Khách"));
		mlstQiGong.Add(new QiGongModel(16, "Bá khí phá giáp", 1, "Đao Khách"));
		mlstQiGong.Add(new QiGongModel(17, "Chân vũ tuyệt kích", 1, "Đao Khách"));
		mlstQiGong.Add(new QiGongModel(18, "Ám ảnh sát kích", 1, "Đao Khách"));
		mlstQiGong.Add(new QiGongModel(19, "Kim cang bất hoại", 1, "Đao Khách"));
		mlstQiGong.Add(new QiGongModel(20, "Trường hồng quán nhật", 2, "Kiếm Khách"));
		mlstQiGong.Add(new QiGongModel(21, "Bách biến thần hánh", 2, "Kiếm Khách"));
		mlstQiGong.Add(new QiGongModel(22, "Liên hoàn phi vũ", 2, "Kiếm Khách"));
		mlstQiGong.Add(new QiGongModel(23, "Nhất kiếm phá thiên", 2, "Kiếm Khách"));
		mlstQiGong.Add(new QiGongModel(24, "Cuồng phong vạn phá", 2, "Kiếm Khách"));
		mlstQiGong.Add(new QiGongModel(25, "Hộ thân canh khí", 2, "Kiếm Khách/ĐHL"));
		mlstQiGong.Add(new QiGongModel(26, "Di hoa tiếp mộc", 2, "Kiếm Khách/ĐHL"));
		mlstQiGong.Add(new QiGongModel(27, "Hồi liễu thân pháp", 2, "Kiếm Khách"));
		mlstQiGong.Add(new QiGongModel(28, "Kiếm khí xung thiên", 2, "Kiếm Khách"));
		mlstQiGong.Add(new QiGongModel(29, "Xung quán nhất nộ", 2, "Kiếm Khách"));
		mlstQiGong.Add(new QiGongModel(30, "Kim chung canh khí", 3, "Thương"));
		mlstQiGong.Add(new QiGongModel(31, "Vận khí liệu thương", 3, "Thương"));
		mlstQiGong.Add(new QiGongModel(32, "Liên hoàn phi vũ", 3, "Thương"));
		mlstQiGong.Add(new QiGongModel(33, "Địa phẫn xung khí", 3, "Thương"));
		mlstQiGong.Add(new QiGongModel(34, "Cuồng phong vạn phá", 3, "Thương"));
		mlstQiGong.Add(new QiGongModel(35, "Hoành luyện thái bảo", 3, "Thương"));
		mlstQiGong.Add(new QiGongModel(36, "Càn khôn na di", 3, "Thương"));
		mlstQiGong.Add(new QiGongModel(37, "Linh giáp hộ thân", 3, "Thương"));
		mlstQiGong.Add(new QiGongModel(38, "Nộ hào nhất thanh", 3, "Thương"));
		mlstQiGong.Add(new QiGongModel(39, "Dĩ công vi thủ", 3, "Thương"));
		mlstQiGong.Add(new QiGongModel(40, "Bách bộ xuyên dương", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(41, "Liệp ưng chi nhãn", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(42, "Ngưng thần tụ khí", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(43, "Hồi lưu chấn khí", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(44, "Cuồng phong vạn phá", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(45, "Chính bản bồi nguyên", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(46, "Tâm thần ngưng tụ", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(47, "Lưu tinh tam tiễn", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(48, "Tiễn hồn quán khí", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(49, "Vô ảnh ám tiễn", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(50, "Vận khí hành tâm", 5, "Đại phu"));
		mlstQiGong.Add(new QiGongModel(51, "Thái cực tâm pháp", 5, "Đại phu"));
		mlstQiGong.Add(new QiGongModel(52, "Thể huyết bội tăng", 5, "Đại phu"));
		mlstQiGong.Add(new QiGongModel(53, "Tẩy tủy dịch cân", 5, "Đại phu"));
		mlstQiGong.Add(new QiGongModel(54, "Hồi phục liệu thương", 5, "Đại phu"));
		mlstQiGong.Add(new QiGongModel(55, "Trường công kích lực", 5, "Đại phu"));
		mlstQiGong.Add(new QiGongModel(56, "Hấp tinh đại pháp", 5, "Đại phu"));
		mlstQiGong.Add(new QiGongModel(57, "Thần nông tiên thuật", 5, "Đại phu"));
		mlstQiGong.Add(new QiGongModel(58, "Hộ thân giáp khí", 5, "Đại phu"));
		mlstQiGong.Add(new QiGongModel(59, "Cửu thiên chân khí", 5, "Đại phu"));
		mlstQiGong.Add(new QiGongModel(60, "Lực phách hoa sơn", 1, "Đao khách"));
		mlstQiGong.Add(new QiGongModel(61, "Nhiếp hồn nhất kích", 1, "Đao khách"));
		mlstQiGong.Add(new QiGongModel(62, "Liên hoan phi vũ", 1, "Đao khách"));
		mlstQiGong.Add(new QiGongModel(63, "Tất sát nhất kích", 1, "Đao khách"));
		mlstQiGong.Add(new QiGongModel(64, "Cuồng phong vạn phá", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(65, "Tâm thần ngưng tụ", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(66, "Bá khí phá giáp", 1, "Đao khách"));
		mlstQiGong.Add(new QiGongModel(67, "Chân vũ tuyệt kích", 1, "Đao khách"));
		mlstQiGong.Add(new QiGongModel(68, "Ám ảnh sát kích", 1, "Đao khách"));
		mlstQiGong.Add(new QiGongModel(69, "Kim cang bất hoại", 1, "Đao khách"));
		mlstQiGong.Add(new QiGongModel(70, "Khích nộ phẫn kế", 6, "Thích khách"));
		mlstQiGong.Add(new QiGongModel(71, "Tam hoa tụ đỉnh", 6, "Thích khách"));
		mlstQiGong.Add(new QiGongModel(72, "Liên hoàn phi tán", 6, "Thích khách"));
		mlstQiGong.Add(new QiGongModel(73, "Nhất kích tất trảm", 6, "Thích khách"));
		mlstQiGong.Add(new QiGongModel(74, "Tâm thần ngưng tụ", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(75, "Trí thủ tuyệt mệnh", 6, "Thích khách"));
		mlstQiGong.Add(new QiGongModel(76, "Thiểm ảnh chấn kinh", 6, "Thích khách"));
		mlstQiGong.Add(new QiGongModel(77, "Tâm thể bế tái", 6, "Thích khách"));
		mlstQiGong.Add(new QiGongModel(78, "Ngự khí xung tiêu", 6, "Thích khách"));
		mlstQiGong.Add(new QiGongModel(79, "Khoái đao loạn vũ", 6, "Thích khách"));
		mlstQiGong.Add(new QiGongModel(80, "Công phá cầm âm", 7, "Cầm sư"));
		mlstQiGong.Add(new QiGongModel(81, "Vũ khúc phiêu phiêu", 7, "Cầm sư"));
		mlstQiGong.Add(new QiGongModel(82, "Dương khúc", 7, "Cầm sư"));
		mlstQiGong.Add(new QiGongModel(83, "Phong tỏa cầm thanh", 7, "Cầm sư"));
		mlstQiGong.Add(new QiGongModel(84, "Sát âm diệu khúc", 7, "Cầm sư"));
		mlstQiGong.Add(new QiGongModel(85, "Tam khúc hòa minh", 7, "Cầm sư"));
		mlstQiGong.Add(new QiGongModel(86, "Cầm thanh quá nguyệt", 7, "Cầm sư"));
		mlstQiGong.Add(new QiGongModel(87, "Nhất âm xuất kích", 7, "Cầm sư"));
		mlstQiGong.Add(new QiGongModel(88, "Kích lực công thanh", 7, "Cầm sư"));
		mlstQiGong.Add(new QiGongModel(89, "Sát tâm cầm khúc", 7, "Cầm sư"));
		mlstQiGong.Add(new QiGongModel(110, "Toàn phong nhất đao", 1, "Đao khách"));
		mlstQiGong.Add(new QiGongModel(120, "Nhất kiếm nhất thể", 2, "Kiếm khách"));
		mlstQiGong.Add(new QiGongModel(130, "Thiên hạ cuồng phong", 3, "Thương"));
		mlstQiGong.Add(new QiGongModel(140, "Độc bá giang hồ", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(150, "Chân khí nguyên thương", 5, "Đại phu"));
		mlstQiGong.Add(new QiGongModel(170, "Mãnh long phong kích", 6, "Thích khách"));
		mlstQiGong.Add(new QiGongModel(180, "Sát mệnh cầm thanh", 7, "Cầm sư"));
		mlstQiGong.Add(new QiGongModel(190, "Toàn phong nhất đao", 8, "HBQ"));
		mlstQiGong.Add(new QiGongModel(250, "Lục phách hoa sơn", 8, "HBQ"));
		mlstQiGong.Add(new QiGongModel(251, "Nhiếp hồn nhất kích", 8, "HBQ"));
		mlstQiGong.Add(new QiGongModel(252, "Thiên mã quang huyết", 8, "HBQ"));
		mlstQiGong.Add(new QiGongModel(253, "Tất sát nhất kích", 8, "HBQ"));
		mlstQiGong.Add(new QiGongModel(254, "Cuồng phong vạn phá", 8, "HBQ"));
		mlstQiGong.Add(new QiGongModel(255, "Thiên ma thoát cốt", 8, "HBQ"));
		mlstQiGong.Add(new QiGongModel(256, "Bá khí phá giáp", 8, "HBQ"));
		mlstQiGong.Add(new QiGongModel(257, "Chân vũ tuyệt kích", 8, "HBQ"));
		mlstQiGong.Add(new QiGongModel(258, "Ám ảnh sát kích", 8, "HBQ"));
		mlstQiGong.Add(new QiGongModel(259, "Hỏa long tất biến", 8, "HBQ"));
		mlstQiGong.Add(new QiGongModel(260, "Toàn phong nhất đao", 8, "HBQ"));
		mlstQiGong.Add(new QiGongModel(270, "Trường hồng quán nhật", 9, "ĐHL"));
		mlstQiGong.Add(new QiGongModel(271, "Bách biến thần hành", 9, "ĐHL"));
		mlstQiGong.Add(new QiGongModel(272, "Liên hoàn phi vũ (mới)", 9, "ĐHL"));
		mlstQiGong.Add(new QiGongModel(273, "Tu nhân đại pháp", 9, "ĐHL"));
		mlstQiGong.Add(new QiGongModel(274, "Cuồng phong vạn phá", 9, "ĐHL"));
		mlstQiGong.Add(new QiGongModel(275, "Hộ thân canh khí", 9, "ĐHL"));
		mlstQiGong.Add(new QiGongModel(276, "Di hoa tiếp mộc", 9, "ĐHL"));
		mlstQiGong.Add(new QiGongModel(278, "Hồi liễu thân pháp (ĐHL)", 9, "ĐHL"));
		mlstQiGong.Add(new QiGongModel(279, "Kiếm khí xung thiên", 9, "ĐHL"));
		mlstQiGong.Add(new QiGongModel(280, "Xung quán nhất nộ", 9, "ĐHL"));
		mlstQiGong.Add(new QiGongModel(290, "Ám ảnh sát kích", 1, "Đao khách"));
		mlstQiGong.Add(new QiGongModel(310, "Thiết huyết phụng linh", 1, "Đao khách"));
		mlstQiGong.Add(new QiGongModel(311, "Hồi quy phẫn nộ", 1, "Đao khách"));
		mlstQiGong.Add(new QiGongModel(312, "Mãnh long sát trận", 1, "Đao khách"));
		mlstQiGong.Add(new QiGongModel(315, "Khí công TT3 (Diệu yến)", 11, "Diệu yến"));
		mlstQiGong.Add(new QiGongModel(316, "Khí công TT1 (Diệu yến)", 11, "Diệu yến"));
		mlstQiGong.Add(new QiGongModel(318, "Khí công 11 (Diệu yến)", 11, "Diệu yến"));
		mlstQiGong.Add(new QiGongModel(320, "Hỗn nguyên kiếm pháp", 2, "Kiếm Khách"));
		mlstQiGong.Add(new QiGongModel(321, "Thiến kiếm phi toái (Kiếm/ĐHL)", 2, "Kiếm Khách"));
		mlstQiGong.Add(new QiGongModel(322, "Hữu huyết tồn sinh (Kiếm/ĐHL)", 2, "Kiếm Khách"));
		mlstQiGong.Add(new QiGongModel(325, "Khí công TT2 (Diệu yến)", 11, "Diệu yến"));
		mlstQiGong.Add(new QiGongModel(330, "Diêm vương phệ nguyệt", 3, "Thương"));
		mlstQiGong.Add(new QiGongModel(331, "Sinh tử hữu mệnh", 3, "Thương"));
		mlstQiGong.Add(new QiGongModel(332, "Nộ huyết xung thiên", 3, "Thương"));
		mlstQiGong.Add(new QiGongModel(340, "Lưu vân cường kích", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(341, "Đoạn ngôn trấm trọng", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(342, "Nhiệt huyết thần cung", 4, "Cung"));
		mlstQiGong.Add(new QiGongModel(351, "Bác sự như ý", 5, "Đại phu"));
		mlstQiGong.Add(new QiGongModel(352, "Tập ý sinh công", 5, "Đại phu"));
		mlstQiGong.Add(new QiGongModel(370, "Viêm đoạn diệt quyết", 6, "Thích khách"));
		mlstQiGong.Add(new QiGongModel(371, "Cô lập vô y", 6, "Thích khách"));
		mlstQiGong.Add(new QiGongModel(372, "Nộ khí hồi lưu", 6, "Thích khách"));
		mlstQiGong.Add(new QiGongModel(390, "Linh thần hộ tâm", 7, "Cầm sư"));
		mlstQiGong.Add(new QiGongModel(391, "Tiên cầm tâm pháp", 7, "Cầm sư"));
		mlstQiGong.Add(new QiGongModel(392, "Cửu khúc hàng ma", 7, "Cầm sư"));
		mlstQiGong.Add(new QiGongModel(550, "Nộ hào nhất thanh", 10, "Quyền sư"));
		mlstQiGong.Add(new QiGongModel(551, "Vận khí liệu thương", 10, "Quyền sư"));
		mlstQiGong.Add(new QiGongModel(552, "Ngưng thần tụ khí", 10, "Quyền sư"));
		mlstQiGong.Add(new QiGongModel(553, "Cuồng phong vạn phá", 10, "Quyền sư"));
		mlstQiGong.Add(new QiGongModel(554, "Kim cang bất hoại", 10, "Quyền sư"));
		mlstQiGong.Add(new QiGongModel(555, "Phong quy diện bích", 10, "Quyền sư"));
		mlstQiGong.Add(new QiGongModel(556, "Vật ngã nhất thể", 10, "Quyền sư"));
		mlstQiGong.Add(new QiGongModel(557, "Nộ tâm nhất kích", 10, "Quyền sư"));
		mlstQiGong.Add(new QiGongModel(558, "Linh giáp hộ thân", 10, "Quyền sư"));
		mlstQiGong.Add(new QiGongModel(559, "Ma phủ vi châm", 10, "Quyền sư"));
		mlstQiGong.Add(new QiGongModel(560, "Thiên hạ cuồng phong", 10, "Quyền sư"));
		mlstQiGong.Add(new QiGongModel(561, "Dĩ nghĩa sát hỏa", 10, "Quyền sư"));
		mlstQiGong.Add(new QiGongModel(561, "Dĩ nghĩa sát hỏa", 10, "Quyền sư"));
		mlstQiGong.Add(new QiGongModel(562, "Điện quang thạch hỏa", 10, "Quyền sư"));
		mlstQiGong.Add(new QiGongModel(563, "Kinh thiên vĩ địa", 10, "Quyền sư"));
	}

	private void btnSearch_Click(object sender, EventArgs e)
	{
		List<QiGongModel> model = Enumerable.Where<QiGongModel>((IEnumerable<QiGongModel>)mlstQiGong, (Func<QiGongModel, bool>)((QiGongModel l) => CultureInfo.CurrentCulture.CompareInfo.IndexOf(l.NameQiGong.ToLower(), txtSearch.Text.ToLower()) >= 0 || CultureInfo.CurrentCulture.CompareInfo.IndexOf(l.ClassName.ToLower(), txtSearch.Text.ToLower()) >= 0 || l.ID.ToString().Contains(txtSearch.Text.Trim()) || l.Class.ToString().Equals(txtSearch.Text.Trim()))).ToList();
		grvData.DataSource = Enumerable.OrderBy<QiGongModel, int>((IEnumerable<QiGongModel>)model, (Func<QiGongModel, int>)((QiGongModel m) => m.Class)).ToList();
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && components != null)
		{
			components.Dispose();
		}
		base.Dispose(disposing);
	}

	private void InitializeComponent()
	{
		this.grvData = new System.Windows.Forms.DataGridView();
		this.groupBox1 = new System.Windows.Forms.GroupBox();
		this.txtSearch = new System.Windows.Forms.TextBox();
		this.label1 = new System.Windows.Forms.Label();
		this.btnSearch = new System.Windows.Forms.Button();
		((System.ComponentModel.ISupportInitialize)this.grvData).BeginInit();
		this.groupBox1.SuspendLayout();
		base.SuspendLayout();
		this.grvData.BackgroundColor = System.Drawing.Color.White;
		this.grvData.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
		this.grvData.Dock = System.Windows.Forms.DockStyle.Bottom;
		this.grvData.Location = new System.Drawing.Point(0, 66);
		this.grvData.Name = "grvData";
		this.grvData.Size = new System.Drawing.Size(712, 337);
		this.grvData.TabIndex = 0;
		this.groupBox1.Controls.Add(this.btnSearch);
		this.groupBox1.Controls.Add(this.label1);
		this.groupBox1.Controls.Add(this.txtSearch);
		this.groupBox1.Dock = System.Windows.Forms.DockStyle.Top;
		this.groupBox1.Location = new System.Drawing.Point(0, 0);
		this.groupBox1.Name = "groupBox1";
		this.groupBox1.Size = new System.Drawing.Size(712, 60);
		this.groupBox1.TabIndex = 1;
		this.groupBox1.TabStop = false;
		this.groupBox1.Text = "Tìm kiếm";
		this.txtSearch.Location = new System.Drawing.Point(132, 19);
		this.txtSearch.Name = "txtSearch";
		this.txtSearch.Size = new System.Drawing.Size(206, 20);
		this.txtSearch.TabIndex = 0;
		this.label1.AutoSize = true;
		this.label1.Location = new System.Drawing.Point(59, 22);
		this.label1.Name = "label1";
		this.label1.Size = new System.Drawing.Size(67, 13);
		this.label1.TabIndex = 1;
		this.label1.Text = "Tên kĩ năng";
		this.btnSearch.Location = new System.Drawing.Point(358, 17);
		this.btnSearch.Name = "btnSearch";
		this.btnSearch.Size = new System.Drawing.Size(75, 23);
		this.btnSearch.TabIndex = 2;
		this.btnSearch.Text = "Tìm kiếm";
		this.btnSearch.UseVisualStyleBackColor = true;
		this.btnSearch.Click += new System.EventHandler(btnSearch_Click);
		base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
		base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
		base.ClientSize = new System.Drawing.Size(712, 403);
		base.Controls.Add(this.groupBox1);
		base.Controls.Add(this.grvData);
		base.Name = "frmShowKC";
		base.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
		this.Text = "Danh sách khí công";
		base.Load += new System.EventHandler(frmShowKC_Load);
		((System.ComponentModel.ISupportInitialize)this.grvData).EndInit();
		this.groupBox1.ResumeLayout(false);
		this.groupBox1.PerformLayout();
		base.ResumeLayout(false);
	}
}
