using System;
using System.Threading;
using System.Timers;
using RxjhServer;
using RxjhServer.DbClss;

public class CongThanhClass : IDisposable
{
	private readonly System.Timers.Timer _thoiGian1;

	private System.Timers.Timer _congThanhChienBatDauThucHienTungPhut;

	private readonly System.Timers.Timer _thoiGian2;

	private DateTime _thienMaCongThanhChienChuanBiThoiGian;

	private DateTime _thienMaCongThanhChienTongThoiGian;

	private int _batDauDemNguoc;

	private int _soNguoiDangKy;

	private int _soNguoiDangKy2;

	public CongThanhClass()
	{
		try
		{
			if (World.jlMsg == 1)
			{
				Form1.WriteLine(0, "EventClass-");
			}
			World.CongThanhChienList.Clear();
			_thienMaCongThanhChienChuanBiThoiGian = DateTime.Now.AddMinutes(1.0);
			World.CongThanhChien_Progress = 1;
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.NhanVatToaDo_BanDo == 42001)
				{
					value.GuiDi_TheLucChien_DemNguoc(_batDauDemNguoc);
					value.ThienMaThanCungBieuTuongHienThi(value, World.DangKyDanhSach_NguoiChoiCongThanhChien.Count);
				}
				else if (value.GangName != string.Empty && value.NhanVatToaDo_BanDo != 42001)
				{
					value.SendAnInvitationToTheDemonShrine();
				}
			}
			_thoiGian1 = new System.Timers.Timer(4000.0);
			_thoiGian1.Elapsed += TimeEndEvent1;
			_thoiGian1.Enabled = true;
			_thoiGian1.AutoReset = true;
			TimeEndEvent1(null, null);
			World.ThienMaThanCungDaiMon_PhaiChangTuVong = 0;
			World.TangThemThienMa_QuaiVat(16431, 57f, 470f, 42001, 0);
			Thread.Sleep(1000);
			World.TangThemThienMa_QuaiVat(16430, -430f, -393f, 42001, 0);
			Thread.Sleep(1000);
			World.TangThemThienMa_QuaiVat(16435, -435f, 523f, 42001, 0);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "攻城战 EventClass 出错：" + ex);
		}
	}

	public void TimeEndEvent1(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "EventClass_TimeEndEvent1");
		}
		try
		{
			int num = (int)_thienMaCongThanhChienChuanBiThoiGian.Subtract(DateTime.Now).TotalSeconds;
			if (num <= 0)
			{
				World.CongThanhChien_Progress = 2;
				num = 0;
			}
			_batDauDemNguoc = num;
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.NhanVatToaDo_BanDo == 42001)
				{
					value.GuiDi_TheLucChien_DemNguoc(_batDauDemNguoc);
					value.ThienMaThanCungBieuTuongHienThi(value, World.DangKyDanhSach_NguoiChoiCongThanhChien.Count);
				}
			}
			if (_batDauDemNguoc > 0)
			{
				return;
			}
			_thoiGian1.Enabled = false;
			_thoiGian1.Close();
			_thoiGian1.Dispose();
			World.CongThanhChien_Progress = 3;
			foreach (Players value2 in World.allConnectedChars.Values)
			{
				if (value2.NhanVatToaDo_BanDo == 42001)
				{
					value2.SwitchPkMode(2);
					value2.ThienMaThanCungThongBaoBatDau();
				}
			}
			_thienMaCongThanhChienTongThoiGian = DateTime.Now.AddMinutes(15.0);
			_congThanhChienBatDauThucHienTungPhut = new System.Timers.Timer(4000.0);
			_congThanhChienBatDauThucHienTungPhut.Elapsed += ThoiGianKetThucSuKien2;
			_congThanhChienBatDauThucHienTungPhut.Enabled = true;
			_congThanhChienBatDauThucHienTungPhut.AutoReset = true;
			ThoiGianKetThucSuKien2(null, null);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "CongThanhChien战      TimeEndEvent1      error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "EventClass_ThoiGianKetThucSuKien2");
		}
		try
		{
			int num = (int)_thienMaCongThanhChienTongThoiGian.Subtract(DateTime.Now).TotalSeconds;
			foreach (Players value in World.allConnectedChars.Values)
			{
				if (value.NhanVatToaDo_BanDo == 42001)
				{
					value.ThienMaThanCungBieuTuongHienThi(value, World.DangKyDanhSach_NguoiChoiCongThanhChien.Count);
					if (World.ThienMaThanCungPhoTuong_PhaiChangTuVong == 1)
					{
						value.LoadCongThanhChienData();
						World.ThienMaThanCungPhoTuongKichSat_TangThemQuaiVat();
						World.ThienMaThanCungPhoTuong_PhaiChangTuVong = 0;
					}
				}
			}
			if (num > 0)
			{
				return;
			}
			_congThanhChienBatDauThucHienTungPhut.Enabled = false;
			_congThanhChienBatDauThucHienTungPhut.Close();
			_congThanhChienBatDauThucHienTungPhut.Dispose();
			World.CongThanhChien_Progress = 4;
			foreach (Players value2 in World.allConnectedChars.Values)
			{
				if (value2.NhanVatToaDo_BanDo == 42001)
				{
					value2.ThienMaThanCungThuThanhThangLoiKetThucPacket(value2);
				}
			}
			DoiMoiThanhChu_TinTuc();
			CongThanhChien_KetThucBanThuong();
			Dispose();
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "CongThanhChien战 ThoiGianKetThucSuKien2  error：" + ex);
		}
	}

	public void DoiMoiThanhChu_TinTuc()
	{
		DateTime now2 = DateTime.Now;
		DBA.ExeSqlCommand($"UPDATE CongThanhChien_ThanhChu SET CongThanhBanThuongThoiGian='{DateTime.Now.AddDays(2.0)}'");
		DBA.ExeSqlCommand($"UPDATE CongThanhChien_ThanhChu SET CongThanhThoiGian='{now2}' ");
	}

	public void CongThanhChien_KetThucBanThuong()
	{
		foreach (Players value in World.allConnectedChars.Values)
		{
			foreach (X_Cong_Thanh_So_Lieu value2 in World.CongThanhSoLieu_list.Values)
			{
				if (value.NhanVatToaDo_BanDo != 42001)
				{
					continue;
				}
				if (value2.CongThanhChien_TenBang == value.GangName)
				{
					if (value.AppendStatusList.ContainsKey(**********))
					{
						value.AppendStatusList[**********].ThoiGianKetThucSuKien();
					}
					X_Them_Vao_Trang_Thai_Loai themVaoTrangThaiClass = new X_Them_Vao_Trang_Thai_Loai(value, *********, **********, 1);
					value.AppendStatusList.Add(themVaoTrangThaiClass.FLD_PID, themVaoTrangThaiClass);
					value.StatusEffect(BitConverter.GetBytes(**********), 1, *********);
					value.Player_WuXun += 2000;
					value.CheckTheNumberOfIngotsInBaibaoge();
					value.KiemSoatNguyenBao_SoLuong(50, 1);
					value.TinhToan_NhanVatCoBan_DuLieu();
					value.UpdateMartialArtsAndStatus();
					value.CapNhat_HP_MP_SP();
					RxjhClass.BachBaoCacRecord(value.Userid, value.UserName, 0.0, "CongThanhChien_ThuDuoc", 1, 50);
					value.Save_NguyenBaoData();
					value.HeThongNhacNho("Hệ thống ban thưởng công thành chiến thắng lợi người mỗi người" + 2000 + "Điểm Võ Huân", 10, "Thông báo ban thưởng");
					value.HeThongNhacNho("Hệ thống ban thưởng công thành chiến thắng lợi người mỗi người" + 50 + "Nguyên bảo", 10, "Thông báo ban thưởng");
				}
				else
				{
					value.Player_WuXun += 500;
					value.CheckTheNumberOfIngotsInBaibaoge();
					value.KiemSoatNguyenBao_SoLuong(10, 1);
					value.TinhToan_NhanVatCoBan_DuLieu();
					value.UpdateMartialArtsAndStatus();
					value.CapNhat_HP_MP_SP();
					value.Save_NguyenBaoData();
					RxjhClass.BachBaoCacRecord(value.Userid, value.UserName, 0.0, "CongThanhChien_ThuDuoc", 1, 50);
					value.HeThongNhacNho("Hệ thống ban thưởng công thành chiến người tham dự mỗi người" + 500 + "Điểm Võ Huân", 10, "Thông báo ban thưởng");
					value.HeThongNhacNho("Hệ thống ban thưởng công thành chiến thắng lợi người mỗi người" + 10 + "Nguyên bảo", 10, "Thông báo ban thưởng");
				}
			}
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "EventClass-Dispose");
		}
		World.CongThanhChien_Progress = 0;
		_soNguoiDangKy2 = 0;
		_soNguoiDangKy = 0;
		World.delNpc(42001, 16431);
		Thread.Sleep(500);
		World.delNpc(42001, 16430);
		Thread.Sleep(1000);
		World.delNpc(42001, 16435);
		if (_thoiGian1 != null)
		{
			_thoiGian1.Enabled = false;
			_thoiGian1.Close();
			_thoiGian1.Dispose();
		}
		if (_congThanhChienBatDauThucHienTungPhut != null)
		{
			_congThanhChienBatDauThucHienTungPhut.Enabled = false;
			_congThanhChienBatDauThucHienTungPhut.Close();
			_congThanhChienBatDauThucHienTungPhut.Dispose();
		}
		if (_thoiGian2 != null)
		{
			_thoiGian2.Enabled = false;
			_thoiGian2.Close();
			_thoiGian2.Dispose();
		}
		World.DangKyDanhSach_NguoiChoiCongThanhChien.Clear();
		World.CongThanhChienList.Clear();
		World.CongThanhChien = null;
	}
}
