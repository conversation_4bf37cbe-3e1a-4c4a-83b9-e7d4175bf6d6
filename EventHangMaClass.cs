using System;
using System.Timers;
using RxjhServer;

public class EventHangMaClass : IDisposable
{
	private System.Timers.Timer Time_Wait_HangMa;

	private System.Timers.Timer Time_Start_HangMa;

	private System.Timers.Timer Time_Wait_End_HangMa;

	private DateTime t_wait_HangMa;

	private DateTime t_start_THC1;

	private DateTime t_wait_end_THC;

	private int HangMa_WAVE = 0;

	private int reset = 0;

	private int thaboss = 0;

	private int BossHangMaID = 0;

	public EventHangMaClass()
	{
		try
		{
			Form1.WriteLine(0, "<PERSON><PERSON> Bản Hàng <PERSON> Begin-");
			foreach (Players players in World.allConnectedChars.Values)
			{
				if (players.NhanVatToaDo_BanDo == World.EventHangMa_MapID)
				{
					players.HeThongNhacNho("Thử thách Hàng Ma sẽ bắt đầu sau 60 giây", 6, "Hàng Ma");
					players.HeThong<PERSON><PERSON><PERSON><PERSON><PERSON>("Thử thách Hàng <PERSON> sẽ bắt đầu sau 60 giây", 8, "Hàng Ma");
					players.GuiDi_TheLucChien_DemNguoc(55);
				}
			}
			t_wait_HangMa = DateTime.Now.AddSeconds(55.0);
			World.EventHangMaProcess = 1;
			HangMa_WAVE = 1;
			Time_Wait_HangMa = new System.Timers.Timer(1000.0);
			Time_Wait_HangMa.Elapsed += TimeWait_HangMa;
			Time_Wait_HangMa.Enabled = true;
			Time_Wait_HangMa.AutoReset = true;
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Event Hàng Ma error：" + ex);
		}
	}

	public void TimeWait_HangMa(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "TimeWait_HangMa");
		}
		try
		{
			int Time_Regist = (int)t_wait_HangMa.Subtract(DateTime.Now).TotalSeconds;
			if (Time_Regist <= 0)
			{
				World.EventHangMaProcess = 2;
				Time_Wait_HangMa.Enabled = false;
				Time_Wait_HangMa.Close();
				Time_Wait_HangMa.Dispose();
				reset = 1;
				t_start_THC1 = DateTime.Now.AddMinutes(5.0);
				Time_Start_HangMa = new System.Timers.Timer(5000.0);
				Time_Start_HangMa.Elapsed += TimeStart_HangMa;
				Time_Start_HangMa.Enabled = true;
				Time_Start_HangMa.AutoReset = true;
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Event HangMa Step1 Error：" + ex);
		}
	}

	public void TimeStart_HangMa(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "TimeStart_HangMa");
		}
		try
		{
			int Time_Start = (int)t_start_THC1.Subtract(DateTime.Now).TotalSeconds;
			if (reset == 1)
			{
				if (HangMa_WAVE <= World.List_PBHangMaSetting.Count)
				{
					reset = 0;
					t_start_THC1 = DateTime.Now.AddMinutes(World.List_PBHangMaSetting[HangMa_WAVE].FLD_TIME);
					foreach (Players players in World.allConnectedChars.Values)
					{
						if (players.NhanVatToaDo_BanDo == World.EventHangMa_MapID)
						{
							players.GuiDi_TheLucChien_DemNguoc(World.List_PBHangMaSetting[HangMa_WAVE].FLD_TIME * 60);
							players.HeThongNhacNho("Thử thách ải " + HangMa_WAVE + " bắt đầu", 7, "Hàng Ma");
							players.GameNotice(HangMa_WAVE, 5159, "1", "1");
						}
					}
					World.Add_NPC_PBHangMa(HangMa_WAVE);
					return;
				}
				Form1.WriteLine(0, "Time_Start HangMa End");
				Time_Start_HangMa.Enabled = false;
				Time_Start_HangMa.Close();
				Time_Start_HangMa.Dispose();
				World.EventHangMaProcess = 100;
				Time_Wait_End_HangMa = new System.Timers.Timer(5000.0);
				t_wait_end_THC = DateTime.Now.AddSeconds(10.0);
				Time_Wait_End_HangMa.Elapsed += Time_WaitEnd_THC;
				Time_Wait_End_HangMa.Enabled = true;
				Time_Wait_End_HangMa.AutoReset = true;
				{
					foreach (Players players2 in World.allConnectedChars.Values)
					{
						if (players2.NhanVatToaDo_BanDo == World.EventHangMa_MapID)
						{
							players2.HeThongNhacNho("Team của bạn đã chiến thắng", 8, "Hàng Ma");
							players2.GameNotice(HangMa_WAVE, 5157, "1", "1");
							string textbox = "[" + players2.Userid + "] " + players2.UserName + " Vượt Ải " + HangMa_WAVE + " thành công";
						}
					}
					return;
				}
			}
			if (World.CheckKillAll_Monster_HangMa())
			{
				if (thaboss == 0)
				{
					Form1.WriteLine(1, " -- Tao Bosss");
					World.Add_NPC_PBHangMa(HangMa_WAVE, 1);
					thaboss = 1;
				}
				else if (World.CheckKillAll_Monster_HangMa())
				{
					int thuong_gold = World.List_PBHangMaSetting[HangMa_WAVE].FLD_GOLD;
					int thuong_vohuan = World.List_PBHangMaSetting[HangMa_WAVE].FLD_VoHuan;
					int thuong_vohoang = World.List_PBHangMaSetting[HangMa_WAVE].FLD_VoHoang;
					string thuong_vatpham = World.List_PBHangMaSetting[HangMa_WAVE].FLD_ITEM;
					foreach (Players players4 in World.allConnectedChars.Values)
					{
						if (players4.NhanVatToaDo_BanDo != World.EventHangMa_MapID)
						{
							continue;
						}
						players4.HeThongNhacNho("Thử thách ải " + HangMa_WAVE + " thành công", 7, "Hàng Ma");
						if (thuong_gold != 0)
						{
							players4.Player_Money += thuong_gold;
							players4.UpdateMoneyAndWeight();
						}
						if (thuong_vohuan != 0)
						{
							players4.Player_WuXun += thuong_vohuan;
							players4.HeThongNhacNho("Bạn nhận được " + thuong_vohuan + " võ huân", 10, "Hêò Thôìng");
						}
						if (thuong_vohoang != 0)
						{
							players4.Player_VoHoang += thuong_vohoang;
						}
						if (thuong_vatpham != "")
						{
							string[] String_Item_PBHangMa = thuong_vatpham.Split(';');
							for (int i = 0; i < String_Item_PBHangMa.Length; i++)
							{
								if (!(String_Item_PBHangMa[i] != ""))
								{
									continue;
								}
								string[] opt = String_Item_PBHangMa[i].Split(',');
								int index_Item = players4.GetParcelVacancy(players4);
								if (index_Item != -1)
								{
									if (opt.Length == 12)
									{
										players4.IncreaseItemWithAttributes(int.Parse(opt[0]), index_Item, int.Parse(opt[1]), int.Parse(opt[2]), int.Parse(opt[3]), int.Parse(opt[4]), int.Parse(opt[5]), int.Parse(opt[6]), int.Parse(opt[7]), int.Parse(opt[8]), int.Parse(opt[9]), int.Parse(opt[10]), int.Parse(opt[11]));
										string textbox3 = "[" + players4.Userid + "] " + players4.UserName + " : " + int.Parse(opt[0]);
									}
									else
									{
										players4.HeThongNhacNho("Phần thưởng có sai xót, vui lòng liên hệ Admin", 7);
									}
								}
								else
								{
									players4.HeThongNhacNho("Túi đồ đã đầy không thể nhận vật phẩm!", 7);
								}
							}
						}
						players4.UpdateMartialArtsAndStatus();
						players4.Update_Item_In_Bag();
						players4.UpdateMoneyAndWeight();
					}
					HangMa_WAVE++;
					reset = 1;
					thaboss = 0;
				}
			}
			if (Time_Start > 0)
			{
				return;
			}
			Form1.WriteLine(0, "Time_Start HangMa End");
			BossHangMaID = 0;
			Time_Start_HangMa.Enabled = false;
			Time_Start_HangMa.Close();
			Time_Start_HangMa.Dispose();
			World.EventHangMaProcess = 100;
			Time_Wait_End_HangMa = new System.Timers.Timer(5000.0);
			t_wait_end_THC = DateTime.Now.AddSeconds(10.0);
			Time_Wait_End_HangMa.Elapsed += Time_WaitEnd_THC;
			Time_Wait_End_HangMa.Enabled = true;
			Time_Wait_End_HangMa.AutoReset = true;
			foreach (Players players3 in World.allConnectedChars.Values)
			{
				if (players3.NhanVatToaDo_BanDo == World.EventHangMa_MapID)
				{
					players3.HeThongNhacNho("Thử thách ải " + HangMa_WAVE + " thất bại", 10, "Hàng Ma");
					players3.GameNotice(HangMa_WAVE, 5161, HangMa_WAVE.ToString() ?? "", HangMa_WAVE.ToString() ?? "");
					string textbox2 = "[" + players3.Userid + "] " + players3.UserName + " Vượt Ải " + HangMa_WAVE + " thất bại";
				}
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Event HangMa Step2 Error：[" + HangMa_WAVE + "]" + ex);
		}
	}

	public void Time_WaitEnd_THC(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "HangMa_Time_Step2_End");
		}
		try
		{
			int Time_Start = (int)t_wait_end_THC.Subtract(DateTime.Now).TotalSeconds;
			if (Time_Start < 0)
			{
				Time_Wait_End_HangMa.Enabled = false;
				Time_Wait_End_HangMa.Close();
				Time_Wait_End_HangMa.Dispose();
				Dispose();
				Form1.WriteLine(0, "ThatHuyenCung_End");
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Event ThatHuyenCung Step3 Error：" + ex);
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			Form1.WriteLine(0, "EventClass-Dispose");
		}
		if (Time_Wait_HangMa != null)
		{
			Time_Wait_HangMa.Enabled = false;
			Time_Wait_HangMa.Close();
			Time_Wait_HangMa.Dispose();
		}
		if (Time_Start_HangMa != null)
		{
			Time_Start_HangMa.Enabled = false;
			Time_Start_HangMa.Close();
			Time_Start_HangMa.Dispose();
		}
		if (Time_Wait_End_HangMa != null)
		{
			Time_Wait_End_HangMa.Enabled = false;
			Time_Wait_End_HangMa.Close();
			Time_Wait_End_HangMa.Dispose();
		}
		foreach (Players players in World.allConnectedChars.Values)
		{
			if (players.NhanVatToaDo_BanDo == World.EventHangMa_MapID)
			{
				players.Mobile(484f, 1734f, 15f, 101);
			}
		}
		BossHangMaID = 0;
		World.EventHangMaProcess = 0;
		World.EventHangMa = null;
		World.List_Player_PBHM.Clear();
		World.delAllBossInMap(World.EventHangMa_MapID);
	}
}
