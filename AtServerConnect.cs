using System;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Timers;
using DNGuard;
using RxjhServer;

[SecureMethod]
public class AtServerConnect
{
	private readonly byte[] _packetData;

	private Socket _socket0;

	private readonly System.Timers.Timer _timer0;

	public AtServerConnect()
	{
		_packetData = new byte[10240000];
		_timer0 = new System.Timers.Timer(5000.0);
		_timer0.Elapsed += timer_0_Elapsed;
		_timer0.AutoReset = true;
		_timer0.Enabled = true;
	}

	public void Dispose()
	{
		if (_timer0 != null)
		{
			_timer0.Enabled = false;
			_timer0.Close();
			_timer0.Dispose();
		}
		try
		{
			_socket0.Shutdown(SocketShutdown.Both);
		}
		catch
		{
		}
		_socket0?.Close();
		_socket0 = null;
	}

	private void method_0(IAsyncResult iasyncResult0)
	{
		try
		{
			((Socket)iasyncResult0.AsyncState).EndConnect(iasyncResult0);
			try
			{
				Form1.WriteLine(2, "Công kích server kết nối thành công Cảng" + World.AtPort + " IP " + World.AccountVerificationServerIP);
				发送("攻击伺服器连接|" + World.GameServerPort2);
				_socket0.BeginReceive(_packetData, 0, _packetData.Length, SocketFlags.None, OnReceiveData, this);
			}
			catch (Exception ex)
			{
				Form1.WriteLine(1, "Công kích server ConnectCallback Phạm sai lầm: " + ex.Message);
			}
		}
		catch (Exception ex2)
		{
			Form1.WriteLine(1, "Công kích server kết nối phạm sai lầm: " + ex2.Message);
		}
	}

	public virtual void OnReceiveData(IAsyncResult iasyncResult0)
	{
		try
		{
			if (_socket0.EndReceive(iasyncResult0) > 0)
			{
				_socket0.BeginReceive(_packetData, 0, _packetData.Length, SocketFlags.None, OnReceiveData, this);
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Công kích server tiếp thu phạm sai lầm: " + ex.Message);
		}
	}

	public void OnSended2(IAsyncResult iasyncResult0)
	{
		try
		{
			_socket0.EndSend(iasyncResult0);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Công kích server gửi đi phạm sai lầm: 111 " + ex.Message);
		}
	}

	public void ProcessDataReceived(byte[] byte0, int int0)
	{
		try
		{
			int num2 = 0;
			byte[] array = new byte[4];
			System.Buffer.BlockCopy(byte0, 2, array, 0, 4);
			int num3 = BitConverter.ToInt32(array, 0);
			if (int0 < num3 + 6)
			{
				return;
			}
			try
			{
				while (true)
				{
					byte[] array2 = new byte[num3];
					System.Buffer.BlockCopy(byte0, num2 + 6, array2, 0, num3);
					num2 += num3 + 6;
					if ((array2[0] != 170 || array2[1] != 85 || num3 != 45 || World.allConnectedChars.TryGetValue(BitConverter.ToInt16(array2, 5), out var _)) && num2 < int0 && byte0[num2] == 170 && byte0[num2 + 1] == 102)
					{
						System.Buffer.BlockCopy(byte0, num2 + 2, array, 0, 4);
						num3 = BitConverter.ToInt16(array, 0);
						continue;
					}
					break;
				}
			}
			catch
			{
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Công kích xác nhận gửi đi phạm sai lầm: " + ex.Message);
		}
	}

	public virtual void Send(byte[] byte0, int int0)
	{
		try
		{
			byte[] array = new byte[int0 + 6];
			array[0] = 170;
			array[1] = 102;
			System.Buffer.BlockCopy(BitConverter.GetBytes(int0), 0, array, 2, 4);
			System.Buffer.BlockCopy(byte0, 0, array, 6, int0);
			_socket0.BeginSend(array, 0, int0 + 6, SocketFlags.None, OnSended2, this);
		}
		catch (SocketException ex)
		{
			Form1.WriteLine(1, "Attack server send error " + ex.Message);
		}
		catch (Exception ex2)
		{
			Form1.WriteLine(1, "Attack server send error 2: " + ex2.Message);
		}
	}

	public void Sestup()
	{
		try
		{
			IPEndPoint remoteEp = new IPEndPoint(IPAddress.Parse(World.AccountVerificationServerIP), World.AtPort);
			_socket0 = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
			_socket0.UseOnlyOverlappedIO = true;
			_socket0.LingerState.Enabled = false;
			_socket0.ExclusiveAddressUse = false;
			_socket0.BeginConnect(remoteEp, method_0, _socket0);
		}
		catch (Exception ex)
		{
			Form1.WriteLine(2, "Kết nối công kích server phạm sai lầm " + World.AccountVerificationServerPort + " IP " + World.AccountVerificationServerIP.ToString() + ex.Message);
		}
	}

	private void timer_0_Elapsed(object sender, ElapsedEventArgs e)
	{
		int num = 0;
		while (true)
		{
			switch (num)
			{
			case 1:
				return;
			case 2:
				Sestup();
				num = 1;
				continue;
			}
			if (!_socket0.Connected)
			{
				num = 2;
				continue;
			}
			return;
		}
	}

	public void 发送(string string0)
	{
		try
		{
			if (_socket0 != null && _socket0.Connected)
			{
				byte[] bytes = Encoding.Default.GetBytes(string0);
				Send(bytes, bytes.Length);
			}
		}
		catch (Exception ex)
		{
			Form1.WriteLine(1, "Công kích server gửi đi phạm sai lầm: " + string0 + ex.Message);
		}
	}
}
