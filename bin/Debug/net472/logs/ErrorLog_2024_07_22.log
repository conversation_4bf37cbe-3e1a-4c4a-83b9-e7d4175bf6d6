7/22/2024 12:07:11 AM -----------<PERSON><PERSON> liệu tầng _ <PERSON> lầm -----------
7/22/2024 12:07:11 AM SELECT  *  FROM  ITMECLSS
7/22/2024 12:07:11 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyH<PERSON><PERSON>, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 12:07:51 AM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/22/2024 12:08:04 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 12:11:01 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 12:11:01 AM SELECT  *  FROM  ITMECLSS
7/22/2024 12:11:01 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 12:19:36 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 12:19:38 AM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/22/2024 12:26:43 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 12:26:43 AM SELECT  *  FROM  ITMECLSS
7/22/2024 12:26:43 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 12:27:43 AM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/22/2024 12:27:44 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 12:29:10 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 12:29:10 AM SELECT  *  FROM  ITMECLSS
7/22/2024 12:29:10 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 12:29:36 AM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/22/2024 12:29:38 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 12:34:18 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 12:34:18 AM SELECT  *  FROM  ITMECLSS
7/22/2024 12:34:18 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 12:34:39 AM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/22/2024 12:34:40 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 12:35:18 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 12:35:20 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 12:35:48 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 12:35:48 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 12:36:55 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 12:36:55 AM SELECT  *  FROM  ITMECLSS
7/22/2024 12:36:55 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 12:37:29 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 12:37:37 AM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/22/2024 1:03:38 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 1:03:38 AM SELECT  *  FROM  ITMECLSS
7/22/2024 1:03:38 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 1:04:04 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 1:04:04 AM SELECT  *  FROM  ITMECLSS
7/22/2024 1:04:04 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 1:04:06 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 1:04:06 AM SELECT  *  FROM  ITMECLSS
7/22/2024 1:04:06 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 1:04:24 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 1:04:24 AM SELECT  *  FROM  ITMECLSS
7/22/2024 1:04:24 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 1:04:59 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 1:05:00 AM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/22/2024 1:07:47 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 1:07:47 AM SELECT  *  FROM  ITMECLSS
7/22/2024 1:07:47 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 1:08:16 AM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/22/2024 1:08:34 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 1:09:49 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 1:09:49 AM SELECT  *  FROM  ITMECLSS
7/22/2024 1:09:49 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 1:10:22 AM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/22/2024 1:10:35 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 1:18:08 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 1:18:08 AM SELECT  *  FROM  ITMECLSS
7/22/2024 1:18:08 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 1:18:43 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 1:18:44 AM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/22/2024 1:34:29 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 1:34:29 AM SELECT  *  FROM  ITMECLSS
7/22/2024 1:34:30 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 1:34:58 AM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/22/2024 1:34:59 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 1:56:42 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 1:56:42 AM SELECT  *  FROM  ITMECLSS
7/22/2024 1:56:42 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 1:57:09 AM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/22/2024 1:57:10 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 1:58:24 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 1:58:24 AM SELECT  *  FROM  ITMECLSS
7/22/2024 1:58:24 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 1:58:54 AM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/22/2024 1:58:55 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/22/2024 3:59:44 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/22/2024 3:59:44 PM SELECT  *  FROM  ITMECLSS
7/22/2024 3:59:44 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/22/2024 4:01:00 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:01:05 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:01:06 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:01:06 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:01:11 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:01:51 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:01:51 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:01:55 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:02:11 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:02:11 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:03:26 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:03:34 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:04:59 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:05:08 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:05:48 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:06:07 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:32 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:32 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:32 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:32 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:44 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:45 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:48 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:49 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:50 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:52 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:53 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:55 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:56 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:57 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:09:59 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:10:00 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:10:01 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:10:03 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:10:04 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:10:05 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:10:05 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:10:05 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:10:05 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:10:05 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:10:06 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:10:08 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:10:34 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:11:27 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:11:27 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:07 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:11 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:18 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:23 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:28 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:33 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:37 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:37 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:37 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:37 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:38 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:39 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:42 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:47 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:52 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:12:57 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:01 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:02 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:04 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:04 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:04 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:04 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:07 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:11 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:16 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:32 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:40 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:45 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:50 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:52 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:55 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:13:59 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:14:00 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:14:06 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/22/2024 4:14:09 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
