10/10/2024 4:33:47 PM -----------<PERSON><PERSON> <PERSON><PERSON> li<PERSON>u tầng _ <PERSON> lầm -----------
10/10/2024 4:33:47 PM SELECT  *  FROM  ITMECLSS
10/10/2024 4:33:47 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBeh<PERSON>or runBeh<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:a6e18e81-609b-49fb-b9f8-37f76fa43aef
Error Number:208,State:1,Class:16
10/10/2024 4:33:51 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/10/2024 4:33:51 PM SELECT * FROM TBL_SummonBoss
10/10/2024 4:33:51 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'TBL_SummonBoss'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:5b86ef7a-b0f2-42a9-b91f-83cb73e2bf69
Error Number:208,State:1,Class:16
10/10/2024 4:33:51 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/10/2024 4:33:51 PM SELECT * FROM TBL_PBHangMa_Setting
10/10/2024 4:33:51 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'TBL_PBHangMa_Setting'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:5b86ef7a-b0f2-42a9-b91f-83cb73e2bf69
Error Number:208,State:1,Class:16
10/10/2024 4:35:31 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:35:48 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:35:48 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:35:48 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:36:02 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:36:02 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:36:02 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:36:02 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:36:50 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:36:58 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:36:58 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:36:58 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:37:47 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:38:56 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:38:56 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:38:56 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:39:34 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:39:34 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:40:20 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:40:39 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:40:39 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:40:42 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:40:42 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:43:31 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:50:49 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 4:52:05 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 5:01:53 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/10/2024 5:01:53 PM SELECT  *  FROM  ITMECLSS
10/10/2024 5:01:53 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:c87b1d08-fb48-45de-8198-f39c53343ccb
Error Number:208,State:1,Class:16
10/10/2024 5:01:54 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/10/2024 5:01:54 PM SELECT * FROM TBL_SummonBoss
10/10/2024 5:01:54 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'TBL_SummonBoss'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:1ae5c7a3-0bb2-4380-ae59-87845ea271dd
Error Number:208,State:1,Class:16
10/10/2024 5:01:54 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/10/2024 5:01:54 PM SELECT * FROM TBL_PBHangMa_Setting
10/10/2024 5:01:54 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'TBL_PBHangMa_Setting'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:1ae5c7a3-0bb2-4380-ae59-87845ea271dd
Error Number:208,State:1,Class:16
10/10/2024 5:02:34 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 5:02:43 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 5:02:43 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 5:02:44 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 5:02:45 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 5:02:45 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 5:02:46 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
10/10/2024 5:02:47 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
