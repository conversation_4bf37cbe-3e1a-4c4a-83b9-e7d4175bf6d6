9/4/2024 10:50:11 PM -----------<PERSON><PERSON> liệu tầng _ <PERSON> lầm -----------
9/4/2024 10:50:11 PM SELECT  *  FROM  ITMECLSS
9/4/2024 10:50:11 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyH<PERSON><PERSON>, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
9/4/2024 10:50:54 PM Nhân vật đăng lục phạm sai lầm 2[1]-[ThanNu] 71 The given key was not present in the dictionary.
9/4/2024 10:53:21 PM -----------DBA Số liệu tầng _ Sai lầm -----------
9/4/2024 10:53:21 PM SELECT  *  FROM  ITMECLSS
9/4/2024 10:53:21 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
9/4/2024 10:56:37 PM Nhân vật đăng lục phạm sai lầm 2[1]-[ThanNu] 71 The given key was not present in the dictionary.
9/4/2024 10:56:37 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
9/4/2024 10:58:52 PM -----------DBA Số liệu tầng _ Sai lầm -----------
9/4/2024 10:58:52 PM SELECT  *  FROM  ITMECLSS
9/4/2024 10:58:52 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
9/4/2024 11:00:32 PM -----------DBA Số liệu tầng _ Sai lầm -----------
9/4/2024 11:00:32 PM SELECT  *  FROM  ITMECLSS
9/4/2024 11:00:32 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
9/4/2024 11:01:13 PM Nhân vật đăng lục phạm sai lầm 2[1]-[ThanNu] 71 The given key was not present in the dictionary.
9/4/2024 11:01:13 PM UpdateMartialArtsAndStatuserror[1]-[ThanNu][0]Input string was not in a correct format.
9/4/2024 11:02:37 PM Nhân vật đăng lục phạm sai lầm 2[2]-[ThanNu2] 71 The given key was not present in the dictionary.
9/4/2024 11:03:58 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:07:31 PM UpdateMartialArtsAndStatuserror[2]-[TestTH][0]Input string was not in a correct format.
9/4/2024 11:13:19 PM Nhân vật đăng lục phạm sai lầm 2[2]-[TestTH] 71 The given key was not present in the dictionary.
9/4/2024 11:31:13 PM -----------DBA Số liệu tầng _ Sai lầm -----------
9/4/2024 11:31:13 PM SELECT  *  FROM  ITMECLSS
9/4/2024 11:31:13 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
9/4/2024 11:31:49 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:31:59 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:31:59 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:32:12 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:32:12 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:32:13 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:32:13 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:32:15 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:32:15 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:32:16 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:32:16 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:32:19 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:32:19 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:32:19 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:32:49 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:32:49 PM Nhân vật đăng lục phạm sai lầm 2[2]-[ThanNu2] 71 The given key was not present in the dictionary.
9/4/2024 11:33:24 PM Nhân vật đăng lục phạm sai lầm 2[2]-[ThanNu2] 71 The given key was not present in the dictionary.
9/4/2024 11:35:11 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:35:20 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:35:21 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:35:21 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:35:54 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:01 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:01 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:01 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:02 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:03 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:04 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:04 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:04 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:05 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:05 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:06 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:06 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:08 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:08 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:08 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:09 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:09 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:09 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:10 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:11 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:11 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:12 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:12 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:16 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:16 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:16 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:17 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:18 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:18 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:18 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:19 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:20 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:21 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:21 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:22 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:22 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:22 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:22 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:23 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:36:56 PM Nhân vật đăng lục phạm sai lầm 2[2]-[ThanNu2] 71 The given key was not present in the dictionary.
9/4/2024 11:38:03 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:39:11 PM Nhân vật đăng lục phạm sai lầm 2[1]-[TestNhanVat] 71 The given key was not present in the dictionary.
9/4/2024 11:39:43 PM Nhân vật đăng lục phạm sai lầm 2[1]-[TestDieuYen] 71 The given key was not present in the dictionary.
9/4/2024 11:40:41 PM Nhân vật đăng lục phạm sai lầm 2[1]-[TestNhanVat] 71 The given key was not present in the dictionary.
9/4/2024 11:41:58 PM -----------DBA Số liệu tầng _ Sai lầm -----------
9/4/2024 11:41:58 PM SELECT  *  FROM  ITMECLSS
9/4/2024 11:41:58 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
9/4/2024 11:42:24 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
9/4/2024 11:48:11 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/4/2024 11:55:41 PM -----------DBA Số liệu tầng _ Sai lầm -----------
9/4/2024 11:55:41 PM SELECT  *  FROM  ITMECLSS
9/4/2024 11:55:41 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
9/4/2024 11:56:54 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
9/4/2024 11:56:57 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
9/4/2024 11:57:46 PM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
