7/24/2024 12:00:01 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/24/2024 12:00:03 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/24/2024 12:00:05 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/24/2024 12:00:08 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/24/2024 12:00:10 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/24/2024 12:00:13 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/24/2024 12:00:15 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/24/2024 12:00:22 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/24/2024 12:00:29 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/24/2024 12:00:29 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/24/2024 12:00:30 AM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/24/2024 2:03:18 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 2:03:18 AM SELECT  *  FROM  ITMECLSS
7/24/2024 2:03:18 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 2:04:12 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:04:40 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:04:40 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:04:40 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:04:40 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:05:00 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:06:04 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:11:27 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 2:11:27 AM SELECT  *  FROM  ITMECLSS
7/24/2024 2:11:27 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 2:12:07 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:12:30 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:12:37 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:13:06 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:13:06 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:13:06 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:13:06 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:13:10 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:13:11 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:13:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:13:57 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:14:49 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 2:14:49 AM SELECT  *  FROM  ITMECLSS
7/24/2024 2:14:49 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 2:16:00 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:16:05 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:16:05 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:16:07 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:16:07 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:16:29 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:16:48 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:16:48 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:16:48 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:16:48 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:17:24 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:21:38 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 2:21:38 AM SELECT  *  FROM  ITMECLSS
7/24/2024 2:21:38 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 2:22:11 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:22:14 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:22:14 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:22:15 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:22:15 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:22:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:22:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:22:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:22:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:22:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:22:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:22:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:22:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:02 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:24 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:24 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:33 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:33 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:33 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:33 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:33 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:36 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:39 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:39 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:45 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:45 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:45 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:45 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:23:45 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:24:56 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:25:17 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:25:27 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:26:03 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:27:10 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:27:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:28:00 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:28:08 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:29:05 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:30:05 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:30:16 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:30:26 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:30:59 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:31:49 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:32:29 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:32:39 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:32:45 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:33:33 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:34:27 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:34:37 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:34:47 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:35:21 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:35:40 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 2:35:40 AM SELECT  *  FROM  ITMECLSS
7/24/2024 2:35:40 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 2:36:10 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:36:53 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:36:53 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:36:53 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:36:53 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:36:54 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:36:54 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:36:54 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:36:54 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:37:07 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:37:59 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:38:24 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:38:24 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:38:29 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:38:35 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:38:35 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:38:35 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:38:35 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:38:35 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:38:45 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:38:45 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:38:47 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:38:47 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:39:02 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:39:27 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:39:30 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:39:30 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:39:30 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:39:43 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:40:01 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:40:32 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:41:03 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:41:54 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 2:41:54 AM SELECT  *  FROM  ITMECLSS
7/24/2024 2:41:54 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 2:42:17 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:42:26 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:42:40 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:42:40 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:42:40 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:42:40 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:42:42 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:42:42 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:42:42 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:42:42 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:42:54 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:43:33 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:44:09 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 2:44:09 AM SELECT  *  FROM  ITMECLSS
7/24/2024 2:44:09 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 2:44:54 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:45:08 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:45:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:45:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:45:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:45:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:45:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:45:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:45:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:45:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:45:44 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:46:18 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:46:48 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:47:06 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:47:16 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:47:20 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:47:36 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:47:51 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:48:22 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:48:54 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:49:26 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:49:26 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:49:57 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:50:15 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:50:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:50:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:50:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:50:27 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:50:34 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:50:34 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:51:00 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:52:10 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:52:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:53:00 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:53:10 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:53:11 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 2:53:20 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:02:36 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 3:02:36 AM SELECT  *  FROM  ITMECLSS
7/24/2024 3:02:36 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 3:03:41 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:04:38 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:04:39 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:04:45 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:05:09 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:05:09 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:05:09 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:05:09 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:05:11 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:05:11 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:05:11 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:05:11 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:05:19 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:05:39 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:05:51 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:05:57 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:08:27 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 3:08:27 AM SELECT  *  FROM  ITMECLSS
7/24/2024 3:08:27 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 3:08:51 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:04 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:10 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:11 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:21 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:21 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:21 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:21 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:25 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:29 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:38 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:44 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:09:54 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:10:05 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:10:15 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:11:53 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 3:11:53 AM SELECT  *  FROM  ITMECLSS
7/24/2024 3:11:53 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 3:12:15 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:12:28 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:12:28 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:12:28 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:12:28 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:12:29 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:12:29 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:12:29 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:12:29 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:12:46 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:13:15 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:13:46 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:14:32 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 3:14:32 AM SELECT  *  FROM  ITMECLSS
7/24/2024 3:14:32 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 3:14:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:12 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:12 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:12 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:12 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:12 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:12 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:12 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:12 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:14 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:36 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:43 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:47 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:53 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:58 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:15:58 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:16:02 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:16:06 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:16:59 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:17:09 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:18:32 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 3:18:32 AM SELECT  *  FROM  ITMECLSS
7/24/2024 3:18:32 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 3:18:53 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:19:06 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:19:31 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:19:31 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:19:31 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:19:31 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:19:32 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:19:32 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:19:32 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:19:32 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:21:33 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:22:48 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:22:58 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:23:04 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:23:58 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 3:23:58 AM SELECT  *  FROM  ITMECLSS
7/24/2024 3:23:58 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 3:24:53 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:27:49 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:27:49 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:27:49 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:27:49 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:27:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:27:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:27:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:27:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:28:00 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:29:12 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 3:29:12 AM SELECT  *  FROM  ITMECLSS
7/24/2024 3:29:12 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 3:29:40 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:29:43 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:29:59 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:29:59 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:29:59 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:29:59 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:29:59 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:29:59 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:29:59 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:29:59 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:30:22 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:31:17 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:31:35 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:31:44 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:31:48 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:31:54 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:32:19 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:32:50 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:35:25 AM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 3:35:25 AM SELECT  *  FROM  ITMECLSS
7/24/2024 3:35:25 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 3:35:45 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:36:06 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:36:08 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:36:29 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:36:29 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:36:29 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:36:29 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:36:30 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:36:30 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:36:30 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:36:30 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:36:37 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:36:37 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:36:37 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:36:44 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:37:18 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:37:26 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:37:49 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:38:10 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:38:23 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:38:26 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:38:36 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:35:46 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 3:35:46 PM SELECT  *  FROM  ITMECLSS
7/24/2024 3:35:47 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 3:36:23 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:38:57 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:40:10 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 3:40:10 PM SELECT  *  FROM  ITMECLSS
7/24/2024 3:40:10 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 3:40:36 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:40:59 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:42:04 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:42:04 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:42:09 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:42:09 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:42:10 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:42:10 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:42:12 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:42:12 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:42:14 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:42:14 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:42:26 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:43:53 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 3:43:53 PM SELECT  *  FROM  ITMECLSS
7/24/2024 3:43:53 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 3:44:21 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:44:49 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:47:19 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/24/2024 3:47:19 PM SELECT  *  FROM  ITMECLSS
7/24/2024 3:47:19 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/24/2024 3:47:52 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/24/2024 3:48:21 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
