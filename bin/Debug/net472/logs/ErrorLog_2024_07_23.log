7/23/2024 11:55:44 PM -----------<PERSON><PERSON> liệu tầng _ <PERSON> lầm -----------
7/23/2024 11:55:44 PM SELECT  *  FROM  ITMECLSS
7/23/2024 11:55:44 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyH<PERSON><PERSON>, Tds<PERSON>arserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/23/2024 11:56:08 PM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
7/23/2024 11:56:54 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:57:15 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:58:28 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:58:30 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:58:32 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:58:38 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:58:42 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:58:46 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:58:51 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:58:59 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:10 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:11 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:12 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:13 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:15 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:17 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:19 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:21 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:23 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:25 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:27 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:30 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:31 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:34 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:36 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:39 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:41 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:43 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:46 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:48 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:50 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:52 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:55 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:57 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:59 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
7/23/2024 11:59:59 PM UpdateMartialArtsAndStatuserror[2]-[TestQS][0]Input string was not in a correct format.
