10/4/2024 2:27:54 PM -----------<PERSON><PERSON> <PERSON><PERSON> li<PERSON>u tầng _ <PERSON> lầm -----------
10/4/2024 2:27:54 PM SELECT  *  FROM  ITMECLSS
10/4/2024 2:27:54 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBeh<PERSON>or runBeh<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:8f52a24c-5df6-4d76-bcfc-ee9964f38e22
Error Number:208,State:1,Class:16
10/4/2024 2:27:56 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/4/2024 2:27:56 PM SELECT * FROM TBL_BossMap_Settings
10/4/2024 2:27:56 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'TBL_BossMap_Settings'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:7c00e7eb-295b-4ff0-88d4-fa094b150f25
Error Number:208,State:1,Class:16
10/4/2024 2:27:56 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/4/2024 2:27:56 PM SELECT * FROM TBL_RANDOM_MAP
10/4/2024 2:27:56 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'TBL_RANDOM_MAP'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:7c00e7eb-295b-4ff0-88d4-fa094b150f25
Error Number:208,State:1,Class:16
10/4/2024 2:34:11 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/4/2024 2:34:11 PM SELECT  *  FROM  ITMECLSS
10/4/2024 2:34:11 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:4945e6d3-4c67-48eb-a7e5-acb281ac6efb
Error Number:208,State:1,Class:16
10/4/2024 2:35:18 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/4/2024 2:35:18 PM SELECT  *  FROM  ITMECLSS
10/4/2024 2:35:18 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:b6040e51-6e52-4209-beea-6c2f9463548d
Error Number:208,State:1,Class:16
10/4/2024 2:36:12 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:36:48 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:36:48 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:36:49 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:36:49 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:36:50 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:36:50 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:36:51 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:36:51 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:37:05 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:37:05 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:37:06 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:37:06 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:37:37 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:37:37 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:38:07 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:38:07 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:38:08 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:38:08 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 2:41:51 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/4/2024 2:41:51 PM SELECT  *  FROM  ITMECLSS
10/4/2024 2:41:51 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:7969a89e-4aee-4a78-b682-df1fe37bb9ba
Error Number:208,State:1,Class:16
10/4/2024 2:41:59 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/4/2024 2:41:59 PM SELECT  *  FROM  ITMECLSS
10/4/2024 2:41:59 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:b7a950e4-aee1-4b13-8068-76485ddfd068
Error Number:208,State:1,Class:16
10/4/2024 3:00:11 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/4/2024 3:00:11 PM SELECT  *  FROM  ITMECLSS
10/4/2024 3:00:11 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:e388f337-4d3d-436c-a527-8e49be84406a
Error Number:208,State:1,Class:16
10/4/2024 3:01:50 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:02:36 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:02:36 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:02:41 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:02:41 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:02:51 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:02:51 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:04:29 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:04:29 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:04:50 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:04:51 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:05:50 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:05:50 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:05:52 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:05:52 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:06:12 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:06:12 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:06:20 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:06:20 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:06:33 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:06:33 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:06:34 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:18:58 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/4/2024 3:18:58 PM SELECT  *  FROM  ITMECLSS
10/4/2024 3:18:58 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:b2ab1cfa-8344-420a-bef3-547ce174e550
Error Number:208,State:1,Class:16
10/4/2024 3:20:56 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:21:34 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:21:34 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:29:09 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/4/2024 3:29:09 PM SELECT  *  FROM  ITMECLSS
10/4/2024 3:29:09 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:c7dc4e67-9859-47d8-82b3-3d38c1b99f4f
Error Number:208,State:1,Class:16
10/4/2024 3:30:12 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:30:20 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:30:22 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:30:22 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:30:22 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:32:42 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/4/2024 3:32:42 PM SELECT  *  FROM  ITMECLSS
10/4/2024 3:32:42 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:967c53bb-8b84-4729-8d3b-8bd392a5742d
Error Number:208,State:1,Class:16
10/4/2024 3:34:10 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:34:17 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:37:07 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/4/2024 3:37:07 PM SELECT  *  FROM  ITMECLSS
10/4/2024 3:37:10 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:b61af35c-837d-48d7-99cd-8464e9ac02f5
Error Number:208,State:1,Class:16
10/4/2024 3:39:41 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:39:50 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:39:50 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:39:50 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:39:54 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:39:54 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:39:56 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:39:56 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:39:56 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:55:34 PM -----------DBA Số liệu tầng _ Sai lầm -----------
10/4/2024 3:55:34 PM SELECT  *  FROM  ITMECLSS
10/4/2024 3:55:34 PM System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, Boolean inRetry, SqlDataReader ds, Boolean describeParameterEncryptionRequest)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean& usedCache, Boolean asyncWrite, Boolean inRetry)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1) in C:\Users\<USER>\Downloads\Source REAL\REAL_GS2909\REAL_GS\RxjhServer.DbClss\DBA.cs:line 1042
ClientConnectionId:e4a5a919-8939-493e-bde9-bb5b1b8ca843
Error Number:208,State:1,Class:16
10/4/2024 3:58:02 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:58:09 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:58:09 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:58:11 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:58:11 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:58:11 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:58:12 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:58:13 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:58:13 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
10/4/2024 3:58:13 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
