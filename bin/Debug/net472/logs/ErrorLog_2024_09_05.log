9/5/2024 12:01:03 AM -----------<PERSON><PERSON> liệu tầng _ <PERSON> lầm -----------
9/5/2024 12:01:03 AM SELECT  *  FROM  ITMECLSS
9/5/2024 12:01:03 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyH<PERSON><PERSON>, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
9/5/2024 12:01:55 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
9/5/2024 12:01:57 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:02:19 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:04:51 AM -----------DBA Số liệu tầng _ Sai lầm -----------
9/5/2024 12:04:51 AM SELECT  *  FROM  ITMECLSS
9/5/2024 12:04:51 AM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
9/5/2024 12:05:26 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:27 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
9/5/2024 12:05:50 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:51 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:51 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:51 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:51 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:52 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:52 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:52 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:52 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:52 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:52 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:53 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:53 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:53 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:53 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:53 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:54 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:54 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:05:54 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:06:01 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
9/5/2024 12:06:01 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
9/5/2024 12:07:01 AM UpdateMartialArtsAndStatuserror[1]-[TestDieuYen][0]Input string was not in a correct format.
9/5/2024 12:07:01 AM UpdateMartialArtsAndStatuserror[2]-[ThanNu2][0]Input string was not in a correct format.
