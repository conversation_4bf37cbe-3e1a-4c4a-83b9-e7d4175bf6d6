7/9/2024 4:05:52 PM -----------<PERSON><PERSON> liệu tầng _ <PERSON> lầm -----------
7/9/2024 4:05:52 PM SELECT  *  FROM  ITMECLSS
7/9/2024 4:05:52 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHand<PERSON>, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 4:07:32 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/9/2024 4:07:32 PM SELECT  *  FROM  ITMECLSS
7/9/2024 4:07:32 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 4:09:46 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:09:46 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:10:01 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:13:06 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:15:12 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/9/2024 4:15:12 PM SELECT  *  FROM  ITMECLSS
7/9/2024 4:15:12 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 4:15:35 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:15:36 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:16:58 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:16:58 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:16:59 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:16:59 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:17:00 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:17:00 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:17:04 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:17:04 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:17:06 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:17:06 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:17:43 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:17:43 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:17:45 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:17:45 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:17:47 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:17:47 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:17:48 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:17:48 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:17:51 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:17:51 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:17:52 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:17:52 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:17:54 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:17:54 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:17:55 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:17:55 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:18:08 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:18:09 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:25:07 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/9/2024 4:25:07 PM SELECT  *  FROM  ITMECLSS
7/9/2024 4:25:07 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 4:25:32 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/9/2024 4:25:32 PM SELECT  *  FROM  ITMECLSS
7/9/2024 4:25:32 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 4:25:53 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/9/2024 4:25:53 PM SELECT  *  FROM  ITMECLSS
7/9/2024 4:25:53 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 4:26:20 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/9/2024 4:26:20 PM SELECT  *  FROM  ITMECLSS
7/9/2024 4:26:20 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 4:27:41 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/9/2024 4:27:41 PM SELECT  *  FROM  ITMECLSS
7/9/2024 4:27:41 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 4:27:53 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/9/2024 4:27:53 PM SELECT  *  FROM  ITMECLSS
7/9/2024 4:27:53 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 4:32:01 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:32:06 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:34:01 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:36:25 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/9/2024 4:36:25 PM SELECT  *  FROM  ITMECLSS
7/9/2024 4:36:25 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 4:36:47 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:36:48 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:38:22 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:38:22 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:38:23 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:38:23 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:38:28 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:38:28 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:38:30 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:38:30 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:41:03 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/9/2024 4:41:03 PM SELECT  *  FROM  ITMECLSS
7/9/2024 4:41:03 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 4:41:29 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:41:30 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:43:55 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:43:55 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:46:12 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/9/2024 4:46:12 PM SELECT  *  FROM  ITMECLSS
7/9/2024 4:46:12 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 4:46:36 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:46:37 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:48:39 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 4:48:56 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 4:48:56 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 5:31:36 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/9/2024 5:31:36 PM SELECT  *  FROM  ITMECLSS
7/9/2024 5:31:36 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 5:31:58 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 5:32:52 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 5:33:15 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 5:35:38 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 5:35:38 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 5:40:02 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/9/2024 5:40:02 PM SELECT  *  FROM  ITMECLSS
7/9/2024 5:40:02 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 5:40:31 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
7/9/2024 5:40:31 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 5:45:38 PM -----------DBA Số liệu tầng _ Sai lầm -----------
7/9/2024 5:45:38 PM SELECT  *  FROM  ITMECLSS
7/9/2024 5:45:38 PM System.Data.SqlClient.SqlException: Invalid object name 'ITMECLSS'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj)
   at System.Data.SqlClient.TdsParser.Run(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj)
   at System.Data.SqlClient.SqlDataReader.ConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method, DbAsyncResult result)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at System.Data.Common.DbCommand.System.Data.IDbCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.FillInternal(DataSet dataset, DataTable[] datatables, Int32 startRecord, Int32 maxRecords, String srcTable, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable[] dataTables, Int32 startRecord, Int32 maxRecords, IDbCommand command, CommandBehavior behavior)
   at System.Data.Common.DbDataAdapter.Fill(DataTable dataTable)
   at RxjhServer.DbClss.DBA.GetDBToDataTable(String string_0, String string_1)
7/9/2024 5:46:06 PM UpdateMartialArtsAndStatuserror[1]-[TestNhanVat][0]Input string was not in a correct format.
7/9/2024 5:46:08 PM UpdateMartialArtsAndStatuserror[2]-[Naruto][0]Input string was not in a correct format.
